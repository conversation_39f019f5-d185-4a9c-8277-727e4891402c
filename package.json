{"name": "route_manage", "version": "1.0.0", "description": "route_manage", "main": "index.js", "scripts": {"postinstall": "concurrently \"npm:client-install\" \"npm:server-install\"", "prepare": "husky install", "client-install": "cd client && npm install", "server-install": "cd server && npm install", "dev-client": "cd ./client && npm run start", "dev-server": "cd ./server && npm run start", "start": "concurrently \"npm:dev-server\" \"npm:dev-client\"", "build": "sh ./build.sh", "clean": "rm -rf output output_resource", "test": "echo \"Error: no test specified\" && exit 1", "ferry-client": "csp_bff ferry-client", "ferry-server": "csp_bff ferry-server", "ferry-gen": "csp_bff ferry-gen", "ferry-mock": "csp_bff ferry-mock", "sync-idl": "cd server && npm run sync-idl", "update-submodule": "git submodule init && git submodule update"}, "repository": {"type": "git", "url": "******************:ies/route_manage.git"}, "keywords": [], "author": "liyucang", "license": "ISC", "devDependencies": {"@byted-ferry/cli": "^0.1.4", "@hi-tools/git-validator": "^1.0.4", "@byted-ferry/client": "^0.1.0", "@byted-ferry/mock": "0.0.11", "@ies/csp-bff-cli": "0.0.16", "@ies/eden-lint": "^3.4.7", "concurrently": "^5.2.0", "dotenv-cli": "^4.0.0", "husky": "8.0.1", "lint-staged": "13.1.0"}, "lint-staged": {"*": ["eden-lint"]}}