{"name": "route_manage_server", "version": "1.0.0", "description": "back end for route_manage_server", "author": "liyucang", "main": "bootstrap.js", "scripts": {"start": "dotenv -e ../.env -- gulu-hmr --exec 'ts-node' ./src/bootstrap.ts", "debug": "NODE_ENV=dev RUNTIME_IDC_NAME=boe ROUTE_IP=*********** gulu-hmr --exec 'node -r ts-node/register --inspect-brk=9229' ./src/bootstrap.ts", "build": "tsc --build ./tsconfig.json", "lint-ferry": "eden-lint src/app/controller/*.ts src/gen_types/idl/**/* --fix", "gen": "cd ./src && gulu gen http_idl/demo.thrift", "idl": "idl_download -c rpc_idl.yml", "sync-idl": "rm -rf src/idl/rpc_idl && mkdir src/idl/rpc_idl && idl_download -c rpc_idl.yml cd ..", "rpc": "./node_modules/.bin/rpc-gen -r ./src -o ./src/gen_types/idl/rpc", "lint": "eden-lint"}, "dependencies": {"@byted-service/env": "^1.5.2", "@byted-service/logger": "^2.4.0", "@byted-service/rpc": "^2.2.9", "@byted-service/tcc": "^1.14.3", "@byted/ferry-node-gulu": "^1.3.2", "@byted/idl_download": "^0.1.1", "@byted/unified_workbench_auth": "^0.6.5", "@gulu/application-http": "^1.21.0", "@gulu/content-unify-login": "^1.1.7-beta.2", "@gulu/fetch": "^2.1.0", "@gulu/nemo": "^1.3.12", "@gulu/rpc": "^2.1.3", "@gulu/runtime-base": "^5.0.2", "@gulu/static": "^1.1.4", "@gulu/tq-log": "^0.1.8", "@gulu/views": "^2.0.3", "@ies/gulu-stability-metrics": "0.0.1", "debug": "^4.1.1", "koa-compress": "^3.0.0", "nunjucks": "^3.2.2"}, "husky": {"hooks": {"commit-msg": "commitlint commitlint.config.js -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*": ["eden-lint"]}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@gulu/application-hot-reload": "^1.6.1", "@ies/commitlint-plugin-eden": "^1.0.3", "@ies/eden-file-sync": "^1.0.17", "@ies/eden-lint": "^3.4.7", "@types/node": "^12.7.8", "axios": "^0.21.1", "byted-gulu-cli": "^1.21.3", "husky": "^3.0.0", "koa-static": "^5.0.0", "lint-staged": "^9.0.2", "nodemon": "^2.0.15", "ts-node": "^8.8.1", "tsconfig-paths-webpack-plugin": "^3.2.0"}}