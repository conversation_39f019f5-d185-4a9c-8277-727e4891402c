{"compilerOptions": {"target": "es2017", "lib": ["es2015", "es2016", "es2017"], "sourceMap": true, "allowJs": true, "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "noImplicitThis": false, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": false, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "esModuleInterop": true, "module": "commonjs", "baseUrl": "./src", "rootDir": "./src", "outDir": "./output", "paths": {"@constants/*": ["app/constants/*"]}, "types": ["node"], "skipDefaultLibCheck": true, "typeRoots": ["node", "./typings", "./typings/auto-generated", "./node_modules/@types"], "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "static", "./eden.pipeline.js", "./commitlint.config.js", "./build", "./output"]}