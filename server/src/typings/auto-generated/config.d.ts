import "@gulu/application-http";
import DefaultAggregatedConfig from "../../config/config.default";
import DefaultRpcConfig from "../../config/rpc.default";
import DefaultSecurityConfig from "../../config/security.default";
import DefaultSessionConfig from "../../config/session.default";
import DefaultUnifiedWorkbenchAuthConfig from "../../config/unifiedWorkbenchAuth.default";
import EnvAggregatedConfig from "../../config/config.dev";

declare module '@gulu/application-http' {
    interface HttpApplicationConfig extends UnpackConfig<typeof DefaultAggregatedConfig>, UnpackConfig<typeof EnvAggregatedConfig> {
        rpc: RpcConfig;
        security: SecurityConfig;
        session: SessionConfig;
        unifiedWorkbenchAuth: UnifiedWorkbenchAuthConfig;
    }

    interface RpcConfig extends UnpackConfig<typeof DefaultRpcConfig> {
    }

    interface SecurityConfig extends UnpackConfig<typeof DefaultSecurityConfig> {
    }

    interface SessionConfig extends UnpackConfig<typeof DefaultSessionConfig> {
    }

    interface UnifiedWorkbenchAuthConfig extends UnpackConfig<typeof DefaultUnifiedWorkbenchAuthConfig> {
    }
}

type UnpackConfig<T> = T extends {(...args: any[]): any} ? ReturnType<T> : T;
