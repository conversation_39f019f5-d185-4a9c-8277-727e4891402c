import "@gulu/application-http";
import AgentSkillGroupController from "../../app/controller/agentSkillGroup";
import BindingConfigController from "../../app/controller/bindingConfig";
import CategoryController from "../../app/controller/category";
import CustomerCloudController from "../../app/controller/customerCloud";
import HomeController from "../../app/controller/home";
import RouteRuleController from "../../app/controller/routeRule";

declare module '@gulu/application-http' {
    interface HttpControllerHub {
        agentSkillGroup: AgentSkillGroupController;
        bindingConfig: BindingConfigController;
        category: CategoryController;
        customerCloud: CustomerCloudController;
        home: HomeController;
        routeRule: RouteRuleController;
    }
}
