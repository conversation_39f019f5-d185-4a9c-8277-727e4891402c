import "./config";
import "./application";
import "./context";
import "./request";
import "./response";
import "./controller";
import "./service";
import "./plugin";
import * as GuluTypes from "@gulu/application-http";
import "../../../node_modules/@gulu/application-mixin/lib/common-builtin-unit/typings/auto-generated";
import "../../../node_modules/@gulu/application-http/lib/http-builtin-unit/typings/auto-generated";

declare global {
    namespace Gulu {
        interface HttpApplication extends GuluTypes.HttpApplication {
        }

        interface HttpApplicationConfig extends GuluTypes.HttpApplicationConfig {
        }

        interface HttpContext extends GuluTypes.HttpContext {
        }

        interface HttpRequest extends GuluTypes.HttpRequest {
        }

        interface HttpResponse extends GuluTypes.HttpResponse {
        }

        interface HttpControllerHub extends GuluTypes.HttpControllerHub {
        }

        interface HttpServiceHub extends GuluTypes.HttpServiceHub {
        }
    }
}
