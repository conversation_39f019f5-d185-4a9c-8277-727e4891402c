import "@gulu/application-http";
import AgentSkillGroupService from "../../app/service/agentSkillGroup";
import CategoryService from "../../app/service/category";
import CustomerCloudService from "../../app/service/customerCloud";
import DatacenterService from "../../app/service/datacenter";
import DatamarketService from "../../app/service/datamarket";
import RouteRuleService from "../../app/service/routeRule";
import TccService from "../../app/service/tcc";
import TqNotifyService from "../../app/service/tqNotify";

declare module '@gulu/application-http' {
    interface HttpServiceHub {
        agentSkillGroup: AgentSkillGroupService;
        category: CategoryService;
        customerCloud: CustomerCloudService;
        datacenter: DatacenterService;
        datamarket: DatamarketService;
        routeRule: RouteRuleService;
        tcc: TccService;
        tqNotify: TqNotifyService;
    }
}
