import { HttpContext } from '@gulu/application-http';

function isObject(value: any) {
  const type = typeof value;
  return value !== null && (type === 'object' || type === 'function');
}

export function getClientIP(ctx: HttpContext) {
  let ip = ctx.headers['ali-cdn-real-ip'] || ctx.headers['x-real-ip'];
  if (ip) {
    return ip;
  }

  ip = ctx.headers['x-forwarded-for'];
  if (ip) {
    if (ip.indexOf(',') === -1) {
      return ip;
    }
    const ips = ip.split(/,\s*/);
    const r_is_local = /^(10|192\.168)\./;
    if (ips.length > 1 && r_is_local.test(ips[0]) && !r_is_local.test(ips[1])) {
      return ips[1];
    }
    return ips[0];
  }

  if (isObject(ctx) && isObject(ctx.req)) {
    // koa
    ip = ctx.req && ctx.req.socket.remoteAddress;
  } else {
    // express
    ip = ctx.socket.remoteAddress;
  }

  if (!ip) {
    return '***************';
  }
  if (ip === '::1') {
    return '127.0.0.1';
  }

  return ip.replace(/^:.*:/, '');
}
