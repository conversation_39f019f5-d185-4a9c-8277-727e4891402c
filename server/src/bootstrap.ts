import * as path from 'path';
import * as Gulu from '@gulu/application-http';
import fs from 'fs';
import { getEnv, isTTP } from './app/utils';

const isDev = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development';

process.env.GULU_ENV = isDev ? 'dev' : getEnv();

const app = new Gulu.HttpApplication({
  root: path.join(__dirname, './'),
});

if (isDev) {
  // eslint-disable-next-line
  const { receiver } = require('@ies/eden-file-sync');
  app.use(receiver({ type: 'koa' }));
  // app.use(require('koa-static')(__dirname + '/static'));
} else {
  const destPath = path.join(app.root, './app/view');
  if (!fs.existsSync(destPath)) {
    fs.mkdirSync(destPath);
  }
  let htmlRelativePath = '';
  if (isTTP()) {
    htmlRelativePath = './app/view/ttp/index.html';
  } else {
    htmlRelativePath = './app/view/va/index.html';
  }
  const htmlPath = path.join(app.root, htmlRelativePath);
  fs.copyFileSync(htmlPath, path.join(destPath, 'index.html'));
}

const PORT = isDev ? process.env.DEV_SERVER_PORT : process.env.PORT;

app.load(path.resolve(__dirname)).listen(PORT);
