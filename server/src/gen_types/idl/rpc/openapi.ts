/* eslint-disable */
/* tslint:disable */

import * as base from './base';
import * as template from './template';

export { base, template };

/** 模版ID和名称 */
export interface Message {
  /** 模版ID */
  TemplateID: string;
  /** 模版中需要的变量内容 */
  Content: string;
  Receivers?: Array<Receiver>;
}

/** 接收者 */
/** 接收者结构 */
export interface Receiver {
  /** 接收者类型 */
  Type: template.ReceiverType;
  Value: string;
}

/** 邮箱或者lark chat ID */
/** 推送消息变量请求 */
export interface PostMessageReq {
  /** 推送消息 */
  Message: Message;
  Base?: base.Base;
}

/** 推送消息变量响应 */
export interface PostMessageResp {
  /** 推送记录的uuid */
  ID: string;
  BaseResp?: base.BaseResp;
}

/** 建群请求 */
export interface CreateChatReq {
  /** 功能模块，用于判断使用哪个机器人 */
  Module: string;
  /** 群成员 */
  Users: Array<string>;
  /** 群聊名称 */
  ChatName: string;
  /** 群聊描述 */
  ChatDescription?: string;
  Base?: base.Base;
}

/** 建群响应 */
export interface CreateChatResp {
  /** 群ID */
  ChatID: string;
  /** 无法拉进群聊的用户 */
  InvalidUsers: Array<string>;
  BaseResp?: base.BaseResp;
}

/** 邀请人员进去请求 */
export interface AddChatMemberReq {
  /** 功能模块，用于判断使用哪个机器人 */
  Module: string;
  /** 群成员 */
  Users: Array<string>;
  /** 群聊的open ID */
  ChatOpenID: string;
  Base?: base.Base;
}

/** 邀请人员进去响应 */
export interface AddChatMemberResp {
  /** 无法拉进群聊的用户 */
  InvalidUsers: Array<string>;
  BaseResp?: base.BaseResp;
}
