/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';
import * as common from './common';
import * as im_model from './im_model';

export { base, common, im_model };

export interface QueryTaskRequest {
  HostAppID: number;
  Channel?: number;
  AppID?: number;
  Uid: string;
  StartTime?: string;
  EndTime?: string;
  Page?: number;
  PageSize?: number;
  Base?: base.Base;
}

export interface QueryTaskResponse {
  TotalCount: string;
  Tasks: Array<Task>;
  BaseResp?: base.BaseResp;
}

export interface QueryTaskMessageRequest {
  TaskID: string;
  Base?: base.Base;
}

export interface QueryTaskMessageResponse {
  Messages: Array<im_model.Message>;
  BaseResp?: base.BaseResp;
}

export interface Task {
  TaskID: string;
  ConversationShortID: string;
  /** 用户ID */
  UID: string;
  /** 客服名称 */
  UniqueName: string;
  /** 客服昵称 */
  Name: string;
  /** 开始转人工时间 */
  StartTime: string;
  /** 结束时间，如果没有则是0 */
  EndTime: string;
  /** 人工进线咨询时长，如果未结束则为 0 */
  DurationSeconds: string;
  /** 队列名 */
  ChannelName: string;
  /** 状态 */
  Status: number;
  /** 结束方式 */
  EndStatus: number;
  /** 用户名 */
  UserName: string;
  /** 客服平台AppID */
  AppID: number;
  /** 客服平台Channel */
  Channel: number;
  /** parent task id */
  ParentTaskID?: string;
  /** 技能组ID */
  SkillGroup?: string;
  /** 租户ID */
  TenantID?: string;
  /** 客服ID */
  AgentID?: string;
  /** 结束的用户 */
  EndUser?: number;
  /** 分配客服的时间 */
  AssignTime?: string;
  /** 客服Email */
  AgentEmail?: string;
  /** 客服的门神uuid */
  AgentUuid?: string;
  /** 技能组名称 */
  SkillGroupName?: string;
  /** 客服首次回复时间 */
  CustomerFirstResponseTime?: string;
  /** 客服Uid */
  AgentUid?: string;
  /** 客服平台用户ID */
  IMUserID?: string;
  BizUserID?: string;
}

/** 业务原始用户ID */
export interface GetTaskRequest {
  TaskID: string;
  Base?: base.Base;
}

export interface GetTaskResponse {
  Task: Task;
  BaseResp?: base.BaseResp;
}

export interface QueryBizTypeRequest {
  HostAppID: number;
  Base?: base.Base;
}

export interface GetBizTypeByAppChannelRequest {
  AppID: number;
  Channel: number;
  TaskID?: number;
  Base?: base.Base;
}

export interface GetBizTypeByAppChannelsRequest {
  AppID: number;
  Channel: Array<number>;
  Base?: base.Base;
}

export interface QueryBizTypesByAccessPartyIDRequest {
  AccessPartyID: number;
  Base?: base.Base;
}

export interface BizType {
  /** ID */
  ID: number;
  /** 客服平台AppID */
  AppID: number;
  /** 客服平台App Name */
  AppName: string;
  /** 客服平台Channel */
  Channel: number;
  /** 客服平台 Channel Name */
  ChannelName: string;
  /** 宿主AppID */
  HostAppID: string;
  /** 宿主App name */
  HostAppName: string;
  /** 标签应用ID */
  AppBaseID: string;
  /** 资源ID */
  ResourceID: string;
  /** 子资源ID */
  SubResourceID: string;
  /** 接入方ID */
  AccessPartyID: string;
  /** 场景 */
  Scene?: string;
  /** 入口ID */
  EntranceId?: string;
  EntranceName?: string;
}

/** 入口名 */
export interface QueryBizTypesResponse {
  BizTypeList: Array<BizType>;
  BaseResp?: base.BaseResp;
}

export interface GetBizTypeRequest {
  ID: number;
  Base?: base.Base;
}

export interface GetBizTypeResponse {
  bizType: BizType;
  BaseResp?: base.BaseResp;
}

export interface QueryNoticesRequest {
  AppID: number;
  Channel: number;
  Base?: base.Base;
}

export interface Notice {
  ID: number;
  Title: string;
  Content: string;
}

export interface QueryNoticesResponse {
  Notices: Array<Notice>;
  BaseResp?: base.BaseResp;
}

export interface LineupConfirmRequest {
  AppID: number;
  Channel: number;
  Base?: base.Base;
}

export interface LineupConfirmResponse {
  NeedConfirm: boolean;
  Position: string;
  Mins: string;
  Message: string;
  ConfirmText: string;
  BaseResp?: base.BaseResp;
}

export interface QueryLatestTaskRequest {
  AppID: number;
  ConversationShortID: string;
  Base?: base.Base;
}

export interface QueryLatestTaskResponse {
  Task?: Task;
  BaseResp?: base.BaseResp;
}

export interface GetWaitingInfoRequest {
  AppID: number;
  ConversationShortID: string;
  Base?: base.Base;
}

export interface GetWaitingInfoResponse {
  position: string;
  mins: number;
  pollingSpan: string;
  message: string;
  BaseResp?: base.BaseResp;
}

export interface GipReplyRequest {
  Source: number;
  Uid: string;
  Operator: string;
  Content: string;
  /** 图片 tos uri */
  ImageUrl: string;
  Base?: base.Base;
}

export interface GipReplyResponse {
  TaskID: string;
  ServerMessageID: string;
  BaseResp?: base.BaseResp;
}

export interface GipSyncUserMessageRequest {
  Source: number;
  Uid: string;
  Content: string;
  /** 图片 tos uri */
  ImageUrl: string;
  Base?: base.Base;
}

export interface GipSyncUserMessageResponse {
  TaskID: string;
  ServerMessageID: string;
  BaseResp?: base.BaseResp;
}

export interface GetUnreadMessageRequest {
  AppID: number;
  Uid: string;
  Source?: number;
  Base?: base.Base;
}

export interface GetUnreadMessageResponse {
  UnreadMessageCount: number;
  BaseResp?: base.BaseResp;
}

export interface LabelInfo {
  Id: string;
  Name: string;
}

export interface MGetMessageLabelsRequest {
  ServerMessageIds: Array<string>;
  Base?: base.Base;
}

export interface MGetMessageLabelsResponse {
  LabelMap: { [key: string]: Array<LabelInfo> };
  BaseResp?: base.BaseResp;
}

export declare class CustomerCloudService {
  /** App映射相关 */
  /** 根据ID获取业务类型 */
  public GetBizType(req: GetBizTypeRequest): Promise<GetBizTypeResponse>;
  public GetBizType(ctx: ClientInvokeOptions, req: GetBizTypeRequest): Promise<GetBizTypeResponse>;

  /** 根据AppID+Channel获取业务类型 */
  public GetBizTypeByAppChannel(req: GetBizTypeByAppChannelRequest): Promise<GetBizTypeResponse>;
  public GetBizTypeByAppChannel(
    ctx: ClientInvokeOptions,
    req: GetBizTypeByAppChannelRequest
  ): Promise<GetBizTypeResponse>;

  /** 根据AppID+多个Channel获取业务类型 */
  public GetBizTypeByAppChannels(req: GetBizTypeByAppChannelsRequest): Promise<QueryBizTypesResponse>;
  public GetBizTypeByAppChannels(
    ctx: ClientInvokeOptions,
    req: GetBizTypeByAppChannelsRequest
  ): Promise<QueryBizTypesResponse>;

  /** 根据宿主AppID查询业务类型 */
  public QueryBizTypes(req: QueryBizTypeRequest): Promise<QueryBizTypesResponse>;
  public QueryBizTypes(ctx: ClientInvokeOptions, req: QueryBizTypeRequest): Promise<QueryBizTypesResponse>;

  /** 根据接入方ID(AccessPartyID)查询所有业务类型 */
  public QueryBizTypesByAccessPartyID(req: QueryBizTypesByAccessPartyIDRequest): Promise<QueryBizTypesResponse>;
  public QueryBizTypesByAccessPartyID(
    ctx: ClientInvokeOptions,
    req: QueryBizTypesByAccessPartyIDRequest
  ): Promise<QueryBizTypesResponse>;

  /** 根据接入方ID(AccessPartyID)查询所有业务类型(通过接入方获取入口列表，根据入口列表再查询业务类型) */
  public QueryBizTypesByAccessPartyIDNew(req: QueryBizTypesByAccessPartyIDRequest): Promise<QueryBizTypesResponse>;
  public QueryBizTypesByAccessPartyIDNew(
    ctx: ClientInvokeOptions,
    req: QueryBizTypesByAccessPartyIDRequest
  ): Promise<QueryBizTypesResponse>;

  /** Task相关 */
  /** 获取Task详情 */
  public GetTask(req: GetTaskRequest): Promise<GetTaskResponse>;
  public GetTask(ctx: ClientInvokeOptions, req: GetTaskRequest): Promise<GetTaskResponse>;

  /** 查询人工进线列表 */
  public QueryTasks(req: QueryTaskRequest): Promise<QueryTaskResponse>;
  public QueryTasks(ctx: ClientInvokeOptions, req: QueryTaskRequest): Promise<QueryTaskResponse>;

  /** 根据TaskID查询聊天记录 */
  public QueryTaskMessages(req: QueryTaskMessageRequest): Promise<QueryTaskMessageResponse>;
  public QueryTaskMessages(ctx: ClientInvokeOptions, req: QueryTaskMessageRequest): Promise<QueryTaskMessageResponse>;

  /** 根据conversationID查询最近一条会话 */
  public QueryLatestTask(req: QueryLatestTaskRequest): Promise<QueryLatestTaskResponse>;
  public QueryLatestTask(ctx: ClientInvokeOptions, req: QueryLatestTaskRequest): Promise<QueryLatestTaskResponse>;

  /** 根据conversationID查询最近一条成功转人工的会话 */
  public QueryLatestValidTask(req: QueryLatestTaskRequest): Promise<QueryLatestTaskResponse>;
  public QueryLatestValidTask(ctx: ClientInvokeOptions, req: QueryLatestTaskRequest): Promise<QueryLatestTaskResponse>;

  /** Misc */
  /** 查询IM页面Nocie列表 */
  public QueryNotices(req: QueryNoticesRequest): Promise<QueryNoticesResponse>;
  public QueryNotices(ctx: ClientInvokeOptions, req: QueryNoticesRequest): Promise<QueryNoticesResponse>;

  /** 转人工二次确认信息 */
  public LineupInfo(req: LineupConfirmRequest): Promise<LineupConfirmResponse>;
  public LineupInfo(ctx: ClientInvokeOptions, req: LineupConfirmRequest): Promise<LineupConfirmResponse>;

  /** 排队 */
  /** 查询排队信息 */
  public GetWaitingInfo(req: GetWaitingInfoRequest): Promise<GetWaitingInfoResponse>;
  public GetWaitingInfo(ctx: ClientInvokeOptions, req: GetWaitingInfoRequest): Promise<GetWaitingInfoResponse>;

  /** GIP */
  /** Gip运营回复用户时调用该 RPC，同步运营的回复到客服平台 */
  public GipReply(req: GipReplyRequest): Promise<GipReplyResponse>;
  public GipReply(ctx: ClientInvokeOptions, req: GipReplyRequest): Promise<GipReplyResponse>;

  /** Gip收到用户消息时，通过该RPC将用户的消息同步至客服平台 */
  public GipSyncUserMessage(req: GipSyncUserMessageRequest): Promise<GipSyncUserMessageResponse>;
  public GipSyncUserMessage(
    ctx: ClientInvokeOptions,
    req: GipSyncUserMessageRequest
  ): Promise<GipSyncUserMessageResponse>;

  /** 消息 */
  /** 获取用户未读的客服消息数 */
  public GetUnreadMessage(req: GetUnreadMessageRequest): Promise<GetUnreadMessageResponse>;
  public GetUnreadMessage(ctx: ClientInvokeOptions, req: GetUnreadMessageRequest): Promise<GetUnreadMessageResponse>;

  /** 批量获取标签信息 */
  public MGetMessageLabels(req: MGetMessageLabelsRequest): Promise<MGetMessageLabelsResponse>;
  public MGetMessageLabels(ctx: ClientInvokeOptions, req: MGetMessageLabelsRequest): Promise<MGetMessageLabelsResponse>;
}
