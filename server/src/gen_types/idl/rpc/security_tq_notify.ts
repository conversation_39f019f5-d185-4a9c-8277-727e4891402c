/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';
import * as openapi from './openapi';
import * as template from './template';

export { base, openapi, template };

export enum ErrorCode {
  ServerError = 100,
  ParameterError = 101,
  DependencyError = 102,
}

export declare class TqNotifierService {
  /** 模版配置相关接口 */
  /** 创建模版 */
  public CreateTemplate(req: template.UpsertTemplateReq): Promise<template.TemplateIDResp>;
  public CreateTemplate(ctx: ClientInvokeOptions, req: template.UpsertTemplateReq): Promise<template.TemplateIDResp>;

  /** 编辑模版 */
  public UpdateTemplate(req: template.UpsertTemplateReq): Promise<template.TemplateIDResp>;
  public UpdateTemplate(ctx: ClientInvokeOptions, req: template.UpsertTemplateReq): Promise<template.TemplateIDResp>;

  /** 查询模版详情 */
  public TemplateDetail(req: template.TemplateIDReq): Promise<template.TemplateDetailResp>;
  public TemplateDetail(ctx: ClientInvokeOptions, req: template.TemplateIDReq): Promise<template.TemplateDetailResp>;

  /** 删除模版 */
  public DeleteTemplate(req: template.TemplateIDReq): Promise<template.DeleteTemplateResp>;
  public DeleteTemplate(ctx: ClientInvokeOptions, req: template.TemplateIDReq): Promise<template.DeleteTemplateResp>;

  /** 查询模版列表 */
  public TemplateList(req: template.TemplateListReq): Promise<template.TemplateListResp>;
  public TemplateList(ctx: ClientInvokeOptions, req: template.TemplateListReq): Promise<template.TemplateListResp>;

  /** 查询某个模版的推送历史 */
  public HistoryList(req: template.HistoryListReq): Promise<template.HistoryListResp>;
  public HistoryList(ctx: ClientInvokeOptions, req: template.HistoryListReq): Promise<template.HistoryListResp>;

  /** OpenAPI相关接口 */
  /** 推送消息变量 */
  public PostMessage(req: openapi.PostMessageReq): Promise<openapi.PostMessageResp>;
  public PostMessage(ctx: ClientInvokeOptions, req: openapi.PostMessageReq): Promise<openapi.PostMessageResp>;

  /** 建群 */
  public CreateChat(req: openapi.CreateChatReq): Promise<openapi.CreateChatResp>;
  public CreateChat(ctx: ClientInvokeOptions, req: openapi.CreateChatReq): Promise<openapi.CreateChatResp>;

  /** 邀请人员进群 */
  public AddChatMember(req: openapi.AddChatMemberReq): Promise<openapi.AddChatMemberResp>;
  public AddChatMember(ctx: ClientInvokeOptions, req: openapi.AddChatMemberReq): Promise<openapi.AddChatMemberResp>;
}
