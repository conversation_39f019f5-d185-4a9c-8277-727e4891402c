/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';
import * as agent_skill_group from './agent_skill_group';

export { base, agent_skill_group };

/** 规则任务状态 */
export enum RuleTaskStatus {
  /** 运行中 */
  HAS_RUNNING = 1,
  NO_RUNNING = 0,
}

/** 无运行中 */
/** 规则禁用状态 */
export enum RuleStopStatus {
  /** 禁用 */
  DISABLED = 1,
  USING = 0,
}

/** 启用 */
/** 规则状态 */
export enum EntityStatus {
  /** 可用 */
  ENABLE = 1,
  /** 不可用 */
  UNABLE = 0,
  WAIT_ENABLE = -1,
}

/** 创建中 */
/** 时间单位 */
export enum TimeUnit {
  /** 分钟 */
  MINUTE = 1,
  /** 小时 */
  HOUR = 2,
  SECOND = 3,
}

/** 秒 */
/** 动作时间类型 */
export enum ActionTimeType {
  /** 目标前 */
  BEFORE = 1,
  AFTER = 0,
}

/** 目标后 */
/** 动作类型 */
export enum ActionWay {
  /** 飞书 */
  LARK = 1,
  /** 飞书加急 */
  LARK_URGENT = 2,
  /** 飞书短信加急 */
  LARK_URGENT_MSG = 3,
  /** 飞书电话加急 */
  LARK_URGENT_PHONE = 4,
  /** 飞书到群（场控用） */
  LARK_GROUP = 5,
  /** 邮件 */
  EMAIL = 6,
  /** 电商站内信角标 */
  E_MESSAGE = 7,
  /** 电商站内信弹窗 */
  E_MESSAGE_POP = 8,
  /** 电商飞书 */
  E_LARK = 9,
  /** 电商飞书加急 */
  E_LARK_URGENT = 10,
  /** 电商飞书短信加急 */
  E_LARK_URGENT_MSG = 11,
  /** 电商飞书电话加急 */
  E_LARK_URGENT_PHONE = 12,
  /** 电商飞书到群 */
  E_LARK_GROUP = 13,
  MESSAGE_ALTER = 14,
}

/** 工单站内信弹窗（场控用） */
/** 条件操作类型 */
export enum FilterOperateType {
  /** 求并集 */
  UNION = 0,
  INTERSECTION = 1,
}

/** 求交集 */
/** 操作类型 */
export enum OperatorType {
  NUMBER_EQUAL = 1,
  NUMBER_NOT_EQUAL = 2,
  CONTAINS = 3,
  GREATER = 4,
  LESS = 5,
  GREATER_OR_EQUAL = 6,
  LESS_OR_EQUAL = 7,
  START_WITH = 8,
  END_WITH = 9,
  IS_NULL = 10,
  IS_NOT_NULL = 11,
  IN = 12,
  STRING_CONTAINS = 13,
  STRING_EQUAL = 14,
  STRING_NOT_EQUAL = 15,
  TIME_EQUAL = 16,
  TIME_NOT_EQUAL = 17,
  TIME_GREATER = 18,
  TIME_LESS = 19,
  TIME_GREATER_OR_EQUAL = 20,
  TIME_LESS_OR_EQUAL = 21,
  NOT_IN = 22,
  LIST_EQUAL = 30,
  LIST_NOT_EQUAL = 31,
  STRING_NOT_CONTAINS = 32,
  LIST_RETAIN = 33,
}

export enum FieldValueType {
  /** 单选 */
  SINGLE_CHOOSE = 1,
  /** 多选 */
  MULTI_CHOOSE = 2,
  /** 混合(多选+输入框) */
  MIX = 3,
  /** 时间控件 */
  DATE = 4,
  /** 输入框 */
  INPUT = 5,
  /** 批量输入 */
  BATCH_INPUT = 6,
}

/** 规则状态 */
export enum RuleStatus {
  /** 启用 */
  ENABLE = 1,
  /** 禁用 */
  UNABLE = 0,
  /** 草稿 */
  DRAFT = 2,
  DELETE = 3,
}

/** 优先级 */
/** 规则生效环境 */
export enum RuleEnv {
  /** PPE环境 */
  PPE = 0,
  PROD = 1,
}

/** 线上环境 */
/** 条件组合操作类型 */
export enum OpGroup {
  /** 求交集 */
  AND = 1,
  /** 求并集 */
  OR = 2,
  NOT = 3,
}

/** filter字段 */
export interface FieldMeta {
  Id: string;
  /** 字段的前端展示名称 */
  DisplayName: string;
  /** 字段的数据库存储名称 */
  MapName: string;
  OperatorIds: Array<number>;
  ModelId: string;
  Type: number;
  DataType: number;
  OriginName?: string;
  CurrentName?: string;
  FieldPath?: string;
  ComputeFunc?: string;
  Scene?: string;
  ParentId?: string;
  /** 租户ID */
  TenantId?: string;
  AccessPartyId?: string;
  /** 创建时间 */
  CreatedAt: string;
  /** 更新时间 */
  UpdatedAt: string;
  /** 附加信息 */
  Extra?: string;
  /** 创建人ID */
  CreatorAgentId: string;
  UpdaterAgentId: string;
}

/** 更新人ID */
/** filter字段 */
export interface FieldCondition {
  FieldId: string;
  /** 字段的前端展示名称 */
  FieldDisplayName: string;
  /** 字段的数据库存储名称。即将废弃 */
  FieldMapName: string;
  /** 即将废弃 */
  OperatorIds: Array<number>;
  /** 字段的计算表达式 */
  ComputeFunc?: string;
  /** admin改造后的字段名，即规则实际运行需要的字段 */
  FieldName?: string;
  /** 字段运行使用的操作符 */
  OperatorList?: Array<string>;
  FieldDataType?: number;
}

/** 字段类型 integer = 1 long = 2 string = 3 double = 5 boolean = 6 */
/** 动作形参元数据 */
export interface FormalParamMeta {
  Id: string;
  /** 前端展示名称 */
  DisplayName: string;
  /** 数据库存储名称 */
  MapName: string;
  /** 值类型 */
  ValueType: number;
  ValueOptions?: string;
}

/** 选项型动作形参的可取值，具体取值取决于FieldGetType 0：字段值的json串；1：获取值的URL */
/** 动作元数据 */
export interface ActionMeta {
  Id: string;
  /** 前端展示名称 */
  DisplayName: string;
  FormalParamMetas: Array<FormalParamMeta>;
}

/** 形参名称及其类型、可取值 */
/** 占位符元数据 */
export interface PlaceholderMeta {
  Id?: string;
  /** 前端展示名称 */
  DisplayName: string;
  MapName: string;
}

/** 数据库存储名称 */
/** 依赖字段元数据 */
export interface DependencyFieldMeta {
  Id?: string;
  /** 前端展示名称 */
  DisplayName: string;
  MapName: string;
}

/** 数据库存储名称 */
/** SLA目标元数据 */
export interface SLAAimMeta {
  /** ID */
  Id: string;
  /** 名称 首响、完结 */
  Name: string;
  /** 条件 */
  Filter: Filter;
  /** 目标支持的全部动作及其形参 TODO 目前不区分目标 */
  ActionMetas: Array<ActionMeta>;
  /** 目标支持的全部占位符 TODO 目前不区分目标 */
  PlaceholderMetas: Array<PlaceholderMeta>;
  /** 目标支持的全部规则依赖字段 TODO 目前不区分目标 */
  DependencyFieldMetas: Array<DependencyFieldMeta>;
  /** 状态 1：可用；0：不可用 */
  Status: EntityStatus;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId?: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 创建时间 */
  CreatedAt: string;
  /** 更新时间 */
  UpdatedAt: string;
  /** 附加信息 */
  Extra: string;
  /** 创建人ID */
  CreatorAgentId: string;
  UpdaterAgentId: string;
}

/** 更新人ID */
/** SLA目标元数据 */
export interface SLAAimMetaSimple {
  /** ID */
  Id: string;
  /** 名称 首响、完结 */
  Name: string;
  Extra: string;
}

/** 附加信息 */
/** 单个字段条件 */
export interface FilterUnit {
  FieldId?: string;
  /** todo 自定义字段前缀 */
  FieldMapName: string;
  /** todo 前端映射ID */
  OperatorId: number;
  FieldValue: string;
}

/** json格式，如"[\"38\",\"40\"]" */
/** 条件 */
export interface Filter {
  OperateType: FilterOperateType;
  FilterUnitList: Array<FilterUnit>;
  Extra?: string;
}

/** 级联关系用 */
/** 规则 */
export interface AdminRule {
  /** ID */
  Id: string;
  /** 名称 */
  DisplayName: string;
  /** 优先级 */
  Priority: number;
  /** 条件 */
  Filter?: Filter;
  /** 动作信息 json, list查询时填充''，get查询时填充 */
  Value: string;
  /** 禁用状态 */
  StopStatus: RuleStopStatus;
  /** 状态 */
  Status: EntityStatus;
  /** 应用类型，如0：触发器、1：SLA、2：后台 */
  AppId: number;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 创建时间 */
  CreatedAt: string;
  /** 更新时间 */
  UpdatedAt: string;
  /** 附加信息 */
  Extra: string;
  /** 创建人ID */
  CreatorAgentId: string;
  /** 更新人ID */
  UpdaterAgentId: string;
  /** 规则任务状态 get查询时填充 */
  TaskStatus?: RuleTaskStatus;
  /** 运行规则ID */
  MainRuleId?: string;
  /** 原始规则ID */
  OriginId?: string;
  /** 版本号 */
  Version?: number;
  TriggerId?: string;
}

/** 拖拽规则 */
export interface AdminRuleSimple {
  /** ID */
  Id: string;
  Priority: number;
}

/** 优先级 */
export interface FieldListGetRequest {
  /** 事件ID，业务方和触发器服务约定 */
  EventId: string;
  /** 接入方ID */
  AccessPartyId?: string;
  lang?: string;
  Base?: base.Base;
}

export interface FieldListGetResponse {
  FieldConditions: Array<FieldCondition>;
  BaseResp: base.BaseResp;
}

export interface FieldValuesGetRequest {
  FieldId: string;
  OperatorId: number;
  Base?: base.Base;
}

export interface FieldValuesGetResponse {
  /** 值类型 */
  FieldValueType: number;
  /** 具体取值取决于FieldGetType 0：字段值的json串；1：获取值的URL */
  FieldValues: string;
  BaseResp: base.BaseResp;
}

export interface FieldCreateRequest {
  /** 字段的前端展示名称 */
  DisplayName: string;
  /** 字段的数据库存储名称 */
  MapName: string;
  OperatorIds: Array<number>;
  ModelId?: string;
  Type: number;
  DataType: number;
  OriginName?: string;
  CurrentName?: string;
  FieldPath?: string;
  ComputeFunc?: string;
  Scene?: string;
  ParentId?: string;
  /** 租户ID */
  TenantId?: string;
  AccessPartyId?: string;
  /** 创建人ID */
  CreatorAgentId: string;
  Base?: base.Base;
}

export interface FieldCreateResponse {
  FieldMeta?: FieldMeta;
  BaseResp: base.BaseResp;
}

export interface FieldUpdateRequest {
  Id: string;
  /** 字段的前端展示名称 */
  DisplayName?: string;
  /** 字段的数据库存储名称 */
  MapName?: string;
  OperatorIds?: Array<number>;
  ModelId?: string;
  Type?: number;
  DataType?: number;
  OriginName?: string;
  CurrentName?: string;
  FieldPath?: string;
  ComputeFunc?: string;
  Scene?: string;
  ParentId?: string;
  /** 租户ID */
  TenantId?: string;
  AccessPartyId?: string;
  UpdaterAgentId: string;
  Base?: base.Base;
}

export interface FieldUpdateResponse {
  FieldMeta?: FieldMeta;
  BaseResp: base.BaseResp;
}

export interface FieldDeleteRequest {
  Id: string;
  UpdaterAgentId: number;
  /** 租户ID */
  TenantId?: string;
  /** 接入方ID */
  AccessPartyId?: string;
  Base?: base.Base;
}

export interface FieldDeleteResponse {
  result: boolean;
  BaseResp: base.BaseResp;
}

export interface ActionMetaListGetRequest {
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId?: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 状态 */
  Status?: EntityStatus;
  Base?: base.Base;
}

export interface ActionMetaListGetResponse {
  /** 返回值 */
  ActionMetas: Array<ActionMeta>;
  BaseResp: base.BaseResp;
}

export interface SLAAimMetaListGetRequest {
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId?: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 状态 */
  Status?: EntityStatus;
  Base?: base.Base;
}

export interface SLAAimMetaListGetResponse {
  /** 返回值 */
  SLAAimMetaList: Array<SLAAimMeta>;
  BaseResp: base.BaseResp;
}

export interface SLAAimMetaSimpleListGetResponse {
  /** 返回值 */
  SLAAimMetaSimpleList: Array<SLAAimMetaSimple>;
  BaseResp: base.BaseResp;
}

export interface SLAAimMetaCreateRequest {
  /** 名称 首响、完结 */
  Name: string;
  /** 条件 */
  Filter: Filter;
  /** 目标支持的全部动作 */
  ActionMetas: Array<ActionMeta>;
  /** 目标支持的全部占位符，如运行时param:${ticket.id}原样写入,配置时param:${aim.time}需替换 */
  PlaceholderMetas: Array<PlaceholderMeta>;
  /** 目标支持的全部规则依赖字段 */
  DependencyFieldMetas: Array<DependencyFieldMeta>;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId?: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 附加信息 */
  Extra: string;
  /** 创建人ID */
  CreatorAgentId: string;
  Base?: base.Base;
}

export interface SLAAimMetaCreateResponse {
  SLAAimMeta?: SLAAimMeta;
  BaseResp: base.BaseResp;
}

export interface AdminRuleListGetRequest {
  /** 应用类型，如0：触发器、1：SLA、2：后台 */
  AppId: number;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  IdList?: Array<string>;
  /** 状态 */
  Status?: EntityStatus;
  /** 禁用状态 */
  StopStatus?: RuleStopStatus;
  /** 名称 */
  DisplayNameLike?: string;
  /** 优先级 */
  Priority?: number;
  /** 事件ID */
  EventId?: string;
  Base?: base.Base;
}

export interface AdminRuleListGetResponse {
  AdminRuleList: Array<AdminRule>;
  Count: number;
  BaseResp: base.BaseResp;
}

export interface AdminRulePageGetRequest {
  /** 应用类型，如0：触发器、1：SLA、2：后台 */
  AppId: number;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 业务标识，区分工单、电商工单 */
  SourceId?: string;
  /** 页码 */
  Page: number;
  /** 每页条数 */
  PageSize: number;
  /** 禁用状态 */
  StopStatus?: RuleStopStatus;
  /** 状态 */
  Status?: EntityStatus;
  IdList?: Array<string>;
  /** 名称 */
  DisplayNameLike?: string;
  /** 优先级 */
  Priority?: number;
  /** 事件ID */
  EventId?: string;
  Base?: base.Base;
}

export interface AdminRulePageGetResponse {
  AdminRuleList: Array<AdminRule>;
  Count: number;
  BaseResp: base.BaseResp;
}

export interface AdminRuleGetByIdRequest {
  AppId: number;
  SourceId: string;
  Id: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  Base?: base.Base;
}

export interface AdminRuleGetByIdResponse {
  AdminRule?: AdminRule;
  BaseResp: base.BaseResp;
}

export interface AdminRuleCreateRequest {
  AppId: number;
  SourceId: string;
  DisplayName: string;
  Priority: number;
  /** 条件 */
  Filter?: Filter;
  /** 动作信息 json
 IM路由：   {
 "skill_group":{"id":303}
 "support_overflow": 1, //1：支持溢出；0：不支持
 "skill_group_overflow": {"id": [1, 2]}
 }
 机器人路由：{
 "is_open": 0,
 	    "bot_id": {
 		    "id": 1
 }
 }
 SLA：目标json，如：
 [
 {'aimMetaId': 1,
 'aimTime': 1,
 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
 'aimSteps': [
 {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
 , {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
 ]}
 ,{'aimMetaId': 2,
 'aimTime': 2,
 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
 'aimSteps': [
 {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
 , {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
 ]}
 ] */
  Value: string;
  Extra: string;
  CreatorAgentId: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 事件ID */
  EventId: string;
  /** 规则组ID */
  RuleGroupId?: string;
  /** 问题卡片内容。非问题卡片请求不可传 */
  CreateCardRequest?: agent_skill_group.CreateCardRequest;
  /** 是否禁用规则，不传默认为启用 */
  Disable?: boolean;
  Base?: base.Base;
}

export interface AdminRuleCreateResponse {
  AdminRule?: AdminRule;
  BaseResp: base.BaseResp;
}

export interface AdminRuleUpdateRequest {
  Id: string;
  AppId: number;
  SourceId: string;
  DisplayName?: string;
  Priority?: number;
  /** 条件 */
  Filter?: Filter;
  Value?: string;
  /** 状态 */
  Status?: EntityStatus;
  /** 禁用状态 */
  StopStatus?: RuleStopStatus;
  Extra?: string;
  UpdaterAgentId: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 事件ID */
  EventId: string;
  /** 卡片的修改请求，未修改时可不传 */
  UpdateCardRequest?: agent_skill_group.UpdateCardRequest;
  Base?: base.Base;
}

export interface AdminRuleUpdateResponse {
  AdminRule?: AdminRule;
  BaseResp: base.BaseResp;
}

export interface AdminRuleDeleteRequest {
  Id: string;
  AppId: number;
  SourceId: string;
  UpdaterAgentId: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  Base?: base.Base;
}

export interface AdminRuleDeleteResponse {
  result: boolean;
  BaseResp: base.BaseResp;
}

export interface AdminRuleBatchUpdateRequest {
  AdminRules: Array<AdminRuleSimple>;
  UpdaterAgentId: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  AppId: number;
  SourceId: string;
  /** 事件ID */
  EventId: string;
  Base?: base.Base;
}

export interface AdminRuleBatchUpdateResponse {
  result: boolean;
  BaseResp: base.BaseResp;
}

/** 已删除 */
/** 规则优先级 */
export interface RulePriority {
  /** ID */
  Id: string;
  Priority: number;
}

/** 求反计算 */
/** 运算型参数表达式 */
export interface MathExpr {
  /** 数学运算符 */
  OpMath: string;
  /** 运算左值 */
  Lhs: Expr;
  Rhs: Expr;
}

/** 运算右值 */
/** 方法型参数表达式 */
export interface FuncExpr {
  /** 方法名，为字符串，如"abc" */
  FuncName: string;
  ParamExprMap?: { [key: string]: Expr };
}

/** 参数map，key为字符串，如"abc" */
/** 特征型参数表达式 */
export interface FeatureExpr {
  /** 特征名，为字符串，如"ticket.status" */
  FeatureName: string;
  ParamExprMap?: { [key: string]: Expr };
}

/** 参数map，key为字符串，如"abc" */
/** 参数表达式 */
export interface Expr {
  /** 元素可以为任意一种Expr */
  ExprList?: Array<Expr>;
  /** 元素可以为任意一种Expr，用于拼成字符串 */
  SubstringList?: Array<Expr>;
  MathExpr?: MathExpr;
  FuncExpr?: FuncExpr;
  /** 特征型参数表达式，如"ticket.assignee_agent.status()" */
  FeatureExpr?: FeatureExpr;
  /** 变量型参数表达式，如"$id" */
  VarExpr?: string;
  /** 元素可以为字符串，数字，布尔值中任意一种常量 */
  ConstantList?: Array<string>;
  Constant?: string;
}

/** 常量型参数表达式：字符串，如\"abc\"，\"300\"；数字，如100.1；布尔值，如true */
/** 条件表达式 */
export interface ConditionExpr {
  OpCheck: string;
  /** 运算左值 */
  Lhs: Expr;
  Rhs?: Expr;
}

/** 运算右值 */
/** 条件组合 */
export interface ConditionGroupExpr {
  OpGroup: string;
  Conditions?: Array<ConditionExpr>;
  ConditionGroups?: Array<ConditionGroupExpr>;
}

/** conditions和conditionGroups有且只有一个非空 */
/** 动作表达式 */
export interface ActionExpr {
  /** 动作名，为字符串，如"abc" */
  ActionName: string;
  ParamExprMap?: { [key: string]: Expr };
}

/** 参数map，key为字符串，如"abc" */
/** 动作组合 */
export interface ActionGroupExpr {
  Sequential: boolean;
  ContinueOnFail: boolean;
  Actions: Array<ActionExpr>;
}

/** 延时步骤 */
export interface DelayStepExpr {
  /** 即时动作 */
  ActionGroup: ActionGroupExpr;
  /** 即时条件名，为字符串，如"abc" */
  FilterAim: string;
  DelayTime: Expr;
}

/** 延时参数表达式 */
/** 规则返回 */
export interface AimExpr {
  /** 延时步骤 */
  DelaySteps?: Array<DelayStepExpr>;
  /** 即时动作 */
  ActionGroup?: ActionGroupExpr;
  ReturnValue?: Expr;
}

/** 路由返回 delaySteps/actionGroup/returnValue有且只有一个非空 */
/** 规则元数据 */
export interface Rule {
  /** ID */
  Id: string;
  /** 名称 */
  DisplayName: string;
  /** 优先级 */
  Priority: number;
  /** 规则条件部分 */
  Expression?: ConditionGroupExpr;
  /** 动作部分 */
  ActionInfo?: AimExpr;
  /** 规则组id */
  RuleGroupId: string;
  /** 规则状态 */
  Status: RuleStatus;
  /** 规则描述 */
  Description?: string;
  /** 创建时间 */
  CreatedAt: string;
  /** 更新时间 */
  UpdatedAt: string;
  /** 创建人ID */
  CreatorAgentId: string;
  /** 更新人ID */
  UpdaterAgentId: string;
  DraftEditType: number;
}

/** 草稿编辑类型1-新增 2-编辑 0-未修改 */
/** 创建规则 */
export interface CreateRuleRequest {
  /** 事件 */
  EventKey: string;
  /** 规则组ID */
  RuleGroupId?: string;
  /** 规则名称 */
  DisplayName: string;
  /** 规则优先级 */
  Priority: number;
  /** 条件-DSL */
  Expression: ConditionGroupExpr;
  /** 动作部分 */
  ActionInfo: AimExpr;
  /** 创建人id */
  CreatorAgentId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 是否启用规则，不传默认为禁用 */
  Enable?: boolean;
  /** 扩展字段，前端可用于复现规则 */
  Extra?: string;
  /** 规则描述 */
  Description: string;
  /** 规则生效环境，不传默认为线上 */
  RuleEnv?: RuleEnv;
  /** 接口版本 UI传v1 */
  Version: string;
  Base?: base.Base;
}

export interface CreateRuleResponse {
  Rule?: Rule;
  BaseResp: base.BaseResp;
}

/** 更新规则 */
export interface UpdateRuleRequest {
  /** 规则ID */
  Id: string;
  /** 规则名称 */
  DisplayName?: string;
  /** 条件-DSL */
  Expression?: ConditionGroupExpr;
  /** 路由的结果部分，用于对结果的特殊处理，比如绑定技能组 */
  ActionInfo?: AimExpr;
  /** 扩展字段，前端可用于复现规则 */
  Extra?: string;
  /** 规则描述 */
  Description?: string;
  /** 操作人 */
  AgentId: string;
  /** 接口版本 UI传v1 */
  Version: string;
  Base?: base.Base;
}

export interface UpdateRuleResponse {
  Rule?: Rule;
  BaseResp: base.BaseResp;
}

/** 启用/禁用/删除规则 */
export interface UpdateRuleStatusRequest {
  /** 规则ID的list */
  Ids: Array<string>;
  /** 建RuleStatus定义 */
  RuleStatus: RuleStatus;
  /** 更新人 */
  UpdaterAgentId: string;
  /** 接口版本 UI传v1 */
  Version: string;
  Base?: base.Base;
}

export interface UpdateRuleStatusResponse {
  BaseResp: base.BaseResp;
}

/** 调整规则优先级 */
export interface UpdateRulePriorityRequest {
  /** 所有规则的优先级信息 */
  Rules: Array<RulePriority>;
  /** 更新人 */
  UpdaterAgentId: string;
  /** 接口版本 UI传v1 */
  Version: string;
  Base?: base.Base;
}

export interface UpdateRulePriorityResponse {
  /** 调整优先级会生成新的规则组版本 */
  RuleGroupId?: string;
  /** 调整优先级会生成新的ruleId */
  newRuleIds?: { [key: string]: string };
  BaseResp: base.BaseResp;
}

/** 根据id获取规则 */
export interface GetRuleByIdRequest {
  Id: string;
  Base?: base.Base;
}

export interface GetRuleByIdResponse {
  Rule?: Rule;
  BaseResp: base.BaseResp;
}

/** 获取规则列表 */
export interface GetRuleListRequest {
  /** 事件ID */
  EventKey: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准 */
  RuleGroupId?: string;
  /** 是否获取草稿箱里的规则 */
  IsDraft?: number;
  Base?: base.Base;
}

export interface GetRuleListResponse {
  /** 规则 */
  RuleList?: Array<Rule>;
  BaseResp: base.BaseResp;
}

export interface PublishRuleGroupRequest {
  /** 规则组id */
  RuleGroupId: string;
  /** 更新人 */
  UpdaterAgentId: string;
  Base?: base.Base;
}

export interface PublishRuleGroupResponse {
  /** 新的规则组id */
  RuleGroupId?: string;
  /** 发布会生成新的ruleId */
  newRuleIds?: { [key: string]: string };
  BaseResp: base.BaseResp;
}

export interface CopyRuleGroupRequest {
  /** 规则组id */
  RuleGroupId: string;
  /** 更新人 */
  UpdaterAgentId: string;
  Base?: base.Base;
}

export interface CopyRuleGroupResponse {
  BaseResp: base.BaseResp;
}

export declare class AdminService {
  /** 查询条件属性列表 */
  public GetFieldList(req: FieldListGetRequest): Promise<FieldListGetResponse>;
  public GetFieldList(ctx: ClientInvokeOptions, req: FieldListGetRequest): Promise<FieldListGetResponse>;

  /** 查询条件属性值列表 */
  public GetFieldValues(req: FieldValuesGetRequest): Promise<FieldValuesGetResponse>;
  public GetFieldValues(ctx: ClientInvokeOptions, req: FieldValuesGetRequest): Promise<FieldValuesGetResponse>;

  /** 添加字段 */
  public CreateField(req: FieldCreateRequest): Promise<FieldCreateResponse>;
  public CreateField(ctx: ClientInvokeOptions, req: FieldCreateRequest): Promise<FieldCreateResponse>;

  /** 修改字段 */
  public UpdateField(req: FieldUpdateRequest): Promise<FieldUpdateResponse>;
  public UpdateField(ctx: ClientInvokeOptions, req: FieldUpdateRequest): Promise<FieldUpdateResponse>;

  /** 删除字段 */
  public DeleteField(req: FieldDeleteRequest): Promise<FieldDeleteResponse>;
  public DeleteField(ctx: ClientInvokeOptions, req: FieldDeleteRequest): Promise<FieldDeleteResponse>;

  /** 查询动作元数据列表--SLA/Trigger use */
  /** ActionMetaListGetResponse GetActionMetaList(1: ActionMetaListGetRequest req) */
  /** 查询SLA目标元数据列表--SLA use */
  public GetSLAAimMetaList(req: SLAAimMetaListGetRequest): Promise<SLAAimMetaListGetResponse>;
  public GetSLAAimMetaList(ctx: ClientInvokeOptions, req: SLAAimMetaListGetRequest): Promise<SLAAimMetaListGetResponse>;

  public GetSLAAimMetaSimpleList(req: SLAAimMetaListGetRequest): Promise<SLAAimMetaSimpleListGetResponse>;
  public GetSLAAimMetaSimpleList(
    ctx: ClientInvokeOptions,
    req: SLAAimMetaListGetRequest
  ): Promise<SLAAimMetaSimpleListGetResponse>;

  /** 添加SLA目标元数据--SLA use */
  public CreateSLAAimMeta(req: SLAAimMetaCreateRequest): Promise<SLAAimMetaCreateResponse>;
  public CreateSLAAimMeta(ctx: ClientInvokeOptions, req: SLAAimMetaCreateRequest): Promise<SLAAimMetaCreateResponse>;

  /** 查询后台规则分页列表--SLA use */
  public GetAdminRulePage(req: AdminRulePageGetRequest): Promise<AdminRulePageGetResponse>;
  public GetAdminRulePage(ctx: ClientInvokeOptions, req: AdminRulePageGetRequest): Promise<AdminRulePageGetResponse>;

  /** 查询后台规则列表 */
  public GetAdminRuleList(req: AdminRuleListGetRequest): Promise<AdminRuleListGetResponse>;
  public GetAdminRuleList(ctx: ClientInvokeOptions, req: AdminRuleListGetRequest): Promise<AdminRuleListGetResponse>;

  /** 查询后台规则 */
  public GetAdminRuleById(req: AdminRuleGetByIdRequest): Promise<AdminRuleGetByIdResponse>;
  public GetAdminRuleById(ctx: ClientInvokeOptions, req: AdminRuleGetByIdRequest): Promise<AdminRuleGetByIdResponse>;

  /** 添加后台规则 */
  public CreateAdminRule(req: AdminRuleCreateRequest): Promise<AdminRuleCreateResponse>;
  public CreateAdminRule(ctx: ClientInvokeOptions, req: AdminRuleCreateRequest): Promise<AdminRuleCreateResponse>;

  /** 修改后台规则 */
  public UpdateAdminRule(req: AdminRuleUpdateRequest): Promise<AdminRuleUpdateResponse>;
  public UpdateAdminRule(ctx: ClientInvokeOptions, req: AdminRuleUpdateRequest): Promise<AdminRuleUpdateResponse>;

  /** 删除后台规则 */
  public DeleteAdminRule(req: AdminRuleDeleteRequest): Promise<AdminRuleDeleteResponse>;
  public DeleteAdminRule(ctx: ClientInvokeOptions, req: AdminRuleDeleteRequest): Promise<AdminRuleDeleteResponse>;

  /** 拖拽修改后台规则 */
  public BatchUpdateAdminRule(req: AdminRuleBatchUpdateRequest): Promise<AdminRuleBatchUpdateResponse>;
  public BatchUpdateAdminRule(
    ctx: ClientInvokeOptions,
    req: AdminRuleBatchUpdateRequest
  ): Promise<AdminRuleBatchUpdateResponse>;

  /** 改造 - 查询后台规则列表 */
  public GetRuleList(req: GetRuleListRequest): Promise<GetRuleListResponse>;
  public GetRuleList(ctx: ClientInvokeOptions, req: GetRuleListRequest): Promise<GetRuleListResponse>;

  /** 根据id查询后台规则 */
  public GetRuleById(req: GetRuleByIdRequest): Promise<GetRuleByIdResponse>;
  public GetRuleById(ctx: ClientInvokeOptions, req: GetRuleByIdRequest): Promise<GetRuleByIdResponse>;

  /** 添加后台规则 */
  public CreateRule(req: CreateRuleRequest): Promise<CreateRuleResponse>;
  public CreateRule(ctx: ClientInvokeOptions, req: CreateRuleRequest): Promise<CreateRuleResponse>;

  /** 修改后台规则 */
  public UpdateRule(req: UpdateRuleRequest): Promise<UpdateRuleResponse>;
  public UpdateRule(ctx: ClientInvokeOptions, req: UpdateRuleRequest): Promise<UpdateRuleResponse>;

  /** 启用、禁用、删除后台规则 */
  public UpdateRuleStatus(req: UpdateRuleStatusRequest): Promise<UpdateRuleStatusResponse>;
  public UpdateRuleStatus(ctx: ClientInvokeOptions, req: UpdateRuleStatusRequest): Promise<UpdateRuleStatusResponse>;

  /** 调整规则优先级 */
  /** 启用、禁用、删除后台规则 */
  /** UpdateRuleStatusResponse UpdateRuleStatus(1: UpdateRuleStatusRequest req) */
  /** 调整规则优先级 */
  public UpdateRulePriority(req: UpdateRulePriorityRequest): Promise<UpdateRulePriorityResponse>;
  public UpdateRulePriority(
    ctx: ClientInvokeOptions,
    req: UpdateRulePriorityRequest
  ): Promise<UpdateRulePriorityResponse>;

  /** 发布规则 */
  public PublishRuleGroup(req: PublishRuleGroupRequest): Promise<PublishRuleGroupResponse>;
  public PublishRuleGroup(ctx: ClientInvokeOptions, req: PublishRuleGroupRequest): Promise<PublishRuleGroupResponse>;

  /** 复制规则 */
  public CopyRuleGroup(req: CopyRuleGroupRequest): Promise<CopyRuleGroupResponse>;
  public CopyRuleGroup(ctx: ClientInvokeOptions, req: CopyRuleGroupRequest): Promise<CopyRuleGroupResponse>;
}
