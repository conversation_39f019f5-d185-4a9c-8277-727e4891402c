/* eslint-disable */
/* tslint:disable */

import * as base from './base';

export { base };

export enum EncryptedType {
  NO_ENCRYPT = 0,
  INNER_ENCRYPT = 1,
  OUTER_ENCRPYT = 2,
}

export enum ConversationType {
  ONE_TO_ONE_CHAT = 1,
  GROUP_CHAT = 2,
  LIVE_CHAT = 3,
  BROADCAST_CHAT = 4,
}

export enum GroupRole {
  /** [群成员] */
  ORDINARY = 0,
  /** [群主] */
  OWNER = 1,
  /** [群管理] */
  MANAGER = 2,
  VISITOR = 3,
}

/** 消息属性 */
export interface PropertyItem {
  UserdId?: string;
  SecUid?: string;
  CreateTime?: string;
  /** 去重Id,相同Id的Item在PropertyItemList中只保留一份 */
  IdempotentId?: string;
  Value?: string;
}

/** 业务方定制，IMCLOUD不理解内容 eg:表情点赞 value:uid */
export interface PropertyItemList {
  items?: Array<PropertyItem>;
}

export interface MessageBody {
  ConversationType: number;
  ConversationId: string;
  ConversationShortId: string;
  ServerMessageId: string;
  MsgType: number;
  Content: string;
  Ext: { [key: string]: string };
  Version: string;
  Status: number;
  CreateTime: string;
  Sender: string;
  /** Deprecated */
  Bitmap?: string;
  ExtraInfo?: string;
  EncryptedType?: number;
  appID?: number;
  SecSender?: string;
  properties?: { [key: string]: PropertyItemList };
  BizAppID?: number;
  UserProfile?: { [key: string]: string };
  /** 新版本的连续自增的index */
  IndexInConversationV2?: string;
}

export interface Message {
  /** IndexInConversationV1 */
  IndexInConversation: string;
  messageBody: MessageBody;
  /** IndexInUserInboxV1 */
  IndexInUserInbox?: string;
  /** 优化后的单链index */
  IndexInConversationV2: string;
  /** 优化后的混链index */
  IndexInUserV2: string;
}
