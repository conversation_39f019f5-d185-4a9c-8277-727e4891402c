/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';

export { base };

/** 操作渠道 */
export enum ChannelType {
  /** IM */
  IM = 1,
  /** 工单 */
  TICKET = 2,
  /** 电话 */
  PHONE = 3,
  /** 管理员 */
  ADMIN = 4,
  /** 电商工单 */
  ECOM_TICKET = 5,
  /** BUZZ工单 */
  BUZZ = 6,
  /** FEEDBACK */
  FEEDBACK = 7,
  /** 质检 */
  QUALITY_CHECK = 8,
  //IM离线留言会话
  IM_OFFLINE_SESSION = 9,
  //触达外呼
  ACCESS_CALL = 10,
  // 预约回呼
  CALLBACK = 11,
  // 售后
  AFTER_SALE = 12,
  TT_IM = 101,
}

export enum WorkType {
  FULL_TIME = 1,
  OUTSOURCING = 2,
  TRAINEE = 3,
  THIRD_PARTY = 4,
}

/** 第三方派遣 */
export enum Status {
  OFF = 0,
  ON = 1,
}

/** 卡片操作类型 */
export enum HandleType {
  OPEN = 1,
  CLOSE = 2,
  DELETE = 3,
}

export interface Agent {
  ID: string;
  TenantId: string;
  WorkType: number;
  UserName: string;
  NickName: string;
  UserId: string;
  UUID: string;
  Email: string;
  Mobile: string;
  CompanyId: string;
  ChannelTypes: Array<ChannelType>;
  Status: number;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
  OperatorName: string;
  DepartmentId: string;
  ImMaxTaskNum: number;
  PhoneSeatNo?: string;
  /** 国家地区 */
  CountryRegion?: string;
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  Extra: { [key: string]: string };
}

/** 目前extra里包含的字段 c_n_agent_appid(IM appId), */
/** c_s_ecom_ticket_role(电商工单系统角色), */
/** c_n_ecom_ticket_is_super(电商工单是否超级管理员) */
/** c_s_ecom_ticket_kind(电商工单业务类型) */
export interface CreateAgentRequest {
  TenantId: string;
  WorkType: number;
  UserName: string;
  NickName?: string;
  UserId?: string;
  UUID: string;
  Email: string;
  Mobile?: string;
  ChannelTypes: Array<ChannelType>;
  OperatorAgentId: string;
  CompanyId: string;
  Extra?: { [key: string]: string };
  Id?: string;
  DepartmentId: string;
  ImMaxTaskNum?: number;
  PhoneSeatNo?: string;
  /** 国家地区 */
  CountryRegion?: string;
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  Base?: base.Base;
}

export interface CreateAgentResponse {
  ID: string;
  BaseResp: base.BaseResp;
}

export interface BatchCreateAgentRequest {
  CreateAgentRequests: Array<CreateAgentRequest>;
  Base?: base.Base;
}

export interface BatchCreateAgentResponse {
  CreateAgentResponses: Array<CreateAgentResponse>;
  BaseResp: base.BaseResp;
}

export interface BatchDeleteAgentRequest {
  TenantId: string;
  AgentIds: Array<string>;
  OperatorAgentId: string;
  AccessKey?: string;
  Base?: base.Base;
}

export interface BatchDeleteAgentResponse {
  BaseResp: base.BaseResp;
}

export interface BatchInsOrUpdtAgentToEsRequest {
  AccessKey: string;
  TenantId: string;
  Base?: base.Base;
}

export interface BatchInsOrUpdtAgentToEsResponse {
  BaseResp: base.BaseResp;
}

export interface GetLeadersRequest {
  TenantId: string;
  AgentId: string;
  Base?: base.Base;
}

export interface GetLeadersResponse {
  LeaderIds: Array<string>;
  BaseResp: base.BaseResp;
}

export interface UpdateAgentRequest {
  Id: string;
  TenantId: string;
  WorkType?: number;
  UserName?: string;
  NickName?: string;
  UserId?: string;
  Mobile?: string;
  ChannelTypes?: Array<ChannelType>;
  OperatorAgentId: string;
  CompanyId?: string;
  Extra?: { [key: string]: string };
  DepartmentId?: string;
  ImMaxTaskNum?: number;
  PhoneSeatNo?: string;
  /** 国家地区 */
  CountryRegion?: string;
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  Base?: base.Base;
}

export interface UpdateAgentResponse {
  BaseResp: base.BaseResp;
}

export interface BatchUpdateAgentRequest {
  TenantId: string;
  AgentIds: Array<string>;
  OperatorAgentId: string;
  ImMaxTaskNum?: number;
  FeedbackMaxTaskNum?: number;
  Base?: base.Base;
}

export interface BatchUpdateAgentResponse {
  BaseResp: base.BaseResp;
}

export interface GetAgentDetailRequest {
  TenantId: string;
  ID: string;
  Base?: base.Base;
}

export interface GetAgentDetailResponse {
  Agent?: Agent;
  BaseResp: base.BaseResp;
}

export interface GetAgentByUUIDRequest {
  TenantId: string;
  UUID: string;
  Base?: base.Base;
}

export interface GetAgentByUUIDResponse {
  Agent: Agent;
  BaseResp: base.BaseResp;
}

export interface GetAgentListByIDsRequest {
  TenantId: string;
  IDs: Array<string>;
  Base?: base.Base;
}

export interface GetAgentListByIDsResponse {
  Agents?: Array<Agent>;
  BaseResp: base.BaseResp;
}

export interface EnableAgentRequest {
  TenantId: string;
  ID: string;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface EnableAgentResponse {
  BaseResp: base.BaseResp;
}

export interface DisableAgentRequest {
  TenantId: string;
  ID: string;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface DisableAgentResponse {
  BaseResp: base.BaseResp;
}

export interface GetAgentsByConditionRequest {
  TenantId: string;
  Email?: string;
  UserName?: string;
  Status?: number;
  PageNo: number;
  PageSize: number;
  DepartmentId?: string;
  SkillGroupIds?: Array<string>;
  DepartmentIds?: Array<string>;
  ChannelType?: ChannelType;
  Base?: base.Base;
}

export interface GetAgentsByConditionResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface SearchAgentRequest {
  TenantId: string;
  ChannelType?: ChannelType;
  UserName?: string;
  Email?: string;
  PhoneSeatNo?: string;
  PageNo: number;
  PageSize: number;
  Status?: number;
  extra?: { [key: string]: string };
  Base?: base.Base;
}

export interface SearchAgentResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface GetAgentsByPermissionRequest {
  OperatorAgentId: string;
  ChannelType: ChannelType;
  TenantId: string;
  Base?: base.Base;
}

export interface GetAgentsByPermissionResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface GetAgentsByOperateTimeRequest {
  OperateTime: string;
  Base?: base.Base;
}

export interface GetAgentsByOperateTimeResponse {
  Agents: Array<Agent>;
  BaseResp: base.BaseResp;
}

/** 技能组 */
export interface SkillGroup {
  ID: string;
  TenantId: string;
  AccessPartyId: Array<string>;
  Name: string;
  ChannelType: ChannelType;
  MaxTaskNum: number;
  QuestionCategoryIds: Array<string>;
  TagCodes: Array<string>;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
  OperatorName: string;
  MemberNum: number;
  LeaderNum: number;
  SkillGroupLevel: number;
  WorkStartTime?: string;
  WorkEndTime?: string;
  /** 国家地区 */
  CountryRegion?: string;
  Extra: { [key: string]: string };
}

export interface CreateSkillGroupRequest {
  TenantId: string;
  AccessPartyId: Array<string>;
  Name: string;
  ChannelType: ChannelType;
  MaxTaskNum: number;
  QuestionCategoryIds?: Array<string>;
  TagCodes: Array<string>;
  OperatorAgentId: string;
  Extra?: { [key: string]: string };
  WorkStartTime?: string;
  WorkEndTime?: string;
  /** 国家地区 */
  CountryRegion?: string;
  Base?: base.Base;
}

export interface CreateSkillGroupResponse {
  ID: string;
  BaseResp: base.BaseResp;
}

export interface UpdateSkillGroupRequest {
  Id: string;
  TenantId: string;
  AccessPartyId: Array<string>;
  ChannelType?: ChannelType;
  MaxTaskNum?: number;
  QuestionCategoryIds?: Array<string>;
  TagCodes?: Array<string>;
  AccessPartyIds?: Array<string>;
  OperatorAgentId?: string;
  Extra?: { [key: string]: string };
  WorkStartTime?: string;
  WorkEndTime?: string;
  /** 国家地区 */
  CountryRegion?: string;
  Base?: base.Base;
}

export interface UpdateSkillGroupResponse {
  BaseResp: base.BaseResp;
}

export interface DeleteSkillGroupRequest {
  ID: string;
  TenantId: string;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface DeleteSkillGroupResponse {
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupDetailRequest {
  ID: string;
  TenantId: string;
  Base?: base.Base;
}

export interface GetSkillGroupDetailResponse {
  SkillGroup: SkillGroup;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByIdsRequest {
  TenantId: string;
  Ids: Array<string>;
  IncludeDeletedData?: boolean;
  Base?: base.Base;
}

export interface GetSkillGroupsByIdsResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByTypeResquest {
  TenantId: string;
  ChannelType?: ChannelType;
  PageNo: number;
  PageSize: number;
  AccessPartyId?: string;
  ChannelTypes?: Array<ChannelType>;
  SkillGroupLevel?: number;
  SkillGroupName?: string;
  Base?: base.Base;
}

export interface GetSkillGroupsByTypeResponse {
  SkillGroups: Array<SkillGroup>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByCategoryRequest {
  TenantId: string;
  CategoryId: string;
  Base?: base.Base;
}

export interface GetSkillGroupsByCategoryResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface BatchGetSkillGroupsByCategoryRequest {
  TenantId: string;
  CategoryIds: Array<string>;
  Base?: base.Base;
}

export interface BatchGetSkillGroupsByCategoryResponse {
  CategoryGroupMap: { [key: string]: Array<string> };
  BaseResp: base.BaseResp;
}

export interface AddAgentToSkillGroupRequest {
  TenantId: string;
  AgentIds: Array<string>;
  SkillGroupId: string;
  IsGroupLeader: number;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface AddAgentToSkillGroupResponse {
  BaseResp: base.BaseResp;
}

export interface DeleteAgentFromSkillGroupRequest {
  TenantId: string;
  AgentIds: Array<string>;
  SkillGroupId: string;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface DeleteAgentFromSkillGroupResponse {
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupAgentsRequest {
  TenantId: string;
  SkillGroupId: string;
  AgentName?: string;
  IsGroupLeader?: number;
  PageNo: number;
  PageSize: number;
  AgentEmail?: string;
  AccessPartyId?: string;
  Base?: base.Base;
}

export interface GetSkillGroupAgentsResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface BatchGetSkillGroupAgentsRequest {
  TenantId: string;
  ChannelType: ChannelType;
  /** 技能组ID列表 */
  GroupList: Array<string>;
  /** 工作状态列表 */
  WorkStatusList?: Array<number>;
  Base?: base.Base;
}

export interface BatchGetSkillGroupAgentsResponse {
  GroupToAgentListMap: { [key: string]: Array<string> };
  BaseResp: base.BaseResp;
}

export interface UpdateGroupAgentsTaskNumRequest {
  SkillGroupId: string;
  AccessPartyId: string;
  AgentIds: Array<string>;
  MaxTaskNum: number;
  TenantId: string;
  OperatorAgentId?: string;
  Base?: base.Base;
}

export interface UpdateGroupAgentsTaskNumResponse {
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByNameRequest {
  TenantId: string;
  SkillGroupName: string;
  ChannelType?: ChannelType;
  AccessPartyId?: string;
  ChannelTypes?: Array<ChannelType>;
  Base?: base.Base;
}

export interface GetSkillGroupsByNameResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetAllSkillGroupsRequest {
  TenantId: string;
  Base?: base.Base;
}

export interface GetAllSkillGroupsResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetAllWorkingSkillGroupsRequest {
  TenantId: string;
  Base?: base.Base;
}

export interface GetAllWorkingSkillGroupsResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByAgentIdRequest {
  TenantId: string;
  AgentId: string;
  Base?: base.Base;
}

export interface GetSkillGroupsByAgentIdResponse {
  SkillGroups: Array<SkillGroup>;
  /** skillGroupId -> taskNum */
  TaskNumMap: { [key: string]: number };
  BaseResp: base.BaseResp;
}

export interface BatchGetSkillGroupsByAgentIdsRequest {
  TenantId: string;
  ChannelType?: ChannelType;
  AccessPartyId?: string;
  AgentIds: Array<string>;
  Base?: base.Base;
}

export interface AgentSkillGroups {
  AgentId: string;
  SkillGroups: Array<SkillGroup>;
}

export interface BatchGetSkillGroupsByAgentIdsResponse {
  AgentSkillGroups: Array<AgentSkillGroups>;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByAccessPartyRequest {
  TenantId: string;
  AccessPartyId: string;
  Base?: base.Base;
}

export interface GetSkillGroupsByAccessPartyResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByAccessPartiesRequest {
  TenantId: string;
  AccessPartyIds: Array<string>;
  ChannelType?: ChannelType;
  Base?: base.Base;
}

export interface GetSkillGroupsByAccessPartiesResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupsByAgentAccessPartyRequest {
  TenantId: string;
  AgentId: string;
  AccessPartyId: string;
  Base?: base.Base;
}

export interface GetSkillGroupsByAgentAccessPartyResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp: base.BaseResp;
}

/** 公司 */
export interface AgentCompany {
  Id: string;
  TenantId: string;
  Name: string;
  IsBytedance: number;
  DgTenantId: string;
  DeletedAt: string;
  CretedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
}

export interface GetAllCompanyRequest {
  Base?: base.Base;
}

export interface GetAllCompanyResponse {
  AgentCompanys: Array<AgentCompany>;
  BaseResp: base.BaseResp;
}

export interface SkillGroupTag {
  TenantId: string;
  Id: string;
  Code: string;
  DispalyName: string;
  CreatedAt: string;
  UpdatedAt: string;
  OperatrAgentName: string;
}

export interface GetAllSkillGroupTagsRequest {
  TenantId: string;
  Base?: base.Base;
}

export interface GetAllSkillGroupTagsResponse {
  SkillGroupTags: Array<SkillGroupTag>;
  BaseResp: base.BaseResp;
}

/** 问题卡片相关部分 */
export interface CardQuestionThrift {
  /** 问题ID */
  Id: string;
  /** 卡片ID */
  CardId: string;
  /** 问题内容 */
  QuestionName: string;
  /** 技能组ID */
  SkillGroupId: string;
  FullName: string;
}

/** 卡片全称 */
export interface AppQuestionCardThrift {
  /** 卡片ID */
  Id: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 应用ID */
  AppId: string;
  /** 引导话术，用于title展示 */
  CardName: string;
  /** 是否启用 */
  IsOpen: number;
  /** 问题列表，list顺序代表C端展示顺序 */
  CardQuestions: Array<CardQuestionThrift>;
  CardDisplayName?: string;
}

/** 卡片展示名称 - 新版必传 向下兼容 */
export interface UpdateAppQuestionCardThrift {
  /** 卡片ID */
  Id: string;
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 应用ID */
  AppId: string;
  /** 引导话术，用于title展示 */
  CardName: string;
  /** 问题列表，list顺序代表C端展示顺序 */
  UpdateCardQuestions: Array<UpdateCardQuestion>;
  CardDisplayName?: string;
}

/** 卡片展示名称 新版必传 向下兼容 */
export interface AddCardQuestion {
  /** 问题内容 */
  QuestionName: string;
  SkillGroupId: string;
}

/** 技能组ID */
export interface UpdateCardQuestion {
  /** 问题ID */
  Id?: string;
  /** 问题内容 */
  QuestionName: string;
  SkillGroupId: string;
}

/** 技能组ID */
export interface AddAppQuestionCard {
  /** 租户ID */
  TenantId: string;
  /** 接入方ID */
  AccessPartyId: string;
  /** 应用ID */
  AppId: string;
  /** 引导语，用于title展示 */
  CardName: string;
  /** 问题列表，list顺序代表C端展示顺序 */
  AddCardQuestions: Array<AddCardQuestion>;
  CardDisplayName?: string;
}

/** 卡片展示名称 - 新版必传 向下兼容 */
export interface GetCardRequest {
  /** 租户ID */
  TenantId: string;
  /** 应用ID列表 */
  AppIds: Array<string>;
  /** 接入方ID */
  AccessPartyId?: string;
  Base?: base.Base;
}

export interface GetCardResponse {
  /** 问题卡片列表 */
  AppQuestionCards: Array<AppQuestionCardThrift>;
  BaseResp: base.BaseResp;
}

export interface GetQuestionByCardIdRequest {
  /** 卡片id */
  Id: string;
  Base?: base.Base;
}

export interface GetQuestionByCardIdResponse {
  /** 问题卡片 */
  CardQuestions?: Array<CardQuestionThrift>;
  BaseResp: base.BaseResp;
}

export interface CreateCardRequest {
  /** 不带主键ID的问题卡片对象 */
  AddAppQuestionCard: AddAppQuestionCard;
  /** 操作人ID */
  AgentId: string;
  Base?: base.Base;
}

export interface UpdateCardRequest {
  /** 带有主键ID的问题卡片对象 */
  UpdateAppQuestionCardThrift: UpdateAppQuestionCardThrift;
  /** 操作人ID */
  AgentId: string;
  Base?: base.Base;
}

export interface HandleCardResponse {
  /** 卡片id */
  Id?: string;
  BaseResp: base.BaseResp;
}

export interface HandleCardRequest {
  /** 卡片ID */
  CardId: string;
  /** 操作类型 */
  HandleType: HandleType;
  /** 操作人ID */
  AgentId: string;
  Base?: base.Base;
}

export interface Department {
  ID: string;
  Name: string;
  ParentId: string;
  AgentNum: number;
  ChildrenDepartments: Array<Department>;
  Level: number;
  LeaderIds: Array<string>;
}

export interface CreateDepartmentRequest {
  TenantId: string;
  Name: string;
  ParentId: string;
  OperateAgentId: string;
  LeaderIds?: Array<string>;
  Base?: base.Base;
}

export interface CreateDepartmentResponse {
  ID: string;
  BaseResp: base.BaseResp;
}

export interface UpdateDepartmentRequest {
  TenantId: string;
  DepartmentId: string;
  ParentId?: string;
  Name?: string;
  OperateAgentId: string;
  LeaderIds?: Array<string>;
  Base?: base.Base;
}

export interface UpdateDepartmentResponse {
  BaseResp: base.BaseResp;
}

export interface DeleteDepartmentRequest {
  TenantId: string;
  DepartmentId: string;
  OperateAgentId: string;
  Base?: base.Base;
}

export interface DeleteDepartmentResponse {
  BaseResp: base.BaseResp;
}

export interface GetDepartmentsRequest {
  TenantId: string;
  AgentStatus?: number;
  AgentName?: string;
  AgentEmail?: string;
  ChannelType?: ChannelType;
  Base?: base.Base;
}

export interface GetDepartmentsResponse {
  rootDepartmentModel: Department;
  BaseResp: base.BaseResp;
}

export interface GetDepartmentsByNameRequest {
  TenantId: string;
  DepartmantName: string;
  Base?: base.Base;
}

export interface GetDepartmentsByNameResponse {
  Departments: Array<Department>;
  BaseResp: base.BaseResp;
}

export interface FixDepartLevelRequest {
  TenantId: string;
  Base?: base.Base;
}

export interface FixDepartLevelResposne {
  BaseResp: base.BaseResp;
}

export interface GetWorkingAgentListRequest {
  TenantId: string;
  ChannelType: number;
  Base?: base.Base;
}

export interface GetWorkingAgentListResponse {
  AgentList: Array<string>;
  BaseResp: base.BaseResp;
}

export interface GetFirstLoginTimeByAgentListRequest {
  TenantId: string;
  ChannelType: ChannelType;
  /** 人员ID列表 */
  AgentList: Array<string>;
  Base?: base.Base;
}

export interface GetFirstLoginTimeByAgentListResponse {
  AgentToFirstLoginTimeMap: { [key: string]: string };
  BaseResp: base.BaseResp;
}

export interface CleanFirstLoginTimeCacheRequest {
  TenantId: string;
  ChannelType: ChannelType;
  /** 人员ID列表 */
  AgentList: Array<string>;
  Base?: base.Base;
}

export interface CleanFirstLoginTimeCacheResponse {
  CleanResult: { [key: string]: boolean };
  BaseResp: base.BaseResp;
}

export interface GetWorkStatusRequest {
  TenantId: string;
  AgentId: string;
  ChannelType: ChannelType;
  Base?: base.Base;
}

export interface GetWorkStatusResponse {
  WorkStatusDetail: WorkStatusEnum;
  IsExist: boolean;
  /** 切换到这个状态的时间 */
  StartTime: string;
  BaseResp: base.BaseResp;
}

export interface BatchGetWorkStatusRequest {
  TenantId: string;
  AgentIds: Array<string>;
  ChannelType: ChannelType;
  Base?: base.Base;
}

export interface BatchGetWorkStatusResponse {
  WorkStatusMap: { [key: string]: number };
  BaseResp: base.BaseResp;
}

/** 工作状态详情 */
export interface WorkStatusEnum {
  /** 租户ID */
  TenantId: string;
  /** 工作状态值 */
  WorkStatus: number;
  /** 工作状态描述 */
  WorkStatusDesc: string;
  /** 工作状态提示 */
  WorkStatusTip: string;
  /** 是否启用 */
  Enabled: boolean;
  /** 接线状态（0：不可接线；1: 可接线） */
  ReceptionStatus: number;
  /** 渠道类型（1：IM；2：工单；3：电话；4：其他；5：电商工单） */
  ChannelType: number;
  /** 是否为禁用/启用后默认 */
  IsDefault: boolean;
  /** 创建时间 */
  CreatedAt: string;
  /** 是否为登入后默认 */
  IsLoginDefault: boolean;
  /** 是否为登出后默认 */
  IsLogoutDefault: boolean;
  /** 是否可被配置（启用禁用） */
  EnableConfig: boolean;
  NGCCStatus?: string;
}

/** 对应的ngcc的status */
export interface LoginRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AgentId: string;
  Base?: base.Base;
}

export interface LoginResponse {
  /** 登入后的工作状态 */
  WorkStatusDetail: WorkStatusEnum;
  BaseResp: base.BaseResp;
}

export interface LogoutRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AgentId: string;
  Base?: base.Base;
}

export interface LogoutResponse {
  /** 登出后的工作状态 */
  WorkStatusDetail: WorkStatusEnum;
  BaseResp: base.BaseResp;
}

export interface GetWorkStatusDetailRequest {
  TenantId: string;
  WorkStatus: number;
  ChannelType: ChannelType;
  Base?: base.Base;
}

export interface GetWorkStatusDetailResponse {
  WorkStatusDetail: WorkStatusEnum;
  BaseResp: base.BaseResp;
}

export interface GetWorkStatusOptionsRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AgentId?: string;
  AccessPartyIds?: Array<string>;
  IsEnabled?: boolean;
  Base?: base.Base;
}

export interface GetWorkStatusOptionsResponse {
  WorkStatusOptions: Array<WorkStatusEnum>;
  BaseResp: base.BaseResp;
}

export interface WorkStatusConfig {
  /** 租户ID */
  TenantId: string;
  /** 坐席类型 */
  ChannelType: ChannelType;
  /** 接入方id */
  AccessPartyId: string;
  /** 工作状态值 */
  WorkStatus: number;
  /** 工作状态描述 */
  WorkStatusDesc: string;
  /** 是否启用 */
  Enabled: boolean;
  /** 接线状态（0：不可接线；1: 可接线） */
  ReceptionStatus: number;
  /** 更新人id */
  UpdaterAgentId: string;
  /** 更新人名字 */
  updaterAgentName: string;
  /** 更新时间 */
  UpdatedAt: string;
  NGCCStatus?: string;
}

/** 对应的ngcc的status */
export interface GetWorkStatusConfigsRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AccessPartyId: string;
  Base?: base.Base;
}

export interface GetWorkStatusConfigsResponse {
  WorkStatusConfigs: Array<WorkStatusConfig>;
  BaseResp: base.BaseResp;
}

export interface GetWorkStatusEnumListRequest {
  TenantId: string;
  ChannelType: ChannelType;
  /** 是否人员可接线状态 */
  IsReception?: boolean;
  /** 是否启用 */
  IsEnabled?: boolean;
  /** 是否为人员的默认禁用/启用状态 */
  IsDefault?: boolean;
  /** 是否为人员的默认登入状态 */
  IsLoginDefault?: boolean;
  /** 是否为人员的默认登出状态 */
  IsLogoutDefault?: boolean;
  Base?: base.Base;
}

export interface GetWorkStatusEnumListResponse {
  WorkStatusEnumList: Array<WorkStatusEnum>;
  BaseResp: base.BaseResp;
}

export interface UpdateWorkStatusRequest {
  TenantId: string;
  AgentId: string;
  ChannelType: ChannelType;
  /** 新的工作状态值 */
  WorkStatus: number;
  Base?: base.Base;
}

export interface UpdateWorkStatusResponse {
  /** 是否已更新 */
  Updated: boolean;
  BaseResp: base.BaseResp;
}

export interface UpdateWorkStatusConfigRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AccessPartyId: string;
  WorkStatus: number;
  Enable: boolean;
  OperatorAgentId: string;
  Base?: base.Base;
}

export interface UpdateWorkStatusConfigResponse {
  BaseResp: base.BaseResp;
}

export interface CreateAgentTemplateRequest {
  TenantId: string;
  AccessKey: string;
  Base?: base.Base;
}

export interface CreateAgentTemplateResponse {
  FileURL: string;
  BaseResp: base.BaseResp;
}

export interface FixMissingEndTimeRequest {
  TenantId: string;
  AccessKey: string;
  UntilDateTime?: string;
  Base?: base.Base;
}

export interface FixMissingEndTimeResponse {
  FailLogIdList: Array<string>;
  SuccessCount: number;
  FailCount: number;
  TotalCount: number;
  BaseResp: base.BaseResp;
}

export interface CheckSkillGroupAutoAssignRequest {
  TenantId: string;
  SkillGroupId: string;
}

export interface CheckSkillGroupAutoAssignResponse {
  AutoAssign: boolean;
  UnautoAssignAgents: Array<Agent>;
  SkillGroupAgentsCount: string;
  BaseResp: base.BaseResp;
}

export declare class AgentSkillGroupService {
  /** 创建组织架构节点 */
  public CreateDepartment(req: CreateDepartmentRequest): Promise<CreateDepartmentResponse>;
  public CreateDepartment(ctx: ClientInvokeOptions, req: CreateDepartmentRequest): Promise<CreateDepartmentResponse>;

  /** 删除组织架构节点 */
  public DeleteDepartment(req: DeleteDepartmentRequest): Promise<DeleteDepartmentResponse>;
  public DeleteDepartment(ctx: ClientInvokeOptions, req: DeleteDepartmentRequest): Promise<DeleteDepartmentResponse>;

  /** 更新组织架构节点 */
  public UpdateDepartment(req: UpdateDepartmentRequest): Promise<UpdateDepartmentResponse>;
  public UpdateDepartment(ctx: ClientInvokeOptions, req: UpdateDepartmentRequest): Promise<UpdateDepartmentResponse>;

  /** 获取组织架构树 */
  public GetDepartments(req: GetDepartmentsRequest): Promise<GetDepartmentsResponse>;
  public GetDepartments(ctx: ClientInvokeOptions, req: GetDepartmentsRequest): Promise<GetDepartmentsResponse>;

  /** 根据组织架构名称模糊匹配 */
  public GetDepartmentsByName(req: GetDepartmentsByNameRequest): Promise<GetDepartmentsByNameResponse>;
  public GetDepartmentsByName(
    ctx: ClientInvokeOptions,
    req: GetDepartmentsByNameRequest
  ): Promise<GetDepartmentsByNameResponse>;

  /** 初始化和修复department中的level字段 */
  public FixDepartLevel(req: FixDepartLevelRequest): Promise<FixDepartLevelResposne>;
  public FixDepartLevel(ctx: ClientInvokeOptions, req: FixDepartLevelRequest): Promise<FixDepartLevelResposne>;

  /** 创建agent */
  public CreateAgent(req: CreateAgentRequest): Promise<CreateAgentResponse>;
  public CreateAgent(ctx: ClientInvokeOptions, req: CreateAgentRequest): Promise<CreateAgentResponse>;

  /** 批量创建agent */
  public BatchCreateAgent(req: BatchCreateAgentRequest): Promise<BatchCreateAgentResponse>;
  public BatchCreateAgent(ctx: ClientInvokeOptions, req: BatchCreateAgentRequest): Promise<BatchCreateAgentResponse>;

  /** 批量删除agent */
  public BatchDeleteAgent(req: BatchDeleteAgentRequest): Promise<BatchDeleteAgentResponse>;
  public BatchDeleteAgent(ctx: ClientInvokeOptions, req: BatchDeleteAgentRequest): Promise<BatchDeleteAgentResponse>;

  /** 批量插入和更新人员信息到ES中 */
  public BatchInsOrUpdtAgentToEs(req: BatchInsOrUpdtAgentToEsRequest): Promise<BatchInsOrUpdtAgentToEsResponse>;
  public BatchInsOrUpdtAgentToEs(
    ctx: ClientInvokeOptions,
    req: BatchInsOrUpdtAgentToEsRequest
  ): Promise<BatchInsOrUpdtAgentToEsResponse>;

  /** 获取人员的leader */
  public GetLeaders(req: GetLeadersRequest): Promise<GetLeadersResponse>;
  public GetLeaders(ctx: ClientInvokeOptions, req: GetLeadersRequest): Promise<GetLeadersResponse>;

  /** 更新agent */
  public UpdateAgent(req: UpdateAgentRequest): Promise<UpdateAgentResponse>;
  public UpdateAgent(ctx: ClientInvokeOptions, req: UpdateAgentRequest): Promise<UpdateAgentResponse>;

  /** 批量更新agent */
  public BatchUpdateAgent(req: BatchUpdateAgentRequest): Promise<BatchUpdateAgentResponse>;
  public BatchUpdateAgent(ctx: ClientInvokeOptions, req: BatchUpdateAgentRequest): Promise<BatchUpdateAgentResponse>;

  /** 获取id获取agent详情 */
  public GetAgentDetail(req: GetAgentDetailRequest): Promise<GetAgentDetailResponse>;
  public GetAgentDetail(ctx: ClientInvokeOptions, req: GetAgentDetailRequest): Promise<GetAgentDetailResponse>;

  /** 根据UUID获取agent详情 */
  public GetAgentByUUID(req: GetAgentByUUIDRequest): Promise<GetAgentByUUIDResponse>;
  public GetAgentByUUID(ctx: ClientInvokeOptions, req: GetAgentByUUIDRequest): Promise<GetAgentByUUIDResponse>;

  /** 根据idList获取agent */
  public GetAgentListByIDs(req: GetAgentListByIDsRequest): Promise<GetAgentListByIDsResponse>;
  public GetAgentListByIDs(ctx: ClientInvokeOptions, req: GetAgentListByIDsRequest): Promise<GetAgentListByIDsResponse>;

  /** 启用客服 */
  public EnableAgent(req: EnableAgentRequest): Promise<EnableAgentResponse>;
  public EnableAgent(ctx: ClientInvokeOptions, req: EnableAgentRequest): Promise<EnableAgentResponse>;

  /** 禁用客服 */
  public DisableAgent(req: DisableAgentRequest): Promise<DisableAgentResponse>;
  public DisableAgent(ctx: ClientInvokeOptions, req: DisableAgentRequest): Promise<DisableAgentResponse>;

  /** 根据条件，获取agent列表 */
  public GetAgentsByCondition(req: GetAgentsByConditionRequest): Promise<GetAgentsByConditionResponse>;
  public GetAgentsByCondition(
    ctx: ClientInvokeOptions,
    req: GetAgentsByConditionRequest
  ): Promise<GetAgentsByConditionResponse>;

  /** 根据条件，从ES中查询人员数据，暂时只对电商工单开放 */
  public SearchAgent(req: SearchAgentRequest): Promise<SearchAgentResponse>;
  public SearchAgent(ctx: ClientInvokeOptions, req: SearchAgentRequest): Promise<SearchAgentResponse>;

  /** 根据权限获取人员列表 */
  public GetAgentsByPermission(req: GetAgentsByPermissionRequest): Promise<GetAgentsByPermissionResponse>;
  public GetAgentsByPermission(
    ctx: ClientInvokeOptions,
    req: GetAgentsByPermissionRequest
  ): Promise<GetAgentsByPermissionResponse>;

  /** 根据操作时间获取人员列表 */
  public GetAgentsByOperateTime(req: GetAgentsByOperateTimeRequest): Promise<GetAgentsByOperateTimeResponse>;
  public GetAgentsByOperateTime(
    ctx: ClientInvokeOptions,
    req: GetAgentsByOperateTimeRequest
  ): Promise<GetAgentsByOperateTimeResponse>;

  /** 创建技能组 */
  public CreateSkillGroup(req: CreateSkillGroupRequest): Promise<CreateSkillGroupResponse>;
  public CreateSkillGroup(ctx: ClientInvokeOptions, req: CreateSkillGroupRequest): Promise<CreateSkillGroupResponse>;

  /** 更新技能组 */
  public UpdateSkillGroup(req: UpdateSkillGroupRequest): Promise<UpdateSkillGroupResponse>;
  public UpdateSkillGroup(ctx: ClientInvokeOptions, req: UpdateSkillGroupRequest): Promise<UpdateSkillGroupResponse>;

  /** 删除技能组 */
  public DeleteSkillGroup(req: DeleteSkillGroupRequest): Promise<DeleteSkillGroupResponse>;
  public DeleteSkillGroup(ctx: ClientInvokeOptions, req: DeleteSkillGroupRequest): Promise<DeleteSkillGroupResponse>;

  /** 获取技能组详情 */
  public GetSkillGroupDetail(req: GetSkillGroupDetailRequest): Promise<GetSkillGroupDetailResponse>;
  public GetSkillGroupDetail(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupDetailRequest
  ): Promise<GetSkillGroupDetailResponse>;

  /** 根据idList获取技能组列表 */
  public GetSkillGroupsByIds(req: GetSkillGroupsByIdsRequest): Promise<GetSkillGroupsByIdsResponse>;
  public GetSkillGroupsByIds(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByIdsRequest
  ): Promise<GetSkillGroupsByIdsResponse>;

  /** 根据类型获取技能组列表 */
  public GetSkillGroupsByType(req: GetSkillGroupsByTypeResquest): Promise<GetSkillGroupsByTypeResponse>;
  public GetSkillGroupsByType(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByTypeResquest
  ): Promise<GetSkillGroupsByTypeResponse>;

  /** 根据标签id获取技能组列表 */
  public GetSkillGroupsByCategory(req: GetSkillGroupsByCategoryRequest): Promise<GetSkillGroupsByCategoryResponse>;
  public GetSkillGroupsByCategory(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByCategoryRequest
  ): Promise<GetSkillGroupsByCategoryResponse>;

  /** 批量根据标签id获取技能组列表 */
  public BatchGetSkillGroupsByCategory(
    req: BatchGetSkillGroupsByCategoryRequest
  ): Promise<BatchGetSkillGroupsByCategoryResponse>;
  public BatchGetSkillGroupsByCategory(
    ctx: ClientInvokeOptions,
    req: BatchGetSkillGroupsByCategoryRequest
  ): Promise<BatchGetSkillGroupsByCategoryResponse>;

  /** 技能组添加成员 */
  public AddAgentToSkillGroup(req: AddAgentToSkillGroupRequest): Promise<AddAgentToSkillGroupResponse>;
  public AddAgentToSkillGroup(
    ctx: ClientInvokeOptions,
    req: AddAgentToSkillGroupRequest
  ): Promise<AddAgentToSkillGroupResponse>;

  /** 技能组删除成员 */
  public DeleteAgentFromSkillGroup(req: DeleteAgentFromSkillGroupRequest): Promise<DeleteAgentFromSkillGroupResponse>;
  public DeleteAgentFromSkillGroup(
    ctx: ClientInvokeOptions,
    req: DeleteAgentFromSkillGroupRequest
  ): Promise<DeleteAgentFromSkillGroupResponse>;

  /** 获取技能组成员 */
  public GetSkillGroupAgents(req: GetSkillGroupAgentsRequest): Promise<GetSkillGroupAgentsResponse>;
  public GetSkillGroupAgents(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupAgentsRequest
  ): Promise<GetSkillGroupAgentsResponse>;

  /** 批量获取技能组成员 */
  public BatchGetSkillGroupAgents(req: BatchGetSkillGroupAgentsRequest): Promise<BatchGetSkillGroupAgentsResponse>;
  public BatchGetSkillGroupAgents(
    ctx: ClientInvokeOptions,
    req: BatchGetSkillGroupAgentsRequest
  ): Promise<BatchGetSkillGroupAgentsResponse>;

  /** 更新技能组下成员的对客数 */
  public UpdateGroupAgentsTaskNum(req: UpdateGroupAgentsTaskNumRequest): Promise<UpdateGroupAgentsTaskNumResponse>;
  public UpdateGroupAgentsTaskNum(
    ctx: ClientInvokeOptions,
    req: UpdateGroupAgentsTaskNumRequest
  ): Promise<UpdateGroupAgentsTaskNumResponse>;

  /** 根据技能组的名字模糊匹配 */
  public GetSkillGroupByName(req: GetSkillGroupsByNameRequest): Promise<GetSkillGroupsByNameResponse>;
  public GetSkillGroupByName(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByNameRequest
  ): Promise<GetSkillGroupsByNameResponse>;

  /** 获取全量的技能组 */
  public GetAllSkillGroups(req: GetAllSkillGroupsRequest): Promise<GetAllSkillGroupsResponse>;
  public GetAllSkillGroups(ctx: ClientInvokeOptions, req: GetAllSkillGroupsRequest): Promise<GetAllSkillGroupsResponse>;

  /** 获取全量的工组中的技能组 */
  public GetAllWorkingSkillGroups(req: GetAllWorkingSkillGroupsRequest): Promise<GetAllWorkingSkillGroupsResponse>;
  public GetAllWorkingSkillGroups(
    ctx: ClientInvokeOptions,
    req: GetAllWorkingSkillGroupsRequest
  ): Promise<GetAllWorkingSkillGroupsResponse>;

  /** 根据客服id获取技能组 */
  public GetSkillGroupsByAgentId(req: GetSkillGroupsByAgentIdRequest): Promise<GetSkillGroupsByAgentIdResponse>;
  public GetSkillGroupsByAgentId(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByAgentIdRequest
  ): Promise<GetSkillGroupsByAgentIdResponse>;

  /** 批量根据客服id获取技能组 */
  public BatchGetSkillGroupsByAgentIds(
    req: BatchGetSkillGroupsByAgentIdsRequest
  ): Promise<BatchGetSkillGroupsByAgentIdsResponse>;
  public BatchGetSkillGroupsByAgentIds(
    ctx: ClientInvokeOptions,
    req: BatchGetSkillGroupsByAgentIdsRequest
  ): Promise<BatchGetSkillGroupsByAgentIdsResponse>;

  /** 根据接入方获取技能组 */
  public GetSkillGroupsByAccessParty(
    req: GetSkillGroupsByAccessPartyRequest
  ): Promise<GetSkillGroupsByAccessPartyResponse>;
  public GetSkillGroupsByAccessParty(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByAccessPartyRequest
  ): Promise<GetSkillGroupsByAccessPartyResponse>;

  /** 根据接入方列表获取技能组 */
  public GetSkillGroupsByAccessParties(
    req: GetSkillGroupsByAccessPartiesRequest
  ): Promise<GetSkillGroupsByAccessPartiesResponse>;
  public GetSkillGroupsByAccessParties(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByAccessPartiesRequest
  ): Promise<GetSkillGroupsByAccessPartiesResponse>;

  /** 根据人员id和接入方，获取技能组 */
  public GetSkillGroupByAgentAccessParty(
    req: GetSkillGroupsByAgentAccessPartyRequest
  ): Promise<GetSkillGroupsByAgentAccessPartyResponse>;
  public GetSkillGroupByAgentAccessParty(
    ctx: ClientInvokeOptions,
    req: GetSkillGroupsByAgentAccessPartyRequest
  ): Promise<GetSkillGroupsByAgentAccessPartyResponse>;

  /** 获取所有公司 */
  public GetAllCompany(req: GetAllCompanyRequest): Promise<GetAllCompanyResponse>;
  public GetAllCompany(ctx: ClientInvokeOptions, req: GetAllCompanyRequest): Promise<GetAllCompanyResponse>;

  public GetAllSkillGroupTags(req: GetAllSkillGroupTagsRequest): Promise<GetAllSkillGroupTagsResponse>;
  public GetAllSkillGroupTags(
    ctx: ClientInvokeOptions,
    req: GetAllSkillGroupTagsRequest
  ): Promise<GetAllSkillGroupTagsResponse>;

  /** 创建问题卡片 */
  public createCard(req: CreateCardRequest): Promise<HandleCardResponse>;
  public createCard(ctx: ClientInvokeOptions, req: CreateCardRequest): Promise<HandleCardResponse>;

  /** 获取问题卡片 */
  public getCard(req: GetCardRequest): Promise<GetCardResponse>;
  public getCard(ctx: ClientInvokeOptions, req: GetCardRequest): Promise<GetCardResponse>;

  /** 获取问题卡片 */
  public getQuestionByCardId(req: GetQuestionByCardIdRequest): Promise<GetQuestionByCardIdResponse>;
  public getQuestionByCardId(
    ctx: ClientInvokeOptions,
    req: GetQuestionByCardIdRequest
  ): Promise<GetQuestionByCardIdResponse>;

  /** 更新问题卡片 */
  public updateCard(req: UpdateCardRequest): Promise<HandleCardResponse>;
  public updateCard(ctx: ClientInvokeOptions, req: UpdateCardRequest): Promise<HandleCardResponse>;

  /** 操作卡片(启用、禁用、删除) */
  public handleCard(req: HandleCardRequest): Promise<HandleCardResponse>;
  public handleCard(ctx: ClientInvokeOptions, req: HandleCardRequest): Promise<HandleCardResponse>;

  /** 获取可以接单的人员列表 */
  public GetWorkingAgentList(req: GetWorkingAgentListRequest): Promise<GetWorkingAgentListResponse>;
  public GetWorkingAgentList(
    ctx: ClientInvokeOptions,
    req: GetWorkingAgentListRequest
  ): Promise<GetWorkingAgentListResponse>;

  /** 获取人员的首次登录时间 */
  public GetFirstLoginTimeByAgentList(
    req: GetFirstLoginTimeByAgentListRequest
  ): Promise<GetFirstLoginTimeByAgentListResponse>;
  public GetFirstLoginTimeByAgentList(
    ctx: ClientInvokeOptions,
    req: GetFirstLoginTimeByAgentListRequest
  ): Promise<GetFirstLoginTimeByAgentListResponse>;

  /** 清除人员首次登录时间的缓存 */
  public CleanFirstLoginTimeCache(req: CleanFirstLoginTimeCacheRequest): Promise<CleanFirstLoginTimeCacheResponse>;
  public CleanFirstLoginTimeCache(
    ctx: ClientInvokeOptions,
    req: CleanFirstLoginTimeCacheRequest
  ): Promise<CleanFirstLoginTimeCacheResponse>;

  /** 登入 */
  public Login(req: LoginRequest): Promise<LoginResponse>;
  public Login(ctx: ClientInvokeOptions, req: LoginRequest): Promise<LoginResponse>;

  /** 登出 */
  public Logout(req: LogoutRequest): Promise<LogoutResponse>;
  public Logout(ctx: ClientInvokeOptions, req: LogoutRequest): Promise<LogoutResponse>;

  /** 获取工作状态 */
  public GetWorkStatus(req: GetWorkStatusRequest): Promise<GetWorkStatusResponse>;
  public GetWorkStatus(ctx: ClientInvokeOptions, req: GetWorkStatusRequest): Promise<GetWorkStatusResponse>;

  /** 批量获取工作状态 */
  public BatchGetWorkStatus(req: BatchGetWorkStatusRequest): Promise<BatchGetWorkStatusResponse>;
  public BatchGetWorkStatus(
    ctx: ClientInvokeOptions,
    req: BatchGetWorkStatusRequest
  ): Promise<BatchGetWorkStatusResponse>;

  /** 获取工作状态的详情 */
  public GetWorkStatusDetail(req: GetWorkStatusDetailRequest): Promise<GetWorkStatusDetailResponse>;
  public GetWorkStatusDetail(
    ctx: ClientInvokeOptions,
    req: GetWorkStatusDetailRequest
  ): Promise<GetWorkStatusDetailResponse>;

  /** 获取工作状态选项列表 */
  public GetWorkStatusOptions(req: GetWorkStatusOptionsRequest): Promise<GetWorkStatusOptionsResponse>;
  public GetWorkStatusOptions(
    ctx: ClientInvokeOptions,
    req: GetWorkStatusOptionsRequest
  ): Promise<GetWorkStatusOptionsResponse>;

  /** 获取工作状态枚举列表 */
  public GetWorkStatusEnumList(req: GetWorkStatusEnumListRequest): Promise<GetWorkStatusEnumListResponse>;
  public GetWorkStatusEnumList(
    ctx: ClientInvokeOptions,
    req: GetWorkStatusEnumListRequest
  ): Promise<GetWorkStatusEnumListResponse>;

  /** 获取工作状态配置 */
  public GetWorkStatusConfigs(req: GetWorkStatusConfigsRequest): Promise<GetWorkStatusConfigsResponse>;
  public GetWorkStatusConfigs(
    ctx: ClientInvokeOptions,
    req: GetWorkStatusConfigsRequest
  ): Promise<GetWorkStatusConfigsResponse>;

  /** 更新工作状态 */
  public UpdateWorkStatus(req: UpdateWorkStatusRequest): Promise<UpdateWorkStatusResponse>;
  public UpdateWorkStatus(ctx: ClientInvokeOptions, req: UpdateWorkStatusRequest): Promise<UpdateWorkStatusResponse>;

  /** 生成最新的人员批量上传模板 */
  public CreateAgentTemplate(req: CreateAgentTemplateRequest): Promise<CreateAgentTemplateResponse>;
  public CreateAgentTemplate(
    ctx: ClientInvokeOptions,
    req: CreateAgentTemplateRequest
  ): Promise<CreateAgentTemplateResponse>;

  /** 更新工作状态配置 */
  public UpdateWorkStatusConfig(req: UpdateWorkStatusConfigRequest): Promise<UpdateWorkStatusConfigResponse>;
  public UpdateWorkStatusConfig(
    ctx: ClientInvokeOptions,
    req: UpdateWorkStatusConfigRequest
  ): Promise<UpdateWorkStatusConfigResponse>;

  /** 修复缺失endTime的工作状态日志 */
  public FixMissingEndTime(req: FixMissingEndTimeRequest): Promise<FixMissingEndTimeResponse>;
  public FixMissingEndTime(ctx: ClientInvokeOptions, req: FixMissingEndTimeRequest): Promise<FixMissingEndTimeResponse>;

  /** 检查技能组自动分单配置 */
  public CheckSkillGroupAutoAssign(req: CheckSkillGroupAutoAssignRequest): Promise<CheckSkillGroupAutoAssignResponse>;
  public CheckSkillGroupAutoAssign(
    ctx: ClientInvokeOptions,
    req: CheckSkillGroupAutoAssignRequest
  ): Promise<CheckSkillGroupAutoAssignResponse>;
}
