/* eslint-disable */
/* tslint:disable */

import * as base from './base';

export { base };

export enum HelpdeskType {
  /** 人工客服 */
  Artificial = 1,
  /** 智能客服 */
  Intelligent = 2,
  Feedback = 3,
}

/** 用户反馈 */
export interface HumanAgentSystemSetting {}

export interface ImOptionSetting {
  ApiUrl: string;
}

export interface FrontierOptionSetting {
  Aid: number;
  Fpid: number;
  Service: number;
  AppKey: string;
}

export interface AppSetting {
  UserInfoURL: string;
  HumanAgentSystem: HumanAgentSystemSetting;
  ImOption: ImOptionSetting;
  FrontierOptionSetting: FrontierOptionSetting;
}

export interface HelpdeskContext {
  AppID: number;
  TicketID: string;
  ConversationShortID: string;
  TaskID: string;
  HelpdeskConversationLogID?: string;
}
