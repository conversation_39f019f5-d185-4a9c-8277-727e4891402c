/* eslint-disable */
/* tslint:disable */

import * as base from './base';

export { base };

/** 推送模式 */
export enum PostMode {
  /** 定时 */
  TIMING = 1,
  REALTIME = 2,
}

/** 实时 */
/** 接收者配置类型 */
export enum ReceiverConfigType {
  /** 发送至固定的人或群 */
  FIXED = 1,
  /** 发送至角色 */
  VARIABLE = 2,
  USERDIFINED = 3,
}

/** 随调用方传入接收者列表 */
/** 推送状态 */
export enum Status {
  /** 待推送 */
  WATING = 1,
  /** 推送中 */
  RUNNING = 2,
  /** 推送成功 */
  SUCCESS = 3,
  ERROR = 4,
}

/** 发生错误 */
export enum ReceiverType {
  /** 邮箱 */
  EMAIL = 1,
  CHATID = 2,
}

/** lark群ID */
/** 决策模式 */
export enum DecisionMode {
  /** 消息通知 */
  MESSAGE = 1,
  FLOW = 2,
}

/** 工单流程 */
/** 用户信息 */
export interface UserInfo {
  /** 邮箱前缀 */
  EmailPrefix: string;
  /** 邮箱 */
  Email: string;
  DepartmentID?: string;
}

/** 所在部门ID */
export interface ReceiverConfig {
  /** 类型，邮箱或者群ID */
  Type: ReceiverType;
  /** 值，如果是发送至角色，该字段传带变量语法，例如$.user_name */
  Value: string;
  Role?: string;
}

/** 如果是「发送至角色」必传，其他情况不传。通过列表选择 */
/** 消息模版结构 */
export interface MessageTemplate {
  /** ID */
  ID?: string;
  /** 模版名称 */
  Name: string;
  /** 功能模块，从TCC获取 */
  Module: string;
  /** 模版内容 */
  Content: string;
  /** 推送模式 */
  Mode: PostMode;
  /** 决策模式 */
  DecisionMode: DecisionMode;
  /** 开始推送时间（用于定时推送） */
  FirstPostAt?: string;
  /** 间隔周期（用于定时推送），从TCC获取 */
  Interval?: string;
  /** 接收者配置类型 */
  ReceiverConfigType: ReceiverConfigType;
  /** 接收者配置，选择「随调用方传入接收者列表」不传，其他情况必传 */
  ReceiverConfig?: Array<ReceiverConfig>;
  /** 创建者(邮箱) */
  Creator?: string;
  /** 创建时间 */
  CreatedAt?: string;
  /** 更新时间 */
  UpdatedAt?: string;
  /** 工单流的唯一标识 */
  FlowName?: string;
  /** 工单流账号 */
  FlowAccount?: string;
  FlowToken?: string;
}

/** 工单账号token */
/** 推送历史记录结构 */
export interface NotifierHistory {
  /** ID */
  ID: string;
  /** 模版ID */
  TemplateID: string;
  /** 模版名称 */
  TemplateName: string;
  /** 模版内容 */
  Content: string;
  /** 推送状态 */
  Status: Status;
  /** 创建时间 */
  CreatedAt: string;
  /** 接收者 */
  ReceiverRecords: Array<ReceiverRecord>;
  FlowID?: string;
}

/** 工单ID */
/** 接收者记录结构 */
export interface ReceiverRecord {
  /** 接收者记录本身ID */
  ID: string;
  /** lark返回的消息ID */
  MessageID: string;
  /** 状态码，lark返回的状态码+平台自身状态码 */
  StatusCode: string;
  /** 只传Type 和 Value */
  Receiver: ReceiverConfig;
  PostAt: string;
}

/** 推送时间 */
/** 传递模版ID的请求 */
export interface TemplateIDReq {
  /** 模版ID */
  ID: string;
  Base?: base.Base;
}

/** 返回模版ID的响应 */
export interface TemplateIDResp {
  /** 模版ID */
  ID: string;
  BaseResp?: base.BaseResp;
}

/** 创建或编辑模版请求 */
export interface UpsertTemplateReq {
  Data: MessageTemplate;
  /** 用户信息 */
  User: UserInfo;
  Base?: base.Base;
}

/** 查询模版详情响应 */
export interface TemplateDetailResp {
  Data: MessageTemplate;
  BaseResp?: base.BaseResp;
}

/** 删除模版响应 */
export interface DeleteTemplateResp {
  BaseResp?: base.BaseResp;
}

/** 查询模版列表请求 */
export interface TemplateListReq {
  /** 是否为数据导出请求，如果是导出则返回查询条件下全量数据 */
  Export: boolean;
  /** 筛选模版名称（模糊） */
  Name?: string;
  /** 筛选创建人（模糊） */
  Creator?: string;
  /** 筛选决策模式 */
  DecisionMode?: DecisionMode;
  /** 分页信息：每一页的数据条数 */
  PerPageItems?: string;
  /** 分页信息：当前页码，从1开始 */
  CurrentPage?: string;
  /** 筛选模版ID，管理员身份时传空 */
  IDs?: Array<string>;
  /** 当前操作人信息 */
  User: UserInfo;
  Base?: base.Base;
}

/** 模版详情响应 */
export interface TemplateListResp {
  /** 数据内容 */
  Results: Array<MessageTemplate>;
  /** 数据总条数，满足请求中查询条件的数据的总数 */
  AllCount: string;
  BaseResp?: base.BaseResp;
}

/** 查询推送历史记录请求 */
export interface HistoryListReq {
  /** 模版ID，前端调用必传，调用方可选 */
  TemplateID?: string;
  /** 是否为数据导出请求，如果是导出则返回查询条件下全量数据 */
  Export: boolean;
  /** 分页信息：每一页的数据条数 */
  PerPageItems?: string;
  /** 分页信息：当前页码，从1开始 */
  CurrentPage?: string;
  /** 筛选状态 */
  Status?: Status;
  /** 筛选创建时间：开始时间 */
  CreatedStartAt?: string;
  /** 筛选创建时间：结束时间 */
  CreatedEndAt?: string;
  /** 筛选推送时间：开始时间 */
  PostStartAt?: string;
  /** 筛选推送时间：结束时间 */
  PostEndAt?: string;
  /** 筛选UUID，供调用方使用，前端调用无需传 */
  IDs?: Array<string>;
  /** 当前操作人信息 */
  User: UserInfo;
  Base?: base.Base;
}

/** 查询推送历史记录响应 */
export interface HistoryListResp {
  /** 数据内容 */
  Results: Array<NotifierHistory>;
  /** 数据总条数，满足请求中查询条件的数据的总数 */
  AllCount: string;
  BaseResp?: base.BaseResp;
}
