/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';

export { base };

export interface DataQueryRequest {
  Psm: string;
  Token: string;
  Query?: string;
  QueryId?: string;
  Variables?: string;
  Base?: base.Base;
}

export interface DataQueryResponse {
  Data?: string;
  Errors?: Array<string>;
  BaseResp: base.BaseResp;
}

export interface WebQueryRequest {
  RequestString: string;
  VariablesString?: string;
  OperationName?: string;
  Base?: base.Base;
}

export interface WebQueryResponse {
  Data?: string;
  BaseResp: base.BaseResp;
}

export declare class DataMarketService {
  public Query(request: DataQueryRequest): Promise<DataQueryResponse>;
  public Query(ctx: ClientInvokeOptions, request: DataQueryRequest): Promise<DataQueryResponse>;

  public WebQuery(request: WebQueryRequest): Promise<WebQueryResponse>;
  public WebQuery(ctx: ClientInvokeOptions, request: WebQueryRequest): Promise<WebQueryResponse>;
}
