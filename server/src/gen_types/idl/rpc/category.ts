/* eslint-disable */
/* tslint:disable */

import { ClientInvokeOptions } from '@byted-service/rpc';
import * as base from './base';

export { base };

/** model */
export interface Resource {
  Id?: string;
  Name: string;
  /** 对接人名称 */
  DockingName: string;
  /** 接入方idA */
  AccessPartyId: string;
  /** 是否开启子资源 0-未开启 1-开启 */
  SubFlag: number;
  /** 是否可用 */
  EnableFlag: number;
  SubList: Array<SubResource>;
}

export interface SubResource {
  Id?: string;
  Name: string;
  EnableFlag: number;
}

/** 是否可用 */
export interface AccessParty {
  Id?: string;
  /** 接入方名称 */
  Name: string;
  /** 二级接入方 */
  SubAccessParty?: Array<AccessParty>;
  EnableFlag: number;
}

/** 是否可用 */
export interface App {
  Id?: string;
  Name: string;
  /** 使用资源数量 */
  ResourceCount?: string;
  /** 使用标签数量 */
  CategoryCount?: string;
  /** 对接人名称 */
  DockingName: string;
  /** 更新时间 */
  LocalUpdateTime?: string;
  EnableFlag?: number;
}

export interface Category {
  /** 标签id 新增为0 */
  Id?: string;
  /** 资源id */
  ResourceId?: string;
  /** 子资源id */
  SubResourceId?: string;
  /** 标签名称 */
  Name: string;
  /** 标签全路径 */
  Path?: string;
  /** 父标签id */
  ParentId?: string;
  /** 层级 */
  Level?: number;
  /** 当前排序 */
  OrderIndex: number;
  /** 子标签集合 */
  SubCategoryList?: Array<Category>;
  /** 是否绑定app */
  IsBindApp?: boolean;
  EnableFlag?: number;
}

/** common */
export interface CommonRequest {
  /** 租户id */
  TenantId: string;
  /** 当前页码 */
  PageNo?: string;
  /** 单页数量 */
  PageSize?: string;
  /** 客服Id */
  AgentId: string;
  /** 客服名称 */
  AgentName: string;
  CountryCode: string;
}

/** 国家码 */
export interface CommonResponse {
  BaseResp: base.BaseResp;
}

/** request and response */
export interface ResourceRequest {
  CommonRequest: CommonRequest;
  /** 资源列表 */
  Resource: Resource;
  Base?: base.Base;
}

export interface GetResourceListRequest {
  CommonRequest: CommonRequest;
  /** 搜索key */
  SearchKey?: string;
  /** 根据appId搜索绑定的资源子资源 */
  appId?: string;
  /** 根据接入方id进行隔离 */
  AccessPartyId: string;
  Base?: base.Base;
}

export interface GetResourceListResponse {
  /** 资源列表 */
  ResourceList: Array<Resource>;
  /** 总数 */
  Total: string;
  BaseResp: base.BaseResp;
}

export interface GetCategoryListRequest {
  CommonRequest: CommonRequest;
  /** 资源id */
  ResourceId: string;
  /** 子资源ID */
  SubResourceId: string;
  Base?: base.Base;
}

export interface GetCategoryListByIdsRequest {
  CommonRequest: CommonRequest;
  /** 标签ID集合 */
  CategoryIds: Array<string>;
  Base?: base.Base;
}

export interface GetCategoryListResponse {
  CategoryList: Array<Category>;
  BaseResp: base.BaseResp;
}

export interface AddOrUpdateCategoryRequest {
  CommonRequest: CommonRequest;
  /** 资源id */
  ResourceId: string;
  /** 子资源ID */
  SubResourceId: string;
  /** 修改/新增的标签集合，新增的标签可能有子标签 */
  CategoryList: Array<Category>;
  DeleteIdList: Array<string>;
  Base?: base.Base;
}

export interface GetAppListRequest {
  CommonRequest: CommonRequest;
  Name: string;
  Base?: base.Base;
}

export interface GetAppListResponse {
  /** App列表 */
  AppList: Array<App>;
  /** 总数 */
  Total: string;
  BaseResp: base.BaseResp;
}

export interface AppRequest {
  CommonRequest: CommonRequest;
  App: App;
  Base?: base.Base;
}

export interface GetCategoryAppListRequest {
  CommonRequest: CommonRequest;
  /** 资源id */
  ResourceId: string;
  /** 子资源ID */
  SubResourceId: string;
  AppId: string;
  Base?: base.Base;
}

export interface BindCategoryToAppRequest {
  CommonRequest: CommonRequest;
  /** 资源id */
  ResourceId: string;
  /** 子资源ID */
  SubResourceId: string;
  AppId: string;
  /** 新增绑定标签id */
  NewBindCategoryIds: Array<string>;
  /** 解绑标签id */
  UnBindCategoryIds: Array<string>;
  Base?: base.Base;
}

/** struct BindTagToCategoryRequest{ */
/** 1: required CommonRequest CommonRequest, */
/** 2: string ExtraCode,//标签三级id */
/** 255: base.BaseResp BaseResp, */
/** } */
export interface GetAccessPartyListRequest {
  CommonRequest: CommonRequest;
  Base?: base.Base;
}

export interface GetAccessPartyListRespone {
  AccessPartyList: Array<AccessParty>;
  BaseResp: base.BaseResp;
}

export declare class CateGoryService {
  /** 资源配置 */
  /** 搜索资源列表 */
  public GetResourceList(req: GetResourceListRequest): Promise<GetResourceListResponse>;
  public GetResourceList(ctx: ClientInvokeOptions, req: GetResourceListRequest): Promise<GetResourceListResponse>;

  /** 新增修改资源 */
  public AddOrUpdateResource(req: ResourceRequest): Promise<CommonResponse>;
  public AddOrUpdateResource(ctx: ClientInvokeOptions, req: ResourceRequest): Promise<CommonResponse>;

  /** 标签配置 */
  /** 按照资源+子资源搜索标签列表 */
  public GetCategoryList(req: GetCategoryListRequest): Promise<GetCategoryListResponse>;
  public GetCategoryList(ctx: ClientInvokeOptions, req: GetCategoryListRequest): Promise<GetCategoryListResponse>;

  /** 新增修改标签 */
  public AddOrUpdateCategory(req: AddOrUpdateCategoryRequest): Promise<CommonResponse>;
  public AddOrUpdateCategory(ctx: ClientInvokeOptions, req: AddOrUpdateCategoryRequest): Promise<CommonResponse>;

  /** 按照标签id批量搜索标签 */
  public GetCategoryListByIds(req: GetCategoryListByIdsRequest): Promise<GetCategoryListResponse>;
  public GetCategoryListByIds(
    ctx: ClientInvokeOptions,
    req: GetCategoryListByIdsRequest
  ): Promise<GetCategoryListResponse>;

  /** 属性配置 */
  /** CommonResponse BindTagToCategory(1: BindCategoryToAppRequest req);//tag绑定标签 */
  /** 应用配置 */
  /** 搜索端列表 */
  public GetAppList(req: GetAppListRequest): Promise<GetAppListResponse>;
  public GetAppList(ctx: ClientInvokeOptions, req: GetAppListRequest): Promise<GetAppListResponse>;

  /** 添加/修改 app */
  public AddOrUpdateApp(req: AppRequest): Promise<CommonResponse>;
  public AddOrUpdateApp(ctx: ClientInvokeOptions, req: AppRequest): Promise<CommonResponse>;

  /** 获取子资源的全量标签，以及app标签绑定关系 */
  public GetAppCategoryList(req: GetCategoryAppListRequest): Promise<GetCategoryListResponse>;
  public GetAppCategoryList(ctx: ClientInvokeOptions, req: GetCategoryAppListRequest): Promise<GetCategoryListResponse>;

  /** app绑定标签 */
  public BindCategoryToApp(req: BindCategoryToAppRequest): Promise<CommonResponse>;
  public BindCategoryToApp(ctx: ClientInvokeOptions, req: BindCategoryToAppRequest): Promise<CommonResponse>;

  /** 接入方 */
  public getAccessPartyList(req: GetAccessPartyListRequest): Promise<GetAccessPartyListRespone>;
  public getAccessPartyList(
    ctx: ClientInvokeOptions,
    req: GetAccessPartyListRequest
  ): Promise<GetAccessPartyListRespone>;
}
