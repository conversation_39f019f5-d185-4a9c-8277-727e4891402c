import * as base from './base';
import * as gulu from '@gulu/application-http';
export * as base from './base';
export { Controller } from '@gulu/application-http';
export namespace Gulu {
  export const { Controller } = gulu;
  export const { Service } = gulu;
  export const Application = gulu.HttpApplication;
}
export interface Context<T> extends gulu.HttpContext {
  args: {
    req: T;
  };
}
export interface QueryBizTypesByAccessPartyIDRequest {
  AccessPartyId: string;
}
// 组件类型
export enum FieldOptionType {
  SINGLE_CHOOSE = 1, // 单选

  MULTI_CHOOSE = 2, // 多选

  MIX = 3, // 混合(多选+输入框)

  DATE = 4, // 时间控件

  INPUT = 5, // 输入框

  BATCH_INPUT = 6, // 批量输入

  TREE = 7, // 树

  CASCADER = 8, // 级联

  TEXT = 9, // 多行文本

  INT = 10, // 数字

  CONSTANT = 11, // 常量

  RICH_TEXT = 12, // 富文本

  MULTI_LANG = 13, // 多语言文本

  DATE_TIME_POINT = 14, // 时间点类型

  CONDITION_CASCADER_LIST = 16, // 条件级联下拉列表

  INPUT_FLOAT = 17, // 浮点数输入框

  TREE_TO_LIST = 701, // 树状结构-只选择当前节点不选择子节点
}
export interface BizType {
  ID: number; // ID
  AppID: number; // 客服平台AppID
  AppName: string; // 客服平台App Name
  Channel: number; // 客服平台Channel
  ChannelName: string; // 客服平台 Channel Name
  HostAppID: string; // 宿主AppID
  HostAppName: string; // 宿主App name
  AppBaseID: string; // 标签应用ID
  ResourceID: string; // 资源ID
  SubResourceID: string; // 子资源ID
  AccessPartyID: string; // 接入方ID
  Scene?: string; // 场景
  EntranceId?: string; // 入口ID
  EntranceName?: string;
}
// 入口名
export interface QueryBizTypesByAccessPartyIDResponse {
  code: number;
  data: BizType[];
  message: string;
}
export interface GetSkillGroupsByAgentIdRequest {
  AccessPartyIds: string[];
  channelType?: ChannelType;
}
// 操作渠道
export enum ChannelType {
  IM = 1, // IM

  TICKET = 2, // 工单

  PHONE = 3, // 电话

  ADMIN = 4, // 管理员

  ECOM_TICKET = 5, // 电商工单

  BUZZ = 6, // BUZZ工单

  FEEDBACK = 7, // FEEDBACK

  QUALITY_CHECK = 8, // 质检

  IM_OFFLINE_SESSION = 9, // IM离线留言会话

  ACCESS_CALL = 10, // 触达外呼

  CALLBACK = 11, // 预约回呼

  AFTER_SALE = 12, // 售后
}
export interface SkillGroup {
  ID: string;
  TenantId: string;
  AccessPartyId: string[];
  Name: string;
  ChannelType: ChannelType;
  MaxTaskNum: number;
  QuestionCategoryIds: string[];
  CategoryIds: string[];
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
}
export interface GetSkillGroupsByAgentIdResponse {
  code: number;
  data: SkillGroup[];
  message: string;
}
export interface GetCardRequest {
  AccessPartyId: string;
  AppIds: string[];
}
// 应用ID列表
export interface CardQuestionThrift {
  Id: string; // 问题ID
  CardId: string; // 卡片ID
  QuestionName: string; // 问题内容
  SkillGroupId: string;
}
// 技能组ID
export interface AppQuestionCardThrift {
  Id: string; // 卡片ID
  TenantId: string; // 租户ID
  AccessPartyId: string; // 接入方ID
  AppId: string; // 应用ID
  CardName: string; // 卡片名称，用于title展示
  IsOpen: number; // 是否启用
  CardQuestions: CardQuestionThrift[];
}
// 问题列表，list顺序代表C端展示顺序
export interface GetCardResponse {
  code: number;
  data: AppQuestionCardThrift[]; // 问题卡片列表
  message: string;
}
export interface AddCardQuestion {
  QuestionName: string; // 问题内容
  SkillGroupId: string;
}
// 技能组ID
export interface AddAppQuestionCard {
  AppId: string; // 应用ID
  CardName: string; // 卡片名称，用于title展示
  AddCardQuestions: AddCardQuestion[];
}
// 问题列表，list顺序代表C端展示顺序
export interface CreateCardRequest {
  AddAppQuestionCard: AddAppQuestionCard; // 不带主键ID的问题卡片对象
  AccessPartyId: string;
}
export interface CreateCardResponse {
  code: number;
  message: string;
}
export interface UpdateCardQuestion {
  Id?: string; // 问题ID
  QuestionName: string; // 问题内容
  SkillGroupId: string;
}
// 技能组ID
export interface UpdateAppQuestionCardThrift {
  Id: string; // 卡片ID
  TenantId: string; // 租户ID
  AccessPartyId: string; // 接入方ID
  AppId: string; // 应用ID
  CardName: string; // 卡片名称，用于title展示
  UpdateCardQuestions: UpdateCardQuestion[]; // 问题列表，list顺序代表C端展示顺序，更新跟创建结构相同，用于兼容同时又问题增删的情况
  CardDisplayName?: string;
}
// 卡片展示名称 新版必传 向下兼容
export interface UpdateCardRequest {
  UpdateAppQuestionCardThrift: UpdateAppQuestionCardThrift; // 带有主键ID的问题卡片对象
  AccessPartyId: string;
}
export interface UpdateCardResponse {
  code: number;
  message: string;
}
// 卡片操作类型
export enum HandleType {
  OPEN = 1,
  CLOSE = 2,
  DELETE = 3,
}
export interface HandleCardRequest {
  CardId: string; // 卡片ID
  HandleType: HandleType; // 操作类型
  AccessPartyId: string;
}
export interface HandleCardResponse {
  code: number;
  message: string;
}
export interface SubResource {
  Id?: string;
  Name: string;
  EnableFlag: number;
}
// 是否可用
export interface Resource {
  Id?: string;
  Name: string;
  DockingName: string; // 对接人名称
  AccessPartyId: string; // 接入方id
  SubFlag: number; // 是否开启子资源 0-未开启 1-开启
  EnableFlag: number; // 是否可用
  SubList: SubResource[];
}
export interface GetResourceListRequest {
  AccessPartyId: string;
}
export interface GetResourceListResponseData {
  ResourceList: Resource[];
}
export interface GetResourceListResponse {
  code: number;
  data: GetResourceListResponseData;
  message: string;
}
export interface Category {
  Id?: string; // 标签id 新增为0
  ResourceId?: string; // 资源id
  SubResourceId?: string; // 子资源id
  Name: string; // 标签名称
  Path?: string; // 标签全路径
  ParentId?: string; // 父标签id
  Level?: number; // 层级
  OrderIndex: number; // 当前排序
  SubCategoryList?: Category[]; // 子标签集合
  IsBindApp?: boolean; // 是否绑定app
  EnableFlag?: number;
}
export interface GetCategoryListRequest {
  ResourceId: string; // 资源id
  SubResourceId: string;
}
// 子资源ID
export interface GetCategoryListResponseData {
  CategoryList: Category[];
}
export interface GetCategoryListResponse {
  code: number;
  data: GetCategoryListResponseData;
  message: string;
}
export interface FieldValueItem {
  name: string;
  value: number;
}
export interface GetFieldValuesRequest {
  FieldId: string;
  OperatorId: number;
  AccessPartyId: string;
  AppIds?: string[];
  Operator?: string;
}
export interface GetFieldValuesResponseData {
  FieldValueType: number;
  FieldValueList: FieldValueItem[];
  FieldValues: string;
}
export interface GetFieldValuesResponse {
  code: number;
  data: GetFieldValuesResponseData;
  message: string;
}
export interface FieldCondition {
  FieldId: string;
  FieldDisplayName: string; // 字段的前端展示名称
  FieldMapName: string; // 字段的数据库存储名称
  OperatorIds: number[];
  Fieldvalues: Record<string, GetFieldValuesResponseData[]>;
}
export interface GetFieldListRequest {
  AccessPartyId: string;
  EventId: string;
  AppIds?: string[];
}
export interface GetFieldListResponse {
  code: number;
  data: FieldCondition[];
  message: string;
}
// 规则任务状态
export enum RuleTaskStatus {
  HAS_RUNNING = 1, // 运行中

  NO_RUNNING = 0,
}

/*
 * 无运行中
 * 规则禁用状态
 */
export enum RuleStopStatus {
  DISABLED = 1, // 禁用

  USING = 0,
}

/*
 * 启用
 * 规则状态
 */
export enum EntityStatus {
  ENABLE = 1, // 可用

  UNABLE = 0,
}

/*
 * 不可用
 * 单个字段条件
 */
export interface FilterUnit {
  FieldId?: string;
  FieldMapName: string; // todo 自定义字段前缀
  OperatorId: number; // todo 前端映射ID
  FieldValue: string;
}
// 条件操作类型
export enum FilterOperateType {
  UNION = 0, // 求并集

  INTERSECTION = 1,
}

/*
 * 求交集
 * 条件
 */
export interface Filter {
  OperateType: FilterOperateType;
  FilterUnitList: FilterUnit[];
}
export interface AdminRule {
  Id: string; // ID
  DisplayName: string; // 名称
  Priority: number; // 优先级
  Filter: Filter; // 条件
  Value: string; // 动作信息 json, list查询时填充''，get查询时填充
  StopStatus: RuleStopStatus; // 禁用状态
  Status: EntityStatus; // 状态
  AppId: number; // 应用类型，如0：触发器、1：SLA、2：后台
  TenantId: string; // 租户ID
  AccessPartyId: string; // 接入方ID
  SourceId?: string; // 业务标识，区分工单、电商工单
  CreatedAt: string; // 创建时间
  UpdatedAt: string; // 更新时间
  Extra: string; // 附加信息
  CreatorAgentId: string; // 创建人ID
  UpdaterAgentId: string; // 更新人ID
  TaskStatus?: RuleTaskStatus; // 规则任务状态 get查询时填充
  UpdaterAgentName?: string;
}
// 规则任务状态 get查询时填充
export interface GetAdminRuleListRequest {
  AccessPartyId: string;
  DisplayNameLike?: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  StopStatus?: RuleStopStatus;
}
export interface GetAdminRuleListResponse {
  code: number;
  data: AdminRule[];
  message: string;
}
export interface CreateAdminRuleRequest {
  AccessPartyId: string;
  DisplayName: string;
  Priority: number;
  Filter: Filter;
  Value: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  CreateCardRequest?: CreateCardRequest;
  Disable?: boolean;
}
export interface CreateAdminRuleResponse {
  code: number;
  message: string;
}
export interface UpdateAdminRuleRequest {
  AccessPartyId: string;
  DisplayName?: string;
  Priority?: number;
  Filter?: Filter;
  Value?: string;
  Id: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  StopStatus?: RuleStopStatus;
  UpdateCardRequest?: UpdateCardRequest; // 卡片的修改请求，未修改时可不传
}
export interface UpdateAdminRuleResponse {
  code: number;
  message: string;
  data?: AdminRule;
}
export interface DeleteAdminRuleRequest {
  AccessPartyId: string;
  Id: string;
  SourceId: string;
  AppId: number;
}
export interface DeleteAdminRuleResponse {
  code: number;
  message: string;
}
export interface AdminRuleSimple {
  Id: string;
  Priority: number;
}
// 优先级
export interface BatchUpdateAdminRuleRequest {
  AccessPartyId: string;
  AdminRules: AdminRuleSimple[];
  EventId: string;
  SourceId: string;
  AppId: number;
}
export interface BatchUpdateAdminRuleResponse {
  code: number;
  message: string;
}
export interface GetSLAAimMetaSimpleListRequest {
  AccessPartyId?: string; // 接入方ID
  SourceId?: string; // 业务标识，区分工单、电商工单
}
export interface SLAAimMetaSimple {
  Id: string; // ID
  Name: string; // 名称 首响、完结
  Extra: string;
}
// 附加信息
export interface GetSLAAimMetaSimpleListResponse {
  code: number;
  message: string;
  data: SLAAimMetaSimple[];
}

/*
 * 新增部分
 * 规则状态
 */
export enum RuleStatus {
  ENABLE = 1, // 启用

  UNABLE = 0, // 禁用

  DRAFT = 2, // 草稿

  DELETE = 3,
}

/*
 * 已删除
 * 规则优先级
 */
export interface RulePriority {
  Id: string; // ID
  Priority: number;
}

/*
 * 优先级
 * 规则生效环境
 */
export enum RuleEnv {
  PPE = 0, // PPE环境

  PROD = 1,
}

/*
 * 线上环境
 * 条件组合操作类型
 */
export enum OpGroup {
  AND = 1, // 求交集

  OR = 2, // 求并集

  NOT = 3,
}

/*
 * 求反计算
 * 运算型参数表达式
 */
export interface MathExpr {
  opMath: string; // 数学运算符
  Lhs: Expr;
  Rhs: Expr;
}
// 方法型参数表达式
export interface FuncExpr {
  FuncName: string; // 方法名
  ParamExprMap?: Record<string, Expr>;
}

/*
 * 参数
 * 特征型参数表达式
 */
export interface FeatureExpr {
  FeatureName: string; // 特征名，为字符串，如"ticket.status"
  ParamExprMap?: Record<string, Expr>;
}

/*
 * 参数map，key为字符串，如"abc"
 * 参数表达式
 */
export interface Expr {
  ExprList?: Expr[]; // 元素可以为任意一种Expr
  SubstringList?: Expr[]; // 元素可以为任意一种Expr，用于拼成字符串
  MathExpr?: MathExpr;
  FuncExpr?: FuncExpr;
  FeatureExpr?: FeatureExpr; // 特征型参数表达式，如"ticket.assignee_agent.status()"
  VarExpr?: string; // 变量型参数表达式，如"$id"
  ConstantList?: string[]; // 元素可以为字符串，数字，布尔值中任意一种常量
  Constant?: string;
}

/*
 * 常量型参数表达式：字符串，如\"abc\"，\"300\"；数字，如100.1；布尔值，如true
 * 条件表达式
 */
export interface ConditionExpr {
  OpCheck: string;
  Lhs: Expr; // 运算左值
  Rhs?: Expr;
}

/*
 * 运算右值
 * 条件组合
 */
export interface ConditionGroupExpr {
  OpGroup: string;
  Conditions?: ConditionExpr[];
  ConditionGroups?: ConditionGroupExpr[];
}

/*
 * conditions和conditionGroups有且只有一个非空
 * 动作表达式
 */
export interface ActionExpr {
  ActionName: string; // 动作名，为字符串，如"abc"
  ParamExprMap?: Record<string, Expr>;
}

/*
 * 参数map，key为字符串，如"abc"
 * 动作组合
 */
export interface ActionGroupExpr {
  Sequential: boolean;
  ContinueOnFail: boolean;
  Actions: ActionExpr[];
}
// 延时步骤
export interface DelayStepExpr {
  ActionGroup: ActionGroupExpr; // 即时动作
  FilterAim: string; // 即时条件名，为字符串，如"abc"
  DelayTime: Expr;
}

/*
 * 延时参数表达式
 * 规则返回
 */
export interface AimExpr {
  DelaySteps?: DelayStepExpr[]; // 延时步骤
  ActionGroup?: ActionGroupExpr; // 即时动作
  ReturnValue?: Expr;
}

/*
 * 路由返回 delaySteps/actionGroup/returnValue有且只有一个非空
 * 规则元数据
 */
export interface Rule {
  Id: string; // ID
  DisplayName: string; // 名称
  Priority: number; // 优先级
  Expression?: ConditionGroupExpr; // 规则条件部分
  ActionInfo?: AimExpr; // 动作部分
  RuleGroupId: string; // 规则组id
  Status: RuleStatus; // 规则状态
  Description?: string; // 规则描述
  CreatedAt: string; // 创建时间
  UpdatedAt: string; // 更新时间
  CreatorAgentId: string; // 创建人ID
  UpdaterAgentId: string; // 更新人ID
  DraftEditType: number; // 草稿编辑类型 1-新增 2-编辑 0-未修改
  UpdaterAgentName: string; // 更新人名字
}
// 创建规则
export interface CreateRuleV2Request {
  EventKey: string; // 事件key
  RuleGroupId?: string; // 规则组ID
  DisplayName: string; // 规则名称
  Priority: number; // 规则优先级
  Expression: ConditionGroupExpr; // 条件-DSL
  ActionInfo: AimExpr; // 动作部分
  Enable?: boolean; // 是否启用规则，不传默认为禁用
  Extra?: string; // 扩展字段，前端可用于复现规则
  Description: string; // 规则描述
  RuleEnv?: RuleEnv; // 规则生效环境，不传默认为线上
  AccessPartyId: string;
  Version: string; // 接口版本 UI传v1
  ExtraInfo?: Record<string, string>;
}
// 额外信息
export interface CreateRuleV2Response {
  Rule?: Rule;
  code: number;
  message: string;
}
// 更新规则
export interface UpdateRuleRequest {
  Id: string; // 规则ID
  DisplayName?: string; // 规则名称
  Expression?: ConditionGroupExpr; // 条件-DSL
  ActionInfo?: AimExpr; // 路由的结果部分，用于对结果的特殊处理，比如绑定技能组
  Extra?: string; // 扩展字段，前端可用于复现规则
  Description?: string; // 规则描述
  Version: string; // 接口版本 UI传v1
  PermCode?: string; // 门神权限点 code
  Priority?: number; // 规则优先级
  ExtraInfo?: Record<string, string>;
}
// 额外字段
export interface UpdateRuleResponse {
  Rule?: Rule;
  code: number;
  message: string;
}
// 启用/禁用/删除规则
export interface UpdateRuleStatusV2Request {
  Ids: string[]; // 规则ID的list
  RuleStatus: RuleStatus; // 建RuleStatus定义
  Version: string; // 接口版本 UI传v1
  PermCode?: string; // 门神权限点 code
  Draft?: boolean; // 是否为草稿规则
  ruleGroupId?: string;
  operateGroupAllRules?: number;
}
// 1 - 需要处理，0 -仅处理部分
export interface UpdateRuleStatusV2Response {
  code: number;
  message: string;
}
// 调整规则优先级
export interface UpdateRulePriorityV2Request {
  Rules: RulePriority[]; // 所有规则的优先级信息
  Version: string;
}
// 接口版本 UI传v1
export interface UpdateRulePriorityV2Response {
  RuleGroupId?: string; // 调整优先级会生成新的规则组版本(不包括草稿)
  newRuleIds?: Record<string, string>; // 调整优先级会生成新的ruleId(不包括草稿)
  code: number;
  message: string;
}
// 根据id获取规则
export interface GetRuleByIdRequest {
  Id: string;
}
export interface GetRuleByIdResponse {
  Rule?: Rule;
  code: number;
  message: string;
}
// 获取规则列表
export interface GetRuleListV2Request {
  EventKey: string; // 事件Key
  AccessPartyId: string; // 接入方ID
  RuleGroupId?: string; // 规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
  IsDraft?: number; // 是否获取草稿箱里的规则1-是
  ExtraInfo?: Record<string, string>; // 额外查询字段
  Page?: number;
  PageSize?: number;
}
export interface GetRuleListV2Response {
  data?: Rule[]; // 规则
  code: number;
  message: string;
  existRulesIfNotFilter?: number;
}
export interface PublishRuleGroupV2Request {
  RuleGroupId: string; // 规则组id
  PermCode?: string; // 权限点
  RuleIds?: string[]; // 规则ID集合
  eventKey?: string;
}
export interface PublishRuleGroupV2Response {
  RuleGroupId?: string; // 新的规则组id
  newRuleIds?: Record<string, string>; // 发布会生成新的ruleId
  code: number;
  message: string;
}
export interface CopyRuleGroupV2Request {
  RuleGroupId: string;
}

/*
 * 规则组id
 * 2: required string UpdaterAgentId, // 更新人
 */
export interface CopyRuleGroupV2Response {
  code: number;
  message: string;
}
export interface GetRuleListV3Response {
  code: number;
}
export interface GetRuleListV3Request {
  name: string;
}
// 获取规则列表
export interface GetRuleListV4Request {
  EventKey: string; // 事件Key
  AccessPartyId: string; // 接入方ID
  RuleGroupId?: string; // 规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
  IsDraft?: number; // 是否获取草稿箱里的规则1-是
  ExtraInfo?: Record<string, string>; // 额外查询字段
  Page?: number;
  PageSize?: number;
  statusList?: number[]; // 状态，1-启动，0-禁用
  ruleName?: string;
}
// 规则名称
export interface GetRuleListV4Response {
  data?: Rule[]; // 规则
  code: number;
  message: string;
  existRulesIfNotFilter?: number;
}
export interface Agent {
  ID: string;
  TenantId: string;
  WorkType: number;
  UserName: string;
  NickName: string;
  UserId: string;
  UUID: string;
  Email: string;
  Mobile: string;
  CompanyId: string;
  ChannelTypes: ChannelType[];
  Status: number;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
  OperatorName: string;
  DepartmentId: string;
  ImMaxTaskNum: number;
  PhoneSeatNo?: string;
  CountryRegion?: string; // 国家地区
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  Extra: Record<string, string>;
}

/*
 * 目前extra里包含的字段 c_n_agent_appid(IM appId),
 * c_s_ecom_ticket_role(电商工单系统角色),
 * c_n_ecom_ticket_is_super(电商工单是否超级管理员)
 * c_s_ecom_ticket_kind(电商工单业务类型)
 */
export interface CheckSkillGroupAutoAssign {
  autoAssign?: boolean;
  unautoAssignAgents?: Agent[];
  skillGroupAgentsCount?: string;
  skillGroupId?: string;
}
export interface CheckSkillGroupAutoAssignRequest {
  skillGroupIdList: string[];
}
export interface CheckSkillGroupAutoAssignResponse {
  /*
   * 1: optional bool autoAssign,
   * 2: optional list<Agent> unautoAssignAgents,
   * 3: optional i64 skillGroupAgentsCount,
   */
  checkSkillGroupAutoAssign?: CheckSkillGroupAutoAssign[];
  code: number;
  message: string;
}
export enum ConditionUpdateType {
  OptionsUpdate = 1, // 选项更新

  DELETE = 2, // 条件删除

  ADD = 3,
}
// 条件新增
export enum OperateRuleItemType {
  STAUTS = 1, // 更新状态

  NAME = 2, // 更新名字

  PRIORITY = 3, // 更新优先级

  CONDITION = 4, // 更新条件

  CONDITION_OPTIONS = 5, // 更新条件选项

  CONDITION_RELATION = 6, // 更新条件间关系

  CONDITION_GROUP_RELATION = 7, // 更新条件组间关系

  RETURN_VALUE = 8, // 更新规则返回值

  ROUTE_TYPE = 9, // 路由时机变更

  OVERFLOW = 10,
}
// 溢出设置变更
export enum RuleOperationType {
  CREATE_ENABLE = 1, // 创建规则并启用

  CREATE_DISABLE = 2, // 创建规则并禁用

  STATUS_CHANGE = 3, // 规则状态变更, 启用/禁用/发布

  PRIORITY_CHANGE = 4, // 规则优先级变更

  UPDATE = 5,
}
// 规则内容变更
export interface ConditionGroupChange {
  ConditionGroupOrder: number; // 条件组序号
  ConditionChangeList?: ConditionChange[]; // 条件变更列表
  Relation?: string; // 条件关系变更后结果
  ConditionNameList?: string[];
}
// 条件关系变更对应的条件列表
export interface ConditionChange {
  ConditionName: string; // 条件名字
  ConditionUpdateType: ConditionUpdateType; // 条件更新类型
  AddOptions?: string[]; // 条件选项新增
  DeleteOptions?: string[];
}
// 条件选项删除
export interface OperateRuleItemLog {
  OperateItemType: OperateRuleItemType;
  BeforeValue?: string;
  AfterValue?: string; // 字符串或json结构
  ConditionGroupChangeList?: ConditionGroupChange[];
}
export interface RuleOperationLog {
  Id: string;
  OperationType: RuleOperationType;
  OperateRuleItemLogs?: OperateRuleItemLog[];
  OperatorAgentId: string;
  CreatedAt: string;
}
export interface GetRuleOperationLogsRequest {
  ruleId: string;
  eventId: string;
  accessPartyId: string;
  page: number;
  pageSize: number;
}
export interface BaseResp {
  StatusMessage: string;
  StatusCode: number;
  Extra?: Record<string, string>;
}
export interface retutnValue {
  value?: string;
  percent?: number;
  skillGroupName?: string;
}
export interface changeItem {
  groupIndex?: string;
  conditionName?: string;
  updateValueType?: string;
  options?: string;
  updateConditionsType?: string;
  OperateItemType?: number;
}
export interface logList {
  status?: string;
  retutnValue?: retutnValue[];
  priority?: string;
  name?: string;
  groupsRelationChange?: string;
  OperateItemType?: number;
  ConditionRelationChange?: changeItem[];
  ValueChange?: changeItem[];
  ConditionsChange?: changeItem[];
}
export interface RuleOperationLog {
  agentId?: string;
  agentName?: string;
  title?: string;
  updateTime?: string;
  logList?: logList[];
}
export interface GetRuleOperationLogsResponse {
  RuleOperationLogList?: Record<string, string>[];
  oldres?: Record<string, string>[];
  totalCount?: string;
  BaseResp: BaseResp;
}
export interface GetSkillGroupsByAccessPartiesResponse {
  SkillGroups: SkillGroup[];
  BaseResp?: BaseResp;
}
export interface GetSkillGroupsByAccessPartiesRequest {
  TenantId: string;
  AccessPartyIds: string[];
  ChannelType?: ChannelType;
}
// 问题匪类卡片rule
export interface ClientRule {
  Id: string; // ID
  DisplayName: string; // 名称
  Priority: number; // 优先级
  Expression?: ConditionGroupExpr; // 规则条件部分
  ActionInfo?: AimExpr; // 动作部分
  RuleGroupId: string; // 规则组id
  Status: RuleStatus; // 规则状态
  Description?: string; // 规则描述
  CreatedAt: string; // 创建时间
  UpdatedAt: string; // 更新时间
  CreatorAgentId: string; // 创建人ID
  UpdaterAgentId: string; // 更新人ID
  DraftEditType: number; // 草稿编辑类型 1-新增 2-编辑 0-未修改
  UpdaterAgentName: string; // 更新人名字
  cardInfo?: AppQuestionCardThrift;
}
export interface GetRuleGroupListByEventKeyRequest {
  EventKey: string;
  RuleGroupDisplayName?: string; // 名称
  RuleGroupStatus?: RuleStatus[]; // 状态
  CreatorAgentId?: string[]; // 创建人
  CreatorTime?: string[]; // 创建时间
  UpdaterAgentId?: string[]; // 更新人
  UpdaterTime?: string[]; // 更新时间
  AccessPartId: string;
  SkillGroupId?: string;
  Page: number;
  PageSize: number;
}
export interface GetRuleGroupListByEventKeyResponse {
  RuleGroupList?: RuleGroupDetail[]; // 规则组的list
  Count?: number; // 数量
  code: number;
  message: string;
}
// 规则组详情
export interface RuleGroupDetail {
  RuleGroupId: string; // 规则组id - 跳转详情页使用
  OriginId: string; // 原始id
  RuleGroupDisplayName: string; // 名称
  RuleGroupStatus: RuleStatus; // 状态
  Version: number; // 版本号
  RuleList: AntlrRule[]; // 规则集合
  DraftRuleGroupId?: string; // 草稿版本id - 跳转编辑页使用
  CreatorAgentId?: string; // 创建人
  CreatorTime?: string; // 创建时间
  UpdaterAgentId?: string; // 更新人
  UpdaterTime?: string; // 更新时间
  HaveReleaseVersion?: boolean; // 是否有线上生效版本
  ExtraInfo?: Record<string, string>;
}

/*
 * 规则类型
 * 规则
 */
export interface AntlrRule {
  DisplayName: string; // 规则名称
  Priority: number; // 规则优先级
  Expression: ConditionGroupExpr; // 条件-DSL
  ActionInfo: AimExpr; // 动作部分
  CreatorAgentId?: string; // 创建人id
  Description?: string; // 规则描述
  Id?: string; // 规则ID
  OriginId?: string; // 规则原始ID
  Extra?: string; // 用于前端复现规则
}
export interface GetSkillGroupsByTypeResquest {
  TenantId: string;
  ChannelType?: ChannelType;
  PageNo: number;
  PageSize: number;
  AccessPartyId?: string;
  ChannelTypes?: ChannelType[];
  SkillGroupLevel?: number;
  SkillGroupName?: string;
  OnlySimpleData?: boolean; // 是否只需要返回技能组的简单信息，不包含问题标签等关联数据
  BizLineEnum?: string; // 业务线枚举
  ImOfflineSessionEnable?: boolean; // 是否打开IM离线会话开关
  AccessPartyIds?: string[];
  OnlyId?: boolean; // 是否只返回技能组的ID以及技能组名称,只支持查询条件包含channelType和接入方ID的简单批量查询，如果需要按照SkillGroupLevel等字段进行筛选，请使用OnlySimpleData
  HasCode?: boolean;
}
// 是否只返回有技能Code的技能组
export interface GetSkillGroupsByTypeResponse {
  SkillGroups: SkillGroup[];
  TotalSize: number;
  code: number;
  message: string;
}
export interface GetExtraInfoRequestV2 {
  Scenes: string;
  EventKey: string;
  ExtraKeys?: string[];
  AccessPartId?: string;
}
export interface GetExtraInfoResponseV2 {
  RuleGroupRelations?: RuleGroupRelationStruct[];
  code: number;
  message: string;
}
export interface RuleGroupRelationStruct {
  RuleGroupId: string;
  extraInfos: Record<string, string>;
  OriginId: string;
}
export interface GetFieldListRequest {
  AccessPartyId: string;
  EventId: string;
  AppIds?: string[];
  ZjOtherAccessPartyId?: string;
}
export interface GetFieldListResponse {
  code: number;
  data: FieldCondition[];
  message: string;
}
export interface BatchCreateRuleGroupResponse {
  RuleGroupIds?: string[];
  code: number;
  message: string;
}
export interface BatchCreateRuleGroupRequest {
  RuleGroups: CreateRuleGroupRequest[];
}
// 创建规则组
export interface CreateRuleGroupRequest {
  EventKey: string; // 事件key
  GroupDisplayName: string; // 规则组名称
  CreatorAgentId: string; // 创建人id
  Product: string; // 产品
  RuleList: AntlrRule[]; // 规则集合
  Enable?: boolean; // 是否启用规则，不传默认为待发布（草稿），点保存-传false，发布传true
  RuleEnv?: RuleEnv; // 规则生效环境，不传默认为线上
  AccessPartyId: string; // 接入方ID
  ExtraInfo?: ExtraInfoStruct[];
  SkillGroupId?: string;
}
// 技能组
export interface ExtraInfoStruct {
  DisplayName: string;
  Key: string;
  Status: number;
  Type: number;
  Value: string;
}
export interface GetWorkStatusConfigsRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AccessPartyId: string;
}
export interface GetWorkStatusConfigsResponse {
  WorkStatusConfigs: WorkStatusConfig[];
  code: number;
  message: string;
}
export interface WorkStatusConfig {
  TenantId: string; // 租户ID
  ChannelType: ChannelType; // 坐席类型
  AccessPartyId: string; // 接入方id
  WorkStatus: number; // 工作状态值
  WorkStatusDesc: string; // 工作状态描述
  Enabled: boolean; // 是否启用
  ReceptionStatus: number; // 接线状态（0：不可接线；1: 可接线）
  UpdaterAgentId: string; // 更新人id
  updaterAgentName: string; // 更新人名字
  UpdatedAt: string; // 更新时间
  NGCCStatus?: string; // 对应的ngcc的status
  NgccEnable?: boolean; // 对应的ngcc电话外呼组件是否启用
  EnableConfig?: boolean;
}

/*
 * 是否可被配置（启用禁用）
 * 启用/禁用/删除规则
 */
export interface UpdateRuleGroupStatusRequest {
  RuleGroupId: string; // 规则组id
  RuleStatus: RuleStatus; // 状态
  UpdaterAgentId: string;
}

/*
 * 更新人
 * 结果
 */
export interface UpdateRuleGroupStatusResponse {
  Success?: boolean; // 成功失败
  code: number;
  message: string;
}
export interface BatchUpdateRuleGroupResponse {
  Success?: boolean; // 成功失败
  code: number;
  message: string;
}
// 修改规则组
export interface UpdateRuleGroupRequest {
  EventKey: string; // 事件key
  RuleGroupId: string; // 规则组ID
  OriginId: string; // 原始id
  GroupDisplayName: string; // 规则组名称
  Version: number; // 版本号
  UpdaterAgentId: string; // 创建人id
  Product: string; // 产品
  RuleList: AntlrRule[]; // 规则集合
  Enable?: boolean; // 是否启用规则，不传默认为禁用
  RuleEnv?: RuleEnv; // 规则生效环境，不传默认为线上
  AccessPartyId: string; // 接入方ID
  RuleType?: RuleType;
  SkillGroupId?: string;
}
// 技能组id
export enum RuleType {
  DOCUMENTARY_RULE = 1, // 跟单规则

  GENERAL_RULE = 2, // 通用规则

  SEND_DOWN = 3, // 下送

  UPGRADE = 4, // 升级

  FINISH = 5, // 完结
}
export interface BatchUpdateRuleGroupRequest {
  RuleGroups: UpdateRuleGroupRequest[];
}
export interface SearchSamBindRequest {
  sellerId?: string; // 商家id
  sellerCountryCode?: string; // 国家码
  imGroupId?: string; // 绑定IM技能组
  imAgentId?: string; // 绑定IM客服
  ticketGroupId?: string; // 绑定工单技能组
  ticketAgentId?: string; // 绑定工单客服
  operateAgentId?: string; // 更新人id
  updateTimeStart?: string; // 更新时间-start
  updateTimeEnd?: string; // 更新时间-end
  createTimeStart?: string; // 创建时间-start
  createTimeEnd?: string; // 创建时间-end
  pageNum?: string; // 页码，从1开始
  pageSize?: string; // 每页的数量
  geo?: string; // 合规区域
  emailDomain?: string; // 邮箱域名
  Base?: base.Base;
}
export interface SearchSamBindData {
  sellerId: string; // 商家id
  sellerImg?: string; // 商家头像地址
  sellerName?: string; // 商家名称
  imGroupName?: string; // 绑定IM技能组名称
  imAgentName?: string; // 绑定IM客服名称
  imAgentEmail?: string; // 绑定IM客服邮箱
  ticketGroupName?: string; // 绑定工单技能组名称
  ticketAgentName?: string; // 绑定工单客服名称
  ticketAgentEmail?: string; // 绑定工单客服邮箱
  note?: string; // 备注
  operateImg?: string; // 操作人头像
  operateName?: string; // 操作人名字
  updateTime?: string; // 更新时间
  createTime?: string; // 创建时间
  imGroupId?: string; // 绑定IM技能组
  imAgentId?: string; // 绑定IM客服
  ticketGroupId?: string; // 绑定工单技能组
  ticketAgentId?: string; // 绑定工单客服
  operateAgentId?: string; // 更新人id
  sellerCountry?: string; // 更新人id
  id?: string; // 商家id
  geo?: string; // 合规区域
  emailDomain?: string; // 邮箱域名
}
// 合规区域
export interface SearchSamBindResponse {
  searchSamBindDataList: SearchSamBindData[];
  totalSize: string;
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface CreateSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface CreateSamBindRequest {
  sellerId: string; // 商家id
  sellerCountryCode: string; // 商家国家/地区
  imGroupId: string; // 绑定IM技能组
  imAgentId: string; // 绑定IM客服
  ticketGroupId: string; // 绑定工单技能组
  ticketAgentId: string; // 绑定工单客服
  note?: string; // 备注
  geo?: string; // 合规区域
  emailDomain?: string; // 邮箱域名
  Base?: base.Base;
}
export interface UpdateSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface UpdateSamBindRequest {
  sellerId: string; // 商家id
  sellerCountryCode: string; // 商家国家/地区
  imGroupId: string; // 绑定IM技能组
  imAgentId: string; // 绑定IM客服
  ticketGroupId: string; // 绑定工单技能组
  ticketAgentId: string; // 绑定工单客服
  isDel: number; // 0 未删除 1删除
  note?: string; // 备注
  id: string; // id
  geo?: string; // 合规区域
  emailDomain?: string; // 邮箱域名
  Base?: base.Base;
}
export interface BatchCreateSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface BatchCreateSamBindRequest {
  excelUrl: string; // 文件地址
  geo?: string; // 合规区域
  Base?: base.Base;
}
export interface BatchDelSamBindByExcelResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface BatchDelSamBindByExcelRequest {
  excelUrl: string; // 文件地址
  geo?: string; // 合规区域
  Base?: base.Base;
}
export interface BatchTransferSamBindRequest {
  sellerIdList: string[]; // 商家id list
  imGroupId: string; // 绑定IM技能组
  imAgentId: string; // 绑定IM客服
  ticketGroupId: string; // 绑定工单技能组
  ticketAgentId: string; // 绑定工单客服
  emailDomain?: string; // 邮箱域名
  Base?: base.Base;
}
export interface BatchTransferSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface BatchDelSamBindRequest {
  sellerIdList: string[]; // 商家id list
  geo?: string; // 合规区域
  Base?: base.Base;
}
export interface BatchDelSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface BatchExportSamBindRequest {
  sellerId?: string; // 商家id
  sellerCountryCode?: string; // 国家码
  imGroupId?: string; // 绑定IM技能组
  imAgentId?: string; // 绑定IM客服
  ticketGroupId?: string; // 绑定工单技能组
  ticketAgentId?: string; // 绑定工单客服
  operateAgentId?: string; // 更新人id
  updateTimeStart?: string; // 更新时间-start
  updateTimeEnd?: string; // 更新时间-end
  createTimeStart?: string; // 创建时间-start
  createTimeEnd?: string; // 创建时间-end
  geo?: string; // 合规区域
  emailDomain?: string; // 邮箱域名
  Base?: base.Base;
}
export interface BatchExportSamBindResponse {
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface GetSellerInfoRequest {
  sellerId: string; // 商家id
  Base?: base.Base;
}
export interface GetSellerInfoResponse {
  sellerCountryCode: string; // 国家码
  bindStatus: number; // 能否绑定：1 能绑定 0：不能绑定
  BaseResp: base.BaseResp;
}
// status:0 成功 其他：失败
export interface GetAllSkillGroupsRequest {
  ShieldTypes?: number[]; // 1 测试技能组 2管理组 3 session特殊屏蔽组
  accessPartyId?: string;
  Base?: base.Base;
}
export interface GetAllSkillGroupsResponse {
  SkillGroupMap: Record<string, SkillGroup[]>;
  BaseResp: base.BaseResp;
}
export interface GetSkillGroupAgentsRequest {
  TenantId?: string;
  SkillGroupId?: string;
  AgentName?: string;
  IsGroupLeader?: number;
  PageNo?: number;
  PageSize?: number;
  AgentEmail?: string;
  AccessPartyId?: string;
  Keyword?: string; // 模糊搜索key  email or name, 优先级高于email/userName
  priority?: number;
  Base?: base.Base;
}
export interface GetSkillGroupAgentsResponse {
  Agents: Agent[];
  TotalSize: number;
  BaseResp: base.BaseResp;
}
export interface GetAgentsByConditionRequest {
  TenantId?: string;
  Status?: number;
  PageNo?: number;
  PageSize?: number;
  ChannelType?: ChannelType;
  Keyword?: string; // 模糊搜索key  email or name, 优先级高于email/userName
  Base?: base.Base;
}
export interface GetAgentsByConditionResponse {
  Agents: Agent[];
  TotalSize: number;
  BaseResp: base.BaseResp;
}
export interface ICustomerCloud {
  queryBizTypesByAccessPartyID(
    ctx: Context<QueryBizTypesByAccessPartyIDRequest>,
    ...args: any[]
  ): Promise<QueryBizTypesByAccessPartyIDResponse>;
}
export interface IAgentSkillGroup {
  getSkillGroupsByAgentId(
    ctx: Context<GetSkillGroupsByAgentIdRequest>,
    ...args: any[]
  ): Promise<GetSkillGroupsByAgentIdResponse>;
  getCard(ctx: Context<GetCardRequest>, ...args: any[]): Promise<GetCardResponse>;
  createCard(ctx: Context<CreateCardRequest>, ...args: any[]): Promise<CreateCardResponse>;
  updateCard(ctx: Context<UpdateCardRequest>, ...args: any[]): Promise<UpdateCardResponse>;
  handleCard(ctx: Context<HandleCardRequest>, ...args: any[]): Promise<HandleCardResponse>;
  checkSkillGroupAutoAssign(
    ctx: Context<CheckSkillGroupAutoAssignRequest>,
    ...args: any[]
  ): Promise<CheckSkillGroupAutoAssignResponse>;
  GetSkillGroupsByType(
    ctx: Context<GetSkillGroupsByTypeResquest>,
    ...args: any[]
  ): Promise<GetSkillGroupsByTypeResponse>; // 根据类型获取技能组列表 -- 编辑页选择技能组

  GetWorkStatusConfigs(
    ctx: Context<GetWorkStatusConfigsRequest>,
    ...args: any[]
  ): Promise<GetWorkStatusConfigsResponse>; // 获取工作状态配置

  GetAllSkillGroups(ctx: Context<GetAllSkillGroupsRequest>, ...args: any[]): Promise<GetAllSkillGroupsResponse>; // 获取全量技能组

  GetSkillGroupAgents(ctx: Context<GetSkillGroupAgentsRequest>, ...args: any[]): Promise<GetSkillGroupAgentsResponse>; // 获取技能组成员

  GetAgentsByCondition(
    ctx: Context<GetAgentsByConditionRequest>,
    ...args: any[]
  ): Promise<GetAgentsByConditionResponse>; // 根据条件获取人员
}
export interface ICategory {
  getResourceList(ctx: Context<GetResourceListRequest>, ...args: any[]): Promise<GetResourceListResponse>;
  getCategoryList(ctx: Context<GetCategoryListRequest>, ...args: any[]): Promise<GetCategoryListResponse>;
}
export interface IRouteRule {
  getSLAAimMetaSimpleList(
    ctx: Context<GetSLAAimMetaSimpleListRequest>,
    ...args: any[]
  ): Promise<GetSLAAimMetaSimpleListResponse>;
  getFieldList(ctx: Context<GetFieldListRequest>, ...args: any[]): Promise<GetFieldListResponse>;
  getFieldValues(ctx: Context<GetFieldValuesRequest>, ...args: any[]): Promise<GetFieldValuesResponse>;
  getAdminRuleList(ctx: Context<GetAdminRuleListRequest>, ...args: any[]): Promise<GetAdminRuleListResponse>;
  createAdminRule(ctx: Context<CreateAdminRuleRequest>, ...args: any[]): Promise<CreateAdminRuleResponse>;
  updateAdminRule(ctx: Context<UpdateAdminRuleRequest>, ...args: any[]): Promise<UpdateAdminRuleResponse>;
  deleteAdminRule(ctx: Context<DeleteAdminRuleRequest>, ...args: any[]): Promise<DeleteAdminRuleResponse>;
  batchUpdateAdminRule(
    ctx: Context<BatchUpdateAdminRuleRequest>,
    ...args: any[]
  ): Promise<BatchUpdateAdminRuleResponse>;
  createRule(ctx: Context<CreateRuleV2Request>, ...args: any[]): Promise<CreateRuleV2Response>;
  updateRuleById(ctx: Context<UpdateRuleRequest>, ...args: any[]): Promise<UpdateRuleResponse>;
  getNewRuleList(ctx: Context<GetRuleListV2Request>, ...args: any[]): Promise<GetRuleListV2Response>;
  getNewRuleListV4(ctx: Context<GetRuleListV4Request>, ...args: any[]): Promise<GetRuleListV4Response>;
  getNewRuleListV3(ctx: Context<GetRuleListV3Request>, ...args: any[]): Promise<GetRuleListV3Response>;
  UpdateRuleStatus(ctx: Context<UpdateRuleStatusV2Request>, ...args: any[]): Promise<UpdateRuleStatusV2Response>;
  UpdateRulePriority(ctx: Context<UpdateRulePriorityV2Request>, ...args: any[]): Promise<UpdateRulePriorityV2Response>;
  PublishRuleGroup(ctx: Context<PublishRuleGroupV2Request>, ...args: any[]): Promise<PublishRuleGroupV2Response>;
  CopyRuleGroup(ctx: Context<CopyRuleGroupV2Request>, ...args: any[]): Promise<CopyRuleGroupV2Response>;
  GetRuleOperationLogs(
    ctx: Context<GetRuleOperationLogsRequest>,
    ...args: any[]
  ): Promise<GetRuleOperationLogsResponse>;
  GetSkillGroupsByAccessParties(
    ctx: Context<GetSkillGroupsByAccessPartiesRequest>,
    ...args: any[]
  ): Promise<GetSkillGroupsByAccessPartiesResponse>;
  GetRuleGroupListByEventKey(
    ctx: Context<GetRuleGroupListByEventKeyRequest>,
    ...args: any[]
  ): Promise<GetRuleGroupListByEventKeyResponse>; // 规则列表查询

  GetExtraInfoV2(ctx: Context<GetExtraInfoRequestV2>, ...args: any[]): Promise<GetExtraInfoResponseV2>; // 获取已配置信息

  getNewFieldList(ctx: Context<GetFieldListRequest>, ...args: any[]): Promise<GetFieldListResponse>;
  BatchCreateRuleGroup(
    ctx: Context<BatchCreateRuleGroupRequest>,
    ...args: any[]
  ): Promise<BatchCreateRuleGroupResponse>; // 批量创建规则

  UpdateRuleGroupStatus(
    ctx: Context<UpdateRuleGroupStatusRequest>,
    ...args: any[]
  ): Promise<UpdateRuleGroupStatusResponse>; // 修改规则组状态 启用禁用

  BatchUpdateRuleGroup(
    ctx: Context<BatchUpdateRuleGroupRequest>,
    ...args: any[]
  ): Promise<BatchUpdateRuleGroupResponse>; // 批量更新规则
}
export interface IBindingConfig {
  SearchSamBind(ctx: Context<SearchSamBindRequest>, ...args: any[]): Promise<SearchSamBindResponse>; // 搜索绑定配置信息

  GetSellerInfo(ctx: Context<GetSellerInfoRequest>, ...args: any[]): Promise<GetSellerInfoResponse>; // 查询商家信息

  BatchExportSamBind(ctx: Context<BatchExportSamBindRequest>, ...args: any[]): Promise<BatchExportSamBindResponse>; // 批量导出绑定配置信息

  BatchDelSamBind(ctx: Context<BatchDelSamBindRequest>, ...args: any[]): Promise<BatchDelSamBindResponse>; // 批量删除绑定配置信息

  BatchTransferSamBind(
    ctx: Context<BatchTransferSamBindRequest>,
    ...args: any[]
  ): Promise<BatchTransferSamBindResponse>; // 批量转移绑定配置信息

  BatchDelSamBindByExcel(
    ctx: Context<BatchDelSamBindByExcelRequest>,
    ...args: any[]
  ): Promise<BatchDelSamBindByExcelResponse>; // 批量导入删除绑定配置信息

  BatchCreateSamBind(ctx: Context<BatchCreateSamBindRequest>, ...args: any[]): Promise<BatchCreateSamBindResponse>; // 批量新增绑定配置信息

  UpdateSamBind(ctx: Context<UpdateSamBindRequest>, ...args: any[]): Promise<UpdateSamBindResponse>; // 编辑绑定配置信息

  CreateSamBind(ctx: Context<CreateSamBindRequest>, ...args: any[]): Promise<CreateSamBindResponse>; // 新增绑定配置信息
}
