import {
  ROUTE_MANAGEMENT_BOT_ROUTING,
  ROUTE_MANAGEMENT_BOT_ROUTING_OPERATE,
  ROUTE_MANAGEMENT_QUESTION_CLASSIFICATION,
  ROUTE_MANAGEMENT_QUESTION_CLASSIFICATION_OPERATE,
  ROUTE_MANAGEMENT_SERVICE_ROUTING,
  ROUTE_MANAGEMENT_SERVICE_ROUTING_OPERATE,
  ROUTE_MANAGEMENT_TICKET_ROUTING,
  ROUTE_MANAGEMENT_TICKET_ROUTING_OPERATE
} from '@byted/unified_workbench_auth/constants';

// 离线路由权限点
const ROUTE_MANAGEMENT_OFFLINE_ROUTING = '/res.route_management/data.route_management_offline_routing';
const ROUTE_MANAGEMENT_OFFLINE_ROUTING_OPERATE = '/res.route_management/data.route_management_offline_routing_operate';

// 客户端问题分类卡片权限点
const ROUTE_MANAGEMENT_CLIENT_ROUTING = '/res.route_management/data.route_management_client_routing';
const ROUTE_MANAGEMENT_CLIENT_ROUTING_OPERATE = '/res.route_management/data.route_management_client_routing_operate';

// 质检路由权限点
const ROUTE_MANAGEMENT_QUALITY_CHECK_ROUTING = '/res.route_management/data.route_management_quality_check_routing';
const ROUTE_MANAGEMENT_QUALITY_CHECK_ROUTING_OPERATE =
  '/res.route_management/data.route_management_quality_check_routing_operate';

enum ROUTE_APP_ID {
  CLIENT_ROUTE = 7,
  SERVICE_ROUTE = 2,
  BOT_ROUTE = 3,
  ECOM_TICKET_ROUTE = 5,
  COMMON_TICKET_ROUTE = 9,
  OFFLINE_ROUTE = 6,
  QUALIFICATION_ROUTE = 15,
}

const CODE_MAP_VIEW = {
  [ROUTE_APP_ID.CLIENT_ROUTE]: ROUTE_MANAGEMENT_CLIENT_ROUTING,
  [ROUTE_APP_ID.SERVICE_ROUTE]: ROUTE_MANAGEMENT_SERVICE_ROUTING, // 人工
  [ROUTE_APP_ID.BOT_ROUTE]: ROUTE_MANAGEMENT_BOT_ROUTING, // 机器人
  [ROUTE_APP_ID.ECOM_TICKET_ROUTE]: ROUTE_MANAGEMENT_TICKET_ROUTING,
  [ROUTE_APP_ID.COMMON_TICKET_ROUTE]: ROUTE_MANAGEMENT_TICKET_ROUTING,
  [ROUTE_APP_ID.OFFLINE_ROUTE]: ROUTE_MANAGEMENT_OFFLINE_ROUTING,
  [ROUTE_APP_ID.QUALIFICATION_ROUTE]: ROUTE_MANAGEMENT_QUALITY_CHECK_ROUTING,
};

const CODE_MAP_OPERATE = {
  [ROUTE_APP_ID.CLIENT_ROUTE]: ROUTE_MANAGEMENT_CLIENT_ROUTING_OPERATE,
  [ROUTE_APP_ID.SERVICE_ROUTE]: ROUTE_MANAGEMENT_SERVICE_ROUTING_OPERATE, // 人工
  [ROUTE_APP_ID.BOT_ROUTE]: ROUTE_MANAGEMENT_BOT_ROUTING_OPERATE, // 机器人
  [ROUTE_APP_ID.ECOM_TICKET_ROUTE]: ROUTE_MANAGEMENT_TICKET_ROUTING_OPERATE,
  [ROUTE_APP_ID.COMMON_TICKET_ROUTE]: ROUTE_MANAGEMENT_TICKET_ROUTING_OPERATE,
  [ROUTE_APP_ID.OFFLINE_ROUTE]: ROUTE_MANAGEMENT_OFFLINE_ROUTING_OPERATE,
  [ROUTE_APP_ID.QUALIFICATION_ROUTE]: ROUTE_MANAGEMENT_QUALITY_CHECK_ROUTING_OPERATE,
};

/**
 * @file
 * <AUTHOR>
 */

export default {
  hordorSSOUserInfo: 'userInfo',
  checkStaffState: true,
  noStaffHandle: (ctx): void => {
    ctx.status = 200;
    ctx.body = {
      BaseResp: {
        StatusCode: 10401,
        StatusMessage: 'Staff authentication failed',
      },
    };
  },
  permissionRule: [
    {
      match: /\/route_management\/api\/(createCard|updateCard|handleCard)/,
      code: ROUTE_MANAGEMENT_QUESTION_CLASSIFICATION_OPERATE,
    },
    {
      match: '/route_management/api/getCard',
      code: ROUTE_MANAGEMENT_QUESTION_CLASSIFICATION,
    },
    {
      match: '/route_management/api/getAdminRuleList',
      code: ctx => {
        const parameter = {
          ...ctx.request.body,
          ...ctx.request.query,
          ...ctx.params,
        };

        return CODE_MAP_VIEW[parameter.AppId];
      },
    },
    {
      match: /\/route_management\/api\/(createAdminRule|updateAdminRule|deleteAdminRule|batchUpdateAdminRule)/,
      code: ctx => {
        const parameter = {
          ...ctx.request.body,
          ...ctx.request.query,
          ...ctx.params,
        };

        return CODE_MAP_OPERATE[parameter.AppId];
      },
    },
  ],
  getAccessPartyId: (ctx): string => {
    const parameter = {
      ...ctx.request.body,
      ...ctx.request.query,
      ...ctx.params,
    };
    return parameter.AccessPartyId;
  },
};
