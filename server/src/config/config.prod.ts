// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export default () => ({
  consul: {
    host: '127.0.0.1',
    port: 2280,
  },
  trace: {
    enable: true, // enable trace ability, `false` defaultly
  },
  nemo: {
    // http://nemo.boe.bytedance.net/app/28prq88rsnjg/process
    // https://nemo.bytedance.net/
    features: {
      disablePatch: true, // disable patch of http and net module, it won't record metrics's data and make less influence in app
    },
    enable: true,
  },
});
