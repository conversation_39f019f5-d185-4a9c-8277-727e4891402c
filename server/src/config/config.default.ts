import { HttpApplication } from '@gulu/application-http';
import { getClientIP } from '../utils/getClientIp';
import ferryConfig from '../ferry.config';

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export default (app: HttpApplication) => ({
  // needed only when developing locally, plugins like ms-logger will report by container's psm in prod mode
  psm: 'ies.fe.route_manage_i18n', // use your own psm to replace
  plugin: [
    '@ies/gulu-stability-metrics',
    '@byted/ferry-node-gulu',
    '@gulu/onerror',
    '@gulu/runtime-base',
    '@gulu/content-unify-login',
    '@byted/unified_workbench_auth',
    '@gulu/fetch',
    '@gulu/views',
    '@gulu/static',
    '@gulu/nemo',
    '@gulu/rpc',
    '@gulu/tq-log',
  ],
  middleware: ['error', 'validator', 'cors'],
  fetch: {
    defaultTimeout: 120000,
  },
  stabilityMetrics: {
    checkIsRequestSuccess: (statusCode): boolean => statusCode === 0 || statusCode === 10401, // 判断接口是否成功的方法，默认只当statusCode === 0为接口成功
    statusCodeKeys: ['code'], // 返回json中，错误码的key，默认为statusCode，支持多个，也支持嵌套如BaseResp.StatusCode
  },

  ferry: {
    idl: ferryConfig.idl,
  },

  tqLog: {
    source: 'kefu_route', // source
    psm: 'ies.fe.route_manage_i18n', // your psm
    handler: (ctx): Record<string, any> => {
      const { headers, href } = ctx.request;
      const { username, user_id: userId } = ctx.userInfo || {};
      const { referer = '' } = headers;
      const ip = getClientIP(ctx);
      return {
        source: 'kefu_route',
        psm: 'ies.fe.route_manage_i18n',
        module: 'route', // 业务模块，可以为空，用于业务自己标识
        user_name: username, // email prefix;required;lower case
        user_id: userId,
        referer,
        ip,
        url: href,
        // title: 页面标题,
      };
    },
  },
  views: {
    map: {
      html: 'nunjucks',
    },
  },
});
