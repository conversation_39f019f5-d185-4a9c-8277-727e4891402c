import { resolve } from 'path';
import { getEnv } from '../app/utils/index';

export default {
  idl: resolve(__dirname, '../idl'),
  dtsGen: {
    i64: 'string',
    rootDir: resolve(__dirname, '../idl'),
  },
  connectionOptions: {
    accessTimeout: 10 * 10000,
  },
  timeout: 10 * 10000,
  connectTimeout: 10 * 10000,
  readTimeout: 10 * 10000,
  writeTimeout: 10 * 10000,
  middleware: [
    async (ctx, next) => {
      const requestStr = `
        service: ${ctx.service}
        method: ${ctx.method}
        request: ${JSON.stringify(ctx.request)}
      `;
      ctx.refContext.logger.info(`
        Rpc Info requestStr：${requestStr}
      `);
      await next();

      const responseStr = `
        service: ${ctx.service}
        method: ${ctx.method}
        request: ${JSON.stringify(ctx.request)}
        response: ${JSON.stringify(ctx.response)}
      `;
      ctx.refContext.logger.info(`
        Rpc Info responseStr：${responseStr}
      `);
    },
  ],
  interpretersOptions: {
    mapAsObject: true,
    int64AsString: true,
  },
  ctxObject: {
    env: getEnv(), // [1] Global level service discovery control
  },
  services: {
    CateGoryService: {
      filename: 'rpc_idl/ies_kefu/ticket/ies.kefu.category.thrift',
      consul: 'ies.kefu.category',
    },
    AgentSkillGroupService: {
      filename: 'rpc_idl/ies_kefu/route/agent_skill_group_i18n.thrift',
      consul: 'ies.kefu.agent_skillgroup',
    },
    CustomerCloudService: {
      filename: 'rpc_idl/helpdesk/customer_cloud.thrift',
      consul: 'ies.helpdesk.im',
    },
    AdminService: {
      filename: 'rpc_idl/ies_kefu/lego/ieslego.admin_i18n.thrift',
      consul: 'ies.kefu.lego_admin',
      thriftOptions: {
        timeout: 10000 * 10 * 60,
      },
    },
    TqNotifierService: {
      filename: 'rpc_idl/security/tq_notify/security_tq_notify.thrift',
      consul: 'security.tq.notify',
    },
    DataCenterService: {
      filename: 'rpc_idl/ies_kefu/ies.kefu.datacenter.thrift',
      consul: 'ies.kefu.datacenter',
    },
    DataMarketService: {
      filename: 'rpc_idl/ies_kefu/ies.kefu.datamarket.thrift',
      consul: 'ies.kefu.datamarket',
    },
  },
};
