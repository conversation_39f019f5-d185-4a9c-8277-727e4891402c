import { treeNodeData } from './index';
interface CacheData<T> {
  requestedAt: number;
  value: T;
  timer: NodeJS.Timeout;
}
export class CachedRequester<T> {
  key: string;
  /**
   * 过期毫秒数
   */
  expire: number;
  cacheMap: Map<string, CacheData<T>>;

  /**
   * @param key 标识
   * @param expire 缓存过期时间
   * @notice 不会减少真实请求
   *
   * 一个请求缓存器
   * 用于缓存不常变化而又时延较长的请求来提升性能
   *
   * 策略：
   *  1. 以请求参数为 key，获取缓存，
   *    - 如果获取到，则返回缓存，并执行 2
   *    - 如果无缓存，执行 2 并返回 2 返回的新请求值
   *  2. 发起请求，得到返回值后：
   *    - 更新缓存、设置定时器，expire 后删除缓存
   *    - 返回新值
   */
  constructor(key: string, expire?: number) {
    this.key = key;
    this.expire = expire || 1000 * 60 * 3;
    this.cacheMap = new Map();
  }

  /**
   *
   * @param params 参数的字符串表示，用作缓存 key，不同参数要确保值不一样
   * @param req 没有缓存时的真实获取数据的请求
   * @returns 缓存值或
   * 
   */
  getValue(params: string, req: Promise<T>): Promise<T> {
    const thisCache = this.cacheMap.get(params);
    const requestedAt = Date.now();
    const nextCache = req.then(res => {
      const value = treeNodeData(res);
      const maybeNewerCache = this.cacheMap.get(params);

      // 请求成功后，检查
      // 需要当前没有缓存，或者有缓存但是缓存请求的时间比本次请求的时间早，才去更新缓存
      // 每次更新缓存会触发一个定时器，expire 后删除该缓存
      // 但是如果更新的时候有旧的缓存，要清除就缓存的定时器
      if (!maybeNewerCache || maybeNewerCache.requestedAt < requestedAt) {
        // 如果上一个缓存还在，清除上一个缓存的 timer
        if (maybeNewerCache) {
          clearTimeout(maybeNewerCache.timer);
        }
        // expire 后删除本次缓存
        const timer = setTimeout(() => {
          this.cacheMap.delete(params);
        }, this.expire);

        this.cacheMap.set(params, {
          requestedAt,
          value,
          timer,
        });
      }
      return value;
    });
    if (thisCache) {
      return Promise.resolve(thisCache.value);
    } else {
      return nextCache;
    }
  }
}
