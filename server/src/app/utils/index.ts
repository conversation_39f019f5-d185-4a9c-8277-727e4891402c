import { idc, isProd } from '@byted-service/env';
import { v1 as uuidv1 } from 'uuid';
import { createHash } from 'crypto';
import { ruleOpcheckArr } from '../constants/index';
export function currentRegion(): 'CN' | 'SG' | 'TTP' | 'VA' | 'GCP' | 'BOE' | 'BOEI18N' {
  if (/(lf|hl|lq)/.test(idc.toLocaleLowerCase())) {
    return 'CN';
  }
  if (idc.includes('sg')) {
    return 'SG';
  }
  if (/(useast3|va)/.test(idc)) {
    return 'VA';
  }
  if (idc.toLowerCase().includes('useast5')) {
    return 'TTP';
  }
  if (idc.toUpperCase().includes('USEAST2A')) {
    return 'GCP';
  }
  if (idc.toLowerCase().includes('boei18n')) {
    return 'BOEI18N';
  }
  if (idc.toLowerCase().includes('boe')) {
    return 'BOE';
  }
  return 'BOE';
}
export function isVA(): boolean {
  return currentRegion() === 'VA';
}
export function isSG(): boolean {
  return currentRegion() === 'SG';
}
export function isCN(): boolean {
  return currentRegion() === 'CN';
}
export function isGCP(): boolean {
  return idc === 'useast2a';
}
export function isTTP(): boolean {
  return currentRegion() === 'TTP';
}
export function isBoe(): boolean {
  return currentRegion() === 'BOE';
}
export function isBoeI18n(): boolean {
  return currentRegion() === 'BOEI18N';
}

export function getEnv(): string {
  if (isProd()) {
    return 'prod';
  }
  if (isBoe()) {
    return 'boe';
  }
  return 'dev';
}

// CasDomain SSO域名
export function CasDomain() {
  if (isProd()) {
    return 'https://sso.bytedance.com/cas';
  }
  return 'http://test-sso.bytedance.net/cas';
}

// Domain 平台本地域名
export function Domain() {
  if (isProd()) {
    return 'https://helpdesk.bytedance.net';
  }
  return 'http://boe-helpdesk.bytedance.net';
}

// AddMaskOnPhone 给手机号打码
export function AddMaskOnPhone(phone) {
  if (phone.length !== 11) {
    return phone;
  }

  const arr = phone.split('');
  arr[3] = '*';
  arr[4] = '*';
  arr[5] = '*';
  arr[6] = '*';
  return arr.join('');
}

export const encrypt = (algorithm, content) => {
  const hash = createHash(algorithm);
  hash.update(content);
  return hash.digest('hex');
};

export const sha1 = content => encrypt('sha1', content);

export const md5 = content => encrypt('md5', content);

// CreateHttpOpadminApiHeader
export function CreateHttpOpadminApiHeader(accessKey, accessSecret) {
  const timeStampStr = `${Date.parse(Date()) / 1000}`;
  const nonce = uuidv1().split('-').join('');
  const values = [accessSecret, timeStampStr, nonce].sort();
  const signature = sha1(values.join(''));
  const header = {
    'X-AccessKey': accessKey,
    'X-Timestamp': timeStampStr,
    'X-Signature': signature,
    'X-Nonce': nonce,
  };
  return header;
}

// CreateHttpBasicAuthHeader
export function CreateHttpBasicAuthHeader(appId, appSecret) {
  const encodeString = Buffer.from(`${appId}:${appSecret}`).toString('base64');
  const header = {
    Accept: 'application/json',
    Authorization: `Basic ${encodeString}`,
  };
  return header;
}

const nameTransformToUpperCase = name => name.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());

const nameTransformFirstToUpperCase = name => name.replace(/^([a-z])/g, (match, p1) => p1.toUpperCase());

export function FormatParameter(Parameter) {
  const res = {};
  Object.entries(Parameter).forEach(([key, val]) => {
    const formatKey = nameTransformToUpperCase(nameTransformFirstToUpperCase(key));
    res[formatKey] = val;
  });
  return res;
}
export function treeNodeData(data) {
  const FieldValueList12 = [];
  let treeDataList = [];
  let index = null;
  let OperatorIds = [];
  const allTicketCategoryList = [];
  const dataMap = {};
  data.forEach((val, idx) => {
    if (val.FieldName === 'ticket_category.full_category_ids') {
      OperatorIds = val.OperatorIds;
      const FieldValueList = val.Fieldvalues[val.OperatorIds[0]].FieldValueList || [];
      // eslint-disable-next-line @typescript-eslint/no-shadow
      FieldValueList.forEach(val => {
        if (!dataMap[val.value]) {
          dataMap[val.value] = true;
          FieldValueList12.push(val);
        }
      });
      index = idx;
    }
  });
  if (!FieldValueList12.length) {
    return data;
  }
  const transformData = (treeData, parentId) => {
    let children = [];
    treeData.value = parentId ? `${parentId},${treeData.value}` : treeData.value;
    if (!treeData.children) {
      children = [];
    } else if (Array.isArray(treeData.children)) {
      children = treeData.children.map(val => transformData(val, treeData.value));
    } else if (Array.isArray(treeData.children.children)) {
      children = treeData.children.children.map(val => transformData(val, treeData.value));
    } else {
      children = [];
    }
    const arr = treeData.value.split(',');
    if (arr.length > 2) {
      arr.splice(0, 2);
      allTicketCategoryList.push(arr);
    }
    return {
      value: arr.join(),
      name: treeData.name,
      children,
    };
  };
  treeDataList = FieldValueList12.map(val => transformData(val, null));
  OperatorIds.map(item => {
    data[index].allTicketCategoryList = allTicketCategoryList;
    data[index].Fieldvalues[item].FieldValueList = treeDataList;
  });
  return [...data];
}

export function treeNodeDataMap(data) {
  const listMap = {};
  data.forEach((val, idx) => {
    const dataMap = {};
    const fieldValueList = val.Fieldvalues[val.OperatorIds[0]].FieldValueList || [];

    const transformData = treeData => {
      let children = [];
      if (!treeData.children) {
        children = [];
      } else if (Array.isArray(treeData.children)) {
        children = treeData.children.map(item => transformData(item));
      } else if (Array.isArray(treeData.children.children)) {
        children = treeData.children.children.map(item => transformData(item));
      } else {
        children = [];
      }
      dataMap[treeData.value] = treeData.name;
      return {
        value: treeData.value,
        name: treeData.name,
        children,
      };
    };
    fieldValueList.forEach((field, index) => {
      transformData(field);
    });

    if (!listMap[val.FieldName]) {
      listMap[val.FieldName] = {
        FieldName: val.FieldName,
        FieldDisplayName: val.FieldDisplayName,
        dataMap,
      };
    }
  });

  return listMap;
}

export function getItemLog(GroupChangeList, fieldListMap, OperateItemType, AfterValue) {
  const changItem = {
    ConditionsChange: [],
    ValueChange: [],
    ConditionRelationChange: [],
    groupsRelationChange: [],
    retutnValue: [],
  };
  if (![4, 5, 6].includes(OperateItemType)) {
    return changItem;
  }
  GroupChangeList?.forEach(itemGroup => {
    itemGroup?.ConditionChangeList?.forEach(itemCondition => {
      const conditionName = fieldListMap[itemCondition?.ConditionName]?.FieldDisplayName || '';
      const AddOptions =
        itemCondition?.AddOptions?.map(
          ele => fieldListMap[itemCondition.ConditionName]?.dataMap[ele?.substring(1, ele?.length - 1)]
        ) || [];
      const DeleteOptions =
        itemCondition?.DeleteOptions?.map(
          ele => fieldListMap[itemCondition.ConditionName]?.dataMap[ele?.substring(1, ele?.length - 1)]
        ) || [];
      if (itemCondition?.Operator) {
        const operator = ruleOpcheckArr.find(val => val.key === itemCondition?.Operator);
        changItem.ValueChange.push({
          groupIndex: itemGroup?.ConditionGroupOrder,
          conditionName,
          updateValueType: 'operator',
          options: operator?.value || itemCondition?.Operator,
        });
      }
      if (itemCondition?.ConditionUpdateType === 1) {
        if (AddOptions.length) {
          changItem.ValueChange.push({
            groupIndex: itemGroup?.ConditionGroupOrder,
            conditionName,
            updateValueType: 'increaseValue',
            options: AddOptions?.join('、'),
          });
        }
        if (DeleteOptions.length) {
          changItem.ValueChange.push({
            groupIndex: itemGroup?.ConditionGroupOrder,
            conditionName,
            updateValueType: 'deleteValue',
            options: DeleteOptions?.join('、'),
          });
        }
      } else if (itemCondition?.ConditionUpdateType === 2) {
        changItem.ConditionsChange.push({
          groupIndex: itemGroup?.ConditionGroupOrder,
          conditionName,
          updateValueType: '',
          options: DeleteOptions?.join('、'),
          updateConditionsType: 'deleteCondition',
        });
      } else {
        changItem.ConditionsChange.push({
          groupIndex: itemGroup?.ConditionGroupOrder,
          conditionName,
          updateValueType: 'increaseValue',
          options: AddOptions?.join('、'),
          updateConditionsType: 'increaseCondition',
        });
      }
    });
    if (OperateItemType === 6) {
      let ConditionRelationText = itemGroup?.ConditionNameList?.map(
        val => `【 ${fieldListMap[val].FieldDisplayName} 】`
      ).join(` ${itemGroup?.Relation} `);
      if (itemGroup?.ConditionNameList?.length === 1) {
        ConditionRelationText = `${ConditionRelationText}${itemGroup?.Relation}`;
      }
      changItem.ConditionRelationChange.push({
        groupIndex: itemGroup?.ConditionGroupOrder,
        conditionName: ConditionRelationText || '',
      });
    }
  });
  return changItem;
}

export const safeJSONParse = (v: unknown, defaultValue?: any): any => {
  if (v && typeof v === 'string') {
    try {
      return JSON.parse(v);
    } catch (error) {
      return defaultValue ?? null;
    }
  }
  return defaultValue ?? null;
};
