export default () => async (ctx, next): Promise<void> => {
  // 设置dev-server headers
  const devServerConifg = {
    headers: {
      'Access-Control-Allow-Credentials': true,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'X-Requested-With',
      'Access-Control-Allow-Methods': 'PUT,POST,GET,DELETE,OPTIONS',
    },
  };
  for (const key of Object.keys(devServerConifg.headers)) {
    ctx.res.setHeader(key, devServerConifg.headers[key]);
  }
  await next();
};
