import * as constants from '../constants';

export default () => async (ctx, next): Promise<void> => {
  const parameter = ctx.utils.FormatParameter({
    ...ctx.request.body,
    ...ctx.request.query,
    ...ctx.params,
  });

  try {
    const agentInfo = (await ctx.service.agentSkillGroup.getAgentByUUID()) || {};
    parameter.agentInfo = agentInfo;
  } catch (error) {
    ctx.logger.error(`
      GetAgentByUUID Error Catch, error: ${error}
    `);
  }

  ctx.state[constants.RequestParameter] = parameter;

  await next();
};
