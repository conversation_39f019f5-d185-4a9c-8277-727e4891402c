import * as constants from '../constants';

export default () => async (ctx, next): Promise<void> => {
  try {
    const commonStr = `
      id: ${Date.now()}
      url: ${ctx.request.url}
      query: ${JSON.stringify(ctx.request.query, null, 4)}
      body: ${JSON.stringify(ctx.request.body, null, 4)}
      userInfo: ${JSON.stringify(ctx.userInfo, null, 4)}
    `;
    ctx.logger.info(`
      Request Params：${commonStr}
    `);
    ctx.logger.info(`userInfo: ${JSON.stringify(ctx.userInfo, null, 4)}`);
    await next();
    if (ctx.body) {
      ctx.logger.info(`
        Response body：${JSON.stringify(ctx.body, null, 4)}
      `);
    }
  } catch (error) {
    const { message, stack } = error;
    ctx.logger.error(`
      Global Error Catch, error: ${error}
    `);
    ctx.errorResponse(constants.ErrorCodeUnknow, {
      statusMsg:
        ctx.utils.getEnv() === 'prod' ?
          message :
          `
        message: ${message}
        stack: ${stack}
        env: ${ctx.utils.getEnv()}
      `,
    });
  }
};
