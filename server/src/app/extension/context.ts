import * as utils from '../utils';
import * as constants from '../constants';

interface Response {
  code: number;
  message: string;
  data: Record<string, any>;
}

export default () => ({
  errorResponse(errorCode, data = {}): void {
    const [httpCode, errorMessage] = constants.DisplayErrorCode(errorCode);
    this.status = httpCode;
    this.body = {
      code: errorCode,
      message: errorMessage,
      data,
    };
  },

  successResponse(data): void {
    this.errorResponse(constants.SuccessCode, data);
  },

  idlResponse(errorCode, data = {}): Response {
    const [, errorMessage] = constants.DisplayErrorCode(errorCode);
    return {
      code: errorCode,
      message: errorMessage,
      data,
    };
  },

  get utils(): typeof utils {
    return utils;
  },
});
