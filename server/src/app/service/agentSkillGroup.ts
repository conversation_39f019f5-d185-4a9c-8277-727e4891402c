import { Service } from '@gulu/application-http';
import * as constants from '../constants';

export default class AgentSkillGroupService extends Service {
  async getAgentByUUID() {
    const res = (
      await this.ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
        TenantId: '1',
        UUID: this.ctx.userInfo.uuid,
      })
    ).Agent;

    return res;
  }

  async getSkillGroupsByAccessPartyIds(AccessPartyIds: string[], channelType?: number) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AgentSkillGroupService.GetSkillGroupsByAccessParties({
      AccessPartyIds, // 接入方ID
      TenantId: req.agentInfo.TenantId, // 租户id
      ChannelType: channelType,
    });

    return res.SkillGroups || [];
  }

  async getCard(appIds: string[], AccessPartyId?: string) {
    const req = this.ctx.state[constants.RequestParameter];
    // let AppIds = [];
    // if (!Array.isArray(appIds)) {
    //   AppIds = [];
    // } else {
    //   AppIds = appIds;
    // }
    const res = await this.ctx.rpc.AgentSkillGroupService.getCard({
      TenantId: req.agentInfo.TenantId, // 租户id
      AppIds: appIds || [], // 入口ID
      // AccessPartyId,
    });

    return res.AppQuestionCards || [];
  }

  async createCard({ AddAppQuestionCard, AccessPartyId }) {
    const req = this.ctx.state[constants.RequestParameter];
    const res = await this.ctx.rpc.AgentSkillGroupService.createCard({
      AgentId: req.agentInfo.ID, // 客服Id
      AddAppQuestionCard: Object.assign({}, AddAppQuestionCard, {
        TenantId: req.agentInfo.TenantId, // 租户id
        AccessPartyId, // 接入方ID
      }), // 不带主键ID的问题卡片对象
    });

    return res;
  }

  async updateCard({ UpdateAppQuestionCardThrift }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AgentSkillGroupService.updateCard({
      AgentId: req.agentInfo.ID, // 客服Id
      UpdateAppQuestionCardThrift, // 带主键ID的问题卡片对象
    });

    return res;
  }

  async handleCard({ CardId, HandleType }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AgentSkillGroupService.handleCard({
      AgentId: req.agentInfo.ID, // 客服Id
      CardId, // 卡片ID
      HandleType, // 操作类型 OPEN = 1 CLOSE = 2 DELETE = 3
    });

    return res;
  }
  async checkSkillGroupAutoAssign({ skillGroupId }) {
    const req = this.ctx.state[constants.RequestParameter];
    try {
      const res = await this.ctx.rpc.AgentSkillGroupService.CheckSkillGroupAutoAssign({
        TenantId: req.agentInfo.TenantId, // 租户id
        SkillGroupId: skillGroupId,
      });
      return { ...res, id: skillGroupId };
    } catch (error) {
      return null;
    }
  }
}
