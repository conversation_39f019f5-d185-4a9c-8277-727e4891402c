import { Service } from '@gulu/application-http';

export default class CustomerCloudService extends Service {
  async queryBizTypesByAccessPartyID(AccessPartyID) {
    const method = [7, 10, 11].includes(Number(AccessPartyID)) ?
      'QueryBizTypesByAccessPartyIDNew' :
      'QueryBizTypesByAccessPartyID';
    const res = await this.ctx.rpc.CustomerCloudService[method]({
      AccessPartyID,
    });
    this.ctx.logger.info(`queryBizTypesByAccessPartyID method ${method} res ${JSON.stringify(res, null, 4)}`);
    return res.BizTypeList || [];
  }
}
