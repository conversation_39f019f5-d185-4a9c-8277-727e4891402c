import { Service } from '@gulu/application-http';
import * as constants from '../constants';

export default class CateGoryService extends Service {
  async getAppList() {
    const req = this.ctx.state[constants.RequestParameter];
    const res = await this.ctx.rpc.CateGoryService.GetAppList({
      CommonRequest: {
        TenantId: req.agentInfo.TenantId, // 租户id
        AgentId: req.agentInfo.ID, // 客服Id
        AgentName: req.agentInfo.UserName, // 客服名称
        CountryCode: 'CN', // 国家码
      },
      Name: '',
    });

    return res.AppList;
  }

  async getResourceList(AccessPartyId) {
    const req = this.ctx.state[constants.RequestParameter];
    const res = await this.ctx.rpc.CateGoryService.GetResourceList({
      CommonRequest: {
        TenantId: req.agentInfo.TenantId, // 租户id
        AgentId: req.agentInfo.ID, // 客服Id
        AgentName: req.agentInfo.UserName, // 客服名称
        CountryCode: 'CN', // 国家码
      },
      AccessPartyId, // 接入方ID
    });

    return res.ResourceList;
  }

  async getCategoryList() {
    const req = this.ctx.state[constants.RequestParameter];
    const res = await this.ctx.rpc.CateGoryService.GetCategoryList({
      CommonRequest: {
        TenantId: req.agentInfo.TenantId, // 租户id
        AgentId: req.agentInfo.ID, // 客服Id
        AgentName: req.agentInfo.UserName, // 客服名称
        CountryCode: 'CN', // 国家码
      },
      ResourceId: req.ResourceId,
      SubResourceId: req.SubResourceId,
    });

    return res.CategoryList;
  }
}
