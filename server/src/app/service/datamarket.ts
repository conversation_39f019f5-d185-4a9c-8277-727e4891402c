import { Service } from '@gulu/application-http';
import * as constants from '../constants';

export default class DataMarketService extends Service {
  async getDataMarketData(query, variables) {
    try {
      const token = '';
      const res = await this.ctx.rpc.DataMarketService.Query({
        Query: query,
        Variables: variables,
        Psm: constants.RouteManagePsm,
        Token: token,
      });
      this.ctx.logger.info(`
        getDataMarketData: ${JSON.stringify({ query, variables, res }, null, 4)}
      `);
      return JSON.parse(res.Data);
    } catch (error) {
      this.ctx.logger.error(`
        DataMarketService.Query Error Catch, error: ${JSON.stringify({ query, variables, error }, null, 4)}
      `);
    }
  }
}
