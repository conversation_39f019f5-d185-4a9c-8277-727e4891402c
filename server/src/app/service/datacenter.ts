import { Service } from '@gulu/application-http';
import * as constants from '../constants';

// @dev 请求问题分类
// const query = 'query listCategoryByParam($req:GetCateRequest){Data:listCategoryByParam(request:$req){DataS:nodes{name,value:cateId,level,children{name,value:cateId,level,children{name,value:cateId,level}}}}}'
// const variables = '{"req": {"parent":0,"IssueCateKind":1}}'
// console.log(query, variables)

export default class DatacenterService extends Service {
  async getDatacenterData(query, variables) {
    try {
      const token = this.ctx.utils.md5(constants.RouteManagePsm + constants.DatacenterSign + query + variables);
      const res = await this.ctx.rpc.DataCenterService.Query({
        Query: query,
        Variables: variables,
        Psm: constants.RouteManagePsm,
        Token: token,
      });
      this.ctx.logger.info(`
        getDatacenterData: ${JSON.stringify({ query, variables, res }, null, 4)}
      `);
      return JSON.parse(res.Data);
    } catch (error) {
      this.ctx.logger.error(`
        DataCenterService.Query Error Catch, error: ${JSON.stringify({ query, variables, error }, null, 4)}
      `);
    }
  }
}
