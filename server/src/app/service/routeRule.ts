import { Service } from '@gulu/application-http';
import * as constants from '../constants';

interface DataItem {
  name: string;
  value: string;
  children?: DataItem[];
}

interface Data {
  Data?: Data | Data[];
  DataS?: DataItem[];
}

function treeToArray(tree) {
  const newTree = Array.isArray(tree) ? JSON.parse(JSON.stringify(tree)) : JSON.parse(JSON.stringify([tree]));
  const optionList = newTree.reduce((res, item) => {
    const { children, ...i } = item;
    return res.concat(i, children && children.length ? treeToArray(children) : []);
  }, []);
  return optionList;
}
const adjustDatacenterData = (data: Data | Data[]): DataItem[] => {
  if (!data) {
    return [];
  }

  // [...]
  if (Array.isArray(data)) {
    return data.map(adjustDatacenterData).reduce((result, items) => result.concat(items), []);
  }

  // { DataS: [...] }
  if (Object.prototype.hasOwnProperty.call(data, 'DataS')) {
    return data.DataS;
  }

  // { Data: {...} }、{ Data: [...] }
  if (Object.prototype.hasOwnProperty.call(data, 'Data')) {
    return adjustDatacenterData(data.Data);
  }

  return [];
};

const adjustDataMarketMultipleData = (data: Data | Data[]): DataItem[] => {
  if (!data) {
    return [];
  }

  if (Object.prototype.hasOwnProperty.call(data, 'Data2')) {
    return adjustDatacenterData(Object.entries(data).map(([_, value]) => value));
  }

  // [...]
  if (Array.isArray(data)) {
    return data.map(adjustDatacenterData).reduce((result, items) => result.concat(items), []);
  }

  // { DataS: [...] }
  if (Object.prototype.hasOwnProperty.call(data, 'DataS')) {
    return data.DataS;
  }

  // { Data: {...} }、{ Data: [...] }
  if (Object.prototype.hasOwnProperty.call(data, 'Data')) {
    return adjustDatacenterData(data.Data);
  }

  return [];
};

export default class RouteRuleService extends Service {
  async getFieldList({ AccessPartyId, EventId, AppIds = [], lang = 'en' }) {
    const { FieldConditions } = await this.ctx.rpc.AdminService.GetFieldList({
      EventId,
      AccessPartyId,
      lang
    });

    const promises: Promise<void>[] = [];

    const setOperatorValues = async (
      fieldCondition,
      options: {
        FieldId: string;
        OperatorId: string;
        AccessPartyId: string;
        AppIds: string[];
        Operator: string;
      }
    ) => {
      try {
        const fieldValues = await this.ctx.service.routeRule.getFieldValues(options);
        fieldCondition.Fieldvalues[options.OperatorId] = fieldValues;
        fieldCondition.OperatorFieldvalues[options.Operator] = fieldValues;
      } catch (error) {
        this.ctx.logger.error(`
          routeRule.getFieldValues Error Catch, error: ${error}
        `);
      }
    };

    for (let i = 0; i < FieldConditions.length; i++) {
      const FieldCondition = FieldConditions[i] as Record<string, any>;
      FieldCondition.Fieldvalues = {};
      FieldCondition.OperatorFieldvalues = {};
      if (!FieldCondition.OperatorIds?.length && FieldCondition?.OperatorList?.length) {
        FieldCondition?.OperatorList?.forEach(v => {
          const ruleOpcheck = constants?.ruleOpcheckArr?.find(ele => ele.key === v);
          FieldCondition.OperatorIds?.push(ruleOpcheck?.type);
        });
      }
      if (FieldCondition.OperatorIds?.length) {
        for (let j = 0; j < FieldCondition.OperatorIds.length; j++) {
          promises.push(
            setOperatorValues(FieldCondition, {
              FieldId: FieldCondition.FieldId,
              OperatorId: FieldCondition.OperatorIds[j],
              Operator: FieldCondition.OperatorList ? FieldCondition.OperatorList[j] : undefined,
              AccessPartyId,
              AppIds,
            })
          );
        }
      }
      // for (let j = 0; j < FieldCondition.OperatorIds.length; j++) {
      //   promises.push(
      //     setOperatorValues(FieldCondition, {
      //       FieldId: FieldCondition.FieldId,
      //       OperatorId: FieldCondition.OperatorIds[j],
      //       Operator: FieldCondition.OperatorList ? FieldCondition.OperatorList[j] : undefined,
      //       AccessPartyId,
      //       AppIds,
      //     })
      //   );
      // }
    }

    await Promise.all(promises);
    return FieldConditions;
  }

  async getFieldValues({ FieldId, OperatorId, AccessPartyId, AppIds = [], Operator = '' }) {
    const req = this.ctx.state[constants.RequestParameter];
    let FieldValueType = null;
    let FieldValues = null;
    const query = {
      FieldId,
      OperatorId: Number(OperatorId),
    } as any;
    if (Operator) {
      query.Operator = Operator;
    }
    try {
      const reqs = await this.ctx.rpc.AdminService.GetFieldValues(query);
      FieldValueType = reqs.FieldValueType;
      FieldValues = reqs.FieldValues;
    } catch (error) {
      console.log(error, query, 'error');
    }
    if (!FieldValueType || !FieldValues) {
      return {
        FieldValueType,
        FieldValueList: [],
        FieldValues,
      };
    }

    const jsonData = JSON.parse(FieldValues);
    let FieldValueList;
    this.ctx.logger.info(`getFieldValues datacenter jsonData ${JSON.stringify(jsonData, null, 4)}`);
    const varObj = {
      TenantId: req.agentInfo.TenantId,
      AppIds,
      AccessPartyId,
    };
    switch (jsonData.format) {
      case 'access_party':
        FieldValueList = jsonData.data[AccessPartyId] || [];

        break;
      case 'normal':
        FieldValueList = jsonData?.data?.default ? jsonData?.data?.default : jsonData?.data || [];
        break;
      case 'date_center':
        let variables = JSON.stringify(jsonData.variables)
          // .replace('"$AppIds"', JSON.stringify(varObj?.AppIds || []))
          .replace('"$TenantId"', JSON.stringify(varObj.TenantId))
          .replace('"$AccessPartyId"', JSON.stringify(varObj.AccessPartyId));

        variables = `{\"req\":${variables}}`;
        this.ctx.logger.info(`getFieldValues datacenter variables ${JSON.stringify(variables, null, 4)}`);
        const dataCenterData = await this.ctx.service.datacenter.getDatacenterData(jsonData.query, variables);
        this.ctx.logger.info(`getFieldValues datacenter data ${JSON.stringify(dataCenterData, null, 4)}`);
        FieldValueList = adjustDatacenterData(dataCenterData || {});
        break;
      case 'data_market':
        let dataMarketVariables = JSON.stringify(jsonData.variables)
          // .replace('"$AppIds"', JSON.stringify(varObj.AppIds))
          .replace('"$TenantId"', JSON.stringify(varObj.TenantId))
          .replace('"$AccessPartyId"', JSON.stringify(varObj.AccessPartyId));

        dataMarketVariables = `{\"req\":${dataMarketVariables}}`;
        this.ctx.logger.info(`getFieldValues datamarket variables ${JSON.stringify(dataMarketVariables, null, 4)}`);
        const dataMarketData = await this.ctx.service.datamarket.getDataMarketData(jsonData.query, dataMarketVariables);
        this.ctx.logger.info(`getFieldValues datamarket data ${JSON.stringify(dataMarketData, null, 4)}`);
        if (FieldValueType === 701) {
          FieldValueList = treeToArray(adjustDatacenterData(dataMarketData || {}));
        } else {
          FieldValueList = adjustDatacenterData(dataMarketData || {});
        }
        // console.log(FieldValueList, '22222 ==== FieldValueList');
        break;
      case 'data_market_multiple':
        const dataMarketCategoryVariables = JSON.stringify(jsonData.variables)
          // .replace('"$AppIds"', JSON.stringify(varObj.AppIds))
          .replace('"$TenantId"', JSON.stringify(varObj.TenantId))
          .replace('"$AccessPartyId"', JSON.stringify(varObj.AccessPartyId));

        this.ctx.logger.info(
          `getFieldValues datamarket multiple variables ${JSON.stringify(dataMarketCategoryVariables, null, 4)}`
        );
        const dataMarketCategoryData = await this.ctx.service.datamarket.getDataMarketData(
          jsonData.query,
          dataMarketCategoryVariables
        );
        this.ctx.logger.info(
          `getFieldValues datamarket multiple data ${JSON.stringify(dataMarketCategoryData, null, 4)}`
        );
        FieldValueList = adjustDataMarketMultipleData(dataMarketCategoryData || {});
        break;
      default:
        FieldValueList = [];
    }

    return {
      FieldValueType,
      FieldValueList,
      FieldValues,
    };
  }

  async getAdminRuleList({ AccessPartyId, DisplayNameLike, EventId, SourceId, AppId, StopStatus }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res =
      (
        await this.ctx.rpc.AdminService.GetAdminRuleList({
          AppId,
          SourceId,
          EventId,
          Status: 1,
          StopStatus,
          TenantId: req.agentInfo.TenantId, // 租户id
          AccessPartyId,
          DisplayNameLike,
        })
      ).AdminRuleList || [];

    try {
      const { Agents } = await this.ctx.rpc.AgentSkillGroupService.GetAgentListByIDs({
        TenantId: '1',
        IDs: res.map(k => k.UpdaterAgentId),
      });

      res.forEach(o => {
        const agent = (Agents || []).find(k => k.ID === o.UpdaterAgentId);
        o.UpdaterAgentName = agent ? agent.UserName : '';
        return o;
      });
    } catch (error) {
      this.ctx.logger.error(`
        AgentSkillGroupService.GetAgentListByIDs Error Catch, error: ${error}
      `);
    }

    return res;
  }

  async createAdminRule({
    AccessPartyId,
    DisplayName,
    Priority,
    Filter,
    Value,
    EventId,
    SourceId,
    AppId,
    CreateCardRequest,
    Disable,
  }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AdminService.CreateAdminRule({
      AppId,
      SourceId,
      Extra: '',
      EventId,
      CreatorAgentId: req.agentInfo.ID,
      TenantId: req.agentInfo.TenantId, // 租户id
      AccessPartyId,
      DisplayName,
      Priority,
      Filter,
      Value,
      Disable,
      CreateCardRequest: CreateCardRequest ?
        {
          AgentId: req.agentInfo.ID,
          AddAppQuestionCard: {
            ...CreateCardRequest.AddAppQuestionCard,
            TenantId: req.agentInfo.TenantId,
            AccessPartyId,
            CardDisplayName: DisplayName,
          },
        } :
        undefined,
    });

    return res;
  }

  async updateAdminRule({
    AccessPartyId,
    Id,
    DisplayName,
    Priority,
    Filter,
    Value,
    EventId,
    SourceId,
    AppId,
    StopStatus,
    UpdateCardRequest,
  }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AdminService.UpdateAdminRule({
      Id,
      AppId,
      SourceId,
      Extra: '',
      EventId,
      Status: 1,
      StopStatus,
      UpdaterAgentId: req.agentInfo.ID,
      TenantId: req.agentInfo.TenantId, // 租户id
      AccessPartyId,
      DisplayName,
      Priority,
      Filter,
      Value,
      UpdateCardRequest: UpdateCardRequest ?
        {
          AgentId: req.agentInfo.ID,
          UpdateAppQuestionCardThrift: UpdateCardRequest.UpdateAppQuestionCardThrift,
        } :
        undefined,
    });

    return res;
  }

  async deleteAdminRule({ AccessPartyId, Id, SourceId, AppId }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AdminService.DeleteAdminRule({
      Id,
      AppId,
      SourceId,
      UpdaterAgentId: req.agentInfo.ID,
      TenantId: req.agentInfo.TenantId, // 租户id
      AccessPartyId,
    });

    return res;
  }

  async batchUpdateAdminRule({ AccessPartyId, AdminRules, EventId, SourceId, AppId }) {
    const req = this.ctx.state[constants.RequestParameter];

    const res = await this.ctx.rpc.AdminService.BatchUpdateAdminRule({
      AdminRules,
      UpdaterAgentId: req.agentInfo.ID,
      TenantId: req.agentInfo.TenantId, // 租户id
      AppId,
      SourceId,
      EventId,
      AccessPartyId,
    });

    return res;
  }

  async getSLAAimMetaSimpleList({ SourceId, AccessPartyId }: { SourceId: string; AccessPartyId: string }) {
    const req = this.ctx.state[constants.RequestParameter];

    const response = await this.ctx.rpc.AdminService.GetSLAAimMetaSimpleList({
      TenantId: req.agentInfo.TenantId,
      AccessPartyId,
      SourceId,
      Status: 1,
    });
    return response.SLAAimMetaSimpleList || [];
  }
  async getNewFieldList({ AccessPartyId, EventId, AppIds = [], zjOtherAccessPartyId = '' }) {
    const { FieldAndValues: FieldConditions } = await this.ctx.rpc.AdminService.GetFieldAndValueList({
      EventId,
      AccessPartyId,
      EventKey: 'DynamicAgentMaxTaskNum'
    });
    const promises: Promise<void>[] = [];
    this.ctx.logger.info('FieldConditions-----', FieldConditions);
    const setOperatorValues = async (
      fieldCondition,
      options: {
        FieldId: string;
        OperatorId: string;
        AccessPartyId: string;
        zjOtherAccessPartyId: string;
        AppIds: string[];
        Operator: string;
      }
    ) => {
      try {
        // await.x = 1;
        const fieldValues = await this.ctx.service.routeRule.getFieldValues(options);
        // fieldCondition.Fieldvalues[options.OperatorId] = fieldValues
        fieldCondition.OperatorFieldvalues[options.Operator] = fieldValues;
      } catch (error) {
        console.log('options----', options);
        this.ctx.logger.error(`
          routeRule.getFieldValues Error Catch, error: ${error}
        `);
      }
    };
    const getFiledOne = async index => {
      console.log('getFiledOne------', index);
      if (FieldConditions[index]) {
        const FieldCondition = FieldConditions[index] as Record<string, any>;
        // FieldCondition.Fieldvalues = {};
        FieldCondition.OperatorFieldvalues = {};
        for (let j = 0; j < FieldCondition.OperatorList.length; j++) {
          // console.log('FieldCondition.OperatorList---', FieldCondition.OperatorList);
          promises.push(
            setOperatorValues(FieldCondition, {
              FieldId: FieldCondition.FieldId,
              Operator: FieldCondition.OperatorList ? FieldCondition.OperatorList[j] : undefined,
              // OperatorId: FieldCondition.OperatorIds ? FieldCondition.OperatorIds[j] : '0',
              OperatorId: '0',
              AccessPartyId,
              zjOtherAccessPartyId,
              AppIds,
            })
          );
        }
        await Promise.all(promises);
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            resolve(1);
          }, 0);
        });
        await getFiledOne(index + 1);
      }
    };
    // await getFiledOne(0);
    // if (zjOtherAccessPartyId !== '') {
    //   console.log('进入质检逻辑');
    //   await getFiledOne(0);
    // } else {
    for (let i = 0; i < FieldConditions.length; i++) {
      const FieldCondition = FieldConditions[i] as Record<string, any>;
      // FieldCondition.Fieldvalues = {};
      FieldCondition.OperatorFieldvalues = {};
      const OperatorList = FieldCondition.OperatorList || [];
      // this.ctx.logger.info('OperatorList------', OperatorList);
      for (let j = 0; j < OperatorList.length; j++) {
        // console.log('FieldCondition.OperatorList---', FieldCondition.OperatorList);
        promises.push(
          setOperatorValues(FieldCondition, {
            FieldId: FieldCondition.FieldId,
            Operator: FieldCondition.OperatorList ? FieldCondition.OperatorList[j] : undefined,
            // OperatorId: FieldCondition.OperatorIds ? FieldCondition.OperatorIds[j] : '0',
            OperatorId: '0',
            AccessPartyId,
            zjOtherAccessPartyId,
            AppIds,
          })
        );
      }
    }
    await Promise.all(promises);
    // }
    // await getFiledOne(0);
    console.log('我已经返回回去了----');
    return FieldConditions as any;
  }
}
