import { Service } from '@gulu/application-http';
import env from '@byted-service/env';

const NotifIdConfig = {
  ruleEdit: {
    boe: 69,
    prod: 729,
  },
  ruleDelete: {
    boe: 70,
    prod: 731,
  },
  ruleCreate: {
    boe: 71,
    prod: 732,
  },
  ruleBatchUpdate: {
    boe: 72,
    prod: 733,
  },
} as const;

type NotifKey = keyof typeof NotifIdConfig;

function getNotifId(key: NotifKey) {
  if (env.isProd() || env.isPPE()) {
    return NotifIdConfig[key].prod;
  } else {
    return NotifIdConfig[key].boe;
  }
}

export default class TqNotifService extends Service {
  private get basicParams() {
    return {
      userName: this.userName,
      env: this.env,
    };
  }

  private get env() {
    if (env.isProd()) {
      return 'Prod';
    } else if (env.isPPE()) {
      return 'PPE';
    } else {
      return 'BOE';
    }
  }

  private get userName() {
    try {
      const { userInfo } = this.ctx;
      return `${userInfo.department.department_name} - ${userInfo.name}（${userInfo.username}:${userInfo.user_id}）`;
    } catch (error) {
      return '-';
    }
  }

  async notify(key: NotifKey, data: Record<string, string>) {
    const templateId = getNotifId(key);
    try {
      this.ctx.rpc.TqNotifierService.PostMessage({
        Message: {
          TemplateID: templateId,
          Content: JSON.stringify({
            ...this.basicParams,
            ...data,
          }),
        },
      });
      this.ctx.logger.info(
        JSON.stringify({
          message: 'tqNotif',
          data: {
            Message: {
              TemplateID: templateId,
              Content: JSON.stringify({
                ...this.basicParams,
                ...data,
              }),
            },
          },
        })
      );
    } catch (error) {
      console.log(error);
    }
  }
}
