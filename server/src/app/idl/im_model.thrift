namespace go im_model
namespace py im_model
namespace java com.bytedance.im.message

include "base.thrift"

// 消息属性
struct PropertyItem {
    1: optional i64 UserdId;
    2: optional string SecUid;
    3: optional i64 CreateTime ;
    4: optional string IdempotentId; //去重Id,相同Id的Item在PropertyItemList中只保留一份
    5: optional string Value ; // 业务方定制，IMCLOUD不理解内容 eg:表情点赞 value:uid
}

struct PropertyItemList {
    1: optional list<PropertyItem> items;
}


struct MessageBody{
    1: i32 ConversationType
    2: string ConversationId
    3: i64 ConversationShortId
    4: i64 ServerMessageId
    5: i32 MsgType
    6: string Content
    7: map<string,string> Ext
    8: i64 Version
    9: i32 Status
    10: i64 CreateTime
    11: i64 Sender
    12: optional binary Bitmap // Deprecated
    13: optional binary ExtraInfo
    14: optional i32 EncryptedType = 0;
    15: optional i32 appID;
    16: optional string SecSender;
    17: optional map<string,  PropertyItemList> properties;
    18: optional i32 BizAppID;
    19: optional map<string, string> UserProfile;
    20: optional i64 IndexInConversationV2 // 新版本的连续自增的index
}

enum EncryptedType {
    NO_ENCRYPT = 0;
    INNER_ENCRYPT = 1;
    OUTER_ENCRPYT = 2;
}

struct Message{
	1: i64 IndexInConversation // IndexInConversationV1
	2: MessageBody messageBody
	3: optional i64 IndexInUserInbox // IndexInUserInboxV1
    4: i64 IndexInConversationV2 // 优化后的单链index
    5: i64 IndexInUserV2 // 优化后的混链index
}


enum ConversationType {
	ONE_TO_ONE_CHAT = 1, 
	GROUP_CHAT = 2,
	LIVE_CHAT = 3,
	BROADCAST_CHAT = 4
}

enum GroupRole {
    ORDINARY = 0, // [群成员]
    OWNER = 1, // [群主]
    MANAGER = 2, // [群管理]
    VISITOR = 3, // [访客]
}


