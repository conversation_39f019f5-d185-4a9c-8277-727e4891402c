include "./base.thrift"

namespace go helpdesk.common.common
namespace py helpdesk.common.common

enum HelpdeskType {
    Artificial = 1,  // 人工客服
    Intelligent = 2, // 智能客服
    Feedback = 3,    // 用户反馈
}

struct HumanAgentSystemSetting {

}
struct ImOptionSetting {
    1: required string ApiUrl,
}
struct FrontierOptionSetting {
    1: required i32 Aid,
    2: required i32 Fpid,
    3: required i32 Service,
    4: required string AppKey,
}
struct AppSetting  {
    1: required string UserInfoURL,
    2: required HumanAgentSystemSetting HumanAgentSystem,
    3: required ImOptionSetting ImOption,
    4: required FrontierOptionSetting FrontierOptionSetting,
}

struct HelpdeskContext {
    1: required i32 AppID,
    2: required i64 TicketID,
    3: required i64 ConversationShortID,
    4: required i64 TaskID,
    5: optional i64 HelpdeskConversationLogID,
}