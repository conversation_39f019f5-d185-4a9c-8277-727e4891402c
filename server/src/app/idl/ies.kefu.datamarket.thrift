include "./base.thrift"

namespace py ies.kefu.datamarket
namespace go ies.kefu.datamarket

struct DataQueryRequest {
    1: required string Psm,
    2: required string Token,
    3: optional string Query,
    4: optional string QueryId,
    5: optional string Variables,
    255: optional base.Base Base,
}

struct DataQueryResponse {
    1: optional string Data,
    2: optional list<string> Errors,
    255: required base.BaseResp BaseResp,
}

struct WebQueryRequest {
    1: required string RequestString,
    2: optional string VariablesString,
    3: optional string OperationName,
    255: optional base.Base Base,
}

struct WebQueryResponse {
    1: optional string Data,
    255: required base.BaseResp BaseResp,
}


service DataMarketService {
    DataQueryResponse    Query        (1: DataQueryRequest        request)
    WebQueryResponse     WebQuery     (1: WebQueryRequest        request)
}
