include "./base.thrift"
include "./common.thrift"
include "./im_model.thrift"

namespace py ies.helpdesk.im
namespace go ies.helpdesk.im
namespace java ies.helpdesk.im

struct QueryTaskRequest {
    1: required i32 HostAppID,
    2: optional i32 Channel,
    3: optional i32 AppID,
    4: required i64 Uid,
    5: optional i64 StartTime,
    6: optional i64 EndTime,
    7: optional i32 Page,
    8: optional i32 PageSize,
    255: optional base.Base Base,
}

struct QueryTaskResponse {
    1: required i64 TotalCount,
    2: required list<Task> Tasks,
    255: optional base.BaseResp BaseResp,
}

struct QueryTaskMessageRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct QueryTaskMessageResponse {
    1: required list<im_model.Message> Messages,
    255: optional base.BaseResp BaseResp,
}

struct Task {
    1: required i64 TaskID,
    2: required i64 ConversationShortID,
    4: required i64 UID, //用户ID
    5: required string UniqueName, //客服名称
    6: required string Name, //客服昵称
    7: required i64 StartTime, //开始转人工时间
    8: required i64 EndTime, //结束时间，如果没有则是0
    9: required i64 DurationSeconds, // 人工进线咨询时长，如果未结束则为 0
    10: required string ChannelName, //队列名
    11: required i32 Status,//状态
    12: required i32 EndStatus, //结束方式
    13: required string UserName,  // 用户名
    14: required i32 AppID,  // 客服平台AppID
    15: required i32 Channel,  // 客服平台Channel
    16: optional i64 ParentTaskID,  // parent task id
    17: optional i64 SkillGroup,  // 技能组ID
    18: optional i64 TenantID,  // 租户ID
    19: optional i64 AgentID,  // 客服ID
    20: optional i32 EndUser,  // 结束的用户
    21: optional i64 AssignTime,  // 分配客服的时间
    22: optional string AgentEmail,  // 客服Email
    23: optional string AgentUuid, // 客服的门神uuid
    24: optional string SkillGroupName,  // 技能组名称
    25: optional i64 CustomerFirstResponseTime,  // 客服首次回复时间
    26: optional i64 AgentUid,  // 客服Uid
    27: optional i64 IMUserID,  // 客服平台用户ID
    28: optional string BizUserID,  // 业务原始用户ID
}

struct GetTaskRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct GetTaskResponse {
    1: required Task Task,
    255: optional base.BaseResp BaseResp,
}

struct QueryBizTypeRequest {
    1: required i32 HostAppID,
    255: optional base.Base Base,
}

struct GetBizTypeByAppChannelRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    3: optional i32 TaskID,
    255: optional base.Base Base,
}

struct GetBizTypeByAppChannelsRequest {
    1: required i32 AppID,
    2: required list<i32> Channel,
    255: optional base.Base Base,
}

struct QueryBizTypesByAccessPartyIDRequest {
    1: required i32 AccessPartyID,
    255: optional base.Base Base,
}

struct BizType {
    1: required i32 ID, // ID
    2: required i32 AppID, //客服平台AppID
    3: required string AppName, //客服平台App Name
    4: required i32 Channel, // 客服平台Channel
    5: required string ChannelName,//客服平台 Channel Name
    6: required i64 HostAppID, // 宿主AppID
    7: required string HostAppName,//宿主App name
    8: required i64 AppBaseID, //标签应用ID
    9: required i64 ResourceID, //资源ID
    10: required i64 SubResourceID, //子资源ID
    11: required i64 AccessPartyID,//接入方ID
    12: optional i64 Scene,//场景
    13: optional string EntranceId,//入口ID
    14: optional string EntranceName,//入口名
}

struct QueryBizTypesResponse {
    1: required list<BizType> BizTypeList,
    255: optional base.BaseResp BaseResp,
}

struct GetBizTypeRequest {
    1: required i32 ID,
    255: optional base.Base Base,
}

struct GetBizTypeResponse {
    1: required BizType bizType
    255: optional base.BaseResp BaseResp,
}

struct QueryNoticesRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    255: optional base.Base Base,
}

struct Notice {
    1: required i32 ID,
    2: required string Title,
    3: required string Content,
}

struct QueryNoticesResponse {
    1: required list<Notice> Notices,
    255: optional base.BaseResp BaseResp,
}

struct LineupConfirmRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    255: optional base.Base Base,
}

struct LineupConfirmResponse {
    1: required bool NeedConfirm
    2: required string Position
    3: required string Mins
    4: required string Message
    5: required string ConfirmText
    255: optional base.BaseResp BaseResp,
}

struct QueryLatestTaskRequest {
    1: required i32 AppID,
    2: required i64 ConversationShortID,
    255: optional base.Base Base,
}

struct QueryLatestTaskResponse {
    1: optional Task Task,
    255: optional base.BaseResp BaseResp,
}

struct GetWaitingInfoRequest {
    1: required i32 AppID,
    2: required i64 ConversationShortID,
    255: optional base.Base Base,
}

struct GetWaitingInfoResponse {
    1: required string position
    2: required i32 mins
    3: required i64 pollingSpan
    4: required string message
    255: optional base.BaseResp BaseResp,
}

struct GipReplyRequest {
    1: required i32 Source,
    2: required i64 Uid,
    3: required string Operator,
    4: required string Content,
    5: required string ImageUrl, // 图片 tos uri
    255: optional base.Base Base,
}

struct GipReplyResponse {
    1: required i64 TaskID
    2: required i64 ServerMessageID
    255: optional base.BaseResp BaseResp,
}


struct GipSyncUserMessageRequest {
    1: required i32 Source,
    2: required i64 Uid,
    3: required string Content,
    4: required string ImageUrl, // 图片 tos uri
    255: optional base.Base Base,
}

struct GipSyncUserMessageResponse {
    1: required i64 TaskID
    2: required i64 ServerMessageID
    255: optional base.BaseResp BaseResp,
}

struct GetUnreadMessageRequest {
    1: required i32 AppID,
    2: required i64 Uid,
    3: optional i32 Source,
    255: optional base.Base Base,
}

struct GetUnreadMessageResponse {
    1: required i32 UnreadMessageCount
    255: optional base.BaseResp BaseResp,
}

struct LabelInfo {
     1: required i64 Id,
     2: required string Name,
}

struct MGetMessageLabelsRequest {
    1: required list<i64> ServerMessageIds,
    255: optional base.Base Base,
}

struct MGetMessageLabelsResponse {
    1: map<i64,list<LabelInfo> >  LabelMap,
    255: optional base.BaseResp BaseResp,
}

service CustomerCloudService {
    // App映射相关
    GetBizTypeResponse GetBizType(1: GetBizTypeRequest req), //根据ID获取业务类型
    GetBizTypeResponse GetBizTypeByAppChannel(1: GetBizTypeByAppChannelRequest req), //根据AppID+Channel获取业务类型
    QueryBizTypesResponse GetBizTypeByAppChannels(1: GetBizTypeByAppChannelsRequest req), //根据AppID+多个Channel获取业务类型
    QueryBizTypesResponse QueryBizTypes(1: QueryBizTypeRequest req), //根据宿主AppID查询业务类型
    QueryBizTypesResponse QueryBizTypesByAccessPartyID(1: QueryBizTypesByAccessPartyIDRequest req), //根据接入方ID(AccessPartyID)查询所有业务类型
    QueryBizTypesResponse QueryBizTypesByAccessPartyIDNew(1: QueryBizTypesByAccessPartyIDRequest req), //根据接入方ID(AccessPartyID)查询所有业务类型(通过接入方获取入口列表，根据入口列表再查询业务类型)

    //Task相关
    GetTaskResponse GetTask(1: GetTaskRequest req), //获取Task详情
    QueryTaskResponse QueryTasks(1: QueryTaskRequest req), //查询人工进线列表
    QueryTaskMessageResponse QueryTaskMessages(1: QueryTaskMessageRequest req), //根据TaskID查询聊天记录
    QueryLatestTaskResponse QueryLatestTask(1: QueryLatestTaskRequest req), // 根据conversationID查询最近一条会话
    QueryLatestTaskResponse QueryLatestValidTask(1: QueryLatestTaskRequest req), // 根据conversationID查询最近一条成功转人工的会话

    //Misc
    QueryNoticesResponse QueryNotices(1: QueryNoticesRequest req), //查询IM页面Nocie列表
    LineupConfirmResponse LineupInfo(1: LineupConfirmRequest req), //转人工二次确认信息

    // 排队
    GetWaitingInfoResponse GetWaitingInfo(1: GetWaitingInfoRequest req), //查询排队信息

    //GIP
    GipReplyResponse GipReply(1: GipReplyRequest req), // Gip运营回复用户时调用该 RPC，同步运营的回复到客服平台
    GipSyncUserMessageResponse GipSyncUserMessage(1: GipSyncUserMessageRequest req), // Gip收到用户消息时，通过该RPC将用户的消息同步至客服平台

    //消息
    GetUnreadMessageResponse GetUnreadMessage(1: GetUnreadMessageRequest req), // 获取用户未读的客服消息数

    //批量获取标签信息
    MGetMessageLabelsResponse MGetMessageLabels(1: MGetMessageLabelsRequest req),
}
