include "./base.thrift"
namespace java com.bytedance.ies.kefu.category.thrift
namespace go bytedance.ies.kefu.category

//model
struct Resource{
    1: optional i64 Id,
    2: string Name,
    4: string DockingName,//对接人名称
    5: i64 AccessPartyId,//接入方idA
    6: i32 SubFlag,//是否开启子资源 0-未开启 1-开启
    7: i32 EnableFlag,//是否可用
    8: list<SubResource> SubList
}

struct SubResource{
    1: optional i64 Id,
    2: string Name,
    6: i32 EnableFlag,//是否可用
}

struct AccessParty{
    1: optional i64 Id,
    2: string Name,//接入方名称
    3: optional list<AccessParty> SubAccessParty,//二级接入方
    4: i32 EnableFlag,//是否可用
}

struct App{
    1: optional i64 Id,
    2: string Name,
    3: optional i64 ResourceCount,//使用资源数量
    4: optional i64 CategoryCount,//使用标签数量
    5: string DockingName,//对接人名称
    6: optional string LocalUpdateTime,//更新时间
    7: optional i32 EnableFlag,//
}

struct Category{
    1: optional i64 Id,//标签id 新增为0
    2: optional i64 ResourceId,//资源id
    3: optional i64 SubResourceId,//子资源id
    4: string Name,//标签名称
    5: optional string Path,//标签全路径
    6: optional i64 ParentId,//父标签id
    7: optional i32 Level,//层级
    8: i32 OrderIndex,//当前排序
    9: optional list<Category> SubCategoryList,//子标签集合
    10: optional bool IsBindApp//是否绑定app
    11: optional i32 EnableFlag,//
}

//common
struct CommonRequest{
    1: required i64 TenantId,//租户id
    2: optional i64 PageNo,//当前页码
    3: optional i64 PageSize,//单页数量
    4: required i64 AgentId,//客服Id
    5: required string AgentName,//客服名称
    6: required string CountryCode,//国家码
}

struct CommonResponse {
    255: base.BaseResp BaseResp,
}

//request and response
struct ResourceRequest{
    1: required CommonRequest CommonRequest,
    2: required Resource Resource,//资源列表
    255: optional base.Base Base,
}

struct GetResourceListRequest {
    1: required CommonRequest CommonRequest,
    2: optional string SearchKey,//搜索key
    3: optional i64 appId,//根据appId搜索绑定的资源子资源
    4: required i64 AccessPartyId,//根据接入方id进行隔离
    255: optional base.Base Base,
}

struct GetResourceListResponse {
    1: required list<Resource> ResourceList,//资源列表
    2: required i64 Total,//总数
    255: base.BaseResp BaseResp,
}


struct GetCategoryListRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    255: optional base.Base Base,
}

struct GetCategoryListByIdsRequest{
    1: required CommonRequest CommonRequest,
    2: required list<i64> CategoryIds,//标签ID集合
    255: optional base.Base Base,
}

struct GetCategoryListResponse{
    1: list<Category> CategoryList,
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateCategoryRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: list<Category> CategoryList,//修改/新增的标签集合，新增的标签可能有子标签
    5: list<i64> DeleteIdList,
    255: optional base.Base Base,
}


struct GetAppListRequest{
    1: required CommonRequest CommonRequest,
    2: string Name,
    255: optional base.Base Base,
}

struct GetAppListResponse {
    1: required list<App> AppList,//App列表
    2: required i64 Total,//总数
    255: base.BaseResp BaseResp,
}

struct AppRequest{
    1: required CommonRequest CommonRequest,
    2: required App App,
    255: optional base.Base Base,
}

struct GetCategoryAppListRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 AppId,
    255: optional base.Base Base,
}

struct BindCategoryToAppRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 AppId,
    5: list<i64> NewBindCategoryIds,//新增绑定标签id
    6: list<i64> UnBindCategoryIds,//解绑标签id
    255: optional base.Base Base,
}
//
//struct BindTagToCategoryRequest{
//    1: required CommonRequest CommonRequest,
//    2: string ExtraCode,//标签三级id
//    255: base.BaseResp BaseResp,
//}

struct GetAccessPartyListRequest{
    1: required CommonRequest CommonRequest,
    255: optional base.Base Base,
}
struct GetAccessPartyListRespone{
    1:required list<AccessParty> AccessPartyList
    255: base.BaseResp BaseResp,
}

service CateGoryService {

    //资源配置
    GetResourceListResponse GetResourceList(1: GetResourceListRequest req);//搜索资源列表
    CommonResponse AddOrUpdateResource(1: ResourceRequest req);//新增修改资源
    //标签配置
    GetCategoryListResponse GetCategoryList(1: GetCategoryListRequest req);//按照资源+子资源搜索标签列表
    CommonResponse AddOrUpdateCategory(1:AddOrUpdateCategoryRequest req);//新增修改标签
    GetCategoryListResponse GetCategoryListByIds(1: GetCategoryListByIdsRequest req);//按照标签id批量搜索标签

    //属性配置
//    CommonResponse BindTagToCategory(1: BindCategoryToAppRequest req);//tag绑定标签

    //应用配置
    GetAppListResponse GetAppList(1: GetAppListRequest req);//搜索端列表
    CommonResponse AddOrUpdateApp(1: AppRequest req);//添加/修改 app
    GetCategoryListResponse GetAppCategoryList(1: GetCategoryAppListRequest req);//获取子资源的全量标签，以及app标签绑定关系
    CommonResponse BindCategoryToApp(1: BindCategoryToAppRequest req);//app绑定标签

    //接入方
    GetAccessPartyListRespone getAccessPartyList(1: GetAccessPartyListRequest req);//根据租户id获取接入方
}
