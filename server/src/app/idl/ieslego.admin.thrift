include "./base.thrift"
include "./agent_skill_group.thrift"
namespace java com.bytedance.ies.lego.admin.thrift
namespace go bytedance.ieslego.admin

//规则任务状态
enum RuleTaskStatus {
    HAS_RUNNING = 1,//运行中
    NO_RUNNING = 0,//无运行中
}
//规则禁用状态
enum RuleStopStatus {
    DISABLED = 1,//禁用
    USING = 0,//启用
}
//规则状态
enum EntityStatus {
    ENABLE = 1,//可用
    UNABLE = 0,//不可用
    WAIT_ENABLE = -1,//创建中
}
//时间单位
enum TimeUnit {
    MINUTE = 1,//分钟
    HOUR = 2,//小时
    SECOND = 3,//秒
}
//动作时间类型
enum ActionTimeType {
    BEFORE = 1,//目标前
    AFTER = 0,//目标后
}
//动作类型
enum ActionWay {
    LARK = 1,//飞书
    LARK_URGENT = 2,//飞书加急
    LARK_URGENT_MSG = 3,//飞书短信加急
    LARK_URGENT_PHONE = 4,//飞书电话加急
    LARK_GROUP = 5,//飞书到群（场控用）
    EMAIL = 6,//邮件
    E_MESSAGE = 7,//电商站内信角标
    E_MESSAGE_POP = 8,//电商站内信弹窗
    E_LARK = 9,//电商飞书
    E_LARK_URGENT = 10,//电商飞书加急
    E_LARK_URGENT_MSG = 11,//电商飞书短信加急
    E_LARK_URGENT_PHONE = 12,//电商飞书电话加急
    E_LARK_GROUP = 13,//电商飞书到群
    MESSAGE_ALTER = 14,//工单站内信弹窗（场控用）
}
//条件操作类型
enum FilterOperateType {
    UNION = 0,//求并集
    INTERSECTION = 1,//求交集
}
//操作类型
enum OperatorType {
    NUMBER_EQUAL = 1;
    NUMBER_NOT_EQUAL = 2;
    CONTAINS = 3;
    GREATER = 4;
    LESS = 5;
    GREATER_OR_EQUAL = 6;
    LESS_OR_EQUAL = 7;
    START_WITH = 8;
    END_WITH = 9;
    IS_NULL = 10;
    IS_NOT_NULL = 11;
    IN = 12;
    STRING_CONTAINS = 13;
    STRING_EQUAL = 14;
    STRING_NOT_EQUAL = 15;
    TIME_EQUAL = 16;
    TIME_NOT_EQUAL = 17;
    TIME_GREATER = 18;
    TIME_LESS = 19;
    TIME_GREATER_OR_EQUAL = 20;
    TIME_LESS_OR_EQUAL = 21;
    NOT_IN = 22;
    LIST_EQUAL = 30;
    LIST_NOT_EQUAL = 31;
    STRING_NOT_CONTAINS = 32;
    LIST_RETAIN = 33;
}
enum FieldValueType {
    //单选
    SINGLE_CHOOSE = 1;
    //多选
    MULTI_CHOOSE = 2;
    //混合(多选+输入框)
    MIX = 3;
    //时间控件
    DATE = 4;
    //输入框
    INPUT = 5;
    //批量输入
    BATCH_INPUT = 6;
}
//filter字段
struct FieldMeta {
    1: required i64 Id,
    2: required string DisplayName, //字段的前端展示名称
    3: required string MapName, //字段的数据库存储名称
    4: required list<i32> OperatorIds,
    5: required i64 ModelId,
    6: required i32 Type,
    7: required i32 DataType,
    8: optional string OriginName,
    9: optional string CurrentName,
    10: optional string FieldPath,
    11: optional string ComputeFunc,
    12: optional string Scene,
    13: optional i64 ParentId,
    14: optional i64 TenantId, //租户ID
    15: optional i64 AccessPartyId,
    16: required string CreatedAt,  //创建时间
    17: required string UpdatedAt,  //更新时间
    18: optional string Extra,  //附加信息
    19: required i64 CreatorAgentId,  //创建人ID
    20: required i64 UpdaterAgentId,  //更新人ID
}

//filter字段
struct FieldCondition {
    1: required i64 FieldId,
    2: required string FieldDisplayName, //字段的前端展示名称
    3: required string FieldMapName, //字段的数据库存储名称。即将废弃
    4: required list<i32> OperatorIds,// 即将废弃
    5: optional string ComputeFunc,//字段的计算表达式
    6: optional string FieldName, //admin改造后的字段名，即规则实际运行需要的字段
    7: optional list<string> OperatorList, //字段运行使用的操作符
    8: optional i32 FieldDataType, //字段类型 integer = 1 long = 2 string = 3 double = 5 boolean = 6
}

//动作形参元数据
struct FormalParamMeta {
    1: required i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required string MapName, //数据库存储名称
    4: required i32 ValueType, //值类型
    5: optional string ValueOptions,  //选项型动作形参的可取值，具体取值取决于FieldGetType 0：字段值的json串；1：获取值的URL
}
//动作元数据
struct ActionMeta {
    1: required i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required list<FormalParamMeta> FormalParamMetas,//形参名称及其类型、可取值
}
//占位符元数据
struct PlaceholderMeta {
    1: optional i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required string MapName, //数据库存储名称
}
//依赖字段元数据
struct DependencyFieldMeta {
    1: optional i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required string MapName, //数据库存储名称
}
//SLA目标元数据
struct SLAAimMeta {
    1: required i64 Id, //ID
    2: required string Name, //名称 首响、完结
    3: required Filter Filter, //条件
    4: required list<ActionMeta> ActionMetas, //目标支持的全部动作及其形参 TODO 目前不区分目标
    5: required list<PlaceholderMeta> PlaceholderMetas, //目标支持的全部占位符 TODO 目前不区分目标
    6: required list<DependencyFieldMeta> DependencyFieldMetas,    //目标支持的全部规则依赖字段 TODO 目前不区分目标
    7: required EntityStatus Status,  //状态 1：可用；0：不可用
    8: required i64 TenantId, //租户ID
    9: optional i64 AccessPartyId, //接入方ID
    10: optional i64 SourceId,    //业务标识，区分工单、电商工单
    11: required string CreatedAt,  //创建时间
    12: required string UpdatedAt,  //更新时间
    13: required string Extra,  //附加信息
    14: required i64 CreatorAgentId,  //创建人ID
    15: required i64 UpdaterAgentId,  //更新人ID
}
//SLA目标元数据
struct SLAAimMetaSimple {
    1: required i64 Id, //ID
    2: required string Name, //名称 首响、完结
    3: required string Extra,  //附加信息
}
//单个字段条件
struct FilterUnit {
    1: optional i64 FieldId,
    2: required string FieldMapName,//todo 自定义字段前缀
    3: required i32 OperatorId,//todo 前端映射ID
    4: required string FieldValue,//json格式，如"[\"38\",\"40\"]"
}
//条件
struct Filter {
    1: required FilterOperateType OperateType,
    2: required list<FilterUnit> FilterUnitList,
    3: optional string Extra,  //级联关系用
}
//规则
struct AdminRule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional Filter Filter, //条件
    5: required string Value, //动作信息 json, list查询时填充''，get查询时填充
    6: required RuleStopStatus StopStatus,  //禁用状态
    7: required EntityStatus Status,  //状态
    8: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    9: required i64 TenantId, //租户ID
    10: required i64 AccessPartyId, //接入方ID
    11: optional i64 SourceId,    //业务标识，区分工单、电商工单
    12: required string CreatedAt,  //创建时间
    13: required string UpdatedAt,  //更新时间
    14: required string Extra,  //附加信息
    15: required i64 CreatorAgentId,  //创建人ID
    16: required i64 UpdaterAgentId,  //更新人ID
    17: optional RuleTaskStatus TaskStatus,  //规则任务状态 get查询时填充
    18: optional i64 MainRuleId,    //运行规则ID
    19: optional i64 OriginId,    //原始规则ID
    20: optional i32 Version,     //版本号
    21: optional i64 TriggerId,
}
//拖拽规则
struct AdminRuleSimple {
    1: required i64 Id, //ID
    2: required i32 Priority,  //优先级
}

struct FieldListGetRequest {
    1: required i64 EventId,  //事件ID，业务方和触发器服务约定
    2: optional i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct FieldListGetResponse {
    1: required list<FieldCondition> FieldConditions,
    255: required base.BaseResp BaseResp,
}

struct FieldValuesGetRequest {
    1: required i64 FieldId,
    2: required i32 OperatorId,
    255: required base.Base Base,
}

struct FieldValuesGetResponse {
    1: required i32 FieldValueType, //值类型
    2: required string FieldValues,  //具体取值取决于FieldGetType 0：字段值的json串；1：获取值的URL
    255: required base.BaseResp BaseResp,
}

struct FieldCreateRequest {
    1: required string DisplayName, //字段的前端展示名称
    2: required string MapName, //字段的数据库存储名称
    3: required list<i32> OperatorIds,
    4: optional i64 ModelId,
    5: required i32 Type,
    6: required i32 DataType,
    7: optional string OriginName,
    8: optional string CurrentName,
    9: optional string FieldPath,
    10: optional string ComputeFunc,
    11: optional string Scene,
    12: optional i64 ParentId,
    13: optional i64 TenantId, //租户ID
    14: optional i64 AccessPartyId,
    15: required i64 CreatorAgentId,  //创建人ID
    255: required base.Base Base,
}

struct FieldCreateResponse {
    1: optional FieldMeta FieldMeta,
    255: required base.BaseResp BaseResp,
}

struct FieldUpdateRequest {
    1: required i64 Id,
    2: optional string DisplayName, //字段的前端展示名称
    3: optional string MapName, //字段的数据库存储名称
    4: optional list<i32> OperatorIds,
    5: optional i64 ModelId,
    6: optional i32 Type,
    7: optional i32 DataType,
    8: optional string OriginName,
    9: optional string CurrentName,
    10: optional string FieldPath,
    11: optional string ComputeFunc,
    12: optional string Scene,
    13: optional i64 ParentId,
    14: optional i64 TenantId, //租户ID
    15: optional i64 AccessPartyId,
    16: required i64 UpdaterAgentId,
    255: required base.Base Base,
}

struct FieldUpdateResponse {
    1: optional FieldMeta FieldMeta,
    255: required base.BaseResp BaseResp,
}

struct FieldDeleteRequest {
    1: required i64 Id,
    2: required i32 UpdaterAgentId,
    3: optional i64 TenantId, //租户ID
    4: optional i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct FieldDeleteResponse {
    1: required bool result,
    255: required base.BaseResp BaseResp,
}

struct ActionMetaListGetRequest {
    1: required i64 TenantId, //租户ID
    2: optional i64 AccessPartyId, //接入方ID
    3: optional i64 SourceId,    //业务标识，区分工单、电商工单
    4: optional EntityStatus Status,  //状态
    255: required base.Base Base,
}

struct ActionMetaListGetResponse {
    1: required list<ActionMeta> ActionMetas, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaListGetRequest {
    1: required i64 TenantId, //租户ID
    2: optional i64 AccessPartyId, //接入方ID
    3: optional i64 SourceId,    //业务标识，区分工单、电商工单
    4: optional EntityStatus Status,  //状态
    255: required base.Base Base,
}

struct SLAAimMetaListGetResponse {
    1: required list<SLAAimMeta> SLAAimMetaList, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaSimpleListGetResponse {
    1: required list<SLAAimMetaSimple> SLAAimMetaSimpleList, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaCreateRequest {
    1: required string Name, //名称 首响、完结
    2: required Filter Filter, //条件
    3: required list<ActionMeta> ActionMetas, //目标支持的全部动作
    4: required list<PlaceholderMeta> PlaceholderMetas, //目标支持的全部占位符，如运行时param:${ticket.id}原样写入,配置时param:${aim.time}需替换
    5: required list<DependencyFieldMeta> DependencyFieldMetas,    //目标支持的全部规则依赖字段
    6: required i64 TenantId, //租户ID
    7: optional i64 AccessPartyId, //接入方ID
    8: optional i64 SourceId,    //业务标识，区分工单、电商工单
    9: required string Extra,  //附加信息
    10: required i64 CreatorAgentId,  //创建人ID
    255: required base.Base Base,
}

struct SLAAimMetaCreateResponse {
    1: optional SLAAimMeta SLAAimMeta,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleListGetRequest {
    1: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    2: required i64 TenantId, //租户ID
    3: required i64 AccessPartyId, //接入方ID
    4: optional i64 SourceId,    //业务标识，区分工单、电商工单
    5: optional list<i64> IdList,
    6: optional EntityStatus Status,  //状态
    7: optional RuleStopStatus StopStatus,  //禁用状态
    8: optional string DisplayNameLike, //名称
    9: optional i32 Priority,  //优先级
    10: optional i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRuleListGetResponse {
    1: required list<AdminRule> AdminRuleList,
    2: required i32 Count,
    255: required base.BaseResp BaseResp,
}

struct AdminRulePageGetRequest {
    1: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    2: required i64 TenantId, //租户ID
    3: required i64 AccessPartyId, //接入方ID
    4: optional i64 SourceId,    //业务标识，区分工单、电商工单
    5: required i32 Page,   //页码
    6: required i32 PageSize,   //每页条数
    7: optional RuleStopStatus StopStatus,  //禁用状态
    8: optional EntityStatus Status,  //状态
    9: optional list<i64> IdList,
    10: optional string DisplayNameLike, //名称
    11: optional i32 Priority,  //优先级
    12: optional i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRulePageGetResponse {
    1: required list<AdminRule> AdminRuleList,
    2: required i32 Count,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleGetByIdRequest {
    1: required i32 AppId,
    2: required i64 SourceId,
    3: required i64 Id,
    4: required i64 TenantId, //租户ID
    5: required i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct AdminRuleGetByIdResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleCreateRequest {
    1: required i32 AppId,
    2: required i64 SourceId,
    3: required string DisplayName,
    4: required i32 Priority,
    5: optional Filter Filter, //条件
    /*动作信息 json
        IM路由：   {
                     "skill_group":{"id":303}
                     "support_overflow": 1, //1：支持溢出；0：不支持
                     "skill_group_overflow": {"id": [1, 2]}
                    }
        机器人路由：{
                    "is_open": 0,
              	    "bot_id": {
              		    "id": 1
                    }
                  }
        SLA：目标json，如：
            [
                {'aimMetaId': 1,
                 'aimTime': 1,
                 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
                 'aimSteps': [
                            {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          , {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          ]}
               ,{'aimMetaId': 2,
                 'aimTime': 2,
                 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
                 'aimSteps': [
                            {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          , {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          ]}
            ]
        */
    6: required string Value,
    7: required string Extra,
    8: required i64 CreatorAgentId,
    9: required i64 TenantId, //租户ID
    10: required i64 AccessPartyId, //接入方ID
    11: required i64 EventId, //事件ID
    12: optional i64 RuleGroupId, //规则组ID
    13: optional agent_skill_group.CreateCardRequest CreateCardRequest, // 问题卡片内容。非问题卡片请求不可传
    14: optional bool Disable, // 是否禁用规则，不传默认为启用
    255: required base.Base Base,
}

struct AdminRuleCreateResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleUpdateRequest {
    1: required i64 Id,
    2: required i32 AppId,
    3: required i64 SourceId,
    4: optional string DisplayName,
    5: optional i32 Priority,
    6: optional Filter Filter, //条件
    7: optional string Value,
    8: optional EntityStatus Status,  //状态
    9: optional RuleStopStatus StopStatus,  //禁用状态
    10: optional string Extra,
    11: required i64 UpdaterAgentId,
    12: required i64 TenantId, //租户ID
    13: required i64 AccessPartyId, //接入方ID
    14: required i64 EventId, //事件ID
    15: optional agent_skill_group.UpdateCardRequest UpdateCardRequest, //卡片的修改请求，未修改时可不传
    255: required base.Base Base,
}

struct AdminRuleUpdateResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleDeleteRequest {
    1: required i64 Id,
    2: required i32 AppId,
    3: required i64 SourceId,
    4: required i64 UpdaterAgentId,
    5: required i64 TenantId, //租户ID
    6: required i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct AdminRuleDeleteResponse {
    1: required bool result,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleBatchUpdateRequest {
    1: required list<AdminRuleSimple> AdminRules,
    2: required i64 UpdaterAgentId,
    3: required i64 TenantId, //租户ID
    4: required i64 AccessPartyId, //接入方ID
    5: required i32 AppId,
    6: required i64 SourceId,
    7: required i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRuleBatchUpdateResponse {
    1: required bool result,
    255: required base.BaseResp BaseResp,
}

//规则状态
enum RuleStatus {
    ENABLE = 1,//启用
    UNABLE = 0,//禁用
    DRAFT = 2,//草稿
    DELETE = 3,//已删除
}
// 规则优先级
struct RulePriority {
    1: required i64 Id, //ID
    2: required i32 Priority,  //优先级
}
// 规则生效环境
enum RuleEnv {
    PPE = 0,//PPE环境
    PROD = 1,//线上环境
}

//条件组合操作类型
enum OpGroup {
    AND = 1,//求交集
    OR = 2,//求并集
    NOT = 3, //求反计算
}
//运算型参数表达式
struct MathExpr {
    1: required string OpMath, //数学运算符
    2: required Expr Lhs, //运算左值
    3: required Expr Rhs, //运算右值
}
//方法型参数表达式
struct FuncExpr {
    1: required string FuncName, //方法名，为字符串，如"abc"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//特征型参数表达式
struct FeatureExpr {
    1: required string FeatureName, //特征名，为字符串，如"ticket.status"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//参数表达式
struct Expr {
    1: optional list<Expr> ExprList, //元素可以为任意一种Expr
    2: optional list<Expr> SubstringList, //元素可以为任意一种Expr，用于拼成字符串
    3: optional MathExpr MathExpr,
    4: optional FuncExpr FuncExpr,
    5: optional FeatureExpr FeatureExpr, //特征型参数表达式，如"ticket.assignee_agent.status()"
    6: optional string VarExpr, //变量型参数表达式，如"$id"
    7: optional list<string> ConstantList, //元素可以为字符串，数字，布尔值中任意一种常量
    8: optional string Constant, //常量型参数表达式：字符串，如\"abc\"，\"300\"；数字，如100.1；布尔值，如true
}
//条件表达式
struct ConditionExpr {
    1: required string OpCheck,
    2: required Expr Lhs, //运算左值
    3: optional Expr Rhs, //运算右值
}
//条件组合
struct ConditionGroupExpr {
    1: required string OpGroup,
    2: optional list<ConditionExpr> Conditions,
    3: optional list<ConditionGroupExpr> ConditionGroups,//conditions和conditionGroups有且只有一个非空
}
//动作表达式
struct ActionExpr {
    1: required string ActionName, //动作名，为字符串，如"abc"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//动作组合
struct ActionGroupExpr {
    1: required bool Sequential,
    2: required bool ContinueOnFail,
    3: required list<ActionExpr> Actions,
}
//延时步骤
struct DelayStepExpr {
    1: required ActionGroupExpr ActionGroup;//即时动作
    2: required string FilterAim;//即时条件名，为字符串，如"abc"
    3: required Expr DelayTime;//延时参数表达式
}
//规则返回
struct AimExpr {
    1: optional list<DelayStepExpr> DelaySteps,//延时步骤
    2: optional ActionGroupExpr ActionGroup,//即时动作
    3: optional Expr ReturnValue,//路由返回 delaySteps/actionGroup/returnValue有且只有一个非空
}

//规则元数据
struct Rule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional ConditionGroupExpr Expression, //规则条件部分
    5: optional AimExpr ActionInfo,  //动作部分
    6: required i64 RuleGroupId,  //规则组id
    7: required RuleStatus Status,  //规则状态
    8: optional string Description, //规则描述
    9: required string CreatedAt,  //创建时间
    10: required string UpdatedAt,  //更新时间
    11: required string CreatorAgentId,  //创建人ID
    12: required string UpdaterAgentId,  //更新人ID
    13: required i32 DraftEditType, //草稿编辑类型1-新增 2-编辑 0-未修改
}

enum RuleOperationType {
    CREATE_ENABLE = 1, // 创建规则并启用
    CREATE_DISABLE = 2, // 创建规则并禁用
    STATUS_CHANGE = 3, // 规则状态变更, 启用/禁用/发布
    PRIORITY_CHANGE = 4, // 规则优先级变更
    UPDATE = 5, // 规则内容变更
}

struct RuleOperationLog {
    1: required i64 Id,
    2: required RuleOperationType OperationType,
    3: optional list<OperateRuleItemLog> OperateRuleItemLogs,
    4: required string OperatorAgentId,
    5: required string CreatedAt,
}

enum OperateRuleItemType {
    STAUTS = 1, // 更新状态
    NAME = 2, // 更新名字
    PRIORITY = 3, // 更新优先级
    CONDITION = 4, // 更新条件
    CONDITION_OPTIONS = 5, // 更新条件选项
    CONDITION_RELATION = 6, // 更新条件间关系
    CONDITION_GROUP_RELATION = 7, // 更新条件组间关系
    RETURN_VALUE = 8 // 更新规则返回值
    ROUTE_TYPE = 9, // 路由时机变更
    OVERFLOW = 10, // 溢出设置变更
}

enum ConditionUpdateType {
    OptionsUpdate = 1, //选项更新
    DELETE = 2, // 条件删除
    ADD = 3, // 条件新增
}

struct ConditionGroupChange {
    1: required i32 ConditionGroupOrder, // 条件组序号
    2: optional list<ConditionChange> ConditionChangeList, // 条件变更列表
    3: optional string Relation, // 条件关系变更后结果
    4: optional list<string> ConditionNameList, // 条件关系变更对应的条件列表
}

struct ConditionChange {
    1: required string ConditionName, // 条件名字
    2: required ConditionUpdateType ConditionUpdateType, // 条件更新类型
    3: optional list<string> AddOptions, // 条件选项新增
    4: optional list<string> DeleteOptions, // 条件选项删除
    5: optional string Operator, // 运算符
}

struct OperateRuleItemLog {
    1: required OperateRuleItemType OperateItemType,
    2: optional string BeforeValue,
    3: optional string AfterValue, //字符串或json结构
    4: optional list<ConditionGroupChange> ConditionGroupChangeList,
}


// 创建规则
struct CreateRuleRequest {
    1: required string EventKey, //事件
    2: optional i64 RuleGroupId, //规则组ID
    3: required string DisplayName, //规则名称
    4: required i32 Priority, // 规则优先级
    5: required ConditionGroupExpr Expression, //条件-DSL
    6: required AimExpr ActionInfo,  //动作部分
    7: required string CreatorAgentId, //创建人id
    8: required i64 AccessPartyId, //接入方ID
    9: optional bool Enable, // 是否启用规则，不传默认为禁用
    10: optional string Extra, //扩展字段，前端可用于复现规则
    11: required string Description, //规则描述
    12: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    13: required string Version, // 接口版本 UI传v1
    255: optional base.Base Base,
}
struct CreateRuleResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

// 更新规则
struct UpdateRuleRequest {
    1: required i64 Id, //规则ID
    2: optional string DisplayName, //规则名称
    3: optional ConditionGroupExpr Expression, //条件-DSL
    4: optional AimExpr ActionInfo, //路由的结果部分，用于对结果的特殊处理，比如绑定技能组
    5: optional string Extra, //扩展字段，前端可用于复现规则
    6: optional string Description, //规则描述
    7: required string AgentId, // 操作人
    8: required string Version, // 接口版本 UI传v1
    255: optional base.Base Base,
}
struct UpdateRuleResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

// 启用/禁用/删除规则
struct UpdateRuleStatusRequest {
    1: required list<i64> Ids, //规则ID的list
    2: required RuleStatus RuleStatus, //建RuleStatus定义
    3: required string UpdaterAgentId, // 更新人
    4: required string Version, // 接口版本 UI传v1
    255: optional base.Base Base,
}
struct UpdateRuleStatusResponse {
    255: required base.BaseResp BaseResp,
}

//调整规则优先级
struct UpdateRulePriorityRequest {
    1: required list<RulePriority> Rules, //所有规则的优先级信息
    2: required string UpdaterAgentId, // 更新人
    3: required string Version, // 接口版本 UI传v1
    255: optional base.Base Base,
}
struct UpdateRulePriorityResponse {
    1: optional i64 RuleGroupId, //调整优先级会生成新的规则组版本
    2: optional map<i64,i64> newRuleIds, //调整优先级会生成新的ruleId
    255: required base.BaseResp BaseResp,
}

// 根据id获取规则
struct GetRuleByIdRequest {
    1: required i64 Id,
    255: optional base.Base Base,
}
struct GetRuleByIdResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

//获取规则列表
struct GetRuleListRequest {
    1: required string EventKey, //事件ID
    2: required i64 AccessPartyId, //接入方ID
    3: optional i64 RuleGroupId, //规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
    4: optional i32 IsDraft,//是否获取草稿箱里的规则
    255: optional base.Base Base,
}
struct GetRuleListResponse {
    1: optional list<Rule> RuleList, //规则
    255: required base.BaseResp BaseResp,
}

struct PublishRuleGroupRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required string UpdaterAgentId, // 更新人
    255: optional base.Base Base,
}

struct PublishRuleGroupResponse {
    1: optional i64 RuleGroupId, //新的规则组id
    2: optional map<i64,i64> newRuleIds, //发布会生成新的ruleId
    255: required base.BaseResp BaseResp,
}

struct CopyRuleGroupRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required string UpdaterAgentId, // 更新人
    255: optional base.Base Base,
}

struct CopyRuleGroupResponse {
    255: required base.BaseResp BaseResp,
}

struct GetRuleOperationLogsRequest {
    1: required i64 RuleId,
    2: required i32 Page,
    3: required i32 PageSize,
    255: optional base.Base Base,
}

struct GetRuleOperationLogsResponse {
    1: optional list<RuleOperationLog> RuleOperationLogList,
     2: optional i64 totalCount,
    255: required base.BaseResp BaseResp,
}

struct GetExtraInfoRequestV2 {
    1: required string Scenes,
    2: required string EventKey,
    3: optional list<string> ExtraKeys,
    4: optional i64 AccessPartId,
    255: optional base.Base Base,
}

struct GetExtraInfoResponseV2 {
    1: optional list<RuleGroupRelationStruct> RuleGroupRelations
    255: required base.BaseResp BaseResp,
}

struct RuleGroupRelationStruct {
    1: required i64 RuleGroupId,
    2: required map<string, string> extraInfos,
    3: required i64 OriginId
}

// 创建规则组
struct CreateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required string GroupDisplayName, //规则组名称
    3: required string CreatorAgentId, //创建人id
    4: required string Product, //产品
    5: required list<AntlrRule> RuleList, // 规则集合
    6: optional bool Enable, // 是否启用规则，不传默认为待发布（草稿），点保存-传false，发布传true
    7: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    8: required i64 AccessPartyId, //接入方ID
    9: optional list<ExtraInfoStruct> ExtraInfo,
    10: optional i64 SkillGroupId, //技能组
    255: optional base.Base Base,
}

// 规则
struct AntlrRule {
    1: required string DisplayName, //规则名称
    2: required i32 Priority, // 规则优先级
    3: required ConditionGroupExpr Expression, //条件-DSL
    4: required AimExpr ActionInfo,  //动作部分
    5: optional string CreatorAgentId, //创建人id
    6: optional string Description, //规则描述
    7: optional i64 Id  // 规则ID
    8: optional i64 OriginId    // 规则原始ID
    9: optional string Extra // 用于前端复现规则
    255: optional base.Base Base,
}

struct ExtraInfoStruct {
    1: required string DisplayName,
    2: required string Key,
    3: required i32 Status,
    4: required i32 Type,
    5: required string Value
}

struct BatchCreateRuleGroupRequest {
    1: required list<CreateRuleGroupRequest> RuleGroups,
    255: optional base.Base Base,
}

struct BatchCreateRuleGroupResponse {
    1: optional list<i64> RuleGroupIds,
    255: required base.BaseResp BaseResp,
}

// 结果
struct UpdateRuleGroupStatusResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp, // 前端收否解析
}

// 启用/禁用/删除规则
struct UpdateRuleGroupStatusRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required RuleStatus RuleStatus, // 状态
    3: required string UpdaterAgentId, // 更新人
    255: optional base.Base Base,
}

struct BatchUpdateRuleGroupResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp, // 前端收否解析
}

struct BatchUpdateRuleGroupRequest {
    1: required list<UpdateRuleGroupRequest> RuleGroups,
    255: optional base.Base Base,
}

// 修改规则组
struct UpdateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required i64 RuleGroupId, //规则组ID
    3: required i64 OriginId, // 原始id
    4: required string GroupDisplayName, //规则组名称
    5: required i32 Version, // 版本号
    6: required string UpdaterAgentId, //创建人id
    7: required string Product, //产品
    8: required list<AntlrRule> RuleList, // 规则集合
    9: optional bool Enable, // 是否启用规则，不传默认为禁用
    10: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    11: required i64 AccessPartyId, //接入方ID
    12: optional RuleType RuleType,
    13: optional i64 SkillGroupId, //技能组id
    255: optional base.Base Base,
}


enum RuleType {
    DOCUMENTARY_RULE   = 1  // 跟单规则
    GENERAL_RULE       = 2  // 通用规则
    SEND_DOWN          = 3  // 下送
    UPGRADE            = 4  // 升级
    FINISH             = 5  // 完结
}

service AdminService {

    //查询条件属性列表
    FieldListGetResponse GetFieldList(1: FieldListGetRequest req)

    //查询条件属性值列表
    FieldValuesGetResponse GetFieldValues(1: FieldValuesGetRequest req)

    //添加字段
    FieldCreateResponse CreateField(1: FieldCreateRequest req)

    //修改字段
    FieldUpdateResponse UpdateField(1: FieldUpdateRequest req)
    // 获取规则操作日志
    GetRuleOperationLogsResponse GetRuleOperationLogs(1: GetRuleOperationLogsRequest req)
    //删除字段
    FieldDeleteResponse DeleteField(1: FieldDeleteRequest req)

    //查询动作元数据列表--SLA/Trigger use
//    ActionMetaListGetResponse GetActionMetaList(1: ActionMetaListGetRequest req)

    //查询SLA目标元数据列表--SLA use
    SLAAimMetaListGetResponse GetSLAAimMetaList(1: SLAAimMetaListGetRequest req)
    SLAAimMetaSimpleListGetResponse GetSLAAimMetaSimpleList(1: SLAAimMetaListGetRequest req)

    //添加SLA目标元数据--SLA use
    SLAAimMetaCreateResponse CreateSLAAimMeta(1: SLAAimMetaCreateRequest req)

    //查询后台规则分页列表--SLA use
    AdminRulePageGetResponse GetAdminRulePage(1: AdminRulePageGetRequest req)

    //查询后台规则列表
    AdminRuleListGetResponse GetAdminRuleList(1: AdminRuleListGetRequest req)

    //查询后台规则
    AdminRuleGetByIdResponse GetAdminRuleById(1: AdminRuleGetByIdRequest req)

    //添加后台规则
    AdminRuleCreateResponse CreateAdminRule(1: AdminRuleCreateRequest req)

    //修改后台规则
    AdminRuleUpdateResponse UpdateAdminRule(1: AdminRuleUpdateRequest req)

    //删除后台规则
    AdminRuleDeleteResponse DeleteAdminRule(1: AdminRuleDeleteRequest req)

    //拖拽修改后台规则
    AdminRuleBatchUpdateResponse BatchUpdateAdminRule(1: AdminRuleBatchUpdateRequest req)

    // 改造 - 查询后台规则列表
    GetRuleListResponse GetRuleList(1: GetRuleListRequest req)

    //根据id查询后台规则
    GetRuleByIdResponse GetRuleById(1: GetRuleByIdRequest req)

    //添加后台规则
    CreateRuleResponse CreateRule(1: CreateRuleRequest req)

    //修改后台规则
    UpdateRuleResponse UpdateRule(1: UpdateRuleRequest req)

    //启用、禁用、删除后台规则
    UpdateRuleStatusResponse UpdateRuleStatus(1: UpdateRuleStatusRequest req)

    //调整规则优先级
    // 启用、禁用、删除后台规则
    // UpdateRuleStatusResponse UpdateRuleStatus(1: UpdateRuleStatusRequest req)

    // 调整规则优先级
    UpdateRulePriorityResponse UpdateRulePriority(1: UpdateRulePriorityRequest req)

    // 发布规则
    PublishRuleGroupResponse PublishRuleGroup(1: PublishRuleGroupRequest req)

    // 复制规则
    CopyRuleGroupResponse CopyRuleGroup(1: CopyRuleGroupRequest req)

    // 获取已配置信息
    GetExtraInfoResponseV2 GetExtraInfoV2(1: GetExtraInfoRequestV2 req)

    // 批量创建规则
    BatchCreateRuleGroupResponse BatchCreateRuleGroup(1: BatchCreateRuleGroupRequest req)

    // 修改规则组状态 启用禁用
    UpdateRuleGroupStatusResponse UpdateRuleGroupStatus(1: UpdateRuleGroupStatusRequest req)

    // 批量更新规则
    BatchUpdateRuleGroupResponse BatchUpdateRuleGroup(1: BatchUpdateRuleGroupRequest req)
    
}
