include "./base.thrift"
include "openapi.thrift"
include "template.thrift"

namespace go security.tq.notify

enum ErrorCode {
    ServerError     = 100
    ParameterError  = 101
    DependencyError = 102
}

service TqNotifierService {
    /*    模版配置相关接口     */
    // 创建模版
    template.TemplateIDResp     CreateTemplate (1: template.UpsertTemplateReq req),
    // 编辑模版
    template.TemplateIDResp     UpdateTemplate (1: template.UpsertTemplateReq req),
    // 查询模版详情
    template.TemplateDetailResp TemplateDetail (1: template.TemplateIDReq req),
    // 删除模版
    template.DeleteTemplateResp DeleteTemplate (1: template.TemplateIDReq req),
    // 查询模版列表
    template.TemplateListResp   TemplateList (1: template.TemplateListReq req),
    // 查询某个模版的推送历史
    template.HistoryListResp    HistoryList (1: template.HistoryListReq req),

    /*    OpenAPI相关接口    */
    // 推送消息变量
    openapi.PostMessageResp   PostMessage (1: openapi.PostMessageReq req),
    // 建群
    openapi.CreateChatResp    CreateChat (1: openapi.CreateChatReq req),
    // 邀请人员进群
    openapi.AddChatMemberResp AddChatMember (1: openapi.AddChatMemberReq req),
}
