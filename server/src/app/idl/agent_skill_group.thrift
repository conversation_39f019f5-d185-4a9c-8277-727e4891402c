include "./base.thrift"
namespace java com.bytedance.agentskillgroup.thrift
namespace go bytedance.ies.kefu.agentskillgroup

enum IdcType {
    ROW = 1,
    TTP = 2,
}
//操作渠道
enum ChannelType {
    IM = 1, //IM
    TICKET = 2, //工单
    PHONE = 3, //电话
    ADMIN = 4, //管理员
    ECOM_TICKET = 5, //电商工单
    BUZZ = 6, //BUZZ工单
    FEEDBACK = 7, // FEEDBACK
    QUALITY_CHECK = 8 // 质检
}

enum WorkType {
    FULL_TIME = 1,
    OUTSOURCING = 2,
    TRAINEE = 3,
    THIRD_PARTY = 4, // 第三方派遣
}

enum Status {
    OFF = 0,
    ON = 1,
}

//卡片操作类型
enum HandleType {
    OPEN = 1,
    CLOSE = 2,
    DELETE = 3,
}

enum IncreaseType {
    ADD = 1,
    MINUS = 2,
}

enum TagType{
    FEEDBACK_CHANNEL = 1; //离线技能组渠道
}

enum TagCode{
    FEEDBACK_CHANNEL_EMAIL = 1;
    FEEDBACK_CHANNEL_INAPP = 2;
}


struct Agent {
    1: i64 ID,
    2: i64 TenantId,
    3: i32 WorkType,
    4: string UserName,
    5: string NickName,
    6: i64 UserId,
    7: string UUID,
    8: string Email,
    9: string Mobile,
    10: i64 CompanyId,
    11: list<ChannelType> ChannelTypes,
    12: i32 Status,
    13: i64 CreatedBy,
    14: string CreatedAt,
    15: i64 UpdatedBy,
    16: string UpdatedAt,
    17: string OperatorName,
    18: i64 DepartmentId,
    19: i32 ImMaxTaskNum,
    20: optional i64 PhoneSeatNo,
    21: optional string CountryRegion, // 国家地区
    22: optional i32 FeedbackMaxTaskNum,
    23: optional string NgccServiceLine,
    24: optional i32 QualityCheckMaxTaskNum,


    31: optional i64 DgTenantId,  //门神租户id
    40: optional i32 TicketMaxTaskNum, //工单最大接待量
    41: optional IdcType IdcType; //机房类型

    255: map<string, string> Extra, //目前extra里包含的字段 c_n_agent_appid(IM appId),
                                    // c_s_ecom_ticket_role(电商工单系统角色),
                                    // c_n_ecom_ticket_is_super(电商工单是否超级管理员)
                                    // c_s_ecom_ticket_kind(电商工单业务类型)
    }

struct CreateAgentRequest {
    1: required i64 TenantId,
    2: required i32 WorkType,
    3: required string UserName,
    4: optional string NickName,
    5: optional i64 UserId,
    6: required string UUID,
    7: required string Email,
    8: optional string Mobile,
    9: required list<ChannelType> ChannelTypes,
    10: required i64 OperatorAgentId,
    11: required i64 CompanyId,
    12: optional map<string, string> Extra,
    13: optional i64 Id,
    14: required i64 DepartmentId,
    15: optional i32 ImMaxTaskNum,
    16: optional i64 PhoneSeatNo,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FeedbackMaxTaskNum,
    19: optional string NgccServiceLine,
    20: optional i32 QualityCheckMaxTaskNum,

    40: optional i32 TicketMaxTaskNum,
    41: optional IdcType IdcType; //机房类型

    255: optional base.Base Base,
}

struct CreateAgentResponse {
    1: i64 ID,

    255: base.BaseResp BaseResp,
}

struct BatchCreateAgentRequest {
    1: required list<CreateAgentRequest> CreateAgentRequests,

    255: optional base.Base Base,
}

struct BatchCreateAgentResponse {
    1: list<CreateAgentResponse> CreateAgentResponses,

    255: base.BaseResp BaseResp,
}

struct BatchDeleteAgentRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 OperatorAgentId,
    4: optional string AccessKey;

    255: optional base.Base Base,
}

struct BatchDeleteAgentResponse {
    255: base.BaseResp BaseResp,
}

struct BatchInsOrUpdtAgentToEsRequest {
    1: required string AccessKey,
    2: required i64 TenantId,

    255: optional base.Base Base,
}

struct BatchInsOrUpdtAgentToEsResponse {
    255: base.BaseResp BaseResp,
}

struct GetLeadersRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,

    255: optional base.Base Base,
}

struct GetLeadersResponse {
    1: required list<i64> LeaderIds,

    255: base.BaseResp BaseResp,
}

struct UpdateAgentRequest {
    1: required i64 Id,
    2: required i64 TenantId,
    3: optional i32 WorkType,
    4: optional string UserName,
    5: optional string NickName,
    6: optional i64 UserId,
    9: optional string Mobile,
    10: optional list<ChannelType> ChannelTypes,
    11: required i64 OperatorAgentId,
    12: optional i64 CompanyId,
    13: optional map<string, string> Extra,
    14: optional i64 DepartmentId,
    15: optional i32 ImMaxTaskNum,
    16: optional i64 PhoneSeatNo,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FeedbackMaxTaskNum,
    19: optional string NgccServiceLine,
    20: optional i32 QualityCheckMaxTaskNum,
    40: optional i32 TicketMaxTaskNum,
    41: optional IdcType IdcType; //机房类型

    255: optional base.Base Base,
}

struct UpdateAgentResponse {
    255: base.BaseResp BaseResp,
}

struct BatchUpdateAgentRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 OperatorAgentId,
    4: optional i32 ImMaxTaskNum,
    5: optional i32 FeedbackMaxTaskNum,
    6: optional i32 TicketMaxTaskNum,

    255: optional base.Base Base,
}

struct BatchUpdateAgentResponse {
    255: base.BaseResp BaseResp,
}

struct GetAgentDetailRequest {
    1: required i64 TenantId,
    2: required i64 ID,

    255: optional base.Base Base,
}

struct GetAgentDetailResponse {
    1: optional Agent Agent,

    255: base.BaseResp BaseResp,
}

struct GetAgentByUUIDRequest {
    1: required i64 TenantId,
    2: required string UUID,

    255: optional base.Base Base,
}

struct GetAgentByUUIDResponse {
    1: Agent Agent,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByIDsRequest {
    1: required i64 TenantId,
    2: required list<i64> IDs

    255: optional base.Base Base,
}

struct GetAgentListByIDsResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByEmailRequest {
    1: required i64 TenantId,
    2: required list<string> Emails,

    255: optional base.Base Base,
}

struct GetAgentListByEmailResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct EnableAgentRequest {
    1: required i64 TenantId,
    2: required i64 ID,
    3: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct EnableAgentResponse {
    255: base.BaseResp BaseResp,
}

struct DisableAgentRequest {
    1: required i64 TenantId,
    2: required i64 ID,
    3: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DisableAgentResponse {
    255: base.BaseResp BaseResp,
}

struct GetAgentsByConditionRequest {
    1: required i64 TenantId,
    2: optional string Email,
    3: optional string UserName,
    4: optional i32 Status,
    5: required i32 PageNo,
    6: required i32 PageSize,
    7: optional i64 DepartmentId,
    8: optional list<i64> SkillGroupIds,
    9: optional list<i64> DepartmentIds,
    10: optional ChannelType ChannelType,
    11: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName

    255: optional base.Base Base,
}

struct GetAgentsByConditionResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct SearchAgentRequest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: optional string UserName,
    4: optional string Email,
    5: optional i64 PhoneSeatNo,
    6: required i32 PageNo,
    7: required i32 PageSize,
    8: optional i32 Status,

    254: optional map<string,string> extra;

    255: optional base.Base Base,
}

struct SearchAgentResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetAgentsByPermissionRequest {
    1: required i64 OperatorAgentId,
    2: required ChannelType ChannelType,
    3: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAgentsByPermissionResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetAgentsByOperateTimeRequest {
    1: required i64 OperateTime;

    255: optional base.Base Base,
}

struct GetAgentsByOperateTimeResponse {
    1: list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

//技能组
struct SkillGroup {
    1: i64 ID,
    2: i64 TenantId,
    3: list<i64> AccessPartyId,
    4: string Name,
    5: ChannelType ChannelType,
    6: i32 MaxTaskNum,
    7: list<i64> QuestionCategoryIds,
    8: list<string> TagCodes,
    9: i64 CreatedBy,
    10: string CreatedAt,
    11: i64 UpdatedBy,
    12: string UpdatedAt,
    13: string OperatorName,
    14: i32 MemberNum;
    15: i32 LeaderNum;
    16: i32 SkillGroupLevel,
    17: optional string WorkStartTime,
    18: optional string WorkEndTime,
    19: optional string CountryRegion, // 国家地区
    20: optional i32 FirstHourMaxTaskNum,
    21: optional i32 HourlyMaxTaskNum,
    22: optional i32 DailyMaxTaskNum,
    23: optional i32 MaxFollowUpNum,

    30: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp  value: TagCode
    31: optional bool AutoAssign, // 是否为自动分单技能组
    32: optional list<i64> LeaderIds,
    33: optional IdcType IdcType; //机房类型
    34: optional bool SupportFallback; //是否支持回退

    255: map<string, string> Extra,
}

struct CreateSkillGroupRequest {
    1: required i64 TenantId,
    2: required list<i64> AccessPartyId,
    3: required string Name,
    4: required ChannelType ChannelType,
    5: required i32 MaxTaskNum,
    6: optional list<i64> QuestionCategoryIds,
    7: required list<string> TagCodes,
    8: required i64 OperatorAgentId,
    9: optional map<string, string> Extra,
    10: optional string WorkStartTime,
    11: optional string WorkEndTime,
    12: optional string CountryRegion, // 国家地区
    13: optional i32 FirstHourMaxTaskNum,
    14: optional i32 HourlyMaxTaskNum,
    15: optional i32 DailyMaxTaskNum,
    16: optional i32 MaxFollowUpNum,
    17: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp value: TagCode
    18: optional IdcType IdcType; //机房类型
    19: optional bool SupportFallback; //是否支持回退

    255: optional base.Base Base,
}

struct CreateSkillGroupResponse {
    1: i64 ID,

    255: base.BaseResp BaseResp,
}


struct UpdateSkillGroupRequest {
    1: required i64 Id,
    2: required i64 TenantId,
    3: required list<i64> AccessPartyId,
    5: optional ChannelType ChannelType,
    6: optional i32 MaxTaskNum,
    7: optional list<i64> QuestionCategoryIds,
    9: optional list<string> TagCodes,
    11: optional list<i64> AccessPartyIds,
    13: optional i64 OperatorAgentId,
    14: optional map<string, string> Extra,
    15: optional string WorkStartTime,
    16: optional string WorkEndTime,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FirstHourMaxTaskNum,
    19: optional i32 HourlyMaxTaskNum,
    20: optional i32 DailyMaxTaskNum,
    21: optional i32 MaxFollowUpNum,
    22: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp  value: TagCode

    40: optional string Name,
    41: optional IdcType IdcType; //机房类型
    42: optional bool SupportFallback; //是否支持回退


    255: optional base.Base Base,
}

struct UpdateSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct DeleteSkillGroupRequest {
    1: required i64 ID,
    2: required i64 TenantId,
    4: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DeleteSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupDetailRequest {
    1: required i64 ID,
    2: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetSkillGroupDetailResponse {
    1: SkillGroup SkillGroup

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByIdsRequest {
    1: required i64 TenantId,
    2: required list<i64> Ids,
    3: optional bool IncludeDeletedData,

    255: optional base.Base Base,
}

struct GetSkillGroupsByIdsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByTypeResquest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: required i32 PageNo,
    4: required i32 PageSize,
    5: optional i64 AccessPartyId,
    6: optional list<ChannelType> ChannelTypes,
    7: optional i32 SkillGroupLevel,
    8: optional string SkillGroupName,
    9: optional bool OnlySimpleData, // 是否只需要返回技能组的简单信息，不包含问题标签等关联数据
    10: optional list<i32> ShieldTypes, // 1 测试技能组

    255: optional base.Base Base,
}

struct GetSkillGroupsByTypeResponse {
    1: list<SkillGroup> SkillGroups,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByCategoryRequest {
    1: required i64 TenantId,
    2: required i64 CategoryId,

    255: optional base.Base Base,
}

struct GetSkillGroupsByCategoryResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupsByCategoryRequest {
    1: required i64 TenantId,
    2: required list<i64> CategoryIds,

    255: optional base.Base Base,
}

struct BatchGetSkillGroupsByCategoryResponse {
    1: map<i64, list<i64>> CategoryGroupMap,

    255: base.BaseResp BaseResp,
}

struct AddAgentToSkillGroupRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 SkillGroupId,
    4: required i32 IsGroupLeader,
    5: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct AddAgentToSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct DeleteAgentFromSkillGroupRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 SkillGroupId,
    4: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DeleteAgentFromSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupAgentsRequest {
    1: required i64 TenantId,
    2: required i64 SkillGroupId,
    3: optional string AgentName,
    4: optional i32 IsGroupLeader,
    5: required i32 PageNo,
    6: required i32 PageSize,
    7: optional string AgentEmail,
    8: optional i64 AccessPartyId,
    9: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName

    255: optional base.Base Base,
}

struct GetSkillGroupAgentsResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupAgentsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> GroupList, // 技能组ID列表
    4: optional list<i32> WorkStatusList, // 工作状态列表

    255: optional base.Base Base,
}

struct BatchGetSkillGroupAgentsResponse {
    1: map<i64, list<i64>> GroupToAgentListMap,

    255: base.BaseResp BaseResp,
}

struct UpdateGroupAgentsTaskNumRequest {
    1: required i64 SkillGroupId,
    2: required i64 AccessPartyId,
    3: required list<i64> AgentIds,
    4: required i32 MaxTaskNum,
    5: required i64 TenantId,
    6: optional i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct UpdateGroupAgentsTaskNumResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByNameRequest {
    1: required i64 TenantId,
    2: required string SkillGroupName,
    3: optional ChannelType ChannelType,
    4: optional i64 AccessPartyId,
    5: optional list<ChannelType> ChannelTypes,

    255: optional base.Base Base,
}

struct GetSkillGroupsByNameResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetAllSkillGroupsRequest {
    1: required i64 TenantId,
    2: optional list<i32> ShieldTypes,  // 1 测试技能组 2管理组 3 session特殊屏蔽组

    255: optional base.Base Base,
}

struct GetAllSkillGroupsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetAllWorkingSkillGroupsRequest {
    1: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAllWorkingSkillGroupsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAgentIdRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: optional i64 AccessPartyId; #新增接入方筛选

    255: optional base.Base Base,
}

struct GetSkillGroupsByAgentIdResponse {
    1: list<SkillGroup> SkillGroups,
    2: map<i64, i32> TaskNumMap,  // skillGroupId -> taskNum
    3: map<i64, i32> LeaderMap,  // 新增是否是组长  key:skillGroupId value 1: 组长, 0 :非组长

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupsByAgentIdsRequest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: optional i64 AccessPartyId,
    4: required list<i64> AgentIds,

    255: optional base.Base Base,
}

struct AgentSkillGroups {
    1: required i64 AgentId,
    2: required list<SkillGroup> SkillGroups,
}

struct BatchGetSkillGroupsByAgentIdsResponse {
    1: required list<AgentSkillGroups> AgentSkillGroups;

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartyRequest {
    1: required i64 TenantId,
    2: required i64 AccessPartyId,
    3: optional list<i32> ShieldTypes, // 1 测试技能组

    255: optional base.Base Base,
}

struct GetSkillGroupsByAccessPartyResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartiesRequest {
    1: required i64 TenantId,
    2: required list<i64> AccessPartyIds,
    3: optional ChannelType ChannelType,

    255: optional base.Base Base,
}

struct GetSkillGroupsByAccessPartiesResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAgentAccessPartyRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required i64 AccessPartyId,
    4: optional list<i32> ShieldTypes, // 1 测试技能组

    255: optional base.Base Base,
}

struct GetSkillGroupsByAgentAccessPartyResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

//公司
struct AgentCompany {
    1: i64 Id,
    2: i64 TenantId,
    3: string Name,
    4: i32 IsBytedance,
    5: i64 DgTenantId,
    6: string DeletedAt,
    7: i64 CretedBy;
    8: string CreatedAt,
    9: i64 UpdatedBy,
    10: string UpdatedAt,
}

struct GetAllCompanyRequest {
    255: optional base.Base Base,
}

struct GetAllCompanyResponse {
    1: list<AgentCompany> AgentCompanys,

    255: base.BaseResp BaseResp,
}

struct SkillGroupTag {
    1: i64 TenantId,
    2: i64 Id,
    3: string Code,
    4: string DispalyName,
    5: string CreatedAt,
    6: string UpdatedAt,
    7: string OperatrAgentName,
}

struct GetAllSkillGroupTagsRequest {
    1: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAllSkillGroupTagsResponse {
    1: list<SkillGroupTag> SkillGroupTags

    255: base.BaseResp BaseResp,
}


/*
* 问题卡片相关部分
* */
struct CardQuestionThrift {
    1: required i64 Id,                 //问题ID
    2: required i64 CardId,             //卡片ID
    3: required string QuestionName,    //问题内容
    4: required i64 SkillGroupId,       //技能组ID
    5: required string FullName,        //卡片全称
}

struct AppQuestionCardThrift {
    1: required i64 Id,                 //卡片ID
    2: required i64 TenantId,           //租户ID
    3: required i64 AccessPartyId,      //接入方ID
    4: required i64 AppId,              //应用ID
    5: required string CardName,        //引导话术，用于title展示
    6: required i32 IsOpen,             //是否启用
    7: list<CardQuestionThrift> CardQuestions,//问题列表，list顺序代表C端展示顺序
    8: optional string CardDisplayName, //卡片展示名称 - 新版必传 向下兼容
}

struct UpdateAppQuestionCardThrift {
    1: required i64 Id,                 //卡片ID
    2: required i64 TenantId,           //租户ID
    3: required i64 AccessPartyId,      //接入方ID
    4: required i64 AppId,              //应用ID
    5: required string CardName,        //引导话术，用于title展示
    6: list<UpdateCardQuestion> UpdateCardQuestions,//问题列表，list顺序代表C端展示顺序
    7: optional string CardDisplayName, //卡片展示名称 新版必传 向下兼容
}

struct AddCardQuestion {
    1: required string QuestionName,    //问题内容
    2: required i64 SkillGroupId,       //技能组ID
}

struct UpdateCardQuestion {
    1: optional i64 Id,                 //问题ID
    2: required string QuestionName,    //问题内容
    3: required i64 SkillGroupId,       //技能组ID
}

struct AddAppQuestionCard {
    1: required i64 TenantId,           //租户ID
    2: required i64 AccessPartyId,      //接入方ID
    3: required i64 AppId,              //应用ID
    4: required string CardName,        //引导语，用于title展示
    5: list<AddCardQuestion> AddCardQuestions,//问题列表，list顺序代表C端展示顺序
    6: optional string CardDisplayName, //卡片展示名称 - 新版必传 向下兼容
}

struct GetCardRequest {
    1: required i64 TenantId,           //租户ID
    2: required list<i64> AppIds,      //应用ID列表
    3: optional i64 AccessPartyId,      //接入方ID
    255: optional base.Base Base,
}

struct GetCardResponse {
    1: list<AppQuestionCardThrift> AppQuestionCards,  //问题卡片列表

    255: base.BaseResp BaseResp,
}

struct GetQuestionByCardIdRequest {
    1: required i64 Id,      //卡片id

    255: optional base.Base Base,
}

struct GetQuestionByCardIdResponse {
    1: optional list<CardQuestionThrift> CardQuestions,  //问题卡片

    255: base.BaseResp BaseResp,
}

struct CreateCardRequest {
    1: required AddAppQuestionCard AddAppQuestionCard,    //不带主键ID的问题卡片对象
    2: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct UpdateCardRequest {
    1: required UpdateAppQuestionCardThrift UpdateAppQuestionCardThrift,    //带有主键ID的问题卡片对象
    2: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct HandleCardResponse {
    1: optional i64 Id, // 卡片id
    255: base.BaseResp BaseResp,
}

struct HandleCardRequest {
    1: required i64 CardId,             //卡片ID
    2: required HandleType HandleType,  //操作类型
    3: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct Department {
    1: i64 ID,
    2: string Name,
    3: i64 ParentId,
    4: i32 AgentNum,
    5: list<Department> ChildrenDepartments,
    6: i32 Level,
    7: list<i64> LeaderIds,
}

struct CreateDepartmentRequest {
    1: required i64 TenantId,
    2: required string Name,
    3: required i64 ParentId,
    4: required i64 OperateAgentId,
    5: optional list<i64> LeaderIds,

    255: optional base.Base Base,
}

struct CreateDepartmentResponse {
    1: i64 ID,

    255: base.BaseResp BaseResp,
}

struct UpdateDepartmentRequest {
    1: required i64 TenantId,
    2: required i64 DepartmentId,
    3: optional i64 ParentId,
    4: optional string Name,
    5: required i64 OperateAgentId,
    6: optional list<i64> LeaderIds,

    255: optional base.Base Base,
}

struct UpdateDepartmentResponse {
    255: base.BaseResp BaseResp,
}

struct DeleteDepartmentRequest {
    1: required i64 TenantId,
    2: required i64 DepartmentId,
    3: required i64 OperateAgentId,

    255: optional base.Base Base,
}

struct DeleteDepartmentResponse {
    255: base.BaseResp BaseResp,
}

struct GetDepartmentResponse {
    1: optional Department Department,
    255: base.BaseResp BaseResp,
}

struct GetDepartmentRequest {
    1: required i64 Id,
    2: required i64 tenantId,
    255: optional base.Base Base,
}

struct GetDepartmentsRequest {
    1: required i64 TenantId,
    2: optional i32 AgentStatus,
    3: optional string AgentName,
    4: optional string AgentEmail,
    5: optional ChannelType ChannelType,

    255: optional base.Base Base,
}

struct GetDepartmentsResponse {
    1: Department rootDepartmentModel,

    255: base.BaseResp BaseResp,
}

struct GetDepartmentsByNameRequest {
    1: required i64 TenantId,
    2: required string DepartmantName,

    255: optional base.Base Base,
}

struct GetDepartmentsByNameResponse {
    1: list<Department> Departments,

    255: base.BaseResp BaseResp,
}

struct FixDepartLevelRequest {
    1: required i64 TenantId,
    255: optional base.Base Base,
}

struct FixDepartLevelResposne {
    255: base.BaseResp BaseResp,
}

struct GetWorkingAgentListRequest {
    1: required i64 TenantId,
    2: required i32 ChannelType,
    255: optional base.Base Base,
}

struct GetWorkingAgentListResponse {
    1: list<i64> AgentList,
    255: base.BaseResp BaseResp,
}

struct GetFirstLoginTimeByAgentListRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> AgentList, // 人员ID列表

    255: optional base.Base Base,
}

struct GetFirstLoginTimeByAgentListResponse {
    1: map<i64, string> AgentToFirstLoginTimeMap,

    255: base.BaseResp BaseResp,
}

struct CleanFirstLoginTimeCacheRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> AgentList, // 人员ID列表

    255: optional base.Base Base,
}

struct CleanFirstLoginTimeCacheResponse {
    1: map<i64, bool> CleanResult,

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,

    255: optional base.Base Base,
}

struct GetWorkStatusResponse {
    1: WorkStatusEnum WorkStatusDetail,
    2: bool IsExist,
    3: i64 StartTime, // 切换到这个状态的时间

    255: base.BaseResp BaseResp,
}

struct BatchGetWorkStatusRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required ChannelType ChannelType,

    255: optional base.Base Base,
}

struct BatchGetWorkStatusResponse {
    1: map<i64, i32> WorkStatusMap,
    2: optional map<i64, string> NotesMap,
    3: optional map<i64, i32> TaskNumMap,

    255: base.BaseResp BaseResp,
}

// 工作状态详情
struct WorkStatusEnum {
    1: i64 TenantId, // 租户ID
    2: i32 WorkStatus, // 工作状态值
    3: string WorkStatusDesc, // 工作状态描述
    4: string WorkStatusTip, // 工作状态提示
    5: bool Enabled, // 是否启用
    6: i32 ReceptionStatus, // 接线状态（0：不可接线；1: 可接线）
    7: i32 ChannelType, // 渠道类型（1：IM；2：工单；3：电话；4：其他；5：电商工单）
    8: bool IsDefault, // 是否为禁用/启用后默认
    9: string CreatedAt, // 创建时间
    10: bool IsLoginDefault, // 是否为登入后默认
    11: bool IsLogoutDefault, // 是否为登出后默认
    12: bool EnableConfig; // 是否可被配置（启用禁用）
    13: optional string NGCCStatus; // 对应的ngcc的status
    14: optional bool NgccEnable; // ngcc 电话外呼组件是否禁用
    15: optional string WorkStatusKey; //staring key
}

struct LoginRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AgentId,

    255: optional base.Base Base,
}

struct LoginResponse {
    1: WorkStatusEnum WorkStatusDetail, // 登入后的工作状态

    255: base.BaseResp BaseResp,
}

struct LogoutRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AgentId,

    255: optional base.Base Base,
}

struct LogoutResponse {
    1: WorkStatusEnum WorkStatusDetail, // 登出后的工作状态

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusDetailRequest {
    1: required i64 TenantId,
    2: required i32 WorkStatus,
    3: required ChannelType ChannelType,

    255: optional base.Base Base,
}

struct GetWorkStatusDetailResponse {
    1: WorkStatusEnum WorkStatusDetail,

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusOptionsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: optional i64 AgentId,
    4: optional list<i64> AccessPartyIds,
    5: optional bool IsEnabled,

    255: optional base.Base Base,
}

struct GetWorkStatusOptionsResponse {
    1: list<WorkStatusEnum> WorkStatusOptions,

    255: base.BaseResp BaseResp,
}


struct WorkStatusConfig {
    1: i64 TenantId, // 租户ID
    2: ChannelType ChannelType, // 坐席类型
    3: i64 AccessPartyId, // 接入方id
    4: i32 WorkStatus, // 工作状态值
    5: string WorkStatusDesc, // 工作状态描述
    6: bool Enabled, // 是否启用
    7: i32 ReceptionStatus, // 接线状态（0：不可接线；1: 可接线）
    8: i64 UpdaterAgentId,  //更新人id
    9: string updaterAgentName, //更新人名字
    10: string UpdatedAt,  // 更新时间
    11: optional string NGCCStatus, // 对应的ngcc的status
    12: optional bool NgccEnable, // 对应的ngcc电话外呼组件是否启用
    13: optional bool EnableConfig, // 是否可被配置（启用禁用）
    14: optional i32 SwitchType, // 0-客服切换, 1-系统切换
    15: optional string WorkStatusKey; //staring key
}

struct GetWorkStatusConfigsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AccessPartyId,

    255: optional base.Base Base,
}

struct GetWorkStatusConfigsResponse {
    1: list<WorkStatusConfig> WorkStatusConfigs,

    255: base.BaseResp BaseResp,
}

struct UpdateWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: required i32 WorkStatus, // 新的工作状态值
    5: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1 默认为AgentId
    6: optional i64 AccessPartyId;//切换动作所在接入方
    7: optional string Note;// 状态切换备注

    255: optional base.Base Base,
}

struct UpdateWorkStatusResponse {
    1: bool Updated, // 是否已更新

    255: base.BaseResp BaseResp,
}

struct BatchUpdateWorkStatusRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required ChannelType ChannelType,
    4: required i32 WorkStatus, // 新的工作状态值
    5: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1  默认为AgentId
    6: optional i64 AccessPartyId;//切换动作所在接入方

    255: optional base.Base Base,
}

struct BatchUpdateWorkStatusResponse {
    255: base.BaseResp BaseResp,
}

struct UpdateWorkStatusConfigRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AccessPartyId,
    4: required i32 WorkStatus,
    5: required bool Enable,
    6: required i64 OperatorAgentId,
    7: optional bool NgccEnable,  // ngcc 外呼组件是否启用，true 为启用

    255: optional base.Base Base,
}

struct UpdateWorkStatusConfigResponse {
    255: base.BaseResp BaseResp,
}

struct CreateAgentTemplateRequest {
    1: required i64 TenantId,
    2: required string AccessKey;

    255: optional base.Base Base,
}

struct CreateAgentTemplateResponse {
    1: string FileURL;

    255: base.BaseResp BaseResp,
}

struct FixMissingEndTimeRequest {
    1: required i64 TenantId,
    2: required string AccessKey,
    3: optional string UntilDateTime,

    255: optional base.Base Base,
}

struct FixMissingEndTimeResponse {
    1: list<i64> FailLogIdList,
    2: i32 SuccessCount,
    3: i32 FailCount,
    4: i32 TotalCount,

    255: base.BaseResp BaseResp,
}

struct IncreaseTaskNumRequest{
    1: required i64 SkillGroupId,
    2: required IncreaseType IncreaseType,
    3: required i64 TenantId,
    4: optional i64 OperatorAgentId,
    5: optional i32 IncreaseNum,

    255: optional base.Base Base,
}

struct IncreaseTaskNumResponse{
    255: base.BaseResp BaseResp,
}

struct CheckAgentInSkillGroupRequest{
    1: required i64 TenantId
    2: required i64 AgentId;
    3: required i64 SkillGroupId;

    255: optional base.Base Base,
}

struct CheckAgentInSkillGroupResponse{
    1: bool InSkillGroup = false; //是否在技能组
    2: bool IsLeader = false; //是否是组长

    255: base.BaseResp BaseResp,
}

struct BatchMoveAgentsSkillGroupsRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: optional list<i64> RemoveSkillGroupIds,
    4: optional list<i64> AddToSkillGroupIds,
    5: optional list<i64> FinalSkillGroupIds,
    6: required i64 OperatorAgentId;
    7: optional ChannelType ChannelType;

    255: optional base.Base Base,
}

struct BatchMoveAgentsSkillGroupsResponse {
    255: base.BaseResp BaseResp,
}

struct CheckSkillGroupAutoAssignRequest {
    1: required i64 TenantId,
    2: required i64 SkillGroupId,

    255: optional base.Base Base,
}

struct CheckSkillGroupAutoAssignResponse {
    1: bool AutoAssign,
    2: list<Agent> UnautoAssignAgents,
    3: i64 SkillGroupAgentsCount,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByUserIdsRequest {
    1: required i64 TenantId,
    2: required list<i64> UserIds

    255: optional base.Base Base,
}

struct GetAgentListByUserIdsResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct UpdateTTPWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: optional i32 CurrWorkStatus, // 原工作状态，提供原子切换能力。可选项
    5: required i32 WorkStatus, // 新的工作状态值
    6: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1 默认为AgentId
    7: optional i64 AccessPartyId;//切换动作所在接入方
    8: optional string Note;// 状态切换备注

    255: optional base.Base Base,
}

struct UpdateTTPWorkStatusResponse {
    255: base.BaseResp BaseResp,
}

struct WorkStatusSyncRequest {
    1: required list<ChannelType> ChannelTypes,

    255: optional base.Base Base,
}

struct WorkStatusSyncResponse {
    255: base.BaseResp BaseResp,
}

service AgentSkillGroupService {
    // 创建组织架构节点
    CreateDepartmentResponse CreateDepartment(1: CreateDepartmentRequest req)

    // 删除组织架构节点
    DeleteDepartmentResponse DeleteDepartment(1: DeleteDepartmentRequest req)

    // 更新组织架构节点
    UpdateDepartmentResponse UpdateDepartment(1: UpdateDepartmentRequest req)

    // 获取组织架构树
    GetDepartmentsResponse GetDepartments(1: GetDepartmentsRequest req)

    // 获取部门信息，目前不返回子部门和客服数信息
    GetDepartmentResponse GetDepartment(1: GetDepartmentRequest req)

    // 根据组织架构名称模糊匹配
    GetDepartmentsByNameResponse GetDepartmentsByName(1: GetDepartmentsByNameRequest req)

    // 初始化和修复department中的level字段
    FixDepartLevelResposne FixDepartLevel(1: FixDepartLevelRequest req)

    // 创建agent
    CreateAgentResponse CreateAgent(1: CreateAgentRequest req)

    // 批量创建agent
    BatchCreateAgentResponse BatchCreateAgent(1: BatchCreateAgentRequest req)

    // 批量删除agent
    BatchDeleteAgentResponse BatchDeleteAgent(1: BatchDeleteAgentRequest req)

    // 批量插入和更新人员信息到ES中
    BatchInsOrUpdtAgentToEsResponse BatchInsOrUpdtAgentToEs(1: BatchInsOrUpdtAgentToEsRequest req)

    // 获取人员的leader
    GetLeadersResponse GetLeaders(1: GetLeadersRequest req)

    // 更新agent
    UpdateAgentResponse UpdateAgent(1: UpdateAgentRequest req)

    // 批量更新agent
    BatchUpdateAgentResponse BatchUpdateAgent(1: BatchUpdateAgentRequest req)

    // 获取id获取agent详情
    GetAgentDetailResponse GetAgentDetail(1: GetAgentDetailRequest req) (api.api_level = '0')

    // 根据UUID获取agent详情
    GetAgentByUUIDResponse GetAgentByUUID(1: GetAgentByUUIDRequest req) (api.api_level = '0')

    // 根据idList获取agent
    GetAgentListByIDsResponse GetAgentListByIDs(1: GetAgentListByIDsRequest req)

    // 根据emailList获取agent,限制数量最多100
    GetAgentListByEmailResponse GetAgentListByEmails(1: GetAgentListByEmailRequest req)

    // 启用客服
    EnableAgentResponse EnableAgent(1: EnableAgentRequest req)

    // 禁用客服
    DisableAgentResponse DisableAgent(1: DisableAgentRequest req)

    // 根据条件，获取agent列表
    GetAgentsByConditionResponse GetAgentsByCondition(1: GetAgentsByConditionRequest req)

    // 根据条件，从ES中查询人员数据，暂时只对电商工单开放
    SearchAgentResponse SearchAgent(1: SearchAgentRequest req)

    // 根据权限获取人员列表
    GetAgentsByPermissionResponse GetAgentsByPermission(1: GetAgentsByPermissionRequest req)

    // 根据操作时间获取人员列表
    GetAgentsByOperateTimeResponse GetAgentsByOperateTime(1: GetAgentsByOperateTimeRequest req)

    // 创建技能组
    CreateSkillGroupResponse CreateSkillGroup(1: CreateSkillGroupRequest req)

    // 更新技能组
    UpdateSkillGroupResponse UpdateSkillGroup(1: UpdateSkillGroupRequest req)

    // 删除技能组
    DeleteSkillGroupResponse DeleteSkillGroup(1: DeleteSkillGroupRequest req)

    // 获取技能组详情
    GetSkillGroupDetailResponse GetSkillGroupDetail(1: GetSkillGroupDetailRequest req) (api.api_level = '0')

    // 根据idList获取技能组列表
    GetSkillGroupsByIdsResponse GetSkillGroupsByIds(1: GetSkillGroupsByIdsRequest req) (api.api_level = '0')

    // 根据类型获取技能组列表
    GetSkillGroupsByTypeResponse GetSkillGroupsByType(1: GetSkillGroupsByTypeResquest req) (api.api_level = '0')

    // 根据标签id获取技能组列表
    GetSkillGroupsByCategoryResponse GetSkillGroupsByCategory(1: GetSkillGroupsByCategoryRequest req) (api.api_level = '0')

    // 批量根据标签id获取技能组列表
    BatchGetSkillGroupsByCategoryResponse BatchGetSkillGroupsByCategory(1: BatchGetSkillGroupsByCategoryRequest req)

    // 技能组添加成员
    AddAgentToSkillGroupResponse AddAgentToSkillGroup(1: AddAgentToSkillGroupRequest req)

    // 技能组删除成员
    DeleteAgentFromSkillGroupResponse DeleteAgentFromSkillGroup(1: DeleteAgentFromSkillGroupRequest req)

    // 获取技能组成员
    GetSkillGroupAgentsResponse GetSkillGroupAgents(1: GetSkillGroupAgentsRequest req) (api.api_level = '0')

    // 批量获取技能组成员
    BatchGetSkillGroupAgentsResponse BatchGetSkillGroupAgents(1: BatchGetSkillGroupAgentsRequest req);

    // 更新技能组下成员的对客数
    UpdateGroupAgentsTaskNumResponse UpdateGroupAgentsTaskNum(1: UpdateGroupAgentsTaskNumRequest req)

    // 根据技能组的名字模糊匹配
    GetSkillGroupsByNameResponse GetSkillGroupByName(1: GetSkillGroupsByNameRequest req)

    //获取全量的技能组
    GetAllSkillGroupsResponse GetAllSkillGroups(1: GetAllSkillGroupsRequest req)

    //获取全量的工组中的技能组
    GetAllWorkingSkillGroupsResponse GetAllWorkingSkillGroups(1: GetAllWorkingSkillGroupsRequest req) (api.api_level = '0')

    //根据客服id获取技能组
    GetSkillGroupsByAgentIdResponse GetSkillGroupsByAgentId(1: GetSkillGroupsByAgentIdRequest req) (api.api_level = '0')

    // 批量根据客服id获取技能组
    BatchGetSkillGroupsByAgentIdsResponse BatchGetSkillGroupsByAgentIds(1: BatchGetSkillGroupsByAgentIdsRequest req)

    //根据接入方获取技能组
    GetSkillGroupsByAccessPartyResponse GetSkillGroupsByAccessParty(1: GetSkillGroupsByAccessPartyRequest req) (api.api_level = '0')

    //根据接入方列表获取技能组
    GetSkillGroupsByAccessPartiesResponse GetSkillGroupsByAccessParties(1: GetSkillGroupsByAccessPartiesRequest req) (api.api_level = '0')

    //根据人员id和接入方，获取技能组
    GetSkillGroupsByAgentAccessPartyResponse GetSkillGroupByAgentAccessParty(1: GetSkillGroupsByAgentAccessPartyRequest req)

    //获取所有公司
    GetAllCompanyResponse GetAllCompany(1: GetAllCompanyRequest req)

    GetAllSkillGroupTagsResponse GetAllSkillGroupTags(1: GetAllSkillGroupTagsRequest req)

    //创建问题卡片
    HandleCardResponse createCard(1: CreateCardRequest req);

    //获取问题卡片
    GetCardResponse getCard(1: GetCardRequest req);

    //获取问题卡片
    GetQuestionByCardIdResponse getQuestionByCardId(1: GetQuestionByCardIdRequest req);

    //更新问题卡片
    HandleCardResponse updateCard(1: UpdateCardRequest req);

    //操作卡片(启用、禁用、删除)
    HandleCardResponse handleCard(1: HandleCardRequest req);

    // 获取可以接单的人员列表
    GetWorkingAgentListResponse GetWorkingAgentList(1: GetWorkingAgentListRequest req) (api.api_level = '0')

    // 获取人员的首次登录时间
    GetFirstLoginTimeByAgentListResponse GetFirstLoginTimeByAgentList(1: GetFirstLoginTimeByAgentListRequest req);

    // 清除人员首次登录时间的缓存
    CleanFirstLoginTimeCacheResponse CleanFirstLoginTimeCache(1: CleanFirstLoginTimeCacheRequest req);

    // 登入
    LoginResponse Login(1: LoginRequest req) (api.api_level = '0')

    // 登出
    LogoutResponse Logout(1: LogoutRequest req);

    // 获取工作状态
    GetWorkStatusResponse GetWorkStatus(1: GetWorkStatusRequest req) (api.api_level = '0')

    // 批量获取工作状态
    BatchGetWorkStatusResponse BatchGetWorkStatus(1: BatchGetWorkStatusRequest req);

    // 获取工作状态的详情
    GetWorkStatusDetailResponse GetWorkStatusDetail(1: GetWorkStatusDetailRequest req);

    // 获取工作状态选项列表
    GetWorkStatusOptionsResponse GetWorkStatusOptions(1: GetWorkStatusOptionsRequest req) (api.api_level = '0')

    // 获取工作状态配置
    GetWorkStatusConfigsResponse GetWorkStatusConfigs(1: GetWorkStatusConfigsRequest req);

    // 更新工作状态
    UpdateWorkStatusResponse UpdateWorkStatus(1: UpdateWorkStatusRequest req) (api.api_level = '0')

    // 批量更新工作状态
    BatchUpdateWorkStatusResponse BatchUpdateWorkStatus(1: BatchUpdateWorkStatusRequest req);

    // 生成最新的人员批量上传模板
    CreateAgentTemplateResponse CreateAgentTemplate(1: CreateAgentTemplateRequest req);

    // 更新工作状态配置
    UpdateWorkStatusConfigResponse UpdateWorkStatusConfig(1: UpdateWorkStatusConfigRequest req);

    // 修复缺失endTime的工作状态日志
    FixMissingEndTimeResponse FixMissingEndTime(1: FixMissingEndTimeRequest req);

    //动态修改人员身上taskNum
    IncreaseTaskNumResponse IncreaseTaskNum(1:required IncreaseTaskNumRequest request);

   //判断客服是否在指定技能组
    CheckAgentInSkillGroupResponse CheckAgentInSkillGroup(1:CheckAgentInSkillGroupRequest req);

    //批量转移人员技能组
    BatchMoveAgentsSkillGroupsResponse BatchMoveAgentsSkillGroups(1: BatchMoveAgentsSkillGroupsRequest req);

    //检查技能组自动分单配置
    CheckSkillGroupAutoAssignResponse CheckSkillGroupAutoAssign(1: CheckSkillGroupAutoAssignRequest req);

    // 工作状态及工作日志同步至多机房同步表
    WorkStatusSyncResponse WorkStatusSync(1: WorkStatusSyncRequest req);

    // 根据userIdList获取agent,限制数据量最多100, 不返回 user_id == 0 的客服
    GetAgentListByUserIdsResponse GetAgentListByUserIds(1: GetAgentListByUserIdsRequest req)

    //供row干预ttp人员状态（ttp内部更新不可用此接口）
    UpdateTTPWorkStatusResponse UpdateTTPWorkStatus(1: UpdateTTPWorkStatusRequest req)
}
