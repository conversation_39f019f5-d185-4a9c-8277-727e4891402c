// common
export const RouteManagePsm = 'ies.fe.route_manage_i18n';
export const RequestParameter = 'RequestParameter';

// datacenter
export const DatacenterSign = 'b72x8k3szf5glo4m';

// response
export const SuccessCode = 0;
export const ErrorCodeNeedParams = 10001;
export const ErrorCodeErrorParamValue = 10002;
export const ErrorCodeNoPermission = 10003;
export const ErrorCodeIsExist = 10004;
export const ErrorCodeDownServiceError = 10005;
export const ErrorCodeUnknow = 10006;

export function DisplayErrorCode(errorCode): [number, string] {
  switch (errorCode) {
    case SuccessCode:
      return [200, 'Success'];
    case ErrorCodeNeedParams:
      return [400, '传入参数不全'];
    case ErrorCodeErrorParamValue:
      return [400, '传入参数值错误'];
    case ErrorCodeNoPermission:
      return [403, '没有权限'];
    case ErrorCodeUnknow:
      return [500, '未知错误'];
    case ErrorCodeIsExist:
      return [500, '该业务下的卡片配置已经存在'];
    case ErrorCodeDownServiceError:
      return [500, '下游服务调用失败'];
    default:
      return [500, '未知错误'];
  }
}

export const titleTextMap = {
  1: '创建规则并启用',
  2: '创建规则并禁用',
  3: '规则状态变更',
  4: '规则优先级变更',
  5: '规则内容变更'
};
export const ruleOpcheckArr = [
  { key: 'LIST_NOT_RETAIN', value: 'not_included', type: 34 },
  { key: 'STRING_NOT_CONTAINS', value: 'not_included', type: 32 },
  { key: 'LIST_NOT_IN', value: 'not_included', type: 22 },
  { key: 'NOT LIST_IN', value: 'not_included', type: 22 },
  { key: 'CONTAINS', value: 'include', type: 3 },
  { key: 'STRING_CONTAINS', value: 'include', type: 13 },
  { key: 'LIST_IN', value: 'include', type: 12 },
  { key: 'LIST_RETAIN', value: 'include', type: 33 },
  { key: '==', value: 'equals', type: 14 },
  { key: '!=', value: 'equals', type: 15 }
];