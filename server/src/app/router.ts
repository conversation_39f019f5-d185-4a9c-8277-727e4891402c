import { HttpApplication } from '@gulu/application-http';
import { PATH_PREFIX } from '../const';

export default (app: HttpApplication): void => {
  const { router, controller } = app;

  // api接口
  router.get(`${PATH_PREFIX}/api/user`, controller.home.getUserInfo);

  // 页面
  router.get(`${PATH_PREFIX}/*`, controller.home.index);

  router.all(`${PATH_PREFIX}/api/*`, (ctx, next) => {
    ctx.tqLogUpload({
      action_type: 'logger',
      action_desc: `${ctx.url} - ${ctx.req}`,
    });
    next();
  });
};
