import * as t from '../../gen_types/idl/http_idl/demo';
/* Auto gen code */

type IBindingConfig = t.IBindingConfig;

class BindingConfigController extends t.Controller implements IBindingConfig {
  async SearchSamBind(ctx: t.Context<t.SearchSamBindRequest>): Promise<t.SearchSamBindResponse> {
    const { req } = ctx.args;
    const res = await ctx.rpc.AdminService.SearchSamBind(req);
    console.log(req);
    return res as t.SearchSamBindResponse;
  }

  async GetSellerInfo(ctx: t.Context<t.GetSellerInfoRequest>): Promise<t.GetSellerInfoResponse> {
    const { req } = ctx.args;
    console.log(req);
    const res = await ctx.rpc.AdminService.GetSellerInfo(req);
    return res as t.GetSellerInfoResponse;
  }

  async BatchExportSamBind(ctx: t.Context<t.BatchExportSamBindRequest>): Promise<t.BatchExportSamBindResponse> {
    const { req } = ctx.args;
    console.log(req);
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.BatchExportSamBind({ ...req, userId: agentUserInfo.Agent.ID });
    return res as t.BatchExportSamBindResponse;
  }

  async BatchDelSamBind(ctx: t.Context<t.BatchDelSamBindRequest>): Promise<t.BatchDelSamBindResponse> {
    const { req } = ctx.args;
    console.log(req);
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.BatchDelSamBind({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.BatchDelSamBindResponse;
  }

  async BatchTransferSamBind(ctx: t.Context<t.BatchTransferSamBindRequest>): Promise<t.BatchTransferSamBindResponse> {
    const { req } = ctx.args;
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.BatchTransferSamBind({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.BatchTransferSamBindResponse;
  }

  async BatchDelSamBindByExcel(
    ctx: t.Context<t.BatchDelSamBindByExcelRequest>
  ): Promise<t.BatchDelSamBindByExcelResponse> {
    const { req } = ctx.args;
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.BatchDelSamBindByExcel({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.BatchDelSamBindByExcelResponse;
  }

  async BatchCreateSamBind(ctx: t.Context<t.BatchCreateSamBindRequest>): Promise<t.BatchCreateSamBindResponse> {
    const { req } = ctx.args;
    console.log(req);
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.BatchCreateSamBind({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.BatchCreateSamBindResponse;
  }

  async UpdateSamBind(ctx: t.Context<t.UpdateSamBindRequest>): Promise<t.UpdateSamBindResponse> {
    const { req } = ctx.args;
    console.log(req);
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    const res = await ctx.rpc.AdminService.UpdateSamBind({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.UpdateSamBindResponse;
  }

  async CreateSamBind(ctx: t.Context<t.CreateSamBindRequest>): Promise<t.CreateSamBindResponse> {
    const { req } = ctx.args;
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: '1',
    });
    console.log(req);
    const res = await ctx.rpc.AdminService.CreateSamBind({
      ...req,
      operateAgentId: agentUserInfo.Agent.ID,
      operateAgentImg: ctx.userInfo.avatar,
    });
    return res as t.CreateSamBindResponse;
  }
}

export default BindingConfigController;
