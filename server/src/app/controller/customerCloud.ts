import * as t from '../../gen_types/idl/http_idl/demo';
import * as constants from '../constants';
type ICustomerCloud = t.ICustomerCloud;
/* Auto gen code */

export default class CustomerCloudController extends t.Controller implements ICustomerCloud {
  async queryBizTypesByAccessPartyID(
    ctx: t.Context<t.QueryBizTypesByAccessPartyIDRequest>
  ): Promise<t.QueryBizTypesByAccessPartyIDResponse> {
    ctx.tqLogUpload({
      action_type: 'queryBizTypesByAccessPartyID',
      action_desc: '根据接入方查询所有业务类型',
    });
    const req = ctx.state[constants.RequestParameter];
    const bizTypeList = await ctx.service.customerCloud.queryBizTypesByAccessPartyID(req.AccessPartyId);
    return ctx.idlResponse(constants.SuccessCode, bizTypeList) as t.QueryBizTypesByAccessPartyIDResponse;
  }
}
