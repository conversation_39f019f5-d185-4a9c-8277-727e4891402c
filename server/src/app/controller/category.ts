import * as t from '../../gen_types/idl/http_idl/demo';
import * as constants from '../constants';
type ICategory = t.ICategory;
/* Auto gen code */

export default class CategoryController extends t.Controller implements ICategory {
  async getResourceList(ctx: t.Context<t.GetResourceListRequest>): Promise<t.GetResourceListResponse> {
    ctx.tqLogUpload({
      action_type: 'getResourceList',
      action_desc: '获取标签资源',
    });
    const req = ctx.state[constants.RequestParameter];
    const resourceList = await ctx.service.category.getResourceList(req.AccessPartyId);
    return ctx.idlResponse(constants.SuccessCode, resourceList) as t.GetResourceListResponse;
  }

  async getCategoryList(ctx: t.Context<t.GetCategoryListRequest>): Promise<t.GetCategoryListResponse> {
    ctx.tqLogUpload({
      action_type: 'getCategoryList',
      action_desc: '获取标签',
    });
    const categoryList = await ctx.service.category.getCategoryList();
    return ctx.idlResponse(constants.SuccessCode, categoryList) as t.GetCategoryListResponse;
  }
}
