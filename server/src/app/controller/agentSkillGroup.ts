import * as t from '../../gen_types/idl/http_idl/demo';
import * as constants from '../constants';
type IAgentSkillGroup = t.IAgentSkillGroup;
/* Auto gen code */

export default class AgentSkillGroupController extends t.Controller implements IAgentSkillGroup {
  async getSkillGroupsByAgentId(
    ctx: t.Context<t.GetSkillGroupsByAgentIdRequest>
  ): Promise<t.GetSkillGroupsByAgentIdResponse> {
    ctx.tqLogUpload({
      action_type: 'getSkillGroups',
      action_desc: '获取技能组',
    });
    const skillGroup = await ctx.service.agentSkillGroup.getSkillGroupsByAccessPartyIds(
      ctx.args.req.AccessPartyIds,
      ctx.args.req.channelType
    );
    return ctx.idlResponse(constants.SuccessCode, skillGroup) as t.GetSkillGroupsByAgentIdResponse;
  }

  async getCard(ctx: t.Context<t.GetCardRequest>): Promise<t.GetCardResponse> {
    ctx.tqLogUpload({
      action_type: 'getCard',
      action_desc: '获取问题分类卡片',
    });
    const skillCard = await ctx.service.agentSkillGroup.getCard(ctx.args.req.AppIds, ctx.args.req.AccessPartyId);
    return ctx.idlResponse(constants.SuccessCode, skillCard) as t.GetCardResponse;
  }

  async createCard(ctx: t.Context<t.CreateCardRequest>): Promise<t.CreateCardResponse> {
    ctx.tqLogUpload({
      action_type: 'createCard',
      action_desc: '创建问题分类卡片',
    });
    const req = ctx.state[constants.RequestParameter];
    const res = await ctx.service.agentSkillGroup.createCard({
      AddAppQuestionCard: req.AddAppQuestionCard,
      AccessPartyId: req.AccessPartyId,
    });

    if (res && res.BaseResp && res.BaseResp.StatusCode === 1) {
      return ctx.idlResponse(constants.ErrorCodeDownServiceError) as t.CreateCardResponse;
    }

    if (res && res.BaseResp && res.BaseResp.StatusCode === 2) {
      return ctx.idlResponse(constants.ErrorCodeIsExist) as t.CreateCardResponse;
    }

    return ctx.idlResponse(constants.SuccessCode) as t.CreateCardResponse;
  }

  async updateCard(ctx: t.Context<t.UpdateCardRequest>): Promise<t.UpdateCardResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'updateCard',
      action_desc: '更新问题分类卡片',
      extra: {
        params: req,
      },
    });
    const res = await ctx.service.agentSkillGroup.updateCard({
      UpdateAppQuestionCardThrift: req.UpdateAppQuestionCardThrift,
    });

    if (res && res.BaseResp && res.BaseResp.StatusCode === 1) {
      return ctx.idlResponse(constants.ErrorCodeDownServiceError) as t.UpdateCardResponse;
    }

    if (res && res.BaseResp && res.BaseResp.StatusCode === 2) {
      return ctx.idlResponse(constants.ErrorCodeIsExist) as t.UpdateCardResponse;
    }

    return ctx.idlResponse(constants.SuccessCode) as t.UpdateCardResponse;
  }

  async handleCard(ctx: t.Context<t.HandleCardRequest>): Promise<t.HandleCardResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'handleCard',
      action_desc: '启用/禁用/删除问题分类卡片',
      extra: {
        params: req,
      },
    });
    const res = await ctx.service.agentSkillGroup.handleCard({
      CardId: req.CardId,
      HandleType: req.HandleType,
    });

    if (res && res.BaseResp && res.BaseResp.StatusCode === 1) {
      return ctx.idlResponse(constants.ErrorCodeDownServiceError) as t.HandleCardResponse;
    }

    return ctx.idlResponse(constants.SuccessCode) as t.HandleCardResponse;
  }

  async checkSkillGroupAutoAssign(
    ctx: t.Context<t.CheckSkillGroupAutoAssignRequest>
  ): Promise<t.CheckSkillGroupAutoAssignResponse> {
    const { req } = ctx.args; // console.log(req);

    try {
      // const res = await ctx.service.agentSkillGroup.checkSkillGroupAutoAssign({
      //   skillGroupId: req.skillGroupIdList,
      // });
      const skillGroupIdList = [...new Set(req.skillGroupIdList)];
      const skillGroupList = await Promise.all(
        skillGroupIdList.map(
          async id =>
            await ctx.service.agentSkillGroup.checkSkillGroupAutoAssign({
              skillGroupId: id,
            })
        )
      ); // console.log(skillGroupList, 'llllll');

      return {
        checkSkillGroupAutoAssign: skillGroupList
          .map(val => ({
            autoAssign: val.AutoAssign,
            unautoAssignAgents: val.UnautoAssignAgents || [],
            skillGroupAgentsCount: val.SkillGroupAgentsCount || '',
            skillGroupId: val.id,
          }))
          .filter(
            val => !(val.autoAssign && val.unautoAssignAgents.length === 0 && Number(val?.skillGroupAgentsCount) > 0)
          ),
        skillGroupList,
        code: 0,
        message: 'success',
      } as t.CheckSkillGroupAutoAssignResponse; // return {} as t.CheckSkillGroupAutoAssignResponse;
    } catch (e) {
      return {
        code: -1,
        message: e || 'error',
      } as t.CheckSkillGroupAutoAssignResponse;
    }
  }

  async GetSkillGroupsByType(ctx: t.Context<t.GetSkillGroupsByTypeResquest>): Promise<t.GetSkillGroupsByTypeResponse> {
    const { req } = ctx.args; // console.log(req);

    const res = await ctx.rpc.AgentSkillGroupService.GetSkillGroupsByType(req);
    const send: t.GetSkillGroupsByTypeResponse = {
      SkillGroups: res.SkillGroups,
      TotalSize: res.TotalSize,
      code: res.BaseResp.StatusCode,
      message: res.BaseResp.StatusMessage,
    };
    return send;
  }

  async GetWorkStatusConfigs(ctx: t.Context<t.GetWorkStatusConfigsRequest>): Promise<t.GetWorkStatusConfigsResponse> {
    const { req } = ctx.args; // console.log(req);
    // console.log('=====GetWorkStatusConfigs==', req);

    const res = await ctx.rpc.AgentSkillGroupService.GetWorkStatusConfigs({
      ...req,
      SupportUnifiedWorkStatus: true,
    });
    const send: t.GetWorkStatusConfigsResponse = {
      WorkStatusConfigs: res.WorkStatusConfigs,
      code: res.BaseResp.StatusCode,
      message: res.BaseResp.StatusMessage,
    };
    return send;
  }

  async GetAllSkillGroups(ctx: t.Context<t.GetAllSkillGroupsRequest>): Promise<t.GetAllSkillGroupsResponse> {
    const { req } = ctx.args;
    console.log(req);
    const res = await ctx.rpc.AgentSkillGroupService.GetAllSkillGroups({
      TenantId: '1',
    });
    const list =
      res.SkillGroups?.filter(item => item.AccessPartyId.includes(req.accessPartyId)).map(skillGroup => ({
        label: skillGroup.Name,
        value: skillGroup.ID,
        ChannelType: skillGroup.ChannelType,
      })) || [];
    const SkillGroupMap = {
      im: list.filter(item => item.ChannelType === t.ChannelType.IM),
      ticket: list.filter(item => item.ChannelType === t.ChannelType.TICKET),
    };
    return ({
      SkillGroupMap,
      BaseResp: res.BaseResp,
    } as unknown) as t.GetAllSkillGroupsResponse;
  }

  async GetSkillGroupAgents(ctx: t.Context<t.GetSkillGroupAgentsRequest>): Promise<t.GetSkillGroupAgentsResponse> {
    const { req } = ctx.args;
    const res = await ctx.rpc.AgentSkillGroupService.GetSkillGroupAgents({
      TenantId: '1',
      SkillGroupId: req.SkillGroupId,
      PageNo: req.PageNo || 1,
      PageSize: req.PageSize || 20,
      ...req,
    });
    return res as t.GetSkillGroupAgentsResponse;
  }

  async GetAgentsByCondition(ctx: t.Context<t.GetAgentsByConditionRequest>): Promise<t.GetAgentsByConditionResponse> {
    const { req } = ctx.args;
    console.log(req);
    const res = await ctx.rpc.AgentSkillGroupService.GetAgentsByCondition({
      ...req,
      TenantId: '1',
      PageNo: 1,
      PageSize: 20,
    });
    return res as t.GetAgentsByConditionResponse;
  }
}
