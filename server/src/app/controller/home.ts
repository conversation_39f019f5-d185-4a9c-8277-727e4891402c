import { Controller } from '@gulu/application-http';
import { getEnv } from '../utils';

export default class HomeController extends Controller {
  async index(ctx): Promise<void> {
    const [agentRes] = await Promise.all([
      ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
        TenantId: '1',
        UUID: ctx.userInfo.uuid,
      }),
    ]);
    ctx.state.$pageData = ctx.security.escapeScript(
      JSON.stringify({
        env: getEnv(),
        agent: {
          ...agentRes.Agent,
        },
        user: {
          ...ctx.userInfo,
        },
      })
    );
    ctx.logger.info(`$pageData: ${JSON.stringify(ctx.state.$pageData, null, 4)}, host: ${ctx.request.host}`);
    await ctx.render('index');
  }

  async getUserInfo(ctx): Promise<void> {
    ctx.successResponse(ctx.userInfo);
  }
}
