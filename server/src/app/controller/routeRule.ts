import * as t from '../../gen_types/idl/http_idl/demo';
import * as constants from '../constants';
import { CachedRequester } from '../utils/cachedRequest';
import { CachedNewRequester } from '../utils/cachedNewRequest';
import { ErrorCodeNoPermission, titleTextMap } from '../constants/index';
import tcc from '@byted-service/tcc';
import { treeNodeDataMap, getItemLog, safeJSONParse } from '../utils/index';
type IRouteRule = t.IRouteRule;
/* Auto gen code */

const appIdToRouteType = (appId: string | number) => {
  switch (appId) {
    case 2:
      return 'IM 人工路由';

    case 3:
      return 'IM 智能路由';

    case 5:
      return '工单路由';

    case 6:
      return '离线路由';

    case 7:
      return '问题分类卡片';

    case 15:
      return '质检路由';

    default:
      return '-';
  }
};

const fieldNewCachedRequester = new CachedNewRequester<t.GetFieldListResponse>('fieldList');
const fieldCachedRequester = new CachedRequester<t.GetFieldListResponse>('fieldList');
/* Auto gen code */
// 判断是否有权限

const haveAuth = async (ctx, permCode, permKey) => {
  try {
    const checkRes = ctx.judgeAccessPartyRights(permCode);

    if (checkRes === false) {
      return false;
    } // tcc 鉴权

    const config = await tcc({
      serviceName: 'ies.fe.route_manage_i18n',
      key: 'accessparty_management_perm',
      version: 2,
      parser: safeJSONParse,
    });
    const agent = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      TenantId: ctx.query.tenantId || '1',
      UUID: ctx.userInfo.uuid,
    });
    const email = agent.Agent.Email; // 权限不存在于 TCC

    if (!config[permKey].includes(email)) {
      return false;
    }

    return true;
  } catch (error) {
    throw new Error('error');
  }
};

export default class RouteRuleController extends t.Controller implements IRouteRule {
  async getFieldList(ctx: t.Context<t.GetFieldListRequest>): Promise<t.GetFieldListResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'getFieldList',
      action_desc: '获取规则列表',
    });
    const { AppIds } = ctx.args.req;
    let appIds = [];

    if (!Array.isArray(AppIds)) {
      appIds = [];
    } else {
      appIds = AppIds;
    }

    ctx.logger.info(`${AppIds} ${req.AppIds}  ====== AppIds`);
    const params = {
      AccessPartyId: req.AccessPartyId,
      EventId: req.EventId,
      AppIds: appIds,
      lang: req.Lang,
    }; // 这个请求太慢了，适当做一下缓存
    // console.log(params, ';;;;');

    const routeRule = await fieldCachedRequester.getValue(
      JSON.stringify(params),
      ctx.service.routeRule.getFieldList(params)
    );
    return ctx.idlResponse(constants.SuccessCode, routeRule) as t.GetFieldListResponse;
  }

  async getFieldValues(ctx: t.Context<t.GetFieldValuesRequest>): Promise<t.GetFieldValuesResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'getFieldValues',
      action_desc: '获取规则 Values',
    });
    const { AppIds } = ctx.args.req;
    ctx.logger.info(`${AppIds} ${req.AppIds}  ====== AppIds`);
    let appIds = [];

    if (!Array.isArray(AppIds)) {
      appIds = [];
    } else {
      appIds = AppIds;
    }

    const fieldValues = await ctx.service.routeRule.getFieldValues({
      FieldId: req.FieldId,
      OperatorId: req.OperatorId,
      AccessPartyId: req.AccessPartyId,
      AppIds: appIds,
      Operator: req.Operator,
    }); // fieldValues = treeNodeData(fieldValues);

    return ctx.idlResponse(constants.SuccessCode, fieldValues) as t.GetFieldValuesResponse;
  }

  async getAdminRuleList(ctx: t.Context<t.GetAdminRuleListRequest>): Promise<t.GetAdminRuleListResponse> {
    const req = ctx.state[constants.RequestParameter] as t.GetAdminRuleListRequest;
    ctx.tqLogUpload({
      action_type: 'getAdminRuleList',
      action_desc: '获取路由规则',
      extra: {
        params: req,
      },
    });
    const adminRuleList = await ctx.service.routeRule.getAdminRuleList({
      AccessPartyId: req.AccessPartyId,
      DisplayNameLike: req.DisplayNameLike,
      EventId: req.EventId,
      SourceId: req.SourceId,
      AppId: req.AppId,
      StopStatus: req.StopStatus,
    });
    return ctx.idlResponse(constants.SuccessCode, adminRuleList) as t.GetAdminRuleListResponse;
  }

  async createAdminRule(ctx: t.Context<t.CreateAdminRuleRequest>): Promise<t.CreateAdminRuleResponse> {
    const req = ctx.state[constants.RequestParameter] as t.CreateAdminRuleRequest;
    ctx.tqLogUpload({
      action_type: 'createAdminRule',
      action_desc: '新建路由规则',
      extra: {
        params: req,
      },
    });
    ctx.service.tqNotify.notify('ruleCreate', {
      accessPartyId: req.AccessPartyId,
      routeName: req.DisplayName,
      routeType: appIdToRouteType(req.AppId),
    });
    await ctx.service.routeRule.createAdminRule({
      AccessPartyId: req.AccessPartyId,
      DisplayName: req.DisplayName,
      Priority: req.Priority,
      Filter: req.Filter,
      Value: req.Value,
      EventId: req.EventId,
      SourceId: req.SourceId,
      AppId: req.AppId,
      CreateCardRequest: req.CreateCardRequest,
      Disable: req.Disable,
    });
    return ctx.idlResponse(constants.SuccessCode) as t.CreateAdminRuleResponse;
  }

  async updateAdminRule(ctx: t.Context<t.UpdateAdminRuleRequest>): Promise<t.UpdateAdminRuleResponse> {
    const req = ctx.state[constants.RequestParameter] as t.UpdateAdminRuleRequest;
    ctx.tqLogUpload({
      action_type: 'updateAdminRule',
      action_desc: '更新编辑路由规则',
      extra: {
        params: req,
      },
    });
    ctx.service.tqNotify.notify('ruleEdit', {
      accessPartyId: req.AccessPartyId,
      routeName: `${req.DisplayName || '-'}, ID: ${req.Id}`,
      routeType: appIdToRouteType(req.AppId),
    });

    try {
      const res = await ctx.service.routeRule.updateAdminRule({
        AccessPartyId: req.AccessPartyId,
        DisplayName: req.DisplayName,
        Priority: req.Priority,
        Filter: req.Filter,
        Value: req.Value,
        Id: req.Id,
        EventId: req.EventId,
        SourceId: req.SourceId,
        AppId: req.AppId,
        StopStatus: req.StopStatus,
        UpdateCardRequest: req.UpdateCardRequest,
      });

      if (res.BaseResp.StatusCode === constants.SuccessCode) {
        return ctx.idlResponse(constants.SuccessCode, res.AdminRule) as t.UpdateAdminRuleResponse;
      } else {
        return ctx.idlResponse(res.BaseResp.StatusCode) as t.UpdateAdminRuleResponse;
      }
    } catch (error) {
      return ctx.idlResponse(constants.ErrorCodeUnknow) as t.UpdateAdminRuleResponse;
    }
  }

  async deleteAdminRule(ctx: t.Context<t.DeleteAdminRuleRequest>): Promise<t.DeleteAdminRuleResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'deleteAdminRule',
      action_desc: '删除路由规则',
      extra: {
        params: req,
      },
    });
    ctx.service.tqNotify.notify('ruleDelete', {
      accessPartyId: req.AccessPartyId,
      routeName: `${req.DisplayName || '-'}, ID: ${req.Id}`,
      routeType: appIdToRouteType(req.AppId),
    });

    try {
      const res = await ctx.service.routeRule.deleteAdminRule({
        AccessPartyId: req.AccessPartyId,
        Id: req.Id,
        SourceId: req.SourceId,
        AppId: req.AppId,
      });

      if (res.BaseResp.StatusCode === constants.SuccessCode) {
        return ctx.idlResponse(constants.SuccessCode) as t.DeleteAdminRuleResponse;
      } else {
        return ctx.idlResponse(res.BaseResp.StatusCode) as t.DeleteAdminRuleResponse;
      }
    } catch (error) {
      return ctx.idlResponse(constants.ErrorCodeUnknow) as t.DeleteAdminRuleResponse;
    }
  }

  async batchUpdateAdminRule(ctx: t.Context<t.BatchUpdateAdminRuleRequest>): Promise<t.BatchUpdateAdminRuleResponse> {
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'batchUpdateAdminRule',
      action_desc: '批量更新路由规则（调整顺序）',
      extra: {
        params: req,
      },
    });
    ctx.service.tqNotify.notify('ruleBatchUpdate', {
      accessPartyId: req.AccessPartyId,
      routeType: appIdToRouteType(req.AppId),
    });

    try {
      const res = await ctx.service.routeRule.batchUpdateAdminRule({
        AccessPartyId: req.AccessPartyId,
        AdminRules: req.AdminRules,
        EventId: req.EventId,
        SourceId: req.SourceId,
        AppId: req.AppId,
      });

      if (res.BaseResp.StatusCode === constants.SuccessCode) {
        return ctx.idlResponse(constants.SuccessCode) as t.BatchUpdateAdminRuleResponse;
      } else {
        return ctx.idlResponse(res.BaseResp.StatusCode) as t.BatchUpdateAdminRuleResponse;
      }
    } catch (error) {
      return ctx.idlResponse(constants.ErrorCodeUnknow) as t.BatchUpdateAdminRuleResponse;
    }
  }

  async getSLAAimMetaSimpleList(
    ctx: t.Context<t.GetSLAAimMetaSimpleListRequest>
  ): Promise<t.GetSLAAimMetaSimpleListResponse> {
    const req = ctx.state[constants.RequestParameter] as t.GetSLAAimMetaSimpleListRequest;
    ctx.tqLogUpload({
      action_type: 'getSLAAimMetaSimpleList',
      action_desc: '获取路由时机',
      extra: {
        params: req,
      },
    });

    try {
      const response = await ctx.service.routeRule.getSLAAimMetaSimpleList({
        SourceId: req.SourceId,
        AccessPartyId: req.AccessPartyId,
      });
      return ctx.idlResponse(constants.SuccessCode, response) as t.GetSLAAimMetaSimpleListResponse;
    } catch (error) {
      throw error;
    }
  }

  async createRule(ctx: t.Context<t.CreateRuleV2Request>): Promise<t.CreateRuleV2Response> {
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: 1,
    });
    const { req } = ctx.args; // console.log(req);

    const serverReq = {
      ...req,
      CreatorAgentId: agentUserInfo.Agent.ID, // AccessPartyId: ctx.headers['x-accesspartyid'] || '',
    }; // console.log(ctx.headers);

    ctx.logger.info('createRule', JSON.stringify(serverReq)); // console.log(ctx.rpc.AdminService);

    const res = await ctx.rpc.AdminService.CreateRule(serverReq);
    const clientRes = {
      code: res.BaseResp.StatusCode,
      message: res.BaseResp.StatusMessage,
      Rule: res.Rule,
    };
    return clientRes as t.CreateRuleV2Response;
  } // 更新规则

  async updateRuleById(ctx: t.Context<t.UpdateRuleRequest>): Promise<t.UpdateRuleResponse> {
    try {
      // 更新规则
      const { req } = ctx.args; // 存在则需要鉴权

      if (req.PermCode) {
        const checkRes = await haveAuth(ctx, req.PermCode, 'edit');

        if (checkRes === false) {
          return {
            code: ErrorCodeNoPermission,
            message: 'no auth',
          };
        }
      }

      const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
        UUID: ctx.userInfo.uuid,
        TenantId: 1,
      });
      const updateReq = { ...req, AgentId: agentUserInfo.Agent.ID };
      ctx.logger.info('updateRuleById', JSON.stringify(updateReq));
      const res = await ctx.rpc.AdminService.UpdateRule(updateReq);
      const clientRes = {
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
        Rule: res.Rule,
      };
      return clientRes as t.UpdateRuleResponse;
    } catch (error) {
      return {
        code: -1,
        message: 'unknown error',
      };
    }
  }

  async getNewRuleList(ctx: t.Context<t.GetRuleListV2Request>): Promise<t.GetRuleListV2Response> {
    const { req } = ctx.args;
    const RuleList = await ctx.rpc.AdminService.GetRuleList({
      EventKey: req.EventKey,
      AccessPartyId: req.AccessPartyId,
      IsDraft: req.IsDraft,
    });

    try {
      const { Agents } = await ctx.rpc.AgentSkillGroupService.GetAgentListByIDs({
        TenantId: '1',
        IDs: RuleList.RuleList.map(k => k.UpdaterAgentId),
      });
      RuleList.RuleList.forEach(o => {
        const agent = (Agents || []).find(k => k.ID === o.UpdaterAgentId);
        o.UpdaterAgentName = agent ? agent.UserName : '';

        if (o.Priority === 9999) {
          const newStr = o.ActionInfo.ReturnValue.Constant.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
          const val = safeJSONParse(unescape(newStr)); // val.skill_group['is_open'] = 1;

          val.is_open = 1;
          o.ActionInfo.ReturnValue.Constant = `'${JSON.stringify(val)}'`;
        }

        return o;
      });
      RuleList.RuleList.forEach(item => {
        if (item.Expression.ConditionGroups.length > 1 && item.Expression.ConditionGroups[1].ConditionGroups) {
          item.Expression.ConditionGroups.shift();
        }
      });
    } catch (error) {} // console.log('==liu new==', RuleList.RuleList);

    return {
      data: RuleList.RuleList,
      code: 200,
      message: 'Request successful',
    } as t.GetRuleListV2Response;
  }

  async getNewRuleListV4(ctx: t.Context<t.GetRuleListV4Request>): Promise<t.GetRuleListV4Response> {
    const { req } = ctx.args;
    const RuleList = await ctx.rpc.AdminService.GetRuleList({
      EventKey: req.EventKey,
      AccessPartyId: req.AccessPartyId,
      IsDraft: req.IsDraft,
      ExtraInfo: req.ExtraInfo,
      statusList: req.statusList,
      ruleName: req.ruleName,
    });

    try {
      const { Agents } = await ctx.rpc.AgentSkillGroupService.GetAgentListByIDs({
        TenantId: '1',
        IDs: RuleList.RuleList.map(k => k.UpdaterAgentId),
      });
      RuleList.RuleList.forEach(o => {
        const agent = (Agents || []).find(k => k.ID === o.UpdaterAgentId);
        o.UpdaterAgentName = agent ? agent.UserName : '';

        if (o.Priority === 9999) {
          const newStr = o.ActionInfo.ReturnValue.Constant.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
          const val = safeJSONParse(unescape(newStr)); // val.skill_group['is_open'] = 1;

          val.is_open = 1;
          o.ActionInfo.ReturnValue.Constant = `'${JSON.stringify(val)}'`;
        }

        return o;
      });
      RuleList.RuleList.forEach(item => {
        if (item.Expression.ConditionGroups.length > 1 && item.Expression.ConditionGroups[1].ConditionGroups) {
          item.Expression.ConditionGroups.shift();
        }
      });
    } catch (error) {} // console.log('==liu new==', RuleList.RuleList);

    return {
      data: RuleList.RuleList,
      existRulesIfNotFilter: RuleList.existRulesIfNotFilter,
      code: 200,
      message: 'Request successful',
    } as t.GetRuleListV2Response;
  }

  async getNewRuleListV3(ctx: t.Context<t.GetRuleListV3Request>): Promise<t.GetRuleListV3Response> {
    const { req } = ctx.args;
    console.log(req);
    return ctx.idlResponse(constants.SuccessCode) as t.GetRuleListV3Response;
  } // 更新规则状态（启用，删除）

  async UpdateRuleStatus(ctx: t.Context<t.UpdateRuleStatusV2Request>): Promise<t.UpdateRuleStatusV2Response> {
    try {
      const { req } = ctx.args; // 存在则需要鉴权

      if (req.PermCode) {
        // 删除鉴权
        const checkRes = await haveAuth(ctx, req.PermCode, 'del');

        if (checkRes === false) {
          return {
            code: ErrorCodeNoPermission,
            message: 'no auth',
          };
        }
      }

      const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
        UUID: ctx.userInfo.uuid,
        TenantId: 1,
      });
      const res = await ctx.rpc.AdminService.UpdateRuleStatus({
        Ids: req.Ids,
        RuleStatus: req.RuleStatus,
        UpdaterAgentId: agentUserInfo.Agent.ID,
        Version: 'v1',
        ruleGroupId: req.ruleGroupId,
        operateGroupAllRules: req.operateGroupAllRules,
      });
      return {
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
    } catch (error) {
      return {
        code: -1,
        message: 'unknown error',
      };
    }
  }

  async UpdateRulePriority(ctx: t.Context<t.UpdateRulePriorityV2Request>): Promise<t.UpdateRulePriorityV2Response> {
    const { req } = ctx.args;
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: 1,
    });
    const res = await ctx.rpc.AdminService.UpdateRulePriority({
      Rules: req.Rules,
      UpdaterAgentId: agentUserInfo.Agent.ID,
      Version: 'v1',
    });
    return {
      code: res.BaseResp.StatusCode,
      message: res.BaseResp.StatusMessage,
    } as t.UpdateRulePriorityV2Response;
  } // 发布

  async PublishRuleGroup(ctx: t.Context<t.PublishRuleGroupV2Request>): Promise<t.PublishRuleGroupV2Response> {
    const { req } = ctx.args;

    try {
      // 存在则需要鉴权
      if (req.PermCode) {
        const checkRes = await haveAuth(ctx, req.PermCode, 'publish');

        if (checkRes === false) {
          return {
            code: ErrorCodeNoPermission,
            message: 'no auth',
          };
        }
      }

      const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
        UUID: ctx.userInfo.uuid,
        TenantId: 1,
      });
      const res = await ctx.rpc.AdminService.PublishRuleGroup({
        RuleGroupId: req.RuleGroupId,
        UpdaterAgentId: agentUserInfo.Agent.ID,
        RuleIds: req.RuleIds,
        eventKey: req.eventKey,
      });
      return {
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      } as t.PublishRuleGroupV2Response;
    } catch (error) {
      return {
        code: -1,
        message: 'unknown error',
      } as t.PublishRuleGroupV2Response;
    }
  }

  async CopyRuleGroup(ctx: t.Context<t.CopyRuleGroupV2Request>): Promise<t.CopyRuleGroupV2Response> {
    const agentUserInfo = await ctx.rpc.AgentSkillGroupService.GetAgentByUUID({
      UUID: ctx.userInfo.uuid,
      TenantId: 1,
    });
    const { req } = ctx.args;
    await ctx.rpc.AdminService.CopyRuleGroup({
      RuleGroupId: req.RuleGroupId,
      UpdaterAgentId: agentUserInfo.Agent.ID,
    });
    return {
      code: 200,
      message: 'Request successful',
    } as t.CopyRuleGroupV2Response;
  }

  async GetRuleOperationLogs(ctx: t.Context<t.GetRuleOperationLogsRequest>): Promise<t.GetRuleOperationLogsResponse> {
    const { req } = ctx.args;
    const params = {
      AccessPartyId: req.accessPartyId,
      EventId: req.eventId,
    }; // 这个请求太慢了，适当做一下缓存

    const routeRuleFieldList = await fieldCachedRequester.getValue(
      JSON.stringify(params),
      ctx.service.routeRule.getFieldList(params)
    );
    const fieldListMap = treeNodeDataMap(routeRuleFieldList); // console.log(fieldListMap, 'routeRuleFieldList', params);

    let res;

    try {
      res = await ctx.rpc.AdminService.GetRuleOperationLogs({
        RuleId: req.ruleId,
        Page: req.page,
        PageSize: req.pageSize,
      }); // console.log(res);
    } catch (error) {
      return {
        RuleOperationLogList: [],
        totalCount: '0',
        BaseResp: {
          StatusMessage: 'error',
          StatusCode: 1,
        },
      };
    }

    if (!res?.RuleOperationLogList?.length) {
      return {
        RuleOperationLogList: [],
        totalCount: '0',
        BaseResp: {
          StatusMessage: 'success',
          StatusCode: 0,
        },
      };
    }

    try {
      const agenList = []; // const agenIdMap = {};

      const skillGroup = [];
      const agentListMap = {};
      const skillGroupMap = {};
      const list = res.RuleOperationLogList.map(val => {
        const log = {
          title: '',
          updateTime: '',
          agentName: '',
          logList: [],
          agentId: '',
        };
        log.title = titleTextMap[val.OperationType];
        log.updateTime = val.CreatedAt;
        log.agentId = val.OperatorAgentId;
        agenList.push(val.OperatorAgentId);
        log.logList = val?.OperateRuleItemLogs?.map(itemlog => {
          const changItem = {
            ValueChange: [],
            ConditionsChange: [],
            ConditionRelationChange: [],
            groupsRelationChange: '',
            status: '',
            name: '',
            priority: '',
            retutnValue: null,
            OperateItemType: itemlog.OperateItemType,
            theTiming: null,
            theOverflow: null,
            automaticShunt: null,
            manualShunt: null,
          };
          const initLog = getItemLog(
            itemlog.ConditionGroupChangeList,
            fieldListMap,
            itemlog.OperateItemType,
            itemlog.AfterValue
          );

          switch (itemlog.OperateItemType) {
            case 1:
              changItem.status = itemlog.AfterValue;
              break;

            case 2:
              changItem.name = `name：【 ${itemlog.BeforeValue} 】to【 ${itemlog.AfterValue} 】`;
              break;

            case 3:
              changItem.priority = `【 ${itemlog.BeforeValue} 】move to【 ${itemlog.AfterValue} 】`;
              break;

            case 4:
              changItem.ConditionsChange = initLog?.ConditionsChange;
              break;

            case 5:
              changItem.ValueChange = initLog.ValueChange;
              break;

            case 6:
              changItem.ConditionRelationChange = initLog.ConditionRelationChange;
              break;

            case 7:
              try {
                const groupsRelationValue = safeJSONParse(itemlog.AfterValue);
                const count = groupsRelationValue.conditionGroupsCount;

                for (let index = 0; index < count; index++) {
                  changItem.groupsRelationChange += `【 规则组${index + 1} 】${groupsRelationValue.relation}`;
                }
              } catch (error) {
                changItem.groupsRelationChange = '';
              }

              break;

            case 8:
              try {
                const before = safeJSONParse(itemlog.BeforeValue, {});
                const after = safeJSONParse(itemlog.AfterValue, {});
                let beforeRetutnValue = [];
                let afterRetutnValue = [];

                if (before?.autoShuntSkillList) {
                  beforeRetutnValue = (before?.autoShuntSkillList || []).map(beforeItem => {
                    skillGroup.push(beforeItem);
                    return {
                      value: beforeItem,
                    };
                  });
                  afterRetutnValue = (after?.autoShuntSkillList || [])?.map(beforeItem => {
                    skillGroup.push(beforeItem);
                    return {
                      value: beforeItem,
                    };
                  });
                } else {
                  beforeRetutnValue = Array.isArray(before) ? before : [before];
                  afterRetutnValue = Array.isArray(after) ? after : [after];
                  beforeRetutnValue = beforeRetutnValue.map(beforeItem => {
                    skillGroup.push(beforeItem.value);
                    return { ...beforeItem };
                  });
                  afterRetutnValue = afterRetutnValue.map(beforeItem => {
                    skillGroup.push(beforeItem.value);
                    return { ...beforeItem };
                  });
                }

                changItem.retutnValue = {
                  beforeValue: beforeRetutnValue,
                  afterValue: afterRetutnValue,
                };
              } catch (error) {
                changItem.retutnValue = {
                  beforeValue: [],
                  afterValue: [],
                };
              }

              break;

            case 9:
              try {
                const before = safeJSONParse(itemlog.BeforeValue || '[]');
                const after = safeJSONParse(itemlog.AfterValue || '[]');
                const beforeRetutnValue = Array.isArray(before) ? before : [before];
                const afterRetutnValue = Array.isArray(after) ? after : [after]; // console.log(beforeRetutnValue, afterRetutnValue);

                changItem.theTiming = {
                  beforeValue: beforeRetutnValue,
                  afterValue: afterRetutnValue,
                };
              } catch (error) {
                changItem.theTiming = {
                  beforeValue: [],
                  afterValue: [],
                };
              }

              break;

            case 10:
              try {
                const before = safeJSONParse(itemlog.BeforeValue, {});
                const after = safeJSONParse(itemlog.AfterValue, {});
                before?.skill_group_overflow?.map(beforeItem => {
                  skillGroup.push(beforeItem.id);
                });
                after?.skill_group_overflow?.map(beforeItem => {
                  skillGroup.push(beforeItem.id);
                });
                changItem.theOverflow = {
                  beforeValue: before,
                  afterValue: after,
                };
              } catch (error) {
                changItem.theOverflow = {
                  beforeValue: {},
                  afterValue: {},
                };
              }

              break;

            case 11:
              try {
                const after = safeJSONParse(itemlog.AfterValue, []);
                const before = safeJSONParse(itemlog.BeforeValue, []);
                let newAfter: any = [];
                let newBefore: any = [];

                if (after?.autoShuntSkillList && after?.autoShuntSkillList !== null) {
                  newAfter = (after?.autoShuntSkillList || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                } else {
                  newAfter = (after || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                }

                if (before?.autoShuntSkillList && before?.autoShuntSkillList !== null) {
                  newBefore = (before?.autoShuntSkillList || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                } else {
                  newBefore = (before || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                }

                changItem.automaticShunt = {
                  beforeValue: newBefore,
                  afterValue: newAfter,
                };
              } catch (error) {
                changItem.automaticShunt = {
                  beforeValue: [],
                  afterValue: [],
                };
              }

              break;

            case 12:
              try {
                const after = safeJSONParse(itemlog.AfterValue, []);
                const before = safeJSONParse(itemlog.BeforeValue, []);
                let newAfter: any = [];
                let newBefore: any = [];

                if (after?.autoShuntSkillList) {
                  newAfter = (after?.autoShuntSkillList || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                } else {
                  newAfter = (after || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                }

                if (before?.autoShuntSkillList) {
                  newBefore = (before?.autoShuntSkillList || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                } else {
                  newBefore = (before || [])?.map(beforeItem => {
                    if (beforeItem) {
                      if (beforeItem?.value) {
                        skillGroup.push(beforeItem?.value);
                        return { ...beforeItem };
                      } else {
                        skillGroup.push(beforeItem);
                        return {
                          value: beforeItem,
                        };
                      }
                    } else {
                      return '';
                    }
                  });
                }

                changItem.manualShunt = {
                  beforeValue: newBefore,
                  afterValue: newAfter,
                };
              } catch (error) {
                changItem.manualShunt = {
                  beforeValue: [],
                  afterValue: [],
                };
              }

              break;

            default:
              changItem;
          }

          return changItem;
        });
        return log;
      });

      if (agenList.length) {
        const { Agents } = await ctx.rpc.AgentSkillGroupService.GetAgentListByIDs({
          TenantId: '1',
          IDs: Array.from(new Set(agenList)),
        });
        Agents.map(val => {
          if (!agentListMap[val.ID]) {
            agentListMap[val.ID] = val.UserName;
          }
        });
      }

      if (skillGroup.length) {
        // console.log(skillGroup, 'llll');
        const { SkillGroups } = await ctx.rpc.AgentSkillGroupService.GetSkillGroupsByIds({
          TenantId: '1',
          Ids: [...new Set(skillGroup)],
        }); // console.log(SkillGroups);

        SkillGroups.map(val => {
          if (!skillGroupMap[val.ID]) {
            skillGroupMap[val.ID] = val.Name;
          }
        });
      }

      list.forEach(ele => {
        ele.agentName = agentListMap[ele?.agentId];
        ele?.logList?.forEach(val => {
          if ([8, 9, 10, 11, 12].includes(val?.OperateItemType)) {
            console.log(JSON.stringify(val), '222222 ==== val');
            val?.retutnValue?.beforeValue?.map(v => {
              v.skillGroupName = skillGroupMap[v?.value] || '';
            });
            val?.retutnValue?.afterValue?.map(v => {
              v.skillGroupName = skillGroupMap[v?.value] || '';
            });
            val?.automaticShunt?.afterValue?.map(v => {
              v.skillGroupName = skillGroupMap[v?.value] || '';
            });
            val?.automaticShunt?.beforeValue?.map(v => {
              if (v) {
                v.skillGroupName = skillGroupMap[v?.value] || '';
              }
            });
            val?.manualShunt?.afterValue?.map(v => {
              v.skillGroupName = skillGroupMap[v?.value] || '';
            });
            val?.manualShunt?.beforeValue?.map(v => {
              v.skillGroupName = skillGroupMap[v?.value] || '';
            });
            val?.theOverflow?.beforeValue?.skill_group_overflow?.map(v => {
              v.skillGroupName = skillGroupMap[v?.id] || '';
            });
            val?.theOverflow?.afterValue?.skill_group_overflow?.map(v => {
              v.skillGroupName = skillGroupMap[v?.id] || '';
            });
          }
        });
      });
      return {
        RuleOperationLogList: list,
        oldres: res.RuleOperationLogList,
        totalCount: res.totalCount,
        BaseResp: {
          StatusMessage: 'success',
          StatusCode: 0,
        },
      };
    } catch (error) {
      console.log(error, '2222222 ==== error');
      ctx.logger.error(`GetRuleOperationLogsError: ${JSON.stringify(error)}`);
      return {
        RuleOperationLogList: [],
        totalCount: '0',
        BaseResp: {
          StatusMessage: 'success',
          StatusCode: 0,
        },
      };
    }
  }

  async GetSkillGroupsByAccessParties(
    ctx: t.Context<t.GetSkillGroupsByAccessPartiesRequest>
  ): Promise<t.GetSkillGroupsByAccessPartiesResponse> {
    const { req } = ctx.args;
    console.log(req);
    const res = await ctx.rpc.AgentSkillGroupService.GetSkillGroupsByAccessParties({
      TenantId: req.TenantId,
      ChannelType: req.ChannelType,
      AccessPartyIds: req.AccessPartyIds,
    });
    return {
      SkillGroups: res.SkillGroups,
      BaseResp: res.BaseResp,
    };
  }

  async GetRuleGroupListByEventKey(
    ctx: t.Context<t.GetRuleGroupListByEventKeyRequest>
  ): Promise<t.GetRuleGroupListByEventKeyResponse> {
    const { req } = ctx.args;

    try {
      console.log('ctx.rpc.AdminService-----', ctx.rpc.AdminService.GetRuleGroupListByEventKey);
      const res = await ctx.rpc.AdminService.GetRuleGroupListByEventKey(req);

      if ((res.RuleGroupList || []).length !== 0) {
        const { Agents } = await ctx.rpc.AgentSkillGroupService.GetAgentListByIDs({
          TenantId: '1',
          IDs: res.RuleGroupList.map(k => k.UpdaterAgentId),
        });
        res.RuleGroupList.forEach(o => {
          const agent = (Agents || []).find(k => k.ID === o.UpdaterAgentId);
          o.updaterName = agent ? agent.UserName : '';
          return o;
        });
      }

      const send: t.GetRuleGroupListByEventKeyResponse = {
        RuleGroupList: res.RuleGroupList || [],
        Count: res.Count || 0,
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
      return send;
    } catch (err) {
      // console.log(err, 'GetRuleGroupListByEventKey=====>');
    }
  }

  async GetExtraInfoV2(ctx: t.Context<t.GetExtraInfoRequestV2>): Promise<t.GetExtraInfoResponseV2> {
    const { req } = ctx.args;
    console.log(req);

    try {
      const res = await ctx.rpc.AdminService.GetExtraInfoV2(req);
      const send: t.GetExtraInfoResponseV2 = {
        RuleGroupRelations: res.RuleGroupRelations || [],
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
      return send;
    } catch (err) {
      console.log(err, 'res=====>');
    }
  }

  async getNewFieldList(ctx: t.Context<t.GetFieldListRequest>): Promise<t.GetFieldListResponse> {
    // console.log('==ctx req===', ctx);
    const req = ctx.state[constants.RequestParameter];
    ctx.tqLogUpload({
      action_type: 'getNewFieldList',
      action_desc: '获取规则列表',
    }); //

    ctx.logger.info('getNewFieldList---req'); // console.log('getNewFieldList---req', req);

    const { AppIds } = ctx.args.req;
    const params = {
      AccessPartyId: req.AccessPartyId,
      EventId: req.EventId,
      zjOtherAccessPartyId: req.ZjOtherAccessPartyId || '',
      // AccessPartyId: 7,
      // EventId: 16,
      AppIds,
    }; // 这个请求太慢了，适当做一下缓存
    // console.log('getNewFieldList-------', params);

    const routeRule = await fieldNewCachedRequester.getNewValue(
      JSON.stringify(params),
      ctx.service.routeRule.getNewFieldList(params)
    );
    ctx.logger.info('==liu routeRule===', routeRule);
    console.log('==liu routeRule===', routeRule);
    return ctx.idlResponse(constants.SuccessCode, routeRule) as t.GetFieldListResponse;
  }

  async BatchCreateRuleGroup(ctx: t.Context<t.BatchCreateRuleGroupRequest>): Promise<t.BatchCreateRuleGroupResponse> {
    const { req } = ctx.args;
    console.log(req);

    try {
      const res = await ctx.rpc.AdminService.BatchCreateRuleGroup(req);
      const send: t.BatchCreateRuleGroupResponse = {
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
      return send;
    } catch (err) {
      console.log(err, 'res=====>');
    }
  }

  async UpdateRuleGroupStatus(
    ctx: t.Context<t.UpdateRuleGroupStatusRequest>
  ): Promise<t.UpdateRuleGroupStatusResponse> {
    const { req } = ctx.args; // console.log(req);

    try {
      const res = await ctx.rpc.AdminService.UpdateRuleGroupStatus(req);
      const send: t.UpdateRuleGroupStatusResponse = {
        Success: res.Success,
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
      return send;
    } catch (err) {
      console.log(err, 'res=====>');
    }
  }

  async BatchUpdateRuleGroup(ctx: t.Context<t.BatchUpdateRuleGroupRequest>): Promise<t.BatchUpdateRuleGroupResponse> {
    const { req } = ctx.args; // console.log(req);

    try {
      const res = await ctx.rpc.AdminService.BatchUpdateRuleGroup(req);
      const send: t.BatchUpdateRuleGroupResponse = {
        Success: res.Success,
        code: res.BaseResp.StatusCode,
        message: res.BaseResp.StatusMessage,
      };
      return send;
    } catch (err) {
      console.log(err, 'res=====>');
    }
  }
}
