include "../../base.thrift"
include "../route/agent_skill_group_i18n.thrift"
include "../route/router_sim_i18n.thrift"
namespace java com.bytedance.ies.lego.admin.thrift
namespace go bytedance.ieslego.admin

//规则任务状态
enum RuleTaskStatus {
    HAS_RUNNING = 1,//运行中
    NO_RUNNING = 0,//无运行中
}
//规则禁用状态
enum RuleStopStatus {
    DISABLED = 1,//禁用
    USING = 0,//启用
}
//规则状态
enum EntityStatus {
    ENABLE = 1,//可用
    UNABLE = 0,//不可用
    WAIT_ENABLE = -1,//创建中
}
//时间单位
enum TimeUnit {
    MINUTE = 1,//分钟
    HOUR = 2,//小时
    SECOND = 3,//秒
}
//动作时间类型
enum ActionTimeType {
    BEFORE = 1,//目标前
    AFTER = 0,//目标后
}
//动作类型
enum ActionWay {
    LARK = 1,//飞书
    LARK_URGENT = 2,//飞书加急
    LARK_URGENT_MSG = 3,//飞书短信加急
    LARK_URGENT_PHONE = 4,//飞书电话加急
    LARK_GROUP = 5,//飞书到群（场控用）
    EMAIL = 6,//邮件
    E_MESSAGE = 7,//电商站内信角标
    E_MESSAGE_POP = 8,//电商站内信弹窗
    E_LARK = 9,//电商飞书
    E_LARK_URGENT = 10,//电商飞书加急
    E_LARK_URGENT_MSG = 11,//电商飞书短信加急
    E_LARK_URGENT_PHONE = 12,//电商飞书电话加急
    E_LARK_GROUP = 13,//电商飞书到群
    MESSAGE_ALTER = 14,//工单站内信弹窗（场控用）
}
//条件操作类型
enum FilterOperateType {
    UNION = 0,//求并集
    INTERSECTION = 1,//求交集
    LEVEL_COMPUTE = 3, //组合计算
}
//组合计算层级操作类型
enum LevelFilterOperateType {
    UNION = 0,//求并集
    INTERSECTION = 1,//求交集
}
//操作类型
enum OperatorType {
    NUMBER_EQUAL = 1;
    NUMBER_NOT_EQUAL = 2;
    CONTAINS = 3;
    GREATER = 4;
    LESS = 5;
    GREATER_OR_EQUAL = 6;
    LESS_OR_EQUAL = 7;
    START_WITH = 8;
    END_WITH = 9;
    IS_NULL = 10;
    IS_NOT_NULL = 11;
    IN = 12;
    STRING_CONTAINS = 13;
    STRING_EQUAL = 14;
    STRING_NOT_EQUAL = 15;
    TIME_EQUAL = 16;
    TIME_NOT_EQUAL = 17;
    TIME_GREATER = 18;
    TIME_LESS = 19;
    TIME_GREATER_OR_EQUAL = 20;
    TIME_LESS_OR_EQUAL = 21;
    NOT_IN = 22;
    LIST_EQUAL = 30;
    LIST_NOT_EQUAL = 31;
    STRING_NOT_CONTAINS = 32;
    LIST_RETAIN = 33;
}
// 字段取值方式
enum FieldFetchWay {
    Null = 0;   // 为兼容老字段，该值不起作用
    ExprList = 1;
    SubstringList = 2;
    MathExpr = 3;
    FuncExpr = 4;
    FeatureExpr = 5;
    VarExpr = 6;
    ConstantList = 7;
    Constant = 8;
}
//字段值类型
enum FieldDataType {
    INTEGER = 1;
    LONG = 2;
    STRING = 3;
    LIST = 4;
    DOUBLE = 5;
    BOOLEAN = 6;
    DATE_TIME = 7;
}

struct FieldValue {
    1: required i64 Id
    2: required string Name
    3: required i32 ValueType
    4: required i64 FieldId
    5: required string ValueJson
    6: optional string Creator
    7: optional string CreateTime
    8: optional string Updater
    9: optional string UpdateTime
    10: optional list<FieldValue> children,
    11: optional string value,
}

// 字段显示类型
enum FieldDisplayType {
    //条件字段
    FILTER = 1;
    //方法参数
    PARAMETER = 2;
    //占位符
    PLACEHOLDER = 3;
    //动作列表
    ACTION_LIST = 4;
    //函数参数
    FUNC_PARAM = 5;
}

// 组件类型
enum FieldOptionType {
    //单选
    SINGLE_CHOOSE = 1;
    //多选
    MULTI_CHOOSE = 2;
    //混合(多选+输入框)
    MIX = 3;
    //时间控件
    DATE = 4;
    //输入框
    INPUT = 5;
    //批量输入
    BATCH_INPUT = 6;
    //树
    TREE = 7;
    //级联
    CASCADER = 8;
    //多行文本
    TEXT = 9;
    //数字
    INT = 10;
    //常量
    CONSTANT = 11;
    //富文本
    RICH_TEXT = 12;
    //多语言文本
    MULTI_LANG = 13;
    //时间点
    DATE_TIME_POINT = 14;
    //时间段
    TIME_PICKER_RANGE = 15;
    //条件级联下拉列表
    CONDITION_CASCADER_LIST = 16
    // 浮点数输入框
    INPUT_FLOAT = 17;
    //树状结构转换为列表结构
    TREE_TO_LIST = 701;
    //坐席搜索
    AGENT_SEARCH = 101;
    //纯文本带占位符
    TEXT_PLACEHOLDER = 901;
    // 时间范围组件
    TIME_RANGE = 801;
    // 星期时间范围组件
    RANGE_TIME_RANGE = 802;
}

//filter字段
struct FieldMeta {
    1: required i64 Id,
    2: required string DisplayName, //字段的前端展示名称
    3: required string MapName, //字段的数据库存储名称
    4: required list<i32> OperatorIds,
    5: required i64 ModelId,
    6: required i32 Type,
    7: required i32 DataType,
    8: optional string OriginName,
    9: optional string CurrentName,
    10: optional string FieldPath,
    11: optional string ComputeFunc,
    12: optional string Scene,
    13: optional i64 ParentId,
    14: optional i64 TenantId, //租户ID
    15: optional i64 AccessPartyId,
    16: required string CreatedAt,  //创建时间
    17: required string UpdatedAt,  //更新时间
    18: optional string Extra,  //附加信息
    19: required i64 CreatorAgentId,  //创建人ID
    20: required i64 UpdaterAgentId,  //更新人ID
    21: required string AccessPartyIds, //接入方ID
    22: required FieldFetchWay FieldFetchWay,
    23: required FieldDataType FieldDataType,
    24: required string AppKey, //页面标识
    25: required string Part, //页面div标识
    26: required FieldDisplayType FieldDisplayType,
    27: required list<FieldOperatorOptions> FieldOperatorOptionsList,
}
//字段simple
struct FieldIntro {
    1: required string DisplayName, //字段的前端展示名称
    2: required string MapName, //字段的数据库存储名称
    3: required FieldFetchWay FieldFetchWay,
    4: required list<FieldOperatorOptions> FieldOperatorOptionsList,
    5: optional i64 ParentId,
    6: optional i32 Sort,   //展示顺序
    7: optional string DisplayNameStarlingKey, //字段的前端展示名称StarlingKey
    8: optional string DisplayNameByLang, //字段的前端展示名称(支持多语)
}
//filter字段
struct FieldCondition {
    1: required i64 FieldId,
    2: required string FieldDisplayName, //字段的前端展示名称
    3: required string FieldMapName, //字段的数据库存储名称。即将废弃
    4: required list<i32> OperatorIds,// 即将废弃
    5: optional string ComputeFunc,//字段的计算表达式
    6: optional string FieldName, //admin改造后的字段名，即规则实际运行需要的字段
    7: optional list<string> OperatorList, //字段运行使用的操作符
    8: optional i32 FieldDataType, //字段类型 integer = 1 long = 2 string = 3 double = 5 boolean = 6
    9: optional FieldIntro FieldIntro,
    10: optional string FieldNameStarlingKey, //字段的前端展示名称StarlingKey
    11: optional string FieldNameByLang, //字段的前端展示名称(支持多语)
    12: optional map<string,string> ExtraInfo
    13: optional string Creator
    14: optional string Updater
    15: optional string CreateAt
    16: optional string UpdateAt
    17: optional string AppKey
    18: optional string ExtraStr    // 用';'分割的额外属性
    19: optional string ModelId
    20: optional list<i64> AccessPartyIds
    21: optional string Part,
}

//动作形参元数据
struct FormalParamMeta {
    1: required string DisplayName, //前端展示名称
    2: required string MapName, //数据库存储名称
    3: required FieldFetchWay FieldFetchWay,
    4: required FieldOptionType FieldOptionType,
    5: optional string FieldValues,  //具体取值：字段值的json串；获取值的URL
    6: optional string Placeholders,
    7: optional i64 FieldId,
    8: optional i32 Sort,   //展示顺序
    9: optional FieldDataType FieldDataType,
    10: optional FunctionMeta FunctionMeta,
    11: optional string DisplayNameStarlingKey, //字段的前端展示名称StarlingKey
    12: optional string DisplayNameByLang, //字段的前端展示名称(支持多语)
}

//动作元数据
struct ActionMeta {
    1: required i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required string MapName, //数据库存储名称
    4: required list<FormalParamMeta> FormalParamMetas, //形参名称及其类型、可取值
    5: optional list<ActionMeta> actionMetaList,
    6: optional string Operation, // 第三方接口method
    7: optional string DisplayNameStarlingKey, //字段的前端展示名称StarlingKey
    8: optional string DisplayNameByLang, //字段的前端展示名称(支持多语)
}

struct FunctionMeta {
    1: required string MapName, //名称
    2: required list<FormalParamMeta> FormalParamMetas, //形参名称及其类型、可取值
}

//占位符元数据
struct PlaceholderMeta {
    1: required string DisplayName, //前端展示名称
    2: required string MapName, //数据库存储名称
    3: required FieldFetchWay FieldFetchWay,
}
//依赖字段元数据
struct DependencyFieldMeta {
    1: optional i64 Id,
    2: required string DisplayName, //前端展示名称
    3: required string MapName, //数据库存储名称
}
//SLA目标元数据
struct SLAAimMeta {
    1: required i64 Id, //ID
    2: required string Name, //名称 首响、完结
    3: required Filter Filter, //条件
    4: required list<ActionMeta> ActionMetas, //目标支持的全部动作及其形参 TODO 目前不区分目标
    5: required list<PlaceholderMeta> PlaceholderMetas, //目标支持的全部占位符 TODO 目前不区分目标
    6: required list<DependencyFieldMeta> DependencyFieldMetas,    //目标支持的全部规则依赖字段 TODO 目前不区分目标
    7: required EntityStatus Status,  //状态 1：可用；0：不可用
    8: required i64 TenantId, //租户ID
    9: optional i64 AccessPartyId, //接入方ID
    10: optional i64 SourceId,    //业务标识，区分工单、电商工单
    11: required string CreatedAt,  //创建时间
    12: required string UpdatedAt,  //更新时间
    13: required string Extra,  //附加信息
    14: required i64 CreatorAgentId,  //创建人ID
    15: required i64 UpdaterAgentId,  //更新人ID
}
//SLA目标元数据
struct SLAAimMetaSimple {
    1: required i64 Id, //ID
    2: required string Name, //名称 首响、完结
    3: required string Extra,  //附加信息
}
//单个字段条件
struct FilterUnit {
    1: optional i64 FieldId,
    2: required string FieldMapName,//todo 自定义字段前缀
    3: required i32 OperatorId,//todo 前端映射ID
    4: required string FieldValue,//json格式，如"[\"38\",\"40\"]"
}
//条件
struct Filter {
   /*
    * 原子计算，对FilterUnitList结果进行或/与
    */
    1: required FilterOperateType OperateType,
    2: required list<FilterUnit> FilterUnitList,
    3: optional string Extra,  //级联关系用

    /*
     * 如果OperateType为LEVEL_COMPUTE，代表为组合计算，需要组合下一层Filter的计算结果
     */
    4: optional LevelFilterOperateType LevelOperateType, // 组合计算类型, 对LevelFilterUnitList结果计算或/与
    5: optional list<Filter> LevelFilterList, // 组合计算结果
}

//条件组合操作类型
enum OpGroup {
    AND = 1,//求交集
    OR = 2,//求并集
    NOT = 3, //求反计算
}
//运算型参数表达式
struct MathExpr {
    1: required string OpMath, //数学运算符
    2: optional Expr Lhs, //运算左值
    3: optional Expr Rhs, //运算右值
    4: optional list<Expr> MultiInputList, //运算输入值list
}
//方法型参数表达式
struct FuncExpr {
    1: required string FuncName, //方法名，为字符串，如"abc"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
    3: optional list<Expr> ParamExprList, //参数值list
}
//特征型参数表达式
struct FeatureExpr {
    1: required string FeatureName, //特征名，为字符串，如"ticket.status"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//参数表达式
struct Expr {
    1: optional list<Expr> ExprList, //元素可以为任意一种Expr
    2: optional list<Expr> SubstringList, //元素可以为任意一种Expr，用于拼成字符串
    3: optional MathExpr MathExpr,
    4: optional FuncExpr FuncExpr,
    5: optional FeatureExpr FeatureExpr, //特征型参数表达式，如"ticket.assignee_agent.status()"
    6: optional string VarExpr, //变量型参数表达式，如"$id"
    7: optional list<string> ConstantList, //元素可以为字符串，数字，布尔值中任意一种常量
    8: optional string Constant, //常量型参数表达式：字符串，如"\"abc\""，"\"300\""；数字，如"100.1"；布尔值，如"true"
}
//条件表达式
struct ConditionExpr {
    1: required string OpCheck,
    2: required Expr Lhs, //运算左值
    3: optional Expr Rhs, //运算右值
}
//条件组合
struct ConditionGroupExpr {
    1: required string OpGroup,
    2: optional list<ConditionExpr> Conditions,
    3: optional list<ConditionGroupExpr> ConditionGroups,//conditions和conditionGroups有且只有一个非空
}
//动作表达式
struct ActionExpr {
    1: required string ActionName, //动作名，为字符串，如"abc"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
    3: optional Expr DelayTime; //延时参数表达式
    4: optional ConditionGroupExpr Expression, //动作执行条件
    5: optional ActionExpr ActionOpposite, //条件为假时的动作，空默认不执行，其中的空字段默认和条件为真时值相同
}
//动作组合
struct ActionGroupExpr {
    1: required bool Sequential,
    2: required bool ContinueOnFail,
    3: required list<ActionExpr> Actions,
}
//延时步骤
struct DelayStepExpr {
    1: required ActionGroupExpr ActionGroup;//即时动作
    2: optional string FilterAim;//即时条件名，为字符串，如"abc"
    3: required Expr DelayTime;//延时参数表达式
    4: optional ConditionGroupExpr FilterAimExpr;// 新增 延时条件判断表达式
}
//规则返回
struct AimExpr {
    1: optional list<DelayStepExpr> DelaySteps,//延时步骤
    2: optional ActionGroupExpr ActionGroup,//即时动作
    3: optional Expr ReturnValue,//路由返回 delaySteps/actionGroup/returnValue有且只有一个非空
}
//规则
struct AdminRule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional Filter Filter, //条件
    5: required string Value, //动作信息 json, list查询时填充''，get查询时填充
    6: required RuleStopStatus StopStatus,  //禁用状态
    7: required EntityStatus Status,  //状态
    8: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    9: required i64 TenantId, //租户ID
    10: required i64 AccessPartyId, //接入方ID
    11: optional i64 SourceId,    //业务标识，区分工单、电商工单
    12: required string CreatedAt,  //创建时间
    13: required string UpdatedAt,  //更新时间
    14: required string Extra,  //附加信息
    15: required i64 CreatorAgentId,  //创建人ID
    16: required i64 UpdaterAgentId,  //更新人ID
    17: optional RuleTaskStatus TaskStatus,  //规则任务状态 get查询时填充
    18: optional i64 MainRuleId,    //运行规则ID
    19: optional i64 OriginId,    //原始规则ID
    20: optional i32 Version,     //版本号
    21: optional i64 TriggerId,
    22: optional i64 RuleGroupId, //规则组id
}
//拖拽规则
struct AdminRuleSimple {
    1: required i64 Id, //ID
    2: required i32 Priority,  //优先级
}

struct FieldListGetRequest {
    1: required i64 EventId,  //事件ID
    2: optional i64 AccessPartyId, //接入方ID
    3: optional i64 TenantId, //租户ID
    4: optional string AppKey, //页面标识
    5: optional string Part, //页面div标识
    6: optional string EventKey,  //事件标识
    7: optional FieldDisplayType FieldDisplayType,
    8: optional string lang, // 请求语言
    9: optional list<string> fieldNameList,
    255: required base.Base Base,
}

struct FieldListGetResponse {
    1: required list<FieldCondition> FieldConditions,
    255: required base.BaseResp BaseResp,
}

struct FieldValuesGetRequest {
    1: required i64 FieldId,
    2: required i32 OperatorId,//旧版的操作符，即将废弃
    3: optional string Operator,//新版的操作符
    255: required base.Base Base,
}

struct FieldValuesGetResponse {
    1: required i32 FieldValueType, //值类型
    2: required string FieldValues,  //具体取值：字段值的json串；获取值的URL
    255: required base.BaseResp BaseResp,
}

struct FieldOperatorOptions {
    1: required string OperatorKey, //操作符
    2: required FieldOptionType FieldOptionType,
    3: optional string FieldValues,  //具体取值：字段值的json串；获取值的URL
    4: optional i64 FieldValueID
}
struct FieldCreateRequest {
    1: required string DisplayName, //字段的前端展示名称
    2: required string MapName, //字段的数据库存储名称
    3: required list<i32> OperatorIds,
    4: optional i64 ModelId,
    5: required i32 Type,
    6: required i32 DataType,
    7: optional string OriginName,
    8: optional string CurrentName,
    9: optional string FieldPath,
    10: optional string ComputeFunc,
    11: optional string Scene,
    12: optional i64 ParentId,
    13: optional i64 TenantId, //租户ID
    14: optional i64 AccessPartyId,
    15: required i64 CreatorAgentId,  //创建人ID
    255: required base.Base Base,
}

struct FieldCreateResponse {
    1: optional FieldMeta FieldMeta,
    255: required base.BaseResp BaseResp,
}

struct FieldUpdateRequest {
    1: required i64 Id,
    2: optional string DisplayName, //字段的前端展示名称
    3: optional string MapName, //字段的数据库存储名称
    4: optional list<i32> OperatorIds,
    5: optional i64 ModelId,
    6: optional i32 Type,
    7: optional i32 DataType,
    8: optional string OriginName,
    9: optional string CurrentName,
    10: optional string FieldPath,
    11: optional string ComputeFunc,
    12: optional string Scene,
    13: optional i64 ParentId,
    14: optional i64 TenantId, //租户ID
    15: optional i64 AccessPartyId,
    16: required i64 UpdaterAgentId,
    255: required base.Base Base,
}

struct FieldUpdateResponse {
    1: optional FieldMeta FieldMeta,
    255: required base.BaseResp BaseResp,
}

struct FieldDeleteRequest {
    1: required i64 Id,
    2: required i64 UpdaterAgentId,
    3: optional i64 TenantId, //租户ID
    4: optional i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct FieldDeleteResponse {
    1: required bool result,
    255: required base.BaseResp BaseResp,
}

struct ActionMetaListGetRequest {
    1: required string AppKey, //页面标识
    2: required i64 TenantId, //租户ID
    3: optional i64 AccessPartyId, //接入方ID
    4: optional i64 EventId;
    5: optional list<ExtraInfoStruct> ExtraInfo, // 动作扩展信息
    6: optional string lang, // 请求语言

    255: required base.Base Base,
}
struct ActionMetaListGetResponse {
    1: required list<ActionMeta> ActionMetas, //返回值
    255: required base.BaseResp BaseResp,
}

struct PlaceholderMetaListGetRequest {
    1: required string AppKey, //页面标识
    2: required i64 TenantId, //租户ID
    3: optional i64 AccessPartyId, //接入方ID
    4: optional string EventKey,  //事件标识
    255: required base.Base Base,
}
struct PlaceholderMetaListGetResponse {
    1: required list<PlaceholderMeta> PlaceholderMetas, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaListGetRequest {
    1: required i64 TenantId, //租户ID
    2: optional i64 AccessPartyId, //接入方ID
    3: optional i64 SourceId,    //业务标识，区分工单、电商工单
    4: optional EntityStatus Status,  //状态
    255: required base.Base Base,
}

struct SLAAimMetaListGetResponse {
    1: required list<SLAAimMeta> SLAAimMetaList, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaSimpleListGetResponse {
    1: required list<SLAAimMetaSimple> SLAAimMetaSimpleList, //返回值
    255: required base.BaseResp BaseResp,
}

struct SLAAimMetaCreateRequest {
    1: required string Name, //名称 首响、完结
    2: required Filter Filter, //条件
    3: required list<ActionMeta> ActionMetas, //目标支持的全部动作
    4: required list<PlaceholderMeta> PlaceholderMetas, //目标支持的全部占位符，如运行时param:${ticket.id}原样写入,配置时param:${aim.time}需替换
    5: required list<DependencyFieldMeta> DependencyFieldMetas,    //目标支持的全部规则依赖字段
    6: required string Extra,  //附加信息
    7: required i64 CreatorAgentId,  //创建人ID
    8: required i64 TenantId, //租户ID
    9: optional i64 AccessPartyId, //接入方ID
    10: optional i64 SourceId,    //业务标识，区分工单、电商工单
    255: required base.Base Base,
}

struct SLAAimMetaCreateResponse {
    1: optional SLAAimMeta SLAAimMeta,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleListGetRequest {
    1: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    2: required i64 TenantId, //租户ID
    3: required i64 AccessPartyId, //接入方ID
    4: optional i64 SourceId,    //业务标识，区分工单、电商工单
    5: optional list<i64> IdList,
    6: optional EntityStatus Status,  //状态
    7: optional RuleStopStatus StopStatus,  //禁用状态
    8: optional string DisplayNameLike, //名称
    9: optional i32 Priority,  //优先级
    10: optional i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRuleListGetResponse {
    1: required list<AdminRule> AdminRuleList,
    2: required i32 Count,
    255: required base.BaseResp BaseResp,
}

struct AdminRulePageGetRequest {
    1: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
    2: required i64 TenantId, //租户ID
    3: required i64 AccessPartyId, //接入方ID
    4: optional i64 SourceId,    //业务标识，区分工单、电商工单
    5: required i32 Page,   //页码
    6: required i32 PageSize,   //每页条数
    7: optional RuleStopStatus StopStatus,  //禁用状态
    8: optional EntityStatus Status,  //状态
    9: optional list<i64> IdList,
    10: optional string DisplayNameLike, //名称
    11: optional i32 Priority,  //优先级
    12: optional i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRulePageGetResponse {
    1: required list<AdminRule> AdminRuleList,
    2: required i32 Count,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleGetByIdRequest {
    1: required i32 AppId,
    2: required i64 SourceId,
    3: required i64 Id,
    4: required i64 TenantId, //租户ID
    5: required i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct AdminRuleGetByIdResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleCreateRequest {
    1: required i32 AppId,
    2: required i64 SourceId,
    3: required string DisplayName,
    4: required i32 Priority,
    5: optional Filter Filter, //条件
    /*动作信息 json
        IM路由：   {
                     "skill_group":{"id":303}
                     "support_overflow": 1, //1：支持溢出；0：不支持
                     "skill_group_overflow": {"id": [1, 2]}
                    }
        机器人路由：{
                    "is_open": 0,
              	    "bot_id": {
              		    "id": 1
                    }
                  }
        SLA：目标json，如：
            [
                {'aimMetaId': 1,
                 'aimTime': 1,
                 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
                 'aimSteps': [
                            {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          , {'actionTimeType': 1, 'actionTime': 2, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          ]}
               ,{'aimMetaId': 2,
                 'aimTime': 2,
                 'aimTimeUnit': 1, //1:分钟；2：小时；3：秒
                 'aimSteps': [
                            {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          , {'actionTimeType': 1, 'actionTime': 5, 'actionTimeUnit': 2, 'actions': [{'actionMetaId': 1, 'actualParams': {'target'(FormalParamMeta--MapName): ["ecommerce_ticket.leader_email"], 'title': "【高危工单处理提醒】", 'text': "您处理中的工单${ecommerce_ticket.issue_no}需要在1小时内完成首次跟进，请及时处理"}}]}
                          ]}
            ]
        */
    6: required string Value,
    7: required string Extra,
    8: required i64 CreatorAgentId,
    9: required i64 TenantId, //租户ID
    10: required i64 AccessPartyId, //接入方ID
    11: required i64 EventId, //事件ID
    12: optional i64 RuleGroupId, //规则组ID
    13: optional agent_skill_group_i18n.CreateCardRequest CreateCardRequest, // 问题卡片内容。非问题卡片请求不可传
    14: optional bool Disable, // 是否禁用规则，不传默认为启用
    255: required base.Base Base,
}

struct AdminRuleCreateResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleUpdateRequest {
    1: required i64 Id,
    2: required i32 AppId,
    3: required i64 SourceId,
    4: optional string DisplayName,
    5: optional i32 Priority,
    6: optional Filter Filter, //条件
    7: optional string Value,
    8: optional EntityStatus Status,  //状态
    9: optional RuleStopStatus StopStatus,  //禁用状态
    10: optional string Extra,
    11: required i64 UpdaterAgentId,
    12: required i64 TenantId, //租户ID
    13: required i64 AccessPartyId, //接入方ID
    14: required i64 EventId, //事件ID
    15: optional agent_skill_group_i18n.UpdateCardRequest UpdateCardRequest, //卡片的修改请求，未修改时可不传
    255: required base.Base Base,
}

struct AdminRuleUpdateResponse {
    1: optional AdminRule AdminRule,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleDeleteRequest {
    1: required i64 Id,
    2: required i32 AppId,
    3: required i64 SourceId,
    4: required i64 UpdaterAgentId,
    5: required i64 TenantId, //租户ID
    6: required i64 AccessPartyId, //接入方ID
    255: required base.Base Base,
}

struct AdminRuleDeleteResponse {
    1: required bool result,
    255: required base.BaseResp BaseResp,
}

struct AdminRuleBatchUpdateRequest {
    1: required list<AdminRuleSimple> AdminRules,
    2: required i64 UpdaterAgentId,
    3: required i64 TenantId, //租户ID
    4: required i64 AccessPartyId, //接入方ID
    5: required i32 AppId,
    6: required i64 SourceId,
    7: required i64 EventId, //事件ID
    255: required base.Base Base,
}

struct AdminRuleBatchUpdateResponse {
    1: required bool result,
    2: optional list<i64> newRuleIds, //调整优先级会生成新的ruleId
    255: required base.BaseResp BaseResp,
}

//规则状态
enum RuleStatus {
    ENABLE = 1,//启用
    UNABLE = 0,//禁用
    DRAFT = 2,//草稿
    DELETE = 3,//已删除
    UNPUBLISHED = 4,//待发布
    PREPUBLISHED = 5,//预发布
    UNDERREVEIW = 6,//审核中
    UNREVIEW = 7,//审核不通过
}
// 规则优先级
struct RulePriority {
    1: required i64 Id, //ID
    2: required i32 Priority,  //优先级
}
// 规则生效环境
enum RuleEnv {
    PPE = 0,//PPE环境
    PROD = 1,//线上环境
}

//规则元数据
struct Rule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional ConditionGroupExpr Expression, //规则条件部分
    5: optional AimExpr ActionInfo,  //动作部分
    6: required i64 RuleGroupId,  //规则组id
    7: required RuleStatus Status,  //规则状态
    8: optional string Description, //规则描述
    9: required string CreatedAt,  //创建时间
    10: required string UpdatedAt,  //更新时间
    11: required string CreatorAgentId,  //创建人ID
    12: required string UpdaterAgentId,  //更新人ID
    13: required i32 DraftEditType, //草稿编辑类型 1-新增 2-编辑 0-未修改
    14: optional i64 OriginId, //多版本规则串联
    15: optional map<string,string> ExtraInfo, // 额外字段
    16: optional string Extra, // 前端用于展示
}

enum RuleOperationType {
    CREATE_ENABLE = 1, // 创建规则并启用
    CREATE_DISABLE = 2, // 创建规则并禁用
    STATUS_CHANGE = 3, // 规则状态变更, 启用/禁用/发布
    PRIORITY_CHANGE = 4, // 规则优先级变更
    UPDATE = 5, // 规则内容变更
}

struct RuleOperationLog {
    1: required i64 Id,
    2: required RuleOperationType OperationType,
    3: optional list<OperateRuleItemLog> OperateRuleItemLogs,
    4: required string OperatorAgentId,
    5: required string CreatedAt,
}

enum OperateRuleItemType {
    STAUTS = 1, // 更新状态
    NAME = 2, // 更新名字
    PRIORITY = 3, // 更新优先级
    CONDITION = 4, // 更新条件
    CONDITION_OPTIONS = 5, // 更新条件选项
    CONDITION_RELATION = 6, // 更新条件间关系
    CONDITION_GROUP_RELATION = 7, // 更新条件组间关系
    RETURN_VALUE = 8, // 更新规则返回值
    ROUTE_TYPE = 9, // 路由时机变更
    OVERFLOW = 10, // 溢出设置变更
    DIVERSION_AUTO_MANUAL = 11, // 自动分流 -> 人工分流
    DIVERSION_MANUAL_TO_AUTO = 12 // 人工分流 -> 自动分流
}

enum ConditionUpdateType {
    OptionsUpdate = 1, //选项更新
    DELETE = 2, // 条件删除
    ADD = 3, // 条件新增
}

struct ConditionGroupChange {
    1: required i32 ConditionGroupOrder, // 条件组序号
    2: optional list<ConditionChange> ConditionChangeList, // 条件变更列表
    3: optional string Relation, // 条件关系变更后结果
    4: optional list<string> ConditionNameList, // 条件关系变更对应的条件列表
}

struct ConditionChange {
    1: required string ConditionName, // 条件名字
    2: required ConditionUpdateType ConditionUpdateType, // 条件更新类型
    3: optional list<string> AddOptions, // 条件选项新增
    4: optional list<string> DeleteOptions, // 条件选项删除
    5: optional string Operator, // 运算符
}

struct OperateRuleItemLog {
    1: required OperateRuleItemType OperateItemType,
    2: optional string BeforeValue,
    3: optional string AfterValue, //字符串或json结构
    4: optional list<ConditionGroupChange> ConditionGroupChangeList,
}

struct ExtraInfoStruct {
    1: required string DisplayName,
    2: required string Key,
    3: required i32 Status,
    4: required i32 Type,
    5: required string Value
}

// 创建规则
struct CreateRuleRequest {
    1: required string EventKey, //事件key
    2: optional i64 RuleGroupId, //规则组ID
    3: required string DisplayName, //规则名称
    4: required i32 Priority, // 规则优先级
    5: required ConditionGroupExpr Expression, //条件-DSL
    6: required AimExpr ActionInfo,  //动作部分
    7: required string CreatorAgentId, //创建人id
    8: required i64 AccessPartyId, //接入方ID
    9: optional bool Enable, // 是否启用规则，不传默认为禁用
    10: optional string Extra, //扩展字段，前端可用于复现规则
    11: required string Description, //规则描述
    12: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    13: required string Version, // 接口版本 UI传v1
    14: optional map<string,string> ExtraInfo, // 额外信息
    255: optional base.Base Base,
}
struct CreateRuleResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

// 更新规则
struct UpdateRuleRequest {
    1: required i64 Id, //规则ID
    2: optional string DisplayName, //规则名称
    3: optional ConditionGroupExpr Expression, //条件-DSL
    4: optional AimExpr ActionInfo, //路由的结果部分，用于对结果的特殊处理，比如绑定技能组
    5: optional string Extra, //扩展字段，前端可用于复现规则
    6: optional string Description, //规则描述
    7: required string AgentId, // 操作人
    8: required string Version, // 接口版本 UI传v1
    9: optional i32 Priority, // 规则优先级
    10: optional map<string,string> ExtraInfo, // 额外字段
    255: optional base.Base Base,
}
struct UpdateRuleResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

// 启用/禁用/删除规则
struct UpdateRuleStatusRequest {
    1: required list<i64> Ids, //规则ID的list
    2: required RuleStatus RuleStatus, //建RuleStatus定义
    3: required string UpdaterAgentId, // 更新人
    4: required string Version, // 接口版本 UI传v1
    5: optional bool Draft, //是否为草稿规则
    6: optional i64 ruleGroupId,
    7: optional i32 operateGroupAllRules, // 1 - 需要处理组里所有规则，0 -仅处理部分
    8: optional string accessPartyId, // 接入方id
    9: optional string eventKey, // 事件key

    255: optional base.Base Base,
}
struct UpdateRuleStatusResponse {
    255: required base.BaseResp BaseResp,
}

//调整规则优先级
struct UpdateRulePriorityRequest {
    1: required list<RulePriority> Rules, //所有规则的优先级信息
    2: required string UpdaterAgentId, // 更新人
    3: required string Version, // 接口版本 UI传v1
    4: optional list<i64> AdaptRuleIds    // 具体修改的是哪些规则
    255: optional base.Base Base,
}
struct UpdateRulePriorityResponse {
    1: optional i64 RuleGroupId, //调整优先级会生成新的规则组版本(不包括草稿)
    2: optional map<i64,i64> newRuleIds, //调整优先级会生成新的ruleId(不包括草稿)
    255: required base.BaseResp BaseResp,
}

// 根据id获取规则
struct GetRuleByIdRequest {
    1: required i64 Id,
    255: optional base.Base Base,
}

struct GetRulesByIdsRequest {
    1: required list<i64> Ids,
    255: optional base.Base Base,
}

struct GetRuleByIdResponse {
    1: optional Rule Rule,
    255: required base.BaseResp BaseResp,
}

struct GetRulesByIdsResponse {
    1: optional list<Rule> Rules,
    255: required base.BaseResp BaseResp,
}

//获取规则列表
struct GetRuleListRequest {
    1: required string EventKey, //事件key
    2: required i64 AccessPartyId, //接入方ID
    3: optional i64 RuleGroupId, //规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
    4: optional i32 IsDraft,//是否获取草稿箱里的规则 1-是
    5: optional map<string,string> ExtraInfo, // 额外查询字段
    6: optional i32 Page,
    7: optional i32 PageSize,
    8: optional list<i32> statusList,  //状态，1-启动，0-禁用
    9: optional string ruleName, //规则名称
    255: optional base.Base Base,
}
struct GetRuleListResponse {
    1: optional list<Rule> RuleList, //规则
    2: optional i64 Total,
    3: optional i32 existRulesIfNotFilter, //1-存在，0-不存在。不过滤是否存在规则。用于应对上游，当不存在时，直接创建默认规则的场景。
    255: required base.BaseResp BaseResp,
}

struct PublishRuleGroupRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required string UpdaterAgentId, // 操作人
    3: optional list<string> RuleIds, //规则ID集合
    4: optional string eventKey,

    255: optional base.Base Base,
}

struct PublishRuleGroupResponse {
    1: optional i64 RuleGroupId, //新的规则组id
    2: optional map<i64,i64> newRuleIds, //发布会生成新的ruleId
    255: required base.BaseResp BaseResp,
}

struct CopyRuleGroupRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required string UpdaterAgentId, // 操作人
    3: optional string EventKey, //事件key
    4: optional i64 AccessPartyId, //接入方ID
    5: optional bool IsVersion, // 是否基于历史版本复制
    255: optional base.Base Base,
}

struct CopyRuleGroupResponse {
    255: required base.BaseResp BaseResp,
}

struct ClearDraftRulesRequest {
    1: required i64 RuleGroupId // 规则组id
    255: optional base.Base Base
}

struct ClearDraftRulesResponse {
    255: required base.BaseResp BaseResp
}

// 创建规则组
struct CreateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required string GroupDisplayName, //规则组名称
    3: required string CreatorAgentId, //创建人id
    4: required string Product, //产品
    5: required list<AntlrRule> RuleList, // 规则集合
    6: optional bool Enable, // 是否启用规则，不传默认为待发布（草稿），点保存-传false，发布传true
    7: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    8: required i64 AccessPartyId, //接入方ID
    9: optional list<ExtraInfoStruct> ExtraInfo,
    10: optional i64 SkillGroupId, //技能组
    11: optional RuleStatus status, //规则组状态
    12: optional i32 needCheckLan, // 是否需要校验语言， 1-需要， 其余不需要

    255: optional base.Base Base,
}

struct BatchCreateRuleGroupRequest {
    1: required list<CreateRuleGroupRequest> RuleGroups,
    255: optional base.Base Base,
}

struct BatchCreateRuleGroupResponse {
    1: optional list<i64> RuleGroupIds,
    255: required base.BaseResp BaseResp,
}

// 规则
struct AntlrRule {
    1: required string DisplayName, //规则名称
    2: required i32 Priority, // 规则优先级
    3: required ConditionGroupExpr Expression, //条件-DSL
    4: required AimExpr ActionInfo,  //动作部分
    5: optional string CreatorAgentId, //创建人id
    6: optional string Description, //规则描述
    7: optional i64 Id,  // 规则ID
    8: optional i64 OriginId,    // 规则原始ID
    9: optional i32 RuleStatus, // 规则状态
    10: optional string Extra, // 用于前端复现规则
    11: optional string CreatorTime, //创建时间
    12: optional string UpdaterAgentId, //更新人
    13: optional string UpdaterTime, //更新时间
    255: optional base.Base Base,
}

// 创建规则组结果
struct CreateRuleGroupResponse {
    1: optional i64 RuleGroupId, //
    255: required base.BaseResp BaseResp, // 前端收否解析
}

// 查询规则组
struct RuleGroupPageQueryRequest {
    1: optional string Product, //产品 - 默认传 TICKET_AUTOMATION_RULE
    2: optional string RuleGroupDisplayName, //名称
    3: optional list<RuleStatus> RuleGroupStatus, //状态
    4: optional list<string> CreatorAgentId, //创建人
    5: optional list<string> CreatorTime, //创建时间
    6: optional list<string> UpdaterAgentId, //更新人
    7: optional list<string> UpdaterTime, //更新时间
    8: optional i32 Page,   //页码
    9: optional i32 PageSize,   //每页条数
    10: required i64 AccessPartyId, //接入方ID
    11: optional list<string> Events, //触发时机
    12: optional map<string, list<string>> Conditions, //筛选条件  eg.<ticket_base.countryCode#LIST_IN, ["1","2"]>
    13: optional list<ExtraInfoStruct> ExtraInfo,
    255: optional base.Base Base,
}

// 规则组详情
struct RuleGroupDetail {
    1: required i64 RuleGroupId, // 规则组id - 跳转详情页使用
    2: required i64 OriginId, // 原始id
    3: required string RuleGroupDisplayName, // 名称
    4: required RuleStatus RuleGroupStatus, // 状态
    5: required i32 Version, // 版本号
    6: required list<AntlrRule> RuleList, // 规则集合
    7: optional i64 DraftRuleGroupId, // 草稿版本id - 跳转编辑页使用
    8: optional string CreatorAgentId, // 创建人
    9: optional string CreatorTime, // 创建时间
    10: optional string UpdaterAgentId, // 更新人
    11: optional string UpdaterTime, // 更新时间
    12: optional bool HaveReleaseVersion, // 是否有线上生效版本
    13: optional map<string,string> ExtraInfo, // 规则类型
    14: optional i64 SkillGroupId,
    255: optional base.Base Base,
}

// 查询规则组结果
struct RuleGroupPageQueryResponse {
    1: optional list<RuleGroupDetail> RuleGroupList, //规则组的list
    2: optional i32 Count,  // 数量
    255: required base.BaseResp BaseResp, // 前端是否解析
}

struct RuleGroupCountQueryResponse {
    1: optional list<RuleGroupConditionCount> ruleGroupCountList, //规则组的list
    255: required base.BaseResp BaseResp, // 前端是否解析
}

struct RuleGroupConditionCount{
    1: optional string key;
    2: optional map<string, RuleGroupCount> countMap;
}

struct RuleGroupCount{
    1: optional i32 enableCount;
    2: optional i32 disableCount;
    3: optional i32 totalCount;
}

enum RuleType {
    DOCUMENTARY_RULE   = 1  // 跟单规则
    GENERAL_RULE       = 2  // 通用规则
    SEND_DOWN          = 3  // 下送
    UPGRADE            = 4  // 升级
    FINISH             = 5  // 完结
    TEMPORARY_STORAGE  = 6  // 暂存
    EDIT               = 7  // 编辑
    FOLLOW             = 8  // 跟进
    AI_CALL            = 9  // 外呼
    MESSAGE            = 10 // 短信
    ARBITRATION        = 11 // 仲裁
    PULL_BELL          = 12 // 仲裁
}

// 修改规则组
struct UpdateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required i64 RuleGroupId, //规则组ID
    3: required i64 OriginId, // 原始id
    4: required string GroupDisplayName, //规则组名称
    5: required i32 Version, // 版本号
    6: required string UpdaterAgentId, //创建人id
    7: required string Product, //产品
    8: required list<AntlrRule> RuleList, // 规则集合
    9: optional bool Enable, // 是否启用规则，不传默认为禁用
    10: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    11: required i64 AccessPartyId, //接入方ID
    12: optional RuleType RuleType,
    13: optional i64 SkillGroupId, //技能组id
    14: optional list<ExtraInfoStruct> ExtraInfo    // 规则组额外信息
    15: optional RuleStatus status, //规则组状态
    16: optional i32 needCheckLan, // 是否需要校验语言， 1-需要， 其余不需要

    255: optional base.Base Base,
}

// 修改结果
struct UpdateRuleGroupResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp, // 前端收否解析
}

// 启用/禁用/删除规则
struct UpdateRuleGroupStatusRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required RuleStatus RuleStatus, // 状态
    3: required string UpdaterAgentId, // 更新人
    255: optional base.Base Base,
}

// 结果
struct UpdateRuleGroupStatusResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp, // 前端收否解析
}


// 规则组详情查询条件
struct GetRuleGroupRequest {
    1: required i64 RuleGroupId, // 规则组id
    255: optional base.Base Base,
}

// 结果
struct GetRuleGroupResponse {
    1: optional RuleGroupDetail RuleGroupDetail, // 规则组详情
    2: optional string EventKey, //事件key
    3: optional i64 AccessPartyId,
    4: optional string Product, //产品线
    255: required base.BaseResp BaseResp, // 前端是否解析
}

// 查询事件类型
struct ListEventRequest {
    1: required string Product, //产品 - 默认传 TICKET_AUTOMATION_RULE
    2: optional i64 AccessPartyId, //接入方ID
    3: optional list<ExtraInfoStruct> ExtraInfo, // 事件扩展信息
    4: optional string lang, // 请求语言
    255: optional base.Base Base,
}

//事件
struct Event {
    1: required i64 EventId, //事件id
    2: required string EventKey, //事件key
    3: required string DisplayName, //展示名称
    4: optional string DisplayNameStarlingKey,  //事件名称starlingkey
    5: optional string DisplayNameByLang, //字段的前端展示名称(支持多语)
    255: optional base.Base Base,
}

// 查询事件类型结果集
struct ListEventResponse {
    1: optional list<Event> EventList, //事件
    255: required base.BaseResp BaseResp,
}

struct EventCreateRequestV2 {
    1: required string EventKey, //事件key
    2: required string Name, //事件描述
    3: required i64 DataSource, //事件所属数据源
    4: required map<string, list<map<string, string>>> InitMapPair, //事件msg转换
    5: required string CreateUser, //创建人
    6: optional list<ExtraInfoStruct> ExtraInfo,
    7: optional string AccessPartyIds,
    8: optional list<string> Product,
    255: optional base.Base Base,
}

struct EventCreateResponseV2 {
    1: required i64 eventId,
    255: required base.BaseResp BaseResp,
}

struct GetRuleOperationLogsRequest {
    1: required i64 RuleId,
    2: required i32 Page,
    3: required i32 PageSize,

    255: optional base.Base Base,
}

struct GetRuleOperationLogsResponse {
    1: optional list<RuleOperationLog> RuleOperationLogList,
    2: optional i64 totalCount,
    255: required base.BaseResp BaseResp,
}

struct DeleteDraftRulesRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required string OperateAgentId, // 操作人

    255: optional base.Base Base,
}

struct DeleteDraftRulesResponse {
    255: required base.BaseResp BaseResp,
}

struct UpdateFieldRequest {
    1: required i64 FieldId,
    2: required string DisplayName,
    3: required string FieldName,
    4: optional FieldDataType DataType,
    5: required string AgentEmail, // 操作人邮箱
    6: optional list<i64> AccessPartyIds,
    7: optional FieldFetchWay FetchWay,
    8: optional string Part, //页面div标识
    9: optional list<FieldValue> FieldValues,
    10: optional string AppKey,

    255: optional base.Base Base,
}

struct UpdateFieldResponse {
    255: required base.BaseResp BaseResp,    //StatusCode=0表示成功，其他表示失败
}

struct TriggerTicketRpcEventRequest {
    1: required i32 TenantId,
    2: required i64 AccessPartyId,
    3: required list<i64> TicketIds,
    4: required string Token,
    5: optional string Idc,

    255: optional base.Base Base,
}

struct TriggerTicketRpcEventResponse {
    255: required base.BaseResp BaseResp,
}

struct TriggerRulesBriefRequest {
    1: required list<string> EventKeys,
    2: required i64 AccessPartyId,
    3: required string Product,
    4: required list<RuleStatus> StatusList,
    5: required i32 Page,
    6: required i32 PageSize,

    255: optional base.Base Base,
}

struct TriggerRulesBriefResponse {
    1: required list<TriggerRuleGroupBrief> TriggerRuleGroupList,
    2: required i64 Total,

    255:required base.BaseResp BaseResp,
}

struct TriggerRuleGroupBrief {
    1: required string RuleGroupName,
    2: required i64 Id,
    3: required i64 OriginId,
    4: required list<TriggerRuleBrief> RuleList,
}

struct TriggerRuleBrief {
    1: required string RuleName,
    2: required i64 Id,
    3: required i64 OriginId,
}

struct CloseLibraRequest {
    1: required i64 ExperimentId,   // 实验ID
    2: required i64 RuleId,   // 子规则ID

    255: optional base.Base Base
}

struct CloseLibraResponse {
    255: required base.BaseResp BaseResp,
}

struct BatchUpdateRuleGroupRequest {
    1: required list<UpdateRuleGroupRequest> RuleGroups,
    255: optional base.Base Base,
}

struct BatchUpdateRuleGroupResponse {
    1: optional bool Success, //成功失败
    2: optional list<i64> NewRuleGroupIds   // 更新成功的规则组ID
    255: required base.BaseResp BaseResp, // 前端收否解析
}

struct GetRuleGroupListByEventKeyRequest {
    1: required string EventKey,
    2: optional string RuleGroupDisplayName, //名称
    3: optional list<RuleStatus> RuleGroupStatus, //状态
    4: optional list<string> CreatorAgentId, //创建人
    5: optional list<string> CreatorTime, //创建时间
    6: optional list<string> UpdaterAgentId, //更新人
    7: optional list<string> UpdaterTime, //更新时间
    8: required i64 AccessPartId,
    9: optional i64 SkillGroupId,
    10: required i32 Page,
    11: required i32 PageSize,
    255: optional base.Base Base,
}

struct GetRuleGroupListByEventKeyResponse {
    1: optional list<RuleGroupDetail> RuleGroupList, //规则组的list
    2: optional i32 Count,  // 数量
    255: required base.BaseResp BaseResp, // 前端是否解析
}

struct GetExtraInfoRequestV2 {
    1: required string Scenes,
    2: required string EventKey,
    3: optional list<string> ExtraKeys,
    4: optional i64 AccessPartId,
    255: optional base.Base Base,
}

struct GetExtraInfoResponseV2 {
    1: optional list<RuleGroupRelationStruct> RuleGroupRelations
    255: required base.BaseResp BaseResp,
}

struct RuleGroupRelationStruct {
    1: required i64 RuleGroupId,
    2: required map<string, string> extraInfos,
    3: required i64 OriginId
}

struct FieldAndValue {
    1: required i64 FieldId,
    2: required string FieldDisplayName, //字段的前端展示名称
    3: optional string ComputeFunc,//字段的计算表达式
    4: required string FieldName, //admin改造后的字段名，即规则实际运行需要的字段
    5: required list<string> OperatorList, //字段运行使用的操作符
    6: required i32 FieldDataType, //字段类型 integer = 1 long = 2 string = 3 double = 5 boolean = 6
    7: required FieldFetchWay FieldFetchWay, //字段获取方式
    8: required map<string,FieldValues> OperatorFieldValues,
    9: optional map<string,string> ExtraInfo
}

struct FieldValueTree {
    1: optional string value,
    2: required string name,
    3: optional list<FieldValueTree> children,
}

struct FieldValues {
    1: required FieldOptionType FieldValueType,
    2: required list<FieldValueTree> FieldValueList,
    3: required string FieldValues,
    4: optional list<FieldValue> fieldValueArr,
}

// 本resp需兼容前端已经使用的结构，因此部分名称词不达意
struct FieldAndValueListResponse {
    1: required list<FieldAndValue> FieldAndValues,
    255: required base.BaseResp BaseResp,
}

//查询触发时机规则组
struct GetEventRuleGroupListRequest {
    1: optional string KeyWord, //关键字
    2: optional list<string> UpdaterAgentId, //更新人
    3: optional list<string> UpdaterTime, //更新时间
    4: required i32 Page,     //页码
    5: required i32 PageSize,   //每页条数

    255: optional base.Base Base,
}

//触发时机规则组信息
struct EventRuleGroupDetail {
    1: required i64 RuleGroupId, //规则组ID
    2: required string EventKey, // 触发时机
    3: required string EventName, // 触发时机名称
    4: optional string UpdaterAgentId, // 更新人
    5: optional string UpdaterTime, // 更新时间

    255: optional base.Base Base,
}

//查询结果
struct GetEventRuleGroupListResponse {
    1: optional list<EventRuleGroupDetail> EventRuleGroupList, //触发时机规则组List
    2: optional i32 Count, // 数量

    255: required base.BaseResp BaseResp,
}

//查询触发时机规则组详情
struct GetEventRuleGroupRequest {
    1: required i64 RuleGroupId, // 规则组id

    255: optional base.Base Base,
}


//批量查询规则组
struct BatchGetRuleGroupRequest {
    1: required list<i64> RuleGroupIds,
    2: optional list<i64> RuleGroupOriginIds
    255: optional base.Base Base
}

struct BatchGetRuleGroupResponse {
    1: optional list<RuleGroupDetail> RuleGroupDetails  // 规则组详情
    255: required base.BaseResp BaseResp
}



//查询结果
struct GetEventRuleGroupResponse {
   1: optional i64 RuleGroupId, // 规则组id - 跳转详情页使用
   2: optional string EventKey, //事件key
   3: optional i64 EventId, //事件ID
   4: optional string EventName, //事件名称
   5: optional list<AntlrRule> RuleList, // 规则集合

    255: required base.BaseResp BaseResp,
}

//更新发布触发时机规则
struct UpdateEventRuleGroupRequest {
   1: required i64 EventId, //事件ID
   2: required i64 AccessPartyId, //接入方ID
   3: required i64 RuleGroupId, // 规则组ID
   4: required string UpdaterAgentId, //更新人ID
   5: required list<AntlrRule> RuleList, // 规则集合

    255: optional base.Base Base,
}

// 修改结果
struct UpdateEventRuleGroupResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp,
}

// 启用/禁用/删除规则
struct BatchUpdateRuleGroupStatusRequest {
    1: required list<i64> RuleGroupIds, //规则组id
    2: required RuleStatus RuleStatus, // 状态
    3: required string UpdaterAgentId, // 更新人
    4: optional list<i64> RuleGroupOriginIds, //规则组原始id
    5: optional string EventKey, // 事件key
    6: optional string AccessPartyId, // 接入方id
    255: optional base.Base Base,
}

// 结果
struct BatchUpdateRuleGroupStatusResponse {
    1: optional bool Success, //成功失败
    255: required base.BaseResp BaseResp, // 前端收否解析
}

struct GetActionListRequest {
    1: required i64 TenantId
    2: required i64 AccessPartyId
    3: optional string AppKey
    4: optional string EventId
    5: optional list<ExtraInfoStruct> ExtraInfo, // 动作扩展信息
    255: optional base.Base Base
}

struct GetActionListResponse {
    1: required list<ActionStruct> ActionList
    255: required base.BaseResp BaseResp
}

struct ActionStruct {
    1: required string ActionName
    2: required string ActionKey
    3: optional map<string,string> ExtraInfo
    4: optional list<ActionStruct> ActionList
}

struct GetActionFieldByActionKeysRequest {
    1: required i64 TenantId
    2: required i64 AccessPartyId
    3: required list<string> ActionKeys
    4: required string AppKey
    5: optional string EventId
    255: optional base.Base Base
}

struct GetActionFieldByActionKeysResponse {
    1: required list<ActionMetaInfoStruct> ActionMetaInfoList
    255: required base.BaseResp BaseResp
}

struct ActionMetaInfoStruct {
    1: required string ActionKey
    2: required list<FieldMetaStruct> FieldMetaList
}

struct FieldMetaStruct {
    1: required string FieldId
    2: required string FieldName
    3: required string FieldDisplayName
    4: required FieldDataType FieldDataType
    5: required FieldFetchWay FieldFetchType
    6: required FieldValues FieldValues
    7: optional FieldValues Placeholders
    8: optional FieldOptionType FieldOptionType
    9: optional FunctionMetaStruct FunctionMeta,
}

struct FunctionMetaStruct {
    1: required string MapName, //名称
    2: required list<FieldMetaStruct> FieldMetaList, //形参名称及其类型、可取值
}

struct CreateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct CreateSamBindRequest {
    1: required i64 sellerId, // 商家id
    2: required string sellerCountryCode, // 商家国家/地区
    3: required i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: required i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: required i64 operateAgentId, // 操作人id
    8: required string operateAgentImg, // 操作人头像地址
    9: optional string note, // 备注
    10: optional string geo, // 合规区域
    11: optional string emailDomain, // 邮箱
    255: optional base.Base Base,
}

struct UpdateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct UpdateSamBindRequest {
    1: required i64 sellerId, // 商家id
    2: required string sellerCountryCode, // 商家国家/地区
    3: required i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: required i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: required i32 isDel, //  0 未删除 1删除
    8: required i64 operateAgentId, // 操作人id
    9: required string operateAgentImg, // 操作人头像地址
    10: optional string note, // 备注
    11: required i64 id, // 绑定关系的id
    12: optional string geo,
    13: optional string emailDomain, // 邮箱
    255: optional base.Base Base,
}

struct BatchCreateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchCreateSamBindRequest {
    1: required string excelUrl, // 文件地址
    2: required i64 operateAgentId, // 操作人id
    3: required string operateAgentImg, // 操作人头像地址
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchDelSamBindByExcelResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchDelSamBindByExcelRequest {
    1: required string excelUrl, // 文件地址
    2: required i64 operateAgentId, // 操作人id
    3: required string operateAgentImg, // 操作人头像地址
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}


struct BatchTransferSamBindRequest {
    1: required list<i64> sellerIdList, // 商家id list
    2: required i64 imGroupId, //  绑定IM技能组
    3: optional i64 imAgentId, //  绑定IM客服
    4: required i64 ticketGroupId, //  绑定工单技能组
    5: optional i64 ticketAgentId, //  绑定工单客服
    6: required i64 operateAgentId, // 操作人id
    7: required string operateAgentImg, // 操作人头像地址
    8: optional string geo,
    9: optional string emailDomain, // 邮箱
    255: optional base.Base Base,
}

struct BatchTransferSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchDelSamBindRequest {
    1: required list<i64> sellerIdList, // 商家id list
    2: required i64 operateAgentId, // 操作人id
    3: required string operateAgentImg, // 操作人头像地址
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchDelSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchExportSamBindRequest {
    1: optional i64 sellerId, // 商家id
    2: optional string sellerCountryCode, // 国家码
    3: optional i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: optional i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: optional i64 operateAgentId, // 更新人id
    8: optional i64 updateTimeStart, // 更新时间-start
    9: optional i64 updateTimeEnd, // 更新时间-end
    10: optional i64 createTimeStart, // 创建时间-start
    11: optional i64 createTimeEnd, // 创建时间-end
    12: optional i64 userId; // 操作人
    13: optional string geo, // 合规区域
    14: optional string emailDomain, // 邮箱
    255: optional base.Base Base,
}

struct BatchExportSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct GetSellerInfoRequest {
    1: required i64 sellerId, // 商家id
    255: optional base.Base Base,
}

struct GetSellerInfoResponse {
    1: required string sellerCountryCode, // 国家码
    2: required i32 bindStatus, // 能否绑定：1 能绑定 0：不能绑定
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct SearchSamBindRequest {
    1: optional i64 sellerId, // 商家id
    2: optional string sellerCountryCode, // 国家码
    3: optional i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: optional i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: optional i64 operateAgentId, // 更新人id
    8: optional i64 updateTimeStart, // 更新时间-start
    9: optional i64 updateTimeEnd, // 更新时间-end
    10: optional i64 createTimeStart, // 创建时间-start
    11: optional i64 createTimeEnd, // 创建时间-end
    12: optional i64 pageNum, // 页码，从1开始
    13: optional i64 pageSize, // 每页的数量
    14: optional string geo, // 新增，合规区域
    15: optional string emailDomain, // 邮箱
    255: optional base.Base Base,
}

struct SearchSamBindData {
    1: required i64 sellerId, // 商家id
    2: optional string sellerImg, // 商家头像地址
    3: optional string sellerName, // 商家名称
    4: optional string imGroupName, // 绑定IM技能组名称
    5: optional string imAgentName, //  绑定IM客服名称
    6: optional string imAgentEmail, //  绑定IM客服邮箱
    7: optional string ticketGroupName, // 绑定工单技能组名称
    8: optional string ticketAgentName, //  绑定工单客服名称
    9: optional string ticketAgentEmail, // 绑定工单客服邮箱
    10: optional string note, // 备注
    11: optional string operateImg, // 操作人头像
    12: optional string operateName, // 操作人名字
    13: optional i64 updateTime, // 更新时间
    14: optional i64 createTime, // 创建时间
    15: optional i64 imGroupId, //  绑定IM技能组
    16: optional i64 imAgentId, //  绑定IM客服
    17: optional i64 ticketGroupId, //  绑定工单技能组
    18: optional i64 ticketAgentId, //  绑定工单客服
    19: optional i64 operateAgentId, // 更新人id
    20: optional string sellerCountry, // 更新人id
    21: required i64 id, // 商家id
    22: optional string geo, // 合规区域
    23: optional string emailDomain, // 邮箱
}

struct SearchSamBindResponse {
    1: required list<SearchSamBindData> searchSamBindDataList,
    2: required i64 totalSize,

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct SamBindJudgeRequest {
    1: optional i64 groupId, //  技能组Id
    2: optional list<i64> agentIdList, //  客服Id

    255: optional base.Base Base,
}

struct SamBindJudgeResponse {
    1: optional list<SamBindJudgeData> samBindJudgeDataList,

    255: optional base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct SamBindJudgeData {
    1: optional i64 groupId, //  技能组Id
    2: optional i64 agentId, //  客服Id
    3: optional bool bindResult //  绑定结果 true 绑定有绑定关系 false 表示没有绑定关系
}

struct RecordRouterRequest {
    1: optional list<string> eventKey;
    2: optional list<i32> accessPartyId;
    3: optional i32 expire;

    255: optional base.Base Base,
}

struct RecordRouterResponse {
    1: optional list<string> keyList;
    255: required base.BaseResp BaseResp,
}

struct GetDimensionsConfigReq {
    1: required string FieldName, // 字段key
    2: optional string FieldId, // 字段id
    3: optional string Source, // 来源
    4: optional i64 AccessPartyId, // 接入方
    255: optional base.Base Base
}

struct GetDimensionsConfigResp {
    1: optional list<DimensionsConfigStruct> DimensionsConfigs,
    255: required base.BaseResp BaseResp
}
struct Target {
    1: required string Name                 // 显示名称
    2: optional string Value                // 节点值
    3: optional string Desc                 // 供前端使用的hover，将来配置到DM
    4: optional list<Target> TargetValue    // 如果有嵌套，要把嵌套也渲染出来
}

struct FormMax {
    1: required i64 Max
}

struct DimensionsConfigStruct {
    1: required string DimKey
    2: required string DimName
    3: optional i64 DimType
    4: optional string TargetCall
    5: optional string Operator
    6: optional list<Target> TargetValue
    7: optional FormMax FormProps
    8: optional string Domain
}

struct DynamicRuleUpdateRequest {
    1: optional string EventKey, // 字段key
    2: optional i64 AccessPartyId, // 接入方
    3: optional list<i64> RuleIdList, // 接入方
    255: optional base.Base Base
}

struct DynamicRuleUpdateResponse {
    1: optional list<i64> RuleIdList, // 接入方
    255: required base.BaseResp BaseResp
}

struct FixSkillGroupBindDataRequest {
    1: required i64 SkillGroupId, // 字段key
    255: optional base.Base Base
}

struct FixSkillGroupBindDataResponse {
    255: required base.BaseResp BaseResp
}

struct AddIdcToRuleExtraRequest {
    1: required i32 syncIdcType,
    2: optional i64 ruleId,
    3: optional string eventKey,
    4: optional i64 accessPartyId,
    5: required i32 limit,

    255: optional base.Base Base,
}

struct AddIdcToRuleExtraResponse {
    1: optional i32 totalRuleCnt,
    2: optional i32 byRuleIdResult,
    3: optional map<i32, i32> resultMap,
    4: optional map<i32, list<i64>> ruleIdsResultMap,

    255: required base.BaseResp BaseResp,
}

struct ListRouteRuleVersionRequest {
    1: optional string EventKey, // 字段key
    2: optional i64 AccessPartyId, // 接入方
    3: optional i64 RuleGroupId,
    4: optional i64 RuleGroupOriginId,
    255: optional base.Base Base
}

struct ListRouteRuleVersionResponse {
    1: optional string RuleGroupOriginId, // 字段key
    2: optional i64 AccessPartyId, // 接入方
    3: optional list<RuleGroupVersion> versions;
    255: required base.BaseResp BaseResp
}

struct RuleGroupVersion {
    1: optional i64 id;
    2: optional i32 version;
    3: optional string env;
    4: optional i32 status;
    6: optional string creator;
    7: optional i64 createTimestamp;
    8: optional string createTime;
    9: optional string updator;
    10: optional i64 updateTimestamp;
    11: optional string updateTime;
}

struct RollbackRouteRuleRequest {
    1: required i64 RuleGroupOriginId,
    2: required i64 RuleGroupId,
    3: required i64 AgentId,
    255: optional base.Base Base
}

struct RollbackRouteRuleResponse {
    1: optional i64 RuleGroupOriginId,
    2: optional i64 RuleGroupId,
    255: required base.BaseResp BaseResp
}

struct GetInfoKeyRequest {
    1: optional string InfoKey,
    2: optional string InfoId,
    255: optional base.Base Base
}

struct GetInfoKeyResponse {
    1: required map<string,string> ParamJson
    255: required base.BaseResp BaseResp
}

struct UpdateEventFilterRequest {
    1: required i64 Id,
    2: required i64 Type, // 1：脚本过滤，2：规则过滤
    3: optional ConditionGroupExpr Expression, //条件-DSL
    4: optional string TangoExpression, // 过滤脚本
    5: required string AgentId, // 操作人
    6: required string Version, // 接口版本 UI传v1
    255: optional base.Base Base
}

struct UpdateEventFilterResponse {
    1: required bool success,
    255: required base.BaseResp BaseResp
}

struct GetFieldListV2Request{
    1: required i64 EventId,  //事件ID
    2: optional i64 AccessPartyId, //接入方ID
    3: optional i64 TenantId, //租户ID
    4: optional string AppKey, //页面标识
    5: optional string Part, //页面div标识
    6: optional string EventKey,  //事件标识
    7: optional FieldDisplayType FieldDisplayType,
    8: optional string ActionKey, // 动作key
    9: optional i64 FieldId, // 字段id
    10: optional string FieldName, // 字段key
    255: required base.Base Base,
}

struct GetFieldListV2Response{
    1: required list<FieldCondition> FieldConditions,
    255: required base.BaseResp BaseResp
}

struct GetFieldValuesV2Request{
    1: required list<GetFieldConditionV2Struct> Fields,
    2: required i64 accessPartyId,
    255: required base.Base Base,
}

struct GetFieldConditionV2Struct {
    1: required i64 FieldId,
    2: required string Operator,//操作符
}

struct GetFieldValuesV2Response{
   1: required list<GetFieldValuesV2Struct> Values,
   255: required base.BaseResp BaseResp
}

struct GetFieldValuesV2Struct {
    1: required FieldOptionType FieldValueType,
    2: required string FieldValues,
    3: required i64 FieldValueId,
    4: required list<FieldValue> FieldValueList,
    5: required string Operator
}

struct ExecutionRecordListReq {
    1: required map<string,string> ParamExprMap, // 场景信息
    2: required i64 StartTime, // 开始时间
    3: required i64 EndTime, // 结束时间
    4: optional string Id, // 关键单据号
    5: optional bool IsFilterHit, // 是否过滤命中
    6: optional string KeyWords, // 关键词
    7: optional string LogId, // LogId
    8: optional string IssueNo, // 工单号
    9: optional string OrderId, // 订单号
    10: optional string UserId, // 用户id
    11: optional string SessionId, // 会话id
    12: optional i64 AccessPartyId, // 接入方id
    13: required i64 Page,
    14: required i64 PageSize,
    255: optional base.Base Base
}

struct ExecutionRecordListResp {
    1: optional list<ExecutionDataStruct> Data, // 列表数据
    2: optional i64 Total
    255: required base.BaseResp BaseResp
}

struct ExecutionDataStruct {
    1: required string Id, // 关键单据号
    2: required i64 ExecutionTime, // 执行时间(时间戳)
    3: required string ExecutionVersion, // 执行版本
    4: required bool IsHit, // 命中结果
    5: optional string HitRuleName, // 命中规则名称
    6: optional string Name, // 关键单据号类型
    7: optional string LogId
}

struct GetFilterItemListRequest {
    1: required string AppKey
    2: required string PSM
    3: required string Method
    4: optional string EventKey
    255: optional base.Base Base
}

struct GetFilterItemListResponse {
    1: optional list<FilterItem> FilterItemList
    2: optional list<HeaderItem> HeaderItemList
    3: optional FilterItem TestOption
    255: optional base.BaseResp BaseResp
}

struct HeaderItem {
    1: required string Field
    2: required string FieldName
    3: required i32 FilterType
}

struct FilterItem {
    1: required string Field
    2: optional string SecondField
    3: required i32 FilterType
    4: required string FieldName
    5: optional list<Option> Options
    6: optional bool IsRequired   // 是否必须
}

struct Option {
    1: required string Label
    2: required string Value
}

struct ExecutionRecordDetailReq {
    1: required string LogId, // LogId
    2: required map<string,string> ParamExprMap, // 场景信息
    3: required i64 StartTime, // 开始时间
    4: required i64 EndTime, // 结束时间
    255: optional base.Base Base
}

struct ExecutionRecordDetailResp {
    1: optional ExecutionRecordDetailStruct RecordDetail, // 执行明细详情
    255: required base.BaseResp BaseResp
}

struct ExecutionRecordDetailStruct {
    1: required string ExecutionVersion, // 执行版本
    2: optional string Id, // 关键单据号
    3: required i64 ExecutionTime, // 执行时间
    4: required i64 ExecutionCostTime, // 执行耗时
    5: required bool IsHit, // 命中结果
    6: required string LogId, // Logid
    7: required string ParamData, // 运行参数
    8: required string ExecutionResult, // 返回结果
    9: required list<RuleDetail> ExecutionDetails // 执行明细
    10: optional list<PlanDetail> PlanDetails // 执行计划明细
    11: optional i64 StrategyPackageType // 策略包类型
}

struct PlanDetail {
    1: required bool IsHit,
    2: required i64 StrategyPackageId,
    3: required string StrategyPackageName
}

struct RuleDetail {
    1: required string OpGroup, //
    2: optional list<ConditionGroupStruct> ConditionGroups //
    3: required bool IsHit, // 命中结果
    4: optional string GroupName, // 规则名称
    5: optional list<ConditionStruct> Conditions
}

struct ConditionGroupStruct {
    1: required string OpGroup, //
    2: optional list<ConditionStruct> Conditions, //
    3: optional list<ConditionGroupStruct> ConditionGroups //
    4: required bool IsHit, // 命中结果
    5: optional string GroupName, // 规则名称
}

struct ConditionStruct {
    1: required string OpCheck,  // 操作符
    2: required string Field, // 左值(原始值)
    3: required string Value, // 右值(原始值)
    4: required bool IsHit, // 命中结果
    5: required string FieldName, // 左值中文名
    6: required string ValueName // 右值中文名
    7: optional string RunTimeValue
    8: optional string Type // 场景:scene，画像:portrait，普通字段:normal
}

enum Version {
    Prod = 1
    Pre = 2
}

enum FormType {
    Cascader = 1, // 级联选择
    Checkbox = 2, // 复选框
    DataPicker = 3, // 日期选择器
    Input = 4, // 文本输入
    InputNumber = 5, // 数字输入
    Radio = 6, // 单选
    Select = 7, // 选择器
    Switch = 8, // 开关
    TagInput = 9, // 标签输入框
    TimePicker = 10, // 时间选择器
    TreeSelect = 11, // 数选择器
}

struct FormItem {
    1: optional string Field
    2: optional string Label
    3: optional FormType FormType
    4: optional bool Require
    5: optional bool Multiple
    6: optional list<OptionItem> Options
}
struct OptionItem {
     1: optional string Label
     2: optional string Value
     3: optional list<OptionItem> Children
}

struct GetTestParamRequest {
    1: optional map<string, string> ParamExprMap
    255: base.Base Base
}

struct GetTestParamResponse {
    1: optional list<FormItem> FieldList
    2: optional string Params
    255: base.BaseResp BaseResp
}

struct CreateExecutionPlanRequest {
    1: required string EventKey, // 应用实例code
    2: required i64 AccessPartyId, // 接入方
    3: required list<ExecutionPlanNode> ExecutionPlanNodes, // 执行计划节点
    4: required i64 CreateAgentId, // 创建人
    5: required i64 TenantId, // 租户id
    255: optional base.Base Base
}

struct ExecutionPlanNode {
    1: required ExecutionPlanNodeEnum Type, // 节点类型
    2: required i64 StrategyPackageId, // 策略包id
    3: required i64 ParentNodeId, // 父执行节点id，若为头结点，则为-1
    4: optional list<ExecutionPlanNode> ChildrenNodes, // 子执行节点id
    5: optional bool IsDefaultStrategyPackage, // 是否兜底策略包
    6: optional list<i64> ParentNodeIds
}

enum ExecutionPlanNodeEnum {
    RULE_CALCULATION = 1, // 规则计算节点
    DISTRIBUTION = 2, // 分流节点(废弃)
    DISTRIBUTION_NEW = 3 // 新分流节点
}

struct CreateExecutionPlanResponse {
    1: required i64 Id, // 执行计划id
    255: required base.BaseResp BaseResp,
}

struct UpdateExecutionPlanRequest {
    1: required i64 Id, // 执行计划版本id
    2: required list<ExecutionPlanNode> ExecutionPlanNodes, // 执行计划节点
    3: required i64 UpdateAgentId, // 操作人
    4: required i64 TenantId, // 租户id
    255: optional base.Base Base
}

struct UpdateExecutionPlanResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct CopyExecutionPlanRequest {
    1: required i64 Id,     // 执行计划版本id
    2: required i64 CreateAgentId,
    3: required i64 TenantId, // 租户id
    4: optional bool IsHistoryVersion, // 是否历史版本
    255: optional base.Base Base
}

struct CopyExecutionPlanResponse {
    1: required i64 NewId,
    2: optional DefaultStrategyPackage DefaultInfo,
    255: required base.BaseResp BaseResp,
}

struct DefaultStrategyPackage {
    1: required i64 Id,
    2: required i64 RuleGroupId,
    3: required i64 RuleGroupOriginId,
    4: optional i64 RuleId,
    5: optional i64 RuleOriginId
}

struct ClearExecutionPlanRequest {
    1: required i64 Id, // 执行计划id
    2: required i64 UpdateAgentId,
    3: required i64 TenantId, // 租户id
    255: optional base.Base Base
}

struct ClearExecutionPlanResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct PublishExecutionPlanRequest {
    1: required i64 Id, // 执行计划id
    2: required i64 UpdateAgentId,
    3: required i64 TenantId, // 租户id
    255: optional base.Base Base
}

struct PublishExecutionPlanResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct ListExecutionPlanRequest {
    1: required string EventKey,
    2: required i64 AccessPartyId,
    3: required ExecutionPlanQueryTypeEnum QueryType,
    255: optional base.Base Base
}

enum ExecutionPlanQueryTypeEnum {
    PROD = 1, // 线上版本
    PRE = 2, // 预发布版本
    HISTORY = 3  // 历史版本
}

struct ListExecutionPlanResponse {
    1: required list<ExecutionPlanStruct> ExecutionPlans,
    255: required base.BaseResp BaseResp,
}

struct ExecutionPlanStruct {
    1: required i64 Id,
    2: required i64 OriginId,
    3: required ExecutionPlanStatus Status,
    4: required i64 Version,
    5: required list<ExecutionPlanNode> ExecutionPlanNodes,
    6: required i64 CreateAgentId,
    7: required string CreateTime,
    8: required i64 UpdateAgentId,
    9: required string UpdateTime
}

enum ExecutionPlanStatus {
    UNENABLE = 0, // 禁用
    ENABLE = 1, // 启用
    DRAFT = 2, // 预发布
    DELETE = 3 // 删除
}

enum StrategyRuleType {
    RULE_GROUP = 1,
    TREE = 2
}

struct CreateStrategyPackageRequest {
    1: required string EventKey, // 应用code
    2: required i64 AccessPartyId, // 接入方
    3: required string Name, // 策略包名称
    4: required string Description, // 策略包描述
    5: required StrategyRuleType RuleType, // 规则组 or 决策树
    6: required i64 CreateAgentId, // 创建人
    7: required i64 TenantId, // 租户id
    8: optional list<string> SceneIds, // 场景id
    255: optional base.Base Base
}

struct CreateStrategyPackageResponse {
    1: required i64 Id, // 策略包id
    255: required base.BaseResp BaseResp,
}

struct UpdateStrategyPackageRequest {
    1: required i64 Id, // 策略包id
    2: required string Name, // 策略包名称
    3: required string Description, // 策略包描述
    4: required i64 StrategyType, // 策略包类型
    5: required i64 UpdateAgentId, // 更新人
    6: optional list<string> SceneIds, // 场景id
    255: optional base.Base Base
}

struct UpdateStrategyPackageResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct CreateRuleGroupByStrategyRequest {
    1: required string StrategyId, //策略包id
    2: required string GroupDisplayName, //规则组名称
    3: required string CreatorAgentId, //创建人id
    4: required string Product, //产品
    5: required list<AntlrRule> RuleList, // 规则集合
    7: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    8: required i64 AccessPartyId, //接入方ID
    9: optional RuleStatus status, //规则组状态
    255: optional base.Base Base,
}

struct CreateRuleGroupByStrategyResponse {
    1: optional i64 RuleGroupId,
    255: required base.BaseResp BaseResp
}

struct CopyStrategyPackageRequest {
    1: required i64 Id, // 策略包id
    2: required i64 CreateAgentId,
    255: optional base.Base Base
}

struct CopyStrategyPackageResponse {
    1: required i64 NewId,
    255: required base.BaseResp BaseResp
}

struct UpdateStrategyPackageStatusRequest {
    1: required i64 Id,
    2: required StrategyPackageStatus Status,
    3: required i64 CreateAgentId,
    4: optional bool IsDefaultStrategyPackage,
    255: optional base.Base Base
}

enum StrategyPackageStatus {
    NEW = 1,    // 新建
    RUNNING = 2,    // 运行中
    UNUSE = 3,    // 未使用
    DEL = 4     // 删除
}

struct UpdateStrategyPackageStatusResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp
}

struct ListStrategyPackageRequest {
    1: required string EventKey,
    2: required i64 AccessPartyId,
    3: required i64 PageNo,
    4: required i64 PageSize,
    255: optional base.Base Base
}

struct ListStrategyPackageResponse {
    1: required list<StrategyPackage> StrategyPackages,
    255: required base.BaseResp BaseResp
}

struct StrategyPackage {
    1: required i64 Id,
    2: required string Name,
    3: required StrategyPackageStatus Status,
    4: required StrategyRuleType StrategyType,
    5: required string CreateTime,
    6: required i64 CreateAgentId,
    7: required string UpdateTime,
    8: required i64 UpdateAgentId,
    9: required i64 RuleOriginId
}

struct CreateDecisionTreeRequest {
    1: required i64 StrategyId,     // 策略包id
    2: required i64 CreateAgentId,
    3: optional list<DecisionTreeNode> DecisionTreeNodes,
    255: optional base.Base Base
}

struct DecisionTreeNode {
    1: required i64 DecisionTreeNodeId,
    2: required DecisionTreeNodeType Type, // 节点类型
    3: required i64 ParentNodeId, // 父执行节点id
    4: optional list<DecisionTreeNode> ChildrenNodes, // 子执行节点
    5: optional i64 CopyDecisionTreeNodeId,
    6: optional string NodeName,
    7: optional list<i64> SceneIds,
}

enum DecisionTreeNodeType {
    CALCULATE_NODE = 1,
    RESULT_NODE = 2
}

struct CreateDecisionTreeResponse {
    1: required i64 Id,     // 决策树id
    255: required base.BaseResp BaseResp,
}

struct UpdateDecisionTreeRequest {
    1: required i64 DecisionTreeId,
    2: required list<DecisionTreeNode> DecisionTreeNodes,
    3: required i64 UpdateAgentId,
    255: optional base.Base Base
}

struct UpdateDecisionTreeResponse {
    1: required i64 Id,    // 决策树id
    255: required base.BaseResp BaseResp,
}

struct CreateDecisionTreeNodeRequest {
    1: required i64 DecisionTreeId, // 决策树id
    2: required DecisionTreeNodeType Type, // 节点类型
    3: optional list<AntlrRule> Rules,
    4: required i64 CreateAgentId,
    5: optional list<i64> SceneIds,
    6: optional i64 StrategyPackageId,
    7: optional string Name,
    255: optional base.Base Base
}

struct CreateDecisionTreeNodeResponse {
    1: required i64 DecisionTreeNodeId,     // 决策树节点id
    2: optional i64 DecisionTreeNodeOriginId // 决策树节点原始id
    255: required base.BaseResp BaseResp,
}

struct BatchCreateDecisionTreeNodeRequest {
    1: required list<CreateDecisionNodeStruct> Data,
    255: optional base.Base Base
}

struct BatchCreateDecisionTreeNodeResponse {
    1: required list<CreateDecisionNodeRespStruct> NodeData,
    255: required base.BaseResp BaseResp,
}


struct CreateDecisionNodeStruct {
    1: required i64 DecisionTreeId, // 决策树id
    2: required DecisionTreeNodeType Type, // 节点类型
    3: optional list<AntlrRule> Rules,
    4: required i64 CreateAgentId,
    5: optional list<i64> SceneIds,
    6: optional i64 StrategyPackageId,
    7: optional string Name,
    8: optional string CovertId, // 前端专用id
}

struct CreateDecisionNodeRespStruct {
    1: required i64 DecisionTreeNodeId,     // 决策树节点id
    2: optional i64 DecisionTreeNodeOriginId // 决策树节点原始id
    3: optional string CovertId, // 前端专用id
}

struct UpdateDecisionTreeNodeRequest {
    1: required i64 DecisionTreeNodeId,     // 决策树节点id
    2: optional list<AntlrRule> Rules,
    3: required i64 UpdateAgentId,
    4: optional list<i64> SceneIds,
    5: optional i64 DecisionTreeId,
    6: optional i64 StrategyPackageId,
    7: optional string Name,
    255: optional base.Base Base
}

struct UpdateDecisionTreeNodeResponse {
    1: required i64 DecisionTreeNodeId,     // 决策树节点id
    255: required base.BaseResp BaseResp,
}

struct DelDecisionTreeNodeRequest {
    1: required list<i64> DecisionTreeNodeIds,    // 决策树节点id
    2: required i64 AgentId,
    255: optional base.Base Base
}

enum DecisionTreeNodeStatus {
    ENABLE = 1,
    DRAFT = 2,
    DELETE = 3
}

struct DelDecisionTreeNodeResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct CopyDecisionTreeNodeRequest {
    1: required list<i64> DecisionTreeNodeIds,     // 决策树节点id
    2: required i64 CreateAgentId,
    255: optional base.Base Base
}

struct CopyDecisionTreeNodeResponse {
    1: required map<i64,i64> DecisionTreeNodes,
    255: required base.BaseResp BaseResp,
}

struct SaveFactorsRequest {
    1: required string KeyWord,
    2: required list<FieldParamsStruct> FieldParams,
    3: required list<FactorsField> Fields,
    4: required string Code,
    5: required string OperatorAgent
}

struct FieldParamsStruct {
    1: required string Name,
    2: required string DisplayName,
    3: required string Type,
    4: required bool Required
}

struct FactorsField {
    1: required string FieldDisplayName,
    2: required string FieldPath,
    3: required list<string> OperatorList,
    4: required map<string,FactorValueStruct> FieldValues,
    5: required list<i64> AccessPartyIds
    6: required string QueryCode
    7: required list<QueryParamStruct> QueryParams
    8: required string QueryName
    9: required string SpaceId
    10: optional string FieldHierarchy
}

struct QueryParamStruct {
    1: required string ParamField,
    2: required string type,
    3: required string ExecutionField
}

struct FactorValueStruct {
    1: required string DataType,
    2: required string BizType,
    3: required string FieldValues
}

struct SaveFactorsResponse {
    255: required base.BaseResp BaseResp,
}

struct CopyDecisionTreeRequest {
    1: required i64 DecisionTreeId,
    2: required i64 CreateAgentId,
    3: optional bool IsVersion,
    255: optional base.Base Base
}

struct CopyDecisionTreeResponse {
    1: required i64 NewDecisionTreeId,
    2: required list<DecisionTreeNode> DecisionTreeNodes,
    255: required base.BaseResp BaseResp,
}

struct PublishDecisionTreeRequest {
    1: required i64 DecisionTreeId,
    2: required i64 CreateAgentId,
    255: optional base.Base Base
}

struct PublishDecisionTreeResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct ClearDecisionTreeRequest {
    1: required i64 DecisionTreeId,
    2: required i64 CreateAgentId,
    255: optional base.Base Base
}

struct ClearDecisionTreeResponse {
    1: required bool IsSuccess,
    255: required base.BaseResp BaseResp,
}

struct GetStrategyDetailRequest {
    1: required i64 StrategyId,
    2: optional StrategyRuleType StrategyType,
    3: optional bool IsDraft,
    255: optional base.Base Base
}

struct GetStrategyDetailResponse {
    1: optional RuleGroupDetail RuleGroupDetail, // 规则组详情
    2: optional list<DecisionTreeNode> DecisionTreeNodes, // 决策树详情
    255: required base.BaseResp BaseResp,
}

struct GetRuleGroupDetailByDecisionTreeNodeResponse {
    1: optional map<i64,RuleGroupDetail> RuleGroupDetail, // 规则组详情
    255: required base.BaseResp BaseResp,
}

struct GetRuleGroupDetailByDecisionTreeNodeRequest {
    1: required list<i64> DecisionTreeNodeIds,
    255: optional base.Base Base
}

struct GetBatchIdResponse {
    1: required string BatchId,
    2: optional i64  ExecuteTimeRange,
    255: required base.BaseResp BaseResp,
}

struct GetBatchIdRequest {
    1: required string EventKey,
    2: required i64 AccessPartyId
    255: optional base.Base Base
}

struct TerminateAutomatedRuleResponse {
    255: required base.BaseResp BaseResp,
}

struct TerminateAutomatedRuleRequest {
    1: required string EventKey,
    2: required i64 AccessPartyId
    255: optional base.Base Base
}

struct QueryRuleUpdateRecordRequest {
    1: optional string EventKey,
    2: optional i64 AccessPartyId,
    3: optional string Product,
    4: optional i64 Seconds,
    255: optional base.Base Base
}

struct QueryRuleUpdateRecordResponse {
    1: optional list<QueryRuleUpdateRecordStruct> Data,
    255: required base.BaseResp BaseResp
}

struct QueryRuleUpdateRecordStruct {
    1: required i64 RuleId,
    2: required i64 RuleGroupId,
    3: required string RuleName,
    4: required string Operator,
    5: required string OperatorTime
}

struct RuleMatchDataInfo {
    1: required string LogId,
    2: optional string EventName,
    3: optional string AccessPartyId,
    4: optional string AccessPartyName,
    5: required i64 RuleGroupId,
    6: optional i64 HitRuleId,
    7: optional string HitRuleName,
    8: optional i64 HitRuleOriginId,
    9: optional string CreateTime,
    10: optional list<ConditionInfo> FalseReason,
    11: optional string ParamData,
    12: optional string Result,
    13: optional string RuleGroupTypeName,
    14: optional string SkillGroupName,
    15: optional i64 Id,
    16: optional string RuleGroupName,
    17: optional list<ConditionInfo> MatchedConds   // 符合的条件
    18: optional i64 RuleGroupCurrentID // 规则组当前ID
}

struct ConditionInfo {
    1: required string LeftValue, // 左值
    2: required string RightValue, // 右值
    3: required string OpCheck, // 操作符
    4: required string RuleName, // 规则名
    5: required string FieldName, // 字段名(左值)
    6: optional FieldProcedure FieldPRD  // 字段值形成过程(左值)
    7: optional string LeftValueZH  // 左值(中文)
    8: optional string RightValueZH // 右值(中文)
    9: optional string UniqueID // 用于解决MIS平台的数据复用问题
}

struct FieldProcedure {
    1: required string FieldName
    2: required string Type // 上游传参 or 下游获取
    3: optional string IfNullReason // 如果字段为空的原因
    4: optional ModProcedure ModPRD
}

struct ModProcedure {
    1: required string ModName
    2: optional string ArgsScript
    3: optional string DMVariables
    4: optional string DMArgs
    5: optional string DMQuery
    6: optional string DMRespData
    7: optional string RespScript
    8: optional string DesMap
    9: optional string CostTime
    10: optional string ErrMsg
}
struct TriggerTraceDataInfo {
    1: required string LogId,
    2: optional string UniqueId,
    3: optional string CepEvent,
    4: optional string FactData,
    5: optional string ActionExecuteResult,
    6: optional i64 RuleGroupId,
    7: optional i64 RuleId,
    8: optional string RequestBody,
    9: optional string Topic,
    10: optional string RuleMatchResult,
    11: optional string MsgId,
    12: optional string EventKey,
    13: optional string CreateTime,
    14: optional i64 Id,
    15: optional string EventOperationType,
    16: optional string EventName,
    17: optional list<ActionExecuteResult> ActionResultList,
    18: optional string RuleGroupName,
    19: optional string RuleName,
}

struct ActionExecuteResult{
    1: optional string actionName,
    2: optional string actionKey,
    3: optional string actionType,
    4: optional string psm,
    5: optional string method,
    6: optional string result,
}

struct OnCallDataInfo {
    1: optional string AccessPartyId,
    2: required string Date,
    3: optional list<ConditionInfo> FalseReason,
    4: required i64 HitRuleId,
    5: required i64 HitRuleOriginId,
    6: required string ParamData,
    7: required i64 RuleGroupId,
    8: required string SkillGroupId,
    9: required string Result,
    10: required string HitRuleName,
    11: required string SkillGroupName,
    12: required i64 RequestTime,
    13: required string LogId,
    14: optional string EventName,
    15: optional string AccessPartyName,
    16: optional string AccessoryPartyId,
    17: optional string AccessoryPartyName,
    18: optional string ArtificialTaskId,
    19: optional string IntelligentTaskId
}

struct EventTraceDataInfo {
    1: required string LogId,
    2: optional string UniqueId,
    3: optional string CepEvent,
    4: optional string FactData,
    5: required string Source,
    6: optional i64 RuleGroupId,
    7: optional string Result,
    8: optional string MsgBody,
    9: optional string Topic,
    10: optional string Tag,
    11: optional string MsgId,
    12: optional string EventKey,
    13: optional string CreateTime,
    14: optional i64 Id,
    15: optional string EventName,
}

struct GetOptionItemListRequest {
    1: optional string key,
    255: optional base.Base Base
}

struct GetOptionItemListResponse {
    1: optional list<OptionItem> items,
    255: required base.BaseResp BaseResp
}

service AdminService {

    //查询条件属性列表
    FieldListGetResponse GetFieldList(1: FieldListGetRequest req)

    //查询条件属性值列表
    FieldValuesGetResponse GetFieldValues(1: FieldValuesGetRequest req)

    FieldAndValueListResponse GetFieldAndValueList(1: FieldListGetRequest req)

    //添加字段
    FieldCreateResponse CreateField(1: FieldCreateRequest req)

    //修改字段
    UpdateFieldResponse UpdateField(1: UpdateFieldRequest req)

    //删除字段
    FieldDeleteResponse DeleteField(1: FieldDeleteRequest req)

    //查询动作元数据列表--SLA/Trigger use
    ActionMetaListGetResponse GetActionMetaList(1: ActionMetaListGetRequest req)
    PlaceholderMetaListGetResponse GetPlaceholderMetaList(1: PlaceholderMetaListGetRequest req)

    //查询SLA目标元数据列表--SLA use
    SLAAimMetaListGetResponse GetSLAAimMetaList(1: SLAAimMetaListGetRequest req)
    SLAAimMetaSimpleListGetResponse GetSLAAimMetaSimpleList(1: SLAAimMetaListGetRequest req)

    //添加SLA目标元数据--SLA use
    SLAAimMetaCreateResponse CreateSLAAimMeta(1: SLAAimMetaCreateRequest req)

    //查询后台规则分页列表--SLA use
    AdminRulePageGetResponse GetAdminRulePage(1: AdminRulePageGetRequest req)

    //查询后台规则列表
    AdminRuleListGetResponse GetAdminRuleList(1: AdminRuleListGetRequest req)

    //查询后台规则
    AdminRuleGetByIdResponse GetAdminRuleById(1: AdminRuleGetByIdRequest req)

    //添加后台规则
    AdminRuleCreateResponse CreateAdminRule(1: AdminRuleCreateRequest req)

    //修改后台规则
    AdminRuleUpdateResponse UpdateAdminRule(1: AdminRuleUpdateRequest req)

    //删除后台规则
    AdminRuleDeleteResponse DeleteAdminRule(1: AdminRuleDeleteRequest req)

    //拖拽修改后台规则
    AdminRuleBatchUpdateResponse BatchUpdateAdminRule(1: AdminRuleBatchUpdateRequest req)

    //改造新加 - 查询后台规则列表
    GetRuleListResponse GetRuleList(1: GetRuleListRequest req)

    //根据id查询后台规则
    GetRuleByIdResponse GetRuleById(1: GetRuleByIdRequest req)

    //批量查询规则
    GetRulesByIdsResponse GetRulesByIds(1: GetRulesByIdsRequest req)

    //添加后台规则
    CreateRuleResponse CreateRule(1: CreateRuleRequest req)

    //修改后台规则
    UpdateRuleResponse UpdateRule(1: UpdateRuleRequest req)

    //启用、禁用、删除后台规则
    UpdateRuleStatusResponse UpdateRuleStatus(1: UpdateRuleStatusRequest req)

    //调整规则优先级
    UpdateRulePriorityResponse UpdateRulePriority(1: UpdateRulePriorityRequest req)

    // 发布规则
    PublishRuleGroupResponse PublishRuleGroup(1: PublishRuleGroupRequest req)

    // 复制规则
    CopyRuleGroupResponse CopyRuleGroup(1: CopyRuleGroupRequest req)

    // 清空所有的草稿规则
    ClearDraftRulesResponse ClearDraftRules(1: ClearDraftRulesRequest req)

    // 获取规则操作日志
    GetRuleOperationLogsResponse GetRuleOperationLogs(1: GetRuleOperationLogsRequest req)

    // 创建规则组
    CreateRuleGroupResponse CreateRuleGroup(1: CreateRuleGroupRequest req)

    // 查询规则组列表
    RuleGroupPageQueryResponse ListRuleGroup(1: RuleGroupPageQueryRequest req)

    // 修改规则组
    UpdateRuleGroupResponse UpdateRuleGroup(1: UpdateRuleGroupRequest req)

    // 修改规则组状态 启用禁用
    UpdateRuleGroupStatusResponse UpdateRuleGroupStatus(1: UpdateRuleGroupStatusRequest req)

    // 查询规则组详情
    GetRuleGroupResponse GetRuleGroup(1: GetRuleGroupRequest req)

    // 查询规则组详情
    GetRuleGroupResponse CopyRuleGroupDetail(1: CopyRuleGroupRequest req)

    // 查询事件类型
    ListEventResponse ListEvent(1: ListEventRequest req)

    // 新增Event事件
    EventCreateResponseV2 CreateEventV2(1: EventCreateRequestV2 req)

    // 工单RPC类型事件批量触发
    TriggerTicketRpcEventResponse TriggerTicketEvent(1: TriggerTicketRpcEventRequest req)

    // 触发器规则ID请求
    TriggerRulesBriefResponse TriggerRuleBriefList(1: TriggerRulesBriefRequest req)

    CloseLibraResponse CloseLibra(1: CloseLibraRequest req)

    // 批量创建规则
    BatchCreateRuleGroupResponse BatchCreateRuleGroup(1: BatchCreateRuleGroupRequest req)

    // 批量更新规则
    BatchUpdateRuleGroupResponse BatchUpdateRuleGroup(1: BatchUpdateRuleGroupRequest req)

    // 路由规则列表查询
    GetRuleGroupListByEventKeyResponse GetRuleGroupListByEventKey(1: GetRuleGroupListByEventKeyRequest req)

    // 获取已配置信息
    GetExtraInfoResponseV2 GetExtraInfoV2(1: GetExtraInfoRequestV2 req)

    //查询触发时机直连人工配置规则列表
    GetEventRuleGroupListResponse GetEventRuleGroupList(1: GetEventRuleGroupListRequest req)

    // 查询触发时机规则组详情
    GetEventRuleGroupResponse GetEventRuleGroup(1: GetEventRuleGroupRequest req)

    // 更新发布触发时机规则
    UpdateEventRuleGroupResponse UpdateEventRuleGroup(1: UpdateEventRuleGroupRequest req)

    // 获取批量规则组详情
    BatchGetRuleGroupResponse BatchGetRuleGroup(1: BatchGetRuleGroupRequest req)

    // 修改规则组状态 启用禁用
    BatchUpdateRuleGroupStatusResponse BatchUpdateRuleGroupStatus(1: BatchUpdateRuleGroupStatusRequest req)

    // 获取动作列表
    GetActionListResponse GetActionList(1: GetActionListRequest req)

    // 获取动作参数
    GetActionFieldByActionKeysResponse GetActionFieldByActionKeys(1: GetActionFieldByActionKeysRequest req)

    // 新增绑定配置信息
    CreateSamBindResponse CreateSamBind(1: CreateSamBindRequest req)

    // 编辑绑定配置信息
    UpdateSamBindResponse UpdateSamBind(1: UpdateSamBindRequest req)

    // 批量新增绑定配置信息
    BatchCreateSamBindResponse BatchCreateSamBind(1: BatchCreateSamBindRequest req)

    // 批量导入删除绑定配置信息
    BatchDelSamBindByExcelResponse BatchDelSamBindByExcel(1: BatchDelSamBindByExcelRequest req)

    // 批量转移绑定配置信息
    BatchTransferSamBindResponse BatchTransferSamBind(1: BatchTransferSamBindRequest req)

    // 批量删除绑定配置信息
    BatchDelSamBindResponse BatchDelSamBind(1: BatchDelSamBindRequest req)

    // 批量导出绑定配置信息
    BatchExportSamBindResponse BatchExportSamBind(1: BatchExportSamBindRequest req)

    // 查询商家信息
    GetSellerInfoResponse GetSellerInfo(1: GetSellerInfoRequest req)

    // 搜索绑定配置信息
    SearchSamBindResponse SearchSamBind(1: SearchSamBindRequest req)

    // 查询是否有sam绑定关系
    SamBindJudgeResponse SamBindJudge(1: SamBindJudgeRequest req)

    RecordRouterResponse AddRecordRouterRedisKey(1: RecordRouterRequest req)

    GetDimensionsConfigResp GetDimensionsConfig(1: GetDimensionsConfigReq req)

    DynamicRuleUpdateResponse DynamicRuleUpdate(1: DynamicRuleUpdateRequest req)
    SearchSamBindResponse BatchProcessSamBind(1: SearchSamBindRequest req)
    FixSkillGroupBindDataResponse FixSkillGroupBindData(1: FixSkillGroupBindDataRequest req)
    //查询组总数，并根据extra进行分组
    RuleGroupCountQueryResponse QueryRuleGroupCountByExtraGroup(1: RuleGroupPageQueryRequest req)
    //刷数据接口：add idc to rule
    AddIdcToRuleExtraResponse AddIdcToRuleExtra(1: AddIdcToRuleExtraRequest req)

    ListRouteRuleVersionResponse ListRouteRuleVersion(1: ListRouteRuleVersionRequest req)

    RollbackRouteRuleResponse RollbackRouteRule(1: RollbackRouteRuleRequest req)

    // 获取infoKey
    GetInfoKeyResponse GetInfoKey(1: GetInfoKeyRequest req)

    UpdateEventFilterResponse UpdateEventFilter(1: UpdateEventFilterRequest req)

    // 获取字段列表
    GetFieldListV2Response GetFieldListV2(1: GetFieldListV2Request req)

    // 获取字段值
    GetFieldValuesV2Response GetFieldValuesV2(1: GetFieldValuesV2Request req)

    ExecutionRecordListResp ExecutionRecords(1: ExecutionRecordListReq req)

    GetFilterItemListResponse  GetFilterItemList(1: GetFilterItemListRequest req)

    ExecutionRecordDetailResp ExecutionRecordDetail(1: ExecutionRecordDetailReq req)

    router_sim_i18n.TestRuleResponse TestRule(1: router_sim_i18n.TestRuleRequest req)

    GetTestParamResponse GetTestParam(1: GetTestParamRequest req)

    router_sim_i18n.GetTestBatchResponse GetTestBatch(1: router_sim_i18n.GetTestBatchRequest req)

    router_sim_i18n.GetResultByBatchIdResponse GetResultByBatchId(1: router_sim_i18n.GetResultByBatchIdRequest req)

    // 服务策略2.0部分
    CreateExecutionPlanResponse CreateExecutionPlan(1: CreateExecutionPlanRequest req)

    UpdateExecutionPlanResponse UpdateExecutionPlan(1: UpdateExecutionPlanRequest req)

    CopyExecutionPlanResponse CopyExecutionPlan(1: CopyExecutionPlanRequest req)

    ClearExecutionPlanResponse ClearExecutionPlan(1: ClearExecutionPlanRequest req)

    PublishExecutionPlanResponse PublishExecutionPlan(1: PublishExecutionPlanRequest req)

    ListExecutionPlanResponse ListExecutionPlan(1: ListExecutionPlanRequest req)

    CreateStrategyPackageResponse CreateStrategyPackage(1: CreateStrategyPackageRequest req)

    UpdateStrategyPackageResponse UpdateStrategyPackage(1: UpdateStrategyPackageRequest req)

    CreateRuleGroupByStrategyResponse CreateRuleGroupByStrategy(1: CreateRuleGroupByStrategyRequest req)

    CopyStrategyPackageResponse CopyStrategyPackage(1: CopyStrategyPackageRequest req)

    UpdateStrategyPackageStatusResponse UpdateStrategyPackageStatus(1: UpdateStrategyPackageStatusRequest req)

    ListStrategyPackageResponse ListStrategyPackage(1: ListStrategyPackageRequest req)

    CreateDecisionTreeResponse CreateDecisionTree(1: CreateDecisionTreeRequest req)

    UpdateDecisionTreeResponse UpdateDecisionTree(1: UpdateDecisionTreeRequest req)

    CopyDecisionTreeResponse CopyDecisionTree(1: CopyDecisionTreeRequest req)

    PublishDecisionTreeResponse PublishDecisionTree(1: PublishDecisionTreeRequest req)

    ClearDecisionTreeResponse ClearDecisionTree(1: ClearDecisionTreeRequest req)

    CreateDecisionTreeNodeResponse CreateDecisionTreeNode(1: CreateDecisionTreeNodeRequest req)

    BatchCreateDecisionTreeNodeResponse BatchCreateDecisionTreeNode(1: BatchCreateDecisionTreeNodeRequest req)

    UpdateDecisionTreeNodeResponse UpdateDecisionTreeNode(1: UpdateDecisionTreeNodeRequest req)

    DelDecisionTreeNodeResponse DelDecisionTreeNode(1: DelDecisionTreeNodeRequest req)

    CopyDecisionTreeNodeResponse CopyDecisionTreeNode(1: CopyDecisionTreeNodeRequest req)

    SaveFactorsResponse SaveFactors(1: SaveFactorsRequest req)

    GetStrategyDetailResponse GetStrategyDetail(1: GetStrategyDetailRequest req)

    GetRuleGroupDetailByDecisionTreeNodeResponse GetRuleGroupDetailByDecisionTreeNode(1: GetRuleGroupDetailByDecisionTreeNodeRequest req)

    GetBatchIdResponse GetBatchId(1: GetBatchIdRequest req)

    TerminateAutomatedRuleResponse TerminateAutomatedRule(1: TerminateAutomatedRuleRequest req)

    QueryRuleUpdateRecordResponse QueryRuleUpdateRecord(1: QueryRuleUpdateRecordRequest req)

    GetOptionItemListResponse GetOptionItemList(1: GetOptionItemListRequest req)
}