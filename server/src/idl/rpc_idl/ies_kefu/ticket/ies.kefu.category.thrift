include "../../base.thrift"
namespace java com.bytedance.ies.kefu.category.thrift
namespace go bytedance.ies.kefu.category
namespace py bytedance.ies.kefu.category

//model
struct Resource{
    1: optional i64 Id,
    2: string Name,
    4: string DockingName,//对接人名称
    5: i64 AccessPartyId,//接入方id
    6: i32 SubFlag,//是否开启子资源 0-未开启 1-开启
    7: i32 EnableFlag,//是否可用
    8: list<SubResource> SubList
    9: optional string AccessPartyName,//接入方名称
    10: SourceType SourceType,//来源类型，1-自建 2-共享
    11: string ModifyAt, // 更新时间
    12: string modifierName, //更新人
}

enum SourceType{
    CREATED = 1,
    SHARED = 2,
}

struct ResourceBase {
    1: i64 Id, // 主键
    2: i64 TenantId, // 租户ID
    3: string Name, // 资源名称
    4: string DockingName, // 对接人名称
    5: i64 AccessPartyId, // 接入方ID
    6: i32 EnableFlag, // 是否有效 0-有效 1-无效
    7: i32 SubFlag, // 是否开启子资源 0-未开启 1-开启
    8: string CreateAt, // 创建时间
    9: string ModifyAt, // 修改时间
    10: i64 AppSystemId, // 应用系统ID
    11: optional list<i64> SharedAccessPartyIdList,//子资源的共享接入方id的并集
}

struct SubResourceBase {
    1: i64 Id, // 主键
    2: i64 TenantId, // 租户ID
    3: i64 ResourceId, // 所属的资源ID
    4: string Name, // 子资源名称
    5: i32 EnableFlag, // 是否有效 0-有效 1-无效
    6: string CreateAt, // 创建时间
    7: string ModifyAt, // 修改时间
    8: optional list<i64> SharedAccessPartyIdList,//共享接入方id集合
    9: optional i64 LockId,   //锁定id
    10: optional string LockDesc, //锁定文案
}

struct SubResource{
    1: optional i64 Id,
    2: string Name,
    6: i32 EnableFlag,//是否可用  0为可用
    7: optional list<i64> SharedAccessPartyIdList,//共享接入方id集合
    8: optional i64 LockId,   //锁定id
    9: optional string LockDesc, //锁定文案
}

struct SubResourceNew{
    1: optional i64 Id,
    2: string Name,
    3: bool EnableFlag,//是否可用 true为可用
}

struct AccessParty{
    1: optional i64 Id,
    2: string Name,//接入方名称
    3: optional list<AccessParty> SubAccessParty,//二级接入方
    4: i32 EnableFlag,//是否可用
    5: optional list<AgentItem> BusinessOwners, # 负责人
    6: optional i64 UpdateTime, # 更新时间
}

struct AgentItem {
    1: i64 AgentId, # 人员ID
    2: string Name, # 人员名字
    3: string Email, # 人员邮箱
}

struct GetResourceParamEnumRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId,//根据接入方id进行隔离
    255: optional base.Base Base,
}

struct GetResourceParamEnumResponse{
    1: required map<ResourceParamType,list<ParamModel>> paramModelMap,//参数枚举集合,key为参数名称和GetResourceList接口的参数对齐
    255: base.BaseResp BaseResp,
}

enum ResourceParamType{
    ACCESS_PARTY = 1, // 接入方
    DOCKING_NAME = 2, // 对接人
}

struct ParamModel{
    1:required string name,//用于下拉显示的文案
    2:required string key,//用户查询时传递的key
}

struct App{
    1: optional i64 Id,
    2: string Name,
    3: optional i64 ResourceCount,//使用资源数量
    4: optional i64 CategoryCount,//使用标签数量
    5: string DockingName,//对接人名称
    6: optional string LocalUpdateTime,//更新时间
    7: optional i32 EnableFlag,//
    8: optional i64 BytedanceAppId,//字节应用id
    9: optional i64 NewTenantId,//字节应用id
}

struct Category{
    1: optional i64 Id,//标签id 新增为0
    2: optional i64 ResourceId,//资源id
    3: optional i64 SubResourceId,//子资源id
    4: string Name,//标签名称
    5: optional string Path,//标签全路径
    6: optional i64 ParentId,//父标签id
    7: optional i32 Level,//层级
    8: i32 OrderIndex,//当前排序
    9: optional list<Category> SubCategoryList,//子标签集合
    10: optional bool IsBindApp//是否绑定app
    11: optional i32 EnableFlag,//
    12: optional i64 BytedanceAppId,//字节应用id
    13: optional RegistCategory UnDisableRegist,
    14: optional RegistCategory UnUpdateRegist,
    15: optional RegistCategory UnAddSubRegist,
    16: optional bool CouldDisable, // 是否能删除
    17: optional bool CouldUpdate, // 是否能修改
    18: optional bool CouldAddSub, // 是否能添加子标签
    19: optional i32 DisplayLevel, // 标签显示级别(0-都展示；1-筛选时才展示，创建时不展示；2-都不展示)
    20: optional string ResourceName, // 资源名称
    21: optional string SubResourceName, // 子资源名称
    22: optional i64 CategoryMetaId, // 元标签id
    23: optional i64 LockId,   //锁定id
    24: optional string LockDesc, //锁定文案
    25: optional i32 IsIssueFlag, // 是否为issue标签(0-否，1-是)
}

//common
struct CommonRequest{
    1: required i64 TenantId,//租户id
    2: optional i64 PageNo,//当前页码
    3: optional i64 PageSize,//单页数量
    4: required i64 AgentId,//客服Id
    5: required string AgentName,//客服名称
    6: required string CountryCode,//国家码
    7: optional i64 AppSystemId=2,//应用系统id  非新工单系统必传
    8: optional list<string> EnableCountryList, //生效国家
    9: optional string AgentEmail,//客服邮箱
}

struct CommonEditRequest{
    1: required i64 TenantId,//租户id
    2: required i64 AppSystemId,//应用系统id
    3: optional i64 OperatorId,//客服Id
    4: required string OperatorName,//客服名称,如果是服务，传psm
}

struct CommonResponse {
    1: optional list<Category> AddCategoryList, // 打平的标签列表
    2: optional string TicketLink; // Ticket链接详情
    3: optional list<i64> CategoryIdList,//新增的标签id
    4: optional i64 ResourceId,//新增的资源id
    5: optional list<i64> SubResourceIdList,//新增的子资源id
    6: optional i64 AppId, //新增的APPID
    255: base.BaseResp BaseResp,
}

//request and response
struct ResourceRequest{
    1: required CommonRequest CommonRequest,
    2: required Resource Resource,//资源列表
    255: optional base.Base Base,
}

struct SubResourceRequest{
    1: required CommonEditRequest CommonEditRequest,
    2: required i64 ResourceId,//资源id
    3: list<SubResourceNew> SubList,//有id则修改，没有则新增
    255: optional base.Base Base,
}

struct AddOrUpdateSubResourceResp{
    1: optional list<i64> ids,//新增或编辑的id列表
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateSharedAccessPartyRequest{
    1: required CommonEditRequest CommonEditRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源id
    4: optional list<i64> SharedAccessPartyIdList,//共享接入方id集合
    255: optional base.Base Base,
}

struct AddOrUpdateSharedAccessPartyResponse{
    255: base.BaseResp BaseResp,
}

struct GetResourceListRequest {
    1: required CommonRequest CommonRequest,
    2: optional string SearchKey,//搜索key,搜索业务id、业务名称
    3: optional i64 AppId,//根据appId搜索绑定的资源子资源
    4: optional i64 AccessPartyId,//根据接入方id进行隔离,不传或传0获取所有资源
    5: optional SourceType SourceType,//来源类型，1-自建 2-共享
    6: optional list<string> DockingNameList,//对接人名称
    7: optional list<string> AccessPartyIdListForSearch,//配置页面的查询条件，基于AccessPartyId的结果再次查询
    8: optional string SearchKeyForSubResource,//搜索子业务id(string是为了向SearechKey对齐，后期可能用于查询子业务名称)
    9: optional bool OnlyEnable, //仅返回生效的资源
    255: optional base.Base Base,
}

struct GetResourceListResponse {
    1: required list<Resource> ResourceList,//资源列表
    2: required i64 Total,//总数
    255: base.BaseResp BaseResp,
}

struct GetResourceBaseListByIdsRequest {
    1: required CommonRequest CommonRequest,
    2: required list<i64> ResourceIds, //资源ID集合
    255: optional base.Base Base,
}

struct GetResourceBaseListByIdsResponse {
    1: required list<ResourceBase> ResourceBaseList, //资源列表
    255: base.BaseResp BaseResp,
}

struct GetSubResourceBaseListByResourceIdsRequest {
    1: required CommonRequest CommonRequest,
    2: required list<i64> ResourceIds, // 资源ID列表
    3: optional i64 AccessPartyId, // 当前接入方ID，依据此id对共享子业务进行过滤
    255: optional base.Base Base,
}

struct GetSubResourceBaseListByResourceIdsResponse {
    1: required map<i64, list<SubResourceBase>> ResourceIdToSubResourceBaseListMap, // 资源ID到子资源列表的映射
    255: base.BaseResp BaseResp,
}

struct GetSubResourceBaseListByIdsRequest {
    1: required CommonRequest CommonRequest,
    2: required list<i64> SubResourceIds, // 子资源ID列表
    255: optional base.Base Base,
}

struct GetSubResourceBaseListByIdsResponse {
    1: required list<SubResourceBase> SubResourceBaseList, // 子资源列表
    255: base.BaseResp BaseResp,
}

struct GetCategoryListRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: optional i64 SubResourceId,//子资源ID
    255: optional base.Base Base,
}

struct GetCategoryListByParentRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 ParentId,//父级id
    255: optional base.Base Base,
}

struct GetCategoryListByIdsRequest{
    1: required CommonRequest CommonRequest,
    2: required list<i64> CategoryIds,//标签ID集合
    3: optional bool NeedResourceName,//是否需要返回所属业务子业务的名称，需要则传true，默认不返回
    255: optional base.Base Base,
}

struct GetCategoryListResponse{
    1: list<Category> CategoryList,
    255: base.BaseResp BaseResp,
}

struct GetCategoryTreeReq {
     1: required i64 accessPartyId,
     2: required i64 resourceId,
     3: required i64 subResourceId,
}

struct GetCategoryTreeResp {
    1: optional list<CategoryPropertyModel> CategoryPropertyList;

    255: base.BaseResp BaseResp,
}

struct CategoryPropertyModel {
  1: optional i64 Id,
  2: optional i64 ResourceId,
  3: optional i64 SubResourceId,
  4: required string Name,
  // 5: optional string Path,
  6: optional i64 ParentId,
  7: optional i32 Level,
  8: required i32 OrderIndex,
  // 9: optional bool IsBindApp,
  10: optional i32 EnableFlag,
  // 11: optional list<PropertyValue> PropertyValues,
  // 12: optional string LocalUpdateTime,
  13: optional list<CategoryPropertyModel> SubCategoryProperty,
  // 14: optional i64 UpdaterAgentId,
  // 15: optional string CreateAt,
  16: optional i32 DisplayLevel,
  //17: optional BindPageModel BindPage,
}

struct GetCategoryIdByNameRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required string CategoryName,//标签名称
    5: optional i32 Level,//层级,不传默认查三级标签，取值范围[1,2,3]
    255: optional base.Base Base,
}

struct GetCategoryIdByNameResponse{
    1: required i64 CategoryId, //标签id
    255: base.BaseResp BaseResp,
}

struct GetCategoryResultByNameRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId, // 资源id
    3: required i64 SubResourceId, // 子资源id
    4: required string CategoryName, // 标签名称
    5: required i32 Level, // 层级。0-一级；1-二级；2-三级
    255: optional base.Base Base,
}

struct GetCategoryResultByNameResponse{
    1: required list<CategoryResult> CategoryResultList, // 标签结果列表
    255: base.BaseResp BaseResp,
}

struct GetCategoryByFuzzyNameRequest{
    1: required CommonRequest CommonRequest, // PageSize用于限制返回数量
    2: optional i64 ResourceId, // 资源id
    3: optional i64 SubResourceId, // 子资源id
    4: required string CategoryName, // 标签名称
    5: optional i64 AppId,//根据appId搜索绑定的资源子资源
    6: required i64 AccessPartyId,//接入方id
    255: optional base.Base Base,
}

struct GetCategoryByFuzzyNameResponse{
    1: required list<Category> FullPathList, // 标签结果列表，根据request中的PageSize确定返回的叶子节点数量
    255: base.BaseResp BaseResp,
}

struct CategoryNamePath{
    1: required string FirstLevelName, //一级标签名称
    2: required string SecondLevelName, // 二级标签名称
    3: required string ThridLevelName, // 三级标签名称
}

struct CategoryBase {
    1: required i64 Id, // 标签id
    2: required i64 TenantId, // 租户id
    3: required i64 ResourceId, // 资源id
    4: required i64 SubResourceId, // 子资源id
    5: required string Name, // 标签名称
    6: required string Path, // 标签全路径
    7: required i64 ParentId, // 父标签id
    8: required i32 Level, // 层级
    9: required i32 OrderIndex, // 当前排序
    10: required i32 EnableFlag, // 是否有效 0-有效 1-无效
    11: required i32 DisplayLevel, // 标签显示级别(0-都展示；1-筛选时才展示，创建时不展示；2-都不展示)
    12: required string CreateAt, // 创建时间
    13: required string ModifyAt, // 修改时间
}

struct CommonGetCategoryBaseRequest{
    1: required CommonRequest CommonRequest,
    2: optional i64 ResourceId, // 资源id
    3: optional i64 SubResourceId, // 子资源id
    4: optional string Name, // 标签名称
    5: optional string Path, // 标签全路径
    6: optional i64 ParentId, // 父标签id
    7: optional i32 Level, // 层级
    8: optional i32 EnableFlag, // 是否有效 0-有效 1-无效
    9: optional i32 DisplayLevel, // 标签显示级别(0-都展示；1-筛选时才展示，创建时不展示；2-都不展示)
    255: optional base.Base Base,
}

struct CommonGetCategoryBaseResponse{
    1: required list<CategoryBase> CategoryBaseList, // 标签结果列表
    255: base.BaseResp BaseResp,
}

struct CategoryResult {
    1: required i64 CategoryId, // 标签id
    2: required string CategoryName, // 标签名称
    3: required i32 Level,
    4: optional CategoryResult parent, // 父级
}

struct AddOrUpdateCategoryRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: list<Category> CategoryList,//修改/新增的标签集合，新增的标签可能有子标签
    5: list<i64> DeleteIdList,
    255: optional base.Base Base,
}

struct AddOrUpdateCategoryByStrRequest{
    1: required CommonRequest CommonRequest,
    2: required string JsonStr,
    255: optional base.Base Base,
}

struct GetAppListRequest{
    1: required CommonRequest CommonRequest,
    2: string Name,
    255: optional base.Base Base,
}

struct GetAppListResponse {
    1: required list<App> AppList,//App列表
    2: required i64 Total,//总数
    255: base.BaseResp BaseResp,
}

struct AppRequest{
    1: required CommonRequest CommonRequest,
    2: required App App,
    255: optional base.Base Base,
}

struct GetCategoryAppListRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 AppId,
    5: optional list<i64> SecondLevelIdList; // 2级标签ID， 若不为空，则只返回这个2级标签相关的标签

    255: optional base.Base Base,
}

struct GetCategoryListByHostAppIDRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 HostAppID, # 公司AppID
    3: required i64 ParentCategoryID, # 父标签ID,如果为0则取第一层
    255: base.Base Base
}

struct BindCategoryToAppRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 AppId,
    5: list<i64> NewBindCategoryIds,//新增绑定标签id
    6: list<i64> UnBindCategoryIds,//解绑标签id
    7: optional i64 LockId    //锁定id(锁定id的申请需要跟管理员申请)
    8: optional i32 From; // 接口请求来源  1-来源Ticket系统回调
    255: optional base.Base Base,
}
//
//struct BindTagToCategoryRequest{
//    1: required CommonRequest CommonRequest,
//    2: string ExtraCode,//标签三级id
//    255: base.BaseResp BaseResp,
//}

struct GetAccessPartyListRequest{
    1: required CommonRequest CommonRequest,
    2: optional bool WithNoDisabled, # 不包含禁用的接入方
    255: optional base.Base Base,
}
struct GetAccessPartyListRespone{
    1:required list<AccessParty> AccessPartyList
    255: base.BaseResp BaseResp,
}

enum AccessPartyOperation {
    Enable = 1
    Disable = 2
}

struct AddOrUpdateAccessPartyRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyID, # 接入方ID，如新增填0或者不传
    3: optional string Name, # 接入方名称
    4: optional list<AgentItem> BusinessOwners, # 负责人ID列表
    5: optional AccessPartyOperation AccessPartyOperation, # 接入方启用、禁用
    255: base.Base Base,
}

struct AddOrUpdateAccessPartyResponse {
    1: required i64 AccessPartyID, # 接入方ID
    255: base.BaseResp BaseResp
}

//属性相关
struct ResourceProperty{
    1: Resource Resource
    2: i64 BindPropertyCount,//绑定的属性数
    3: string LocalUpdateTime,//更新时间
}

struct GetResourcePropertyListResponse{
    1: required list<ResourceProperty> ResourcePropertyList,//资源属性列表
    2: required i64 Total,//总数
    255: base.BaseResp BaseResp,
}

struct GetResourcePropertyListRequest {
    1: required CommonRequest CommonRequest,
    2: optional string SearchKey,//搜索key
    3: required i64 AccessPartyId,//接入方id
    4: optional SourceType SourceType,//来源类型，1-自建 2-共享
    255: base.Base Base,
}

struct CategoryProperty{
    1: optional i64 Id,//标签id 新增为0
    2: optional i64 ResourceId,//资源id
    3: optional i64 SubResourceId,//子资源id
    4: string Name,//标签名称
    5: optional string Path,//标签全路径
    6: optional i64 ParentId,//父标签id
    7: optional i32 Level,//层级
    8: i32 OrderIndex,//当前排序
    10: optional bool IsBindApp//是否绑定app
    11: optional i32 EnableFlag,//
    12: optional list<PropertyValue> PropertyValues,//绑定的属性数
    13: optional string LocalUpdateTime,//更新时间
    14: optional list<CategoryProperty> SubCategoryProperty
    15: optional i64 UpdaterAgentId, //修改人
    16: optional string CreateAt, // 创建时间
    17: optional i32 DisplayLevel, // 标签显示级别(0-都展示；1-筛选时才展示，创建时不展示；2-都不展示)
}

struct PropertyValue{
    1: required string PropertyCode, //字段code
    2: required string DisplayName, //字段名称
    3: required string OptionChoosedCode, //选项code
    4: required string OptionChoosedValue,//选项值
    5: required i64 OptionChoosedId,
    6: optional string OptionChoosedExtra, // 选项extra
    7: optional string Value, // 属性value
    8: optional i32 PropertyType, // 属性类型
}

struct GetCategoryPropertyListResponse{
    1: required list<CategoryProperty> CategoryPropertyList,//标签属性列表
    255: base.BaseResp BaseResp,
}

struct GetCategoryPropertyListRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required i64 SubResourceId,//子资源ID
    4: required i64 AccessPartyId,//接入方id
    255: base.Base Base,
}

struct GetCategoryPropertyListByIdsResponse{
    1: required list<CategoryProperty> CategoryPropertyList,//标签属性列表
    255: base.BaseResp BaseResp,
}

struct GetCategoryPropertyListByIdsRequest {
    1: required CommonRequest CommonRequest,
    2: required list<i64> CategoryIds,//标签ID集合

    255: base.Base Base,
}

//保存字段值的参数
struct ExtraUpdateOrInsertRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId, //接入方ID
    3: required i64 EntityId,
    4: required map<string, string> Values, //key:字段code，value:字段值 单选/多选/级联字段时，值为long型id list的json string, 如："[1,2,3]"；其他，为用户输入值，如："value"
    255: required base.Base Base,
}

struct ExtraUpdateOrInsertResponse {
    1: required i32 InsertCount,
    2: required i32 UpdateCount,
    255: required base.BaseResp BaseResp,
}

//批量实体修改字段值的参数
struct BatchExtraSaveRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId, //接入方ID
    3: required list<i64> EntityIds,
    4: required map<string, string> Values, //key:字段code，value:字段值 单选/多选/级联字段时，值为long型id list的json string, 如："[1,2,3]"；其他，为用户输入值，如："value"
    255: required base.Base Base,
}

struct BatchExtraDeleteRequest {
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId, //接入方ID
    3: required list<i64> EntityIds,
    4: required list<string> PropertyCodes,
    255: required base.Base Base,
}
enum RegisterType{
    UN_DISABLE = 1, // 不能删除标签
    UN_UPDATE = 2, // 不能更新标签
    UN_ADD_SUB = 3, // 不能新增子标签
}
struct RegistCategory{
    1: required i64 CategoryId,//标签id
    2: required RegisterType RegisterType,//注册类型
    3: required string CallerName,//调用方名称
    4: required string RegisterDesc,//注册文案
    5: required string LinkName,//链接名称
    6: required string LinkUrl,//链接地址
    7: required string RegisterKey,//调用方用于注册的key
}

struct RegisterCategoryRequest{
    1: required i64 TenantId,//租户id
    2: optional i64 AgentId,//客服Id
    3: optional string AgentName,//客服名称
    4: required i64 AppSystemId,//应用系统id
    5: required RegistCategory RegistCategory,
    255: required base.Base Base,
}

struct UnRegisterCategoryRequest{
    1: required i64 TenantId,//租户id
    2: optional i64 AgentId,//客服Id
    3: optional string AgentName,//客服名称
    4: required i64 AppSystemId,//应用系统id
    5: required RegisterType RegisterType,//解除注册类型
    6: required string RegisterKey,//调用方用于解除注册的key
    7: required i64 CategoryId,//标签id
    255: required base.Base Base,
}

struct FindCateRegistResp{
    1: optional list<RegistCategory> RegistCategorys,//调用方名称
    2: optional i64 ResourceId,//资源id
    3: optional i64 SubResourceId,//子资源id
    255: base.BaseResp BaseResp,
}

struct FindCateRegistReq{
    1: required i64 TenantId,//租户id
    2: required i64 AppSystemId,//应用系统id
    3: required i64 CategoryId,//标签id
    4: required RegisterType RegisterType,//注册类型
    5: optional string RegisterKey,//调用方用于注册的key
    255: required base.Base Base,
}


struct ImportCategoryByExcelRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 ResourceId,//资源id
    3: required list<i64> SubResourceIds,//子资源ID列表->每个sheet依次对应
    4: required string ExcelName,
    5: required i32 startIndex,//从第几个sheet开始导入，从0开始
    255: required base.Base Base,
}

struct AppCategory {
    1: i64 Id, // 主键
    2: i64 TenantId, // 租户ID
    3: i64 AppId, // app端ID
    4: i64 ResourceId, // 资源ID
    5: i64 SubResourceId, // 子资源ID
    6: string CountryCode, // 国家Code
    7: i64 CategoryId, // 标签ID
    8: string Name, // 标签名称
    9: string Path, // 标签全路径
    10: i32 Level, // 标签层级
    11: i32 EnableFlag, // 是否有效 0-有效 1-无效
    12: string CreateAt, // 创建时间
    13: string ModifyAt, // 修改时间
    14: i64 BytedanceAppId, // 字节应用ID
}

struct CommonGetAppCategoryRequest {
    1: required CommonRequest CommonRequest,
    2: optional i64 AppId, // app端ID
    3: optional i64 ResourceId, // 资源ID
    4: optional i64 SubResourceId, // 子资源ID
    5: optional i64 CategoryId, // 标签ID
    6: optional string Name, // 标签名称
    7: optional string Path, // 标签全路径
    8: optional i32 Level, // 标签层级
    9: optional i64 BytedanceAppId, // 字节应用ID

    255: optional base.Base Base,
}

struct CommonGetAppCategoryResponse {
    1: required list<AppCategory> AppCategoryList, // 结果列表
    2: required i64 Count, // 结果总数

    255: base.BaseResp BaseResp,
}

struct CommonGetResourceBaseRequest {
    1: required CommonRequest CommonRequest,
    2: optional string Name, // 资源名称
    3: optional string DockingName, // 对接人名称
    4: optional i64 AccessparyId, // 接入方ID
    5: optional i32 SubFlag, // 子资源是否开启 0-未开启 1-开启

    255: optional base.Base Base,
}

struct CommonGetResourceBaseResponse {
    1: required list<ResourceBase> ResourceBaseList, // 资源结果列表
    255: base.BaseResp BaseResp,
}

struct CommonGetSubResourceBaseRequest {
    1: required CommonRequest CommonRequest,
    2: optional i64 ResourceId, // 所属的资源ID
    3: optional string Name, // 子资源名称
    4: optional i64 AccessPartyId, // 当前接入方ID，依据此id对共享子业务进行过滤

    255: optional base.Base Base,
}

struct CommonGetSubResourceBaseResponse {
    1: required list<SubResourceBase> SubResourceBaseList, // 子资源结果列表
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateCategoryMetaRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId,//所属接入方id,
    3: list<CategoryMeta> CategoryMetaInsertList,//增量数据集合(新增)
    4: list<CategoryMeta> CategoryMetaUpdateList,//增量数据集合(修改)
    5: list<i64> DeleteIdList,//删除的元标签Id
    6: optional i64 LockId    //锁定id(锁定id的申请需要跟管理员申请)
    255: optional base.Base Base,
}

struct CategoryMeta{
    1: optional i64 Id,//元标签id 新增为0
    2: string Name,//元标签名称
    3: optional string Path,//元标签全路径
    4: optional i64 ParentId,//父元标签id
    5: optional i32 Level,//层级
    6: i32 OrderIndex,//当前排序
    7: optional list<CategoryMeta> SubCategoryList,//子元标签集合
    8: optional i32 EnableFlag,//
    9: optional i64 AccessPartyId,//所属接入方id
    10: optional list<i64> SharedAccessPartyIdList,//已共享接入方id集合
    11: optional SourceType SourceType,//来源类型，1-自建 2-共享
    12: i64 CreateAt, // 创建时间 秒级时间戳
    13: string CreatorName, // 创建人
    14: i64 ModifyAt, // 修改时间 秒级时间戳
    15: string ModifierName, // 修改人
    16: optional i64 LockId,   //锁定id
    17: optional string LockDesc, //锁定文案
    18: optional string IssueCategoryId, // issue标签的标签ID
}

struct GetCategoryMetaListRequest{
    1: required CommonRequest CommonRequest,
    2: required i64 AccessPartyId,//接入方id，当前登录接入方id
    3: optional SourceType SourceType,//来源
    4: optional list<i64> AccessPartyIdForSearch,//查询条件，当来源为自建，则查询共享出去的接入方id,否则查询从哪个接入方id共享而来。
    5: optional string FuzzyName,//模糊查询名称
    6: optional i64 LockId,   //锁定id
    255: optional base.Base Base,
}

struct GetCategoryMetaListResponse{
    1: list<CategoryMeta> CategoryMetaList,
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateCategoryMetaSharedAccessPartyRequest{
    1: required CommonEditRequest CommonEditRequest,
    2: required list<i64> CategoryMetaIdList,//三级元标签id集合
    3: required list<i64> SharedAccessPartyIdList,//共享接入方id集合
    4: required ShareEditType ShareEditType,
   255: optional base.Base Base,
}

enum ShareEditType{
    OVERRIDE = 1,//覆盖更新
    APPEND = 2,//追加，如果已有则忽略
    DELETE = 3,//删除,如果没有则忽略
}

struct AddOrUpdateCategoryMetaSharedAccessPartyResponse{
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateCategoryBindRequest{
     1: required CommonRequest CommonRequest,
     2: required i64 ResourceId,//资源id
     3: required i64 SubResourceId,//子资源ID
     4: list<CategoryBind> CategoryBindInsertList,//新增的标签绑定关系集合
     5: list<CategoryBind> CategoryBindUpdateList,//修改的标签绑定顺序集合
     6: list<i64> DeleteBindIdList,//解除绑定的绑定关系id集合
     7: optional i64 LockId    //锁定id(锁定id的申请需要跟管理员申请)
     8: optional i32 From; // 接口请求来源  1-来源Ticket系统回调
     255: optional base.Base Base,
}

struct CategoryBind{
    1: optional i64 Id,//绑定关系id,等同于原category对象的id，修改时传递
    2: optional i64 CategoryMetaId,//绑定的元标签id，新增时必传，不可修改
    3: optional i32 OrderIndex,//当前排序
    4: optional list<CategoryBind> SubCategoryBindList,//子绑定关系集合，新增时需传递完整三级链路
}

struct GetAppWithResourceListRequest {
    1: required CommonRequest CommonRequest;
    255: optional base.Base Base
}

struct AppWithResource {
    1: required App App,
    2: optional list<Resource> ResourceList,
}

struct GetAppWithResourceListResponse {
    1: optional list<AppWithResource> AppWithResourceList,
    255: optional base.BaseResp BaseResp
}

// ------------------------Ticket审批回调接口-----------------------
enum CreateChannel{
        API = 0           // API调用
        WEB = 1           // web页面
        SDK = 2           // SDK
        ONCALL = 3        // oncall
        PRIVATE_CHAT = 4  // 飞书发起
        CONSUMER = 5      // C端创建
}

enum FormTypeEnum{
        SUBMIT = 1         // 提交表单
        HANDLE = 2         // 处理表单
        FAULT = 3          // 故障处置表单
        NON_FORM = 4       //非表单
}

struct Field{
    1: required string field_key        //  字段key
    2: optional string field_value      //  字段值，不同类型的值会被json.dumps序列化, 需要调用方解json
    3: required FormTypeEnum form_type  //  所在的表单类型
    4: optional string field_type       //  字段类型，见文档下面自定义字段类型
}

struct TicketInfo{
    1: required string id                           // 工单id
    2: required string label_raw_id                 // 问题标签
    3: required i64 create_time                     // 创建时间
    4: optional i64 finish_time                     // 完结时间
    5: required i64 update_time                     // 更新时间
    6: required string status                       // 状态
    7: required string priority                     // 优先级
    8: optional string primary_ticket_id            // 若工单为主单，该参数为空
    9: optional list<string> secondary_ticket_ids   // 若工单为子单，该参数为空
    10: optional string submit_system               // 提交系统
    11: optional string submitter                   // 提交人邮箱
    12: optional string handler                     // 处理人邮箱
    13: required CreateChannel create_channel       // 反馈渠道
    14: required list<Field> fields                 //工单自定义字段
}

struct NotifyTicketReq{
    1: TicketInfo ticket_info

    255: base.Base Base
}

struct NotifyTicketRsp{
    255: base.BaseResp BaseResp
}
// ------------------------Ticket审批回调接口-----------------------

struct GetIssueCategoryIdReq{
    1: required i64 CategoryId, // bytehi标签id

    255: base.Base Base
}

struct GetIssueCategoryIdResp{
    1: i64 IssueCategoryId, // issue标签id
    2: string IssueCategryProject // issue标签的业务

    255: base.BaseResp BaseResp
}

struct GetIssueCategoryParentInfoReq{
    1: required i64 IssueCategoryId,
    2: required string IssueCategoryProject;

    255: base.Base Base
}

struct GetIssueCategoryParentInfoResp{
    1: i64 IssueCategoryParentID;
    2: string IssueCategoryParentName;
    3: i32 level;

    255: base.BaseResp BaseResp
}

struct SetDisableCategoryMetaReq{
    1: required i64 CategoryId;

    255: base.Base Base
}

struct SetDisableCategoryMetaResp{
    1: i32 biz_code;

    255: base.BaseResp BaseResp
}

struct SetAbleCategoryMetaReq{
    1: required i64 CategoryId;

    255: base.Base Base
}

struct SetAbleCategoryMetaResp{
    1: i32 biz_code;

    255: base.BaseResp BaseResp
}

struct SetIssueCategoryParentIdReq{
    1: required i64 CategoryId;
    2: required i64 ParentId;

    255: base.Base Base
}

struct SetIssueCategoryParentIdResp{
    1: i32 biz_code;

    255: base.BaseResp BaseResp
}

struct GetCategoryMetaByIssueCatIdsReq{
    1: required list<string> IssueCatIds;

    255: base.Base Base
}

struct GetCategoryMetaByIssueCatIdsResp {
    1: list<i64> CategoryIds;

    255: base.BaseResp BaseResp
}

struct updateAppCategoryAbleReq{
    1: i64 Id;

    255: base.Base Base
}

struct updateAppCategoryAbleResp{
    1: i64 Code;

    255: base.BaseResp BaseResp
}

service CateGoryService {

    //资源配置
    GetResourceListResponse GetResourceList(1: GetResourceListRequest req);//搜索资源列表
    GetResourceBaseListByIdsResponse GetResourceBaseListByIds(1: GetResourceBaseListByIdsRequest req);//通过资源ID搜索资源列表
    GetSubResourceBaseListByResourceIdsResponse GetSubResourceBaseListByResourceIds(1: GetSubResourceBaseListByResourceIdsRequest req); // 通过资源ID获取有效的子资源列表
    GetSubResourceBaseListByIdsResponse GetSubResourceBaseListByIds(1: GetSubResourceBaseListByIdsRequest req); // 通过子资源ID获取子资源列表
    CommonResponse AddOrUpdateResource(1: ResourceRequest req);//新增修改资源
    AddOrUpdateSubResourceResp AddOrUpdateSubResource(1: SubResourceRequest req);//新增修改子资源
    CommonGetResourceBaseResponse CommonGetResourceBase(1: CommonGetResourceBaseRequest req); // 资源通用获取接口
    CommonGetSubResourceBaseResponse CommonGetSubResourceBase(1: CommonGetSubResourceBaseRequest req); // 子资源通用获取接口
    AddOrUpdateSharedAccessPartyResponse AddOrUpdateSharedAccessParty(1: AddOrUpdateSharedAccessPartyRequest req);//新增修改子资源的共享接入方
    GetResourceParamEnumResponse GetResourceParamEnum(1: GetResourceParamEnumRequest req);//获取业务标签配置页面的查询参数枚举



    //标签配置
    GetCategoryListResponse GetCategoryList(1: GetCategoryListRequest req);//按照资源+子资源搜索标签列表
    CommonResponse AddOrUpdateCategory(1:AddOrUpdateCategoryRequest req);//新增修改标签
    GetCategoryListResponse GetCategoryListByIds(1: GetCategoryListByIdsRequest req);//按照标签id批量搜索标签
    GetCategoryListResponse GetCategoryListByParentId(1: GetCategoryListByParentRequest req);//按照资源+子资源+父Id搜索标签列表
    GetCategoryIdByNameResponse GetCategoryIdByName(1: GetCategoryIdByNameRequest req);//按照标签名称搜索标签id
    GetCategoryResultByNameResponse GetCategoryResultByName(1: GetCategoryResultByNameRequest req);//按照标签名称搜索标签结果
    CommonGetCategoryBaseResponse CommonGetCategoryBase(1: CommonGetCategoryBaseRequest req); // 标签通用获取接口
    GetCategoryByFuzzyNameResponse GetCategoryByFuzzyName(1: GetCategoryByFuzzyNameRequest req);//按照标签名称搜索标签结果并返回命中结果的父级+子级标签

    //属性配置
    GetResourcePropertyListResponse GetResourcePropertyList(1: GetResourcePropertyListRequest req);//搜索资源属性列表
    GetCategoryTreeResp GetCategoryTree(1: GetCategoryTreeReq req);// 为SLA标签定制的接口
    GetCategoryPropertyListResponse GetCategoryPropertyList(1: GetCategoryPropertyListRequest req);//搜索标签属性列表
    GetCategoryPropertyListByIdsResponse GetCategoryPropertyListByIds(1: GetCategoryPropertyListByIdsRequest req);//按标签ID搜索标签属性列表
    //调用自定义字段接口
    ExtraUpdateOrInsertResponse UpdateOrInsertExtraValues(1: ExtraUpdateOrInsertRequest req)//创建/更新自定义字段值
    CommonResponse BatchSaveExtraValues(1: BatchExtraSaveRequest req)//批量实体创建自定义字段值
    CommonResponse BatchDeleteExtraValues(1: BatchExtraDeleteRequest req)//批量实体删除自定义字段值

    //应用配置
    GetAppListResponse GetAppList(1: GetAppListRequest req);//搜索应用列表
    CommonResponse AddOrUpdateApp(1: AppRequest req);//添加/修改 app
    GetCategoryListResponse GetAppCategoryList(1: GetCategoryAppListRequest req);//获取子资源的全量标签，以及app标签绑定关系
    GetCategoryListResponse GetCategoryListByHostAppID(1: GetCategoryListByHostAppIDRequest req); # 通过公司应用ID获取标签列表
    CommonResponse BindCategoryToApp(1: BindCategoryToAppRequest req);//app绑定标签
    CommonGetAppCategoryResponse CommonGetAppCategory(1: CommonGetAppCategoryRequest req); // App和标签关系通用获取接口

    GetAppWithResourceListResponse GetAppWithResourceList(1: GetAppWithResourceListRequest req); // 获取全量应用、资源、子资源

    //接入方
    GetAccessPartyListRespone getAccessPartyList(1: GetAccessPartyListRequest req);//根据租户id获取接入方
    AddOrUpdateAccessPartyResponse AddOrUpdateAccessParty(1: AddOrUpdateAccessPartyRequest req); # 添加或更新接入方


    CommonResponse AddOrUpdateCategoryByStr(1: AddOrUpdateCategoryByStrRequest req);//添加/修改 app
    CommonResponse ImportCategoryByExcel(1: ImportCategoryByExcelRequest req);//通过tos导入标签

    //标签注册
    CommonResponse RegisterCategory(1: RegisterCategoryRequest req);
    CommonResponse UnRegisterCategory(1: UnRegisterCategoryRequest req);
    FindCateRegistResp FindCateRegist(1:FindCateRegistReq req)

    //标签解耦功能，新增配置接口
    CommonResponse AddOrUpdateCategoryMeta(1:AddOrUpdateCategoryMetaRequest req);//新增修改元标签,for标签池中的标签管理
    GetCategoryMetaListResponse GetCategoryMetaList(1: GetCategoryMetaListRequest req);//查询接入方下的自建+共享的元标签信息，for标签池
    AddOrUpdateCategoryMetaSharedAccessPartyResponse AddOrUpdateCategoryMetaSharedAccessParty(1: AddOrUpdateCategoryMetaSharedAccessPartyRequest req);//新增修改元标签的共享接入方
    CommonResponse AddOrUpdateCategoryBind(1:AddOrUpdateCategoryBindRequest req);//新增修改元标签与业务子业务绑定关系

    // Ticket审批回调接口
    NotifyTicketRsp notify_ticket(1: NotifyTicketReq req);

    // 根据bytehi绑定标签id获取issue标签id
    GetIssueCategoryIdResp GetIssueCategoryId(1: GetIssueCategoryIdReq req);

    // 根据issue标签id获取上一级节点信息
    GetIssueCategoryParentInfoResp GetIssueCategoryParentInfo(1: GetIssueCategoryParentInfoReq req);

    // 设置元数据有效/无效
    SetDisableCategoryMetaResp SetDisableCategoryMeta(1: SetDisableCategoryMetaReq req)
    SetAbleCategoryMetaResp SetAbleCategoryMeta(1: SetAbleCategoryMetaReq req)

    // 更新issue 2/3级标签的parent_id
    SetIssueCategoryParentIdResp SetIssueCategoryParentId(1: SetIssueCategoryParentIdReq req);

    // 根据issue category id 获取标签数据
    GetCategoryMetaByIssueCatIdsResp GetCategoryMetaByIssueCatIds(1: GetCategoryMetaByIssueCatIdsReq req);


}(agw.js_conv="str")
