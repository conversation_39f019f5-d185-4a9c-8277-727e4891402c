include "../base.thrift"

namespace go ies.kefu.pigeon_communication

struct QuestionCommitRequest {
    1: required i64 Qid,            //问卷实例的id
    2: required string Token,       //用户校验的token，必须跟Qid能匹配上才能查询和提交
    3: required i32 Star,           //用户选择的星级1~5
    4: required string Tags,        //用户选的tag，逗号分隔
    5: optional string Reason,      //tag为other时的原因
    6: optional string Feedback,     //用户的评价反馈
    7: optional bool IsSolved = false,   //是否解决，目前不需要
    8: optional i32 ComeFrom,

    255: optional base.Base Base,
}

struct QuestionCommitResponse {
    1: optional i64 Qid,    //提交成功后会生成问卷实例的id

    255: base.BaseResp BaseResp,
}

struct CanEstablishCommunicationRequest {
    1: required string OrderID, // 订单ID
    2: required i64 AgentID, // 客服ID
    3: required i64 ShopID,
    4: optional i64 RelationID, // IM task ID或工单ID
    5: optional i32 Channel, // 渠道，扩展字段，1表示IM, 2表示工单
    6: optional i64 TaskID,

    255: optional base.Base Base,
}

struct CanEstablishCommunicationResponse {
    1: required bool CanEstablish, // 是否能建立会话
    2: required string Reason, // 不能建立会话的原因
    255: optional base.BaseResp BaseResp,
}

struct GetConnectInfoRequest {
    1: required i64 AgentID,

    255: optional base.Base Base,
}

struct GetConnectInfoResponse {
    1: required i64 AgentID,
    2: required i64 LinkID,
    3: required string Token,
    4: required string WebsocktUrl,
    5: required string ApiUrl,
    6: required i64 inbox,
    7: required i64 IMAppID,
    8: required string AppKey,
    255: optional base.BaseResp BaseResp,
}


struct CreateConversationsRequest {
    1: required i32 Channel // 渠道，扩展字段，1表示IM
    2: required string OrderID // 订单ID
    3: required string ShopOrderId // 店铺单ID
    4: required i64 AgentID,
    5: required i64 RelationID,
    6: required i64 ShopID,
    7: required i64 ProductID,
    8: required string RelationUserID,

    255: optional base.Base Base,
}

struct CreateConversationsResponse {
    1: required ConversationInfo Conversation, // 会话
    255: optional base.BaseResp BaseResp,
}

struct BachGetAgentProfileRequest {
    1: required list<i64> AgentIDs,
    255: optional base.Base Base,
}

struct BachGetAgentProfileResponse {
    1: required list<AgentProfile> Profiles,
    255: optional base.BaseResp BaseResp,
}

struct AgentProfile {
    1: required i64 ID
    2: required string AvatarUrl
    3:  required string Name
}


struct EndConversationRequest {
    1: required i64 PigeonConversationID,
    255: optional base.Base Base,
}

struct EndConversationResponse {
    255: optional base.BaseResp BaseResp,
}

struct EndConversationByAgentRequest {
    1: required i64 PigeonConversationID,
    255: optional base.Base Base,
}

struct EndConversationByAgentResponse {
    255: optional base.BaseResp BaseResp,
}


struct QueryConversationsRequest {
    1: required i32 Channel,
    2: optional string OrderID,
    255: optional base.Base Base,
}

struct QueryConversationsResponse {
    1: required list<ConversationInfo> ConversationInfos, // 会话列表
    255: optional base.BaseResp BaseResp,
}

struct ConversationInfo {
    1: optional i64 ConversationId,
    2: optional i64 PigeonConversationID
    3: optional i64 AgentID
    4: optional string AgentName
    5: optional string CreatedAt
    6: optional i64 ShopID
    7: optional string ShopName
    8: optional string ShopAvatarUrl
    9: optional i64 TaskID
    10: optional i64 Status
    11: optional string StatusDesc
    12: optional string RelationUserID
    13: optional i64 IsShow
}

struct JudgeConversationsRequest {
    1: required i64 PigeonConversationID
    2: required bool Solved
    255: optional base.Base Base,
}

struct JudgeConversationsResponse {
    255: optional base.BaseResp BaseResp,
}

struct GetJudgeResultRequest {
    1: required i64 Channel
    2: required i64 RelationID
    3: required i64 OrderID
    255: optional base.Base Base,
}

struct GetJudgeResultResponse {
    1: required i64 Solved
    255: optional base.BaseResp BaseResp,
}

struct BatchGetShopProfileRequest {
    1: required list<i64> ShopIDs,
    255: optional base.Base Base,
}

struct BatchGetShopProfileResponse {
    1: required list<ShopProfile> Profiles,
    255: optional base.BaseResp BaseResp,
}

struct ShopProfile {
    1: required i64 ID
    2: required string AvatarUrl
    3:  required string Name
}

struct GetConversationsByAgentIDRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetConversationsByAgentIDResponse {
    1: required list<ConversationInfo> ConversationInfos, // 会话列表
    255: optional base.BaseResp BaseResp,
}

struct GetConversationByRelationIDRequest {
    1: optional i32 Channel, // 渠道，扩展字段，1表示IM, 2表示工单
    2: optional i64 RelationID, // IM task ID或工单ID
    3: optional i64 PigeonConversationShortID, // 飞鸽会话ID
    255: optional base.Base Base,
}

struct GetConversationByRelationIDResponse {
    1: required ConversationInfo ConversationInfo, // 会话
    255: optional base.BaseResp BaseResp,
}

struct GetConversationsByRelationRequest {
    1: required i32 Channel, // 渠道，扩展字段，1表示IM, 2表示工单
    2: required i64 RelationID, // IM task ID或工单ID
    255: optional base.Base Base,
}

struct GetConversationsByRelationResponse {
    1: required list<ConversationInfo> Conversations, // 会话列表
    255: optional base.BaseResp BaseResp,
}

struct GetSTS2TokenRequest {
    255: optional base.Base Base,
}

struct GetSTS2TokenResponse {
    1: optional string AccessKeyID,
    2: required string SecretAccessKey,
    3: required string SessionToken,
    4: required string ExpiredTime,
    5: required string CurrentTime,
    255: optional base.BaseResp BaseResp,
}

struct GetUrl4UriRequest {
    1: required string Uri,
    255: optional base.Base Base,
}

struct GetUrl4UriResponse {
    1: optional string K3sUrl,
    255: optional base.BaseResp BaseResp,
}

service PigeonCommunicationService {
    //结束会话（飞鸽使用）
    EndConversationResponse EndConversation(1: EndConversationRequest req)
    //结束会话（前端使用）
    EndConversationByAgentResponse EndConversationByAgent(1: EndConversationByAgentRequest req)
    //获取客服信息（飞鸽使用）
    BachGetAgentProfileResponse BatchGetAgentProfile(1: BachGetAgentProfileRequest req)
    //是否可以发起会话（前端使用）
    CanEstablishCommunicationResponse CanEstablishCommunication(1: CanEstablishCommunicationRequest req)
    //获取连接信息（前端使用）
    GetConnectInfoResponse GetConnectInfo(1: GetConnectInfoRequest req)
    //创建会话（前端使用）
    CreateConversationsResponse CreateConversations(1: CreateConversationsRequest req)
    //获取订单对应的会话列表（前端使用）
    QueryConversationsResponse QueryConversations(1: QueryConversationsRequest req)
    //评价会话（前端使用）
    JudgeConversationsResponse JudgeConversations(1: JudgeConversationsRequest req)
    //获取会话评价结果
    GetJudgeResultResponse GetJudgeResult(1: GetJudgeResultRequest req)
    //获取商家信息（前端使用）
    BatchGetShopProfileResponse BatchGetShopProfile(1: BatchGetShopProfileRequest req)
    //获取会话列表（工作台使用）
    GetConversationsByAgentIDResponse GetConversationsByAgentID(1: GetConversationsByAgentIDRequest req)
    //获取会话
    GetConversationByRelationIDResponse GetConversationByRelationID(1: GetConversationByRelationIDRequest req)
    //获取会话列表
    GetConversationsByRelationResponse GetConversationsByRelation(1: GetConversationsByRelationRequest req)
    //获取imageX上传token
    GetSTS2TokenResponse GetSTS2Token(1: GetSTS2TokenRequest req)
    //获取imageX上传token
    GetUrl4UriResponse GetUrl4Uri(1: GetUrl4UriRequest req)
}