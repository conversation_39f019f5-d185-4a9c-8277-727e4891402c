include "../../base.thrift"

namespace java com.bytedance.ies_kefu_sop_engine_admin
namespace go ies.kefu.sop_engine_admin

/* 流程定义部分
 _ __  _ __ ___   ___ ___  ___ ___
| '_ \| '__/ _ \ / __/ _ \/ __/ __|
| |_) | | | (_) | (_|  __/\__ \__ \
| .__/|_|  \___/ \___\___||___/___/
| |
|_|

 */


// 流程定义
struct Process {
    1: required list<Node> Nodes,   // 节点集合
    2: required list<SequenceFlow> SequenceFlows,   // 线集合
}

// 节点定义
struct Node {
    1: required string Name,    // 节点名称
    2: required string Key, // 节点key
    3: required string ComponentKey,    // 节点的所属组件的key
    4: required string Type,    // 节点的类型
    5: optional string TransData,   // 透传的数据
    6: optional list<Input> Input,  // 节点的输入
    7: optional string DefaultFlowKey,  // 节点默认流转的下一个节点key
    8: optional string Statistic,    // 节点的统计数据, JSON格式的数据
}

// 节点所需的输入
struct Input {
    1: required string InputType,    // 输入的类型，如关联表单、自流程等
    2: required string Protocols,   // 输入协议，为序列化后的字符串
    3: required string ApplyElement,   // 输入应用的元素key, 应用的元素、输入协议以及inputType见：https://bytedance.larkoffice.com/wiki/Dk0awvRJ9i34WVknsgrckKjjnAg 在新流程节点和协议部分
}

// 连线
struct SequenceFlow {
    1: required string Name,    // 连线名称
    2: required string Key, // 连线的key
    3: required string SourceRefKey,    // 线的源点
    4: required string TargetRefKey,    // 线的目标点
    5: optional string Condition,   // 连线的条件表达式，语法:https://bytedance.larkoffice.com/wiki/wikcn6Xbm1Vfs2Jt685fkIKINFi
    6: optional i32 Order,  // 连线的优先级序号
    7: optional string TransData,   // 透传的数据
}

// 组件
// 组件和元素由SOP-RD创建完成后提供给使用方
struct Component {
    1: required string Key, // 组件的key
    2: required list<Element> Elements, // 组件的元素
    3: required list<SequenceFlow> SequenceFlows,   // 组件的元素连线
}

// 元素
struct Element {
    1: required string Key, // 元素的唯一key
    2: required string Type,    // 元素的类型
    3: optional string DefaultFlowKey,  // 默认流转的下一个元素key
}



/* 流程组定义
 _ __  _ __ ___   ___ ___  ___ ___    __ _ _ __ ___  _   _ _ __
| '_ \| '__/ _ \ / __/ _ \/ __/ __|  / _` | '__/ _ \| | | | '_ \
| |_) | | | (_) | (_|  __/\__ \__ \ | (_| | | | (_) | |_| | |_) |
| .__/|_|  \___/ \___\___||___/___/  \__, |_|  \___/ \__,_| .__/
| |                                   __/ |               | |
|_|                                  |___/                |_|
*/

struct ProcessGroup {
    1: optional i64 ID, // 流程组ID
    2: required i64 AppID, // 应用ID
    3: required string Name, //流程组名称
    4: required i32 Status, // 状态 1-使用中 2-已下线
    5: optional string Description, //流程组描述
    6: required i32 ProcessType, //流程类型 类型 1-正常 2-嵌入式子流程
    7: optional string Config, //流程组配置
    8: optional string ExtraConfData,   // 流程组的其他额外配置信息，如原子流程组的枚举信息也会放在这里面
}

/* 实验部分
                           _                      _
                          (_)                    | |
  _____  ___ __   ___ _ __ _ _ __ ___   ___ _ __ | |_
 / _ \ \/ / '_ \ / _ \ '__| | '_ ` _ \ / _ \ '_ \| __|
|  __/>  <| |_) |  __/ |  | | | | | | |  __/ | | | |_
 \___/_/\_\ .__/ \___|_|  |_|_| |_| |_|\___|_| |_|\__|
          | |
          |_|
*/

struct GetLibraFlightListRequest{
    1: required i64 AccessID, //接入方ID
    2: optional i64 SopGroupID // 流程组ID

    255: base.Base Base,
}

struct GetLibraFlightListResponse{
    1: optional list<LibraFlight> FlightList

    255: required base.BaseResp BaseResp,
}

struct LibraFlight  {
    1: required  string   LayerName      // 实验层名
    2: required  i64      LayerID        // 实验层ID
    3: required  i64      FlightID       // 实验ID
    4: required string    FlightName     // 实验名
    5: optional i64       ProcessGroupID  // 流程组ID， 若实验没有绑定流程，则为空
    6: required list<LibraVersion> Versions   // 实验组列表
}

struct LibraVersion {
    1: required string Name  // 实验组名称
    2: required i64 ID       // 实验组ID
    3: required i64 Weight   // 流量权重比，仅针对流量不均等实验有意义，权重比值0 ~ 1000，实验每个version的weight加起来一定等于1000
    4: required i64 Type     // 0 对照组  1 实验组
    5: required string Description    // 描述
    6: optional i64 ProcessID   // 实验组绑定的流程定义ID，所没有绑定实验则为空
}

struct BindProcessFlightRequest {
    1: required i64 FlightID, // 实验ID
    2: required i64 ProcessGroupID // 流程组ID
    3: required i64 Operator, // 操作用户
    4: required list<VersionAndProcess> VersionAndProcess, // 实验组和流程版本配置
    5: required bool Offline, //是否下线全量版本
    6: required i64 LayerId, //实验层id
    255: optional base.Base Base,
}

struct VersionAndProcess{
    1: required i64 VersionID //实验组ID
    2: required i64 ProcessID //(灰度状态的流程)
}

struct BindProcessFlightResponse {
    1: optional i64 BoundGroupId //实验已经被绑定在该流程上
    255: required base.BaseResp BaseResp,
}

struct UnbindProcessFlightRequest {
    1: required i64 FlightID, // 实验ID
    2: required i64 ProcessGroupID
    3: required i64 Operator, // 操作用户

    255: optional base.Base Base,
}
struct UnbindProcessFlightResponse {
     255: required base.BaseResp BaseResp,
}

/* 接口部分
                     _               _       _             __               
                    (_)             (_)     | |           / _|              
 ___  ___ _ ____   ___  ___ ___      _ _ __ | |_ ___ _ __| |_ __ _  ___ ___ 
/ __|/ _ \ '__\ \ / / |/ __/ _ \    | | '_ \| __/ _ \ '__|  _/ _` |/ __/ _ \
\__ \  __/ |   \ V /| | (_|  __/    | | | | | ||  __/ |  | || (_| | (_|  __/
|___/\___|_|    \_/ |_|\___\___|    |_|_| |_|\__\___|_|  |_| \__,_|\___\___|
*/

struct CommonInfo {
    1: required i64 OperatorID, // 操作人ID
    2: optional i64 AccessPartyID, // 接入方ID
    3: optional i64 TenantID, //租户ID
}

struct CreateProcessGroupRequest {
    1: required CommonInfo CommonInfo,
    2: required ProcessGroup ProcessGroup,
    255: optional base.Base Base
}

struct CreateProcessGroupResponse {
    1: required i64 ID, // 流程组ID
    255: required base.BaseResp BaseResp
}

struct UpdateProcessGroupRequest {
    1: required i64 GroupID,    // 组ID
    2: required i64 OperatorID, // 操作人ID
    3: optional string Name,
    4: optional string Description,
    5: optional string Config,

    255: base.Base Base,
}

struct UpdateProcessGroupResponse {
    1: required base.BaseResp BaseResp,
}

struct DeleteProcessGroupRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, //流程定义组id
    255: optional base.Base Base,
}

struct DeleteProcessGroupResponse {
    1: required base.BaseResp BaseResp,
}

struct SearchProcessGroupRequest {
    1: required i64 AppID, // 应用ID
    2: optional i64 AccessPartyID, // 接入方ID
    3: optional i64 TenantID, //租户ID
    4: optional i32 ProcessType, //流程类型 类型 1-正常 2-嵌入式子流程 3-引用式子流程
    5: optional i64 CreatedBy, //创建人
    6: optional i64 updatedBy, //更新人

    255: optional base.Base Base
}

struct SearchProcessGroupResponse {
    1: optional list<ProcessGroup> ProcessGroupList, // 流程组

    255: required base.BaseResp BaseResp
}

struct GetProcessGroupByIDsRequest {
    1: required list<i64> GroupIDs, // 流程组ID

    255: optional base.Base Base
}

struct GetProcessGroupByIDsResponse {
    1: optional list<ProcessGroup> ProcessGroupList

    255: required base.BaseResp BaseResp
}

struct CreateProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ProcessGroupID, // 流程组ID
    3: optional Process Process, // 静态流程，可选，如果创建时不传，可以用update接口更新定义
    4: required string Name,
    5: optional string Diagram, // 用于前端复现流程图
    6: optional string Description, //流程图的描述

    255: optional base.Base Base,
}

struct CreateProcessResponse {
    1: required i64 ProcessID,	// 创建失败返回-1

    255: required base.BaseResp BaseResp,
}

struct StartEditProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID
    255: optional base.Base Base,
}

struct StartEditProcessResponse {
    1: optional string CurApplier, // 当前编辑者信息
    2: optional bool IsEditedByOther, //是否被其他人编辑
    255: base.BaseResp BaseResp,
}

struct FinishEditProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID
    255: optional base.Base Base,
}

struct FinishEditProcessResponse {
    255: base.BaseResp BaseResp,
}

struct KeepProcessEditRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID
    255: optional base.Base Base,
}

struct KeepProcessEditResponse {
    1: optional string CurApplier, // 当前编辑者信息
    2: optional bool IsEditedByOther, //是否被其他人编辑
    255: required base.BaseResp BaseResp,
}

// 只能更新草稿状态的，不允许编辑线上版本
struct UpdateProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID
    3: required Process Process, // 静态流程
    4: required string Diagram, // 用于前端复现流程图

    255: optional base.Base Base,
}

struct UpdateProcessResponse {
    255: required base.BaseResp BaseResp,
}

struct UpdateProcessStatusRequest {
    1: required i64 ID, // 流程定义ID
    2: required i32 Status //流程状态
    3: required i64 OperatorID, // 操作人ID

    255: optional base.Base Base,
}

struct UpdateProcessStatusResponse {
    255: required base.BaseResp BaseResp,
}
// 下线时需要校验是否被引用，是则不允许下线
struct OfflineProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID

    255: optional base.Base Base
}

struct OfflineProcessResponse {
    255: base.BaseResp BaseResp
}

struct DeleteProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID,
    255: optional base.Base Base,
}

struct DeleteProcessResponse {
    1: required base.BaseResp BaseResp,
}

struct GrayReleaseProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID
    3: required i8 ProcessStatus, //灰度类型 1白名单 3AB
    4: optional list<string> MainProcessGroupIds, // 原子sop 白名单支持指定流程组id

    255: optional base.Base Base
}

struct GrayReleaseProcessResponse {
    1: required i32 Version, //当前灰度发布的流程的版本, 发布失败返回-1

    2: base.BaseResp BaseResp
}

struct ReleaseProcessRequest {
    1: required CommonInfo CommonInfo,
    2: required i64 ID, // 流程定义ID

    255: optional base.Base Base
}

struct ReleaseProcessResponse {
    1: base.BaseResp BaseResp
}

struct ProcessMeta {
    1: optional i64 ID //流程定义ID
    2: required ProcessGroup ProcessGroup, // 流程定义组信息
    3: required i32 Version, // 版本
    4: required i32 ProcessStatus, //  流程定义状态
    5: required i64 CreateBy,
    6: required i64 CreateTime, //创建时间
    7: optional i64 ReleaseTime, // 发布时间
    8: optional i64 ReleaseBy, //发布人
    10: optional i64 UpdateTime //更新时间
    11: optional i64 UpdateBy //更新人
    12: optional Process Process, // 流程定义 如果入参控制不需要会返回个空对象或nil
    13: optional string Diagram,    // 用于复现前端流程图
}

struct GetProcessByIDsRequest {
    1: required CommonInfo CommonInfo,
    2: required list<i64> ProcessIDs, // 流程定义ID

    255: base.Base Base
}

struct GetProcessByIDsResponse {
    1: optional list<ProcessMeta> ProcessList,

    255: base.BaseResp BaseResp
}

struct GetProcessByGroupIDsRequest {
    1: required CommonInfo CommonInfo,
    2: required list<i64> ProcessGroupIDs, // 流程组ID
    3: optional list<i32> Status,// 指定流程状态
    255: base.Base Base
}

struct GetProcessByGroupIDsResponse {
    1: optional map<i64, list<ProcessMeta>> ProcessMap,

    255: base.BaseResp BaseResp
}

struct CloneProcessRequest {
    1: required i64 ProcessID,  // 流程ID
    2: optional string NewProcessName, // 克隆新流程的名称
    3: optional string Description, // 描述
    4: required bool CreateNewGroup,  // 是否创建新的流程组来克隆流程
    5: required i64 OperatorID, // 操作人ID
    6: optional bool IsDelExistedDraft, //是否删除已有草稿

    255: base.Base Base
}

struct CloneProcessResponse {
    1: optional i64 ProcessID,   // 流程ID
    2: optional i64 ProcessGroupID,  // 流程组ID

    255: required base.BaseResp BaseResp;
}

struct CheckProcessConfigRequest {
    1: required i64 ProcessID,
    2: optional bool SkipSelfCycleCheck,    // 是否跳过自身环检测
    3: optional bool SkipRefCycleCheck,    // 是否跳过引用环检测

    255: base.Base Base
}

struct ProcessReference {
    1: required i64 GroupID,
    2: required i64 DefineID,
    3: required i64 ReferredGroupID,
}

struct GetProcessReferenceRequest {
    1: required i64 ProcessID,

    255: base.Base Base
}

struct GetProcessReferenceResponse {
    1: optional list<ProcessReference> ReferredList,
    2: optional list<ProcessReference> BeReferredList,

    255: required base.BaseResp BaseResp
}

struct CheckProcessConfigResponse {
    255: required base.BaseResp BaseResp;
}

struct GetNodeTypeRequest {
    1: required i64 AppID, // 应用ID

    255: optional base.Base Base,
}

// 查innerkey映射接口
struct ListInnerDataRequest{
    1: optional i32 Type //info:1 or factor:2
    2: optional i64 AccessID //接入方
    3: optional list<i32> AppIDs // https://bytedance.larkoffice.com/wiki/Y3f8wkzf5iKptAkv1ctccHYHned

    255: optional base.Base Base;
}

struct ListInnerDataResponse{
    1: optional list<InnerData> InnerDataList //info映射
    255: required base.BaseResp BaseResp;
}

struct InnerData{
    1: required string Key,   // $Data.xxx
    2: required string InnerKey, // inner_xxxx
    3: required string Name, // inner_xxxx
    4: required i32 Type
}

struct ListComponentRequest {
    1: required i64 AppID, // 应用ID

    255: optional base.Base Base,
}

struct ComponentMeta {
    1: required string Name, //节点命中
    2: required string Key, //节点唯一标识
    3: required string Type,    // 类型
    4: required string Description, // 节点描述
    5: required string Icon, // 节点的图标

    6: optional list<ComponentInput> InputList, //节点输入
}

struct ComponentInput {
    1: required string InputType, // 节点输入的类型
    2: required string ApplyElementKey, // 输入是for哪个元素的
}

struct ListComponentResponse {
    1: required list<ComponentMeta> ComponentMetaList,

    255: required base.BaseResp BaseResp,
}

service SopEngineAdminService {

	/* 流程组部分 */
	// 创建流程组
	CreateProcessGroupResponse CreateProcessGroup(1: CreateProcessGroupRequest req);
	// 修改流程组
	UpdateProcessGroupResponse UpdateProcessGroup(1: UpdateProcessGroupRequest req);
	// 通过ID查找流程组
	GetProcessGroupByIDsResponse GetProcessGroupByIDs(1: GetProcessGroupByIDsRequest req);
	// 搜索流程组
	SearchProcessGroupResponse SearchProcessGroup(1: SearchProcessGroupRequest req);
	// 删除流程组
	DeleteProcessGroupResponse DeleteProcessGroup(1: DeleteProcessGroupRequest req);

	/* 流程定义部分 */

	// 创建流程
	CreateProcessResponse CreateProcess(1: CreateProcessRequest req);
	// 开始编辑流程
	StartEditProcessResponse StartEditProcess(1: StartEditProcessRequest req);
	// 心跳编辑流程
	KeepProcessEditResponse KeepProcessEdit(1: KeepProcessEditRequest req);
	// 退出编辑流程
	FinishEditProcessResponse FinishEditProcess(1: FinishEditProcessRequest req);
	// 更新流程
	UpdateProcessResponse UpdateProcess(1: UpdateProcessRequest req);
	// 更新流程状态
    UpdateProcessStatusResponse UpdateProcessStatus(1: UpdateProcessStatusRequest req);
	// 下线流程
	OfflineProcessResponse OfflineProcess(1: OfflineProcessRequest req);
    // 删除流程
    DeleteProcessResponse DeleteProcess(1: DeleteProcessRequest req);
	// 灰度发布流程
	GrayReleaseProcessResponse GrayReleaseProcess(1: GrayReleaseProcessRequest req);
	// 全量发布流程
	ReleaseProcessResponse ReleaseProcess(1: ReleaseProcessRequest req);
	// 通过ID搜索流程
	GetProcessByIDsResponse GetProcessByIDs(1: GetProcessByIDsRequest req);
	// 通过组ID查找流程
	GetProcessByGroupIDsResponse GetProcessByGroupIDs(1: GetProcessByGroupIDsRequest req);
	// 克隆流程定义
	CloneProcessResponse CloneProcess(1: CloneProcessRequest req);
	// 检测流程配置是否合法
	CheckProcessConfigResponse CheckProcessConfig(1: CheckProcessConfigRequest req);
	// 获取流程的引用关系
	GetProcessReferenceResponse GetProcessReference(1: GetProcessReferenceRequest req);

	/* 节点、因子 */
	// 因子/info下拉映射
	ListInnerDataResponse ListInnerData(1: ListInnerDataRequest req);
	// 节点列表
	ListComponentResponse ListComponent(1: ListComponentRequest req);

	/* 实验 */
	// 绑定实验
	BindProcessFlightResponse BindProcessFlight(1: BindProcessFlightRequest req);
	// 解绑实验
	UnbindProcessFlightResponse UnbindProcessFlight(1: UnbindProcessFlightRequest req);
}
