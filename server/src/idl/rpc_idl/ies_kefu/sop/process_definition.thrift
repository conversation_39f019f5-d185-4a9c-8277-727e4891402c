namespace go ies.cs.sop_engine_definition
include "ies_kefu_sop_engine_admin.thrift"

struct ProcessDefinition {
    1: required Process Process (go.tag = "json:\"process\"")// 流程定义
    2: required string  Diagram, // 流程布局
    3: optional  ies_kefu_sop_engine_admin.Process  NewProcess, // 新协议流程定义
	4: optional  list<NodeStatistics> NodeStatistics; // deprecated 支持新协议节点的统计数据，查询返回用
	5: optional  list<DynamicNodeInfo> DynamicNodeInfos // 支持新协议子流程节点元数据
}

struct DynamicNodeInfo {
	1: required string NodeKey // 节点key
	2: optional DynamicData DynamicData //子流程的最新全量版本
	3: optional NodeStatistics NodeStatistics //  统计数据，查询返回用
}

struct DynamicData{
	1: optional string Name
	2: optional i32 Version
	3: optional string GroupId
}

struct Process {
    1: required string Id,   // 流程定义ID，是一组流程的标示相当与ProcessDefinitionGroupId
    2: required string Name, // 流程名称
    3: optional StartNode StartNode, // 开始节点
    4: optional list<SystemJudgmentNode> SystemJudgmentNodes, // 系统判断节点
    5: optional list<ManualJudgmentNode> ManualJudgmentNodes, // 人工判断节点
    6: optional list<SystemExecutionNode> SystemExecutionNodes, // 系统执行节点
    7: optional list<ManualExecutionNode> ManualExecutionNodes, // 人工执行节点
    8: optional list<EndNode> EndNodes, // 结束节点
    9: optional list<SubProcessNode> SubProcessNodes, // 子流程节点列表
    10: optional list<ExclusiveGatewayNode> ExclusiveGatewayNodes,// 排他网关节点
    11: optional list<CommonSysExecutionNode> CommonSysExecutionNodes, //通用系统任务执行节点
    12: optional list<WaitNode> WaitNodes, //等待节点
    13: optional list<ParallelGatewayNode> ParallelGatewayNodes, //并行网关节点

}


//等待节点 （异步驱动）
struct WaitNode {
    1: required string Id,   //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional list<Value> Values // list<Value> Values 等待要发生的事件列表
    4: optional SequenceFlow OutFlow // 表达式连接线
    5: optional string ConfigJson,// 等待节点扩展数据
}

//并行网关 （提供符合条件分支并行 + 判断能力，不要求多分支汇集等待）
struct ParallelGatewayNode {
    1: required string Id, # ID 唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name,
    3: optional list<Value> Values, // 节点绑定的变量
    4: optional list<SequenceFlow> OutFlows // 节点的出连接线
    5: optional string ConfigJson,//节点扩展信息
}

//排他网关节点
struct ExclusiveGatewayNode {
    1: required string Id, # ID 唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name,
    3: optional list<Value> Values, // 节点绑定的变量
    4: optional list<SequenceFlow> OutFlows // 节点的出连接线
    5: optional string ConfigJson,//节点扩展信息

}


# 子流程引用节点
struct SubProcessNode {
    1: required string Id, # ID 唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name,
    3: optional string SubProcessGroupId, # 子流程的组ID
    4: optional string SubProcessName, # 返回时子流程名称，给前端展示用，实际不保存到数据库
    5: optional i32 SubProcessVersion, # 返回时子流程最新版本，给前端展示用，实际不保存到数据库
    6: optional string StageCode,//阶段

}

// 开始节点
//表示一个流程的开始，一个流程中只能有一个开始节点。
struct StartNode {
    1: required string Id, // ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    5: optional SequenceFlow OutFlow, // 节点的出连接线
    6: optional string ConfigJson,//节点扩展信息

}

// 结束节点
// 表示一个流程的结束，一个流程中可以有多个结束节点。
struct EndNode {
    1: required string Id, //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional string ConfigJson,// 结束节点元数据
}

/*
 * 系统判断节点
 */
struct SystemJudgmentNode {
    1: required string Id,   //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional list<Value> Values, // 节点绑定的变量
    4: optional list<SequenceFlow> OutFlows // 节点的出连接线
    5: optional bool IsNeedHide // 默认不需要 ，true 需要隐藏
    6: optional list<DisplayVariable> DisplayVariable // 变量展示
    30: optional NodeStatistics NodeStatistics; // 节点的统计数据，查询返回用，新增流程时不需要传递该值
}

struct DisplayVariable {
    1: optional list<Value> Values, // 展示字段绑定的变量
    2: optional string Name // 名称
    3: optional DisplayType DisplayType // 显示节点解释类型
    4: optional string Desc // 变量详细描述
    5: optional Expression Expression, // 表达式
}


enum DisplayType {
    Banned = 1
    PermanentDisplay = 2
    HoverDisplay = 3
}

// 人工判断节点
struct ManualJudgmentNode {
     1: required string Id, //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
     2: required string Name, // 名称
     3: optional string FormId, // 节点绑定的表单
     4: optional list<Value> Values, // 节点绑定的变量
     5: optional list<SequenceFlow> OutFlows, // 节点的出连接线
     6: optional string StageCode,//阶段
     7: optional string NodeConfigJson, // 节点扩展信息
     30: optional NodeStatistics NodeStatistics; // 节点的统计数据，查询返回用，新增流程时不需要传递该值
}

// 人工执行节点
struct ManualExecutionNode {
    1: required string Id,  //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional string FormId; // 表单ID
    4: optional SequenceFlow OutFlow,
    5: optional string StageCode,//阶段
    6: optional string NodeConfigJson, //标签信息
    30: optional NodeStatistics NodeStatistics; // 节点的统计数据，查询返回用，新增流程时不需要传递该值
}


// 通用系统任务执行节点
struct CommonSysExecutionNode {
    1: required string Id,  //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional Action Action, // 系统任务节点绑定的动作(泛化调用)
    4: optional SequenceFlow OutFlow
    5: optional string ConfigJson,// 任务节点配置信息
    6: optional string Script,//脚本
    30: optional NodeStatistics NodeStatistics; // 节点的统计数据，查询返回用，新增流程时不需要传递该值
}

// 系统任务节点
struct SystemExecutionNode {
    1: required string Id,  //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: required string Name, // 名称
    3: optional list<Action> Actions, // 系统任务节点绑定的动作(泛化调用)
    4: optional SequenceFlow OutFlow  // 历史逻辑
    5: optional string ConfigJson,// 任务节点配置信息
    6: optional string Script,//脚本
    7: optional list<SequenceFlow> OutFlows // 节点的出连接线  多条
    30: optional NodeStatistics NodeStatistics; // 节点的统计数据，查询返回用，新增流程时不需要传递该值
}


struct Action {
    1: required string Key; // Action 的标示 API id
    2: required string Name; // Action的名称
    3: required i64 Order   // Action的顺序
    4: optional list<ActionInput> ActionInputs  // Action的映射
    5: optional list<Value> OutPuts // 输出
    6: optional string DymParamJson// 泛化调用参数
}

struct ActionInput {
    1: required Value Left, // 左值
    2: required Value Right, // 右值
}

// 连接线
struct SequenceFlow {
    1: required string Id, //ID  唯一标示 "UUID" 组成,UUID 由前端生成。
    2: optional string Name, // 连接线的名称
    3: required string SourceId, // 起点
    4: optional string TargetId, // 终点
    5: optional Expression Expression, // 表达式
    6: optional bool IsDefault //在判断节点中是否是默认分支
}

// 表达式
/**
* 对应关系doc:https://bytedance.feishu.cn/docs/doccnZixauHP1OXwYwux3UA4XTe#
* Operator
 AndOP          = "&&"
 OrOP           = "||"
 EqOP           = "=="
 InOP           = "in"
 NotInOP        = "not in"
 NqOP           = "!="
 GreaterOP      = ">"
 LessOP         = "<"
 GreaterAndEqOP = ">="
 LessAndEqOP    = "<="
 MinusOP        = "-"
 ContainsOP     = "contains"
 NotContainsOP  = "not contains"
**/
struct Expression {
    1: required list<SingleExpression> SingleExpressions, // 二元表达式，普通表达式
    3: required string Operator // SingleExpressions的连接符
}

struct SingleExpression{
    1: optional BinaryExpression BinaryExpression, // 二元表达式，普通表达式
    2: optional TernaryExpression TernaryExpression, // 三元表达式 例如日期类需要运算的表达式
}

// a > b 二元表达式
struct BinaryExpression {
    1: required Value Left, // 表达式的左值  a
    3: required string Operator, // 操作符 >
    4: required list<Value> Rights, // 表达式的右值 b
}

// a + b = c 三元表达式
struct TernaryExpression {
    1: required Value First, // 表达式的左值  a
    2: required string LeftOperator, // 操作符 +
    3: required Value Second, // 表达式的左值  b
    4: required string Operator, // 操作符 =
    5: required list<Value> Rights, // 表达式的右值 c
}

// 值
struct Value {
    1: required string V, // 值 如果是变量，值为变量的Key
    2: required ValueType Type, // 值的类型
    3: optional string SubType, // 对应info的subType,直接透传
    4: required bool IsVariable, // 是否变量的标识
    5: optional bool IsList, // 是否列表，用作info的判断
    6: optional bool IsDefaultVal,// 是否是默认值 作为表达式右值时生效
    7: optional list<ValueInput> InfoInputs //info注入变量
}
struct ValueInput{
    1: required Value Left
    2: required Value Right
}
enum ValueType {
    STRING, // 字符串，只可以作为右值
    NUMBER, // 数值，只可以作为右值
    BOOL, // BOOLEAN，只可以作为右值
    OTHER // OTHER 类型 info定义
}

// 节点的统计数据
struct NodeStatistics {
    1: required i64 OpenTimes;      // 打开次数
    2: required double OpenRate;       // 打开比率
    3: required i64 AverageTime;    // 平均耗时
    4: required i64 DislikeCount;   // 点踩量
    5: required double DislikeRate;    // 点踩率
    6: optional string NodeKey // 节点key 支持新协议
}