include "../../base.thrift"
include "sop_common.thrift"
namespace go ies.cs.sop_engine_api

struct ProcessParam {
    1: optional string ProcessGroupKey, // 流程定义组key
    2: optional i64 ThirdCategoryID,// 问题分类三级ID,deprecated,准备废弃,新需求不要用，都走下面的
    3: optional i64 SecondCategoryID, // 命中的规则组
    4: optional i64 FeatureID, // 特征ID
    5: optional string ProcessDefintionId // 流程定义ID ，指定后使用确定版本的流程，for LLM @shanpengyu
}

struct FeedbackContent{
    1: list<sop_common.FeedbackType>  Feedbacks, // 反馈，可以多选
    2: optional string Comment // 评论
    3: optional list<string> Imgs // 图片列表
}

enum CommentType {
    ProcessLike = 1, // 流程点赞
    ProcessDisLike = 2, // 流程点踩
    NodeDisLike = 3, // 节点点踩
}

struct StartProcessRequest {
    1: required sop_common.BaseInfo BaseInfo ,// 基本信息
    2: required ProcessParam ProcessParam, //定位流程定义的信息
    3: optional list<sop_common.Info> InitInfos, //提交的数据
    4: optional string DataExtra, // 数据打点字段，具体的format 请见https://bytedance.feishu.cn/docs/doccn10pRq0LWonZgifrurFswhf#
    5: optional sop_common.StartProcessType StartProcessType, //拉起流程方式
    6: optional bool NeedRefresh, //是否需要强制开启一个新sop;false:否(默认值) true:是
    7: optional bool NeedWatchSysParamAndRefresh  // 是否重跑流程的系统变量，如果变量跟心导致历史路径改变则返回最近路径
    8: optional bool FindAndExecuteLatestSopVersion  // 查找并自动执行最新版本的流程
    9: optional bool ForceNeedWholePath  // 强制返回完整路径，包括配置的隐藏系统判断节点
	10: optional string ExtraData // 前端维护的拉起时的信息
    255: base.Base Base
}

struct CommentRequest {
    1: required sop_common.BaseInfo BaseInfo ,
    2: required string ProcessInstanceID, // 流程ID
    3: required string ProcessDefID, // 流程定义ID
    4: optional string NodeID, //节点ID
    5: optional string NodeKey, //节点Key
    6: optional string NodeName, // 节点名字
    7: optional sop_common.NodeType NodeType, // 节点类型
    8: required string TaskID,// TaskID
    9: required CommentType CommentType, // 评论类型
    10: optional FeedbackContent FeedbackContent, // 评论内容
    11: optional string ObjectId,// 实体id
}

struct CommentResponse {
    255: base.BaseResp BaseResp
}

struct LookupRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: optional i64 SecondCategoryID, //二级问题分类ID
    3: optional i64 ThirdCategoryID, // 三级问题分类ID
    4: optional list<i64> FeatureIDs, // 特征key列表
    5: optional bool IsInitialize, // 是否初始化，初始化时候，流程列表为多个不返回
    6: optional list<i64> ThirdCategoryIDs,
    7: optional i64 DataVersion //  区分国际化新一代工作台2.0数据  1.0工作台：0, 下一代工作台 1 ，默认0
    255: base.Base Base
}

// RuleGroup 规则组，组合规则对应一个特征 和 一个三级问题分类ID
struct RuleGroup {
    1: optional Feature Feature, // 命中的特征
    2: optional QuestionCategory Category, // 命中的问题分类
}

struct Feature {
    1: i64 FeatureID, // 特征key
    2: string FeatureName, // 特征名字
}

struct QuestionCategory {
    1: i64 FirstCateID, // 一级问题分类ID
    2: string FirstCateName, // 一级问题分类名字
    3: i64 SecondCateID, // 二级问题分类ID
    4: string SecondCateName, // 二级问题分类名字
    5: i64 ThirdCateID, // 三级问题分类ID
    6: string ThirdCateName, // 三级问题分类名字
}

struct ProcessDescription {
    1: string ProcessName, // 流程名字
    2: string ProcessDescription, // 流程描述
    3: string ProcessGroupKey, // 流程组定义key
    4: list<RuleGroup> HitRuleGroup, // 命中的规则组
}

struct LookupResponse {
    1: optional list<ProcessDescription> ProcessList, // 流程信息
    2: optional list<Feature> RecommendFeatures, // 搜索推荐的特征key
    255: base.BaseResp BaseResp
}

struct TaskCloseRequset {
    1: required string UserID, // 用户ID
    2: required string TaskID ,// 会话ID
    3: optional sop_common.ChannelType ChannelType, // 渠道信息 默认不传递（国内IM）兼容处理
    4: optional string ObjectId ,// 不同渠道 不同的统计维度实体id（taskIds, queryId, 国际化工单ID等）
    255: base.Base Base
}

struct TaskCloseResponse {
    255: base.BaseResp BaseResp
}

struct NewInterceptionExperimentRequest {
    1: required string AgentID, //坐席ID
    2: required string Env, //灰度环境, 默认为prod，必传
    3: required InterceptionType InterceptType, // 拦截类型
    4: optional string SopID, // 流程组ID
    6: optional string InterceptParam, // 拦截参数, 如果为空，把实验param置空
    255: required base.Base Base
}

struct NewInterceptionExperimentResponse {
    1: optional string ExperimentID, // 实验ID，如果agentID+SopID+Env参数相同，默认覆盖旧的param，ID取旧的
    255: base.BaseResp BaseResp
}

enum InterceptionType {
    InjectInfos = 1 // 注入参数类型
}

enum SearchEngineType {
    Mysql = 1, // mysql数据库
    Es    = 2, // Es 搜索引擎
}

struct SearchProcessByNameRequest {
    1: required sop_common.BaseInfo BaseInfo,     //基本入参
    2: optional string ProcessGroupName,          //流程定义组名称
    3: optional bool FilterFeatureBindProcess,    //是否过滤特征绑定的流程：默认false不过滤
    4: optional SearchEngineType SearchEngineType //使用那种搜索引擎对流程名进行搜索, 不指定默认使用mysql
    5: optional i32 PageSize                      //最多返回的数据条数
    6: optional i64 DataVersion                   // sop类型 区分国际化新一代工作台2.0  1.0工作台：1, 下一代工作台 2 ，默认1
    255: base.Base Base
}

struct SearchProcessBySituationRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: optional string SituationName, //场景名称
    255: base.Base Base
}


struct SearchProcessBySituationResponse {
    1: optional list<SearchSituationAndProcessGroup> ProcessGroupList, // 流程组信息
    255: base.BaseResp BaseResp
}


struct SearchSituationAndProcessGroup {
    1: string SituationName, // 场景名字
    2: string ProcessGroupKey, // 流程组定义key
    3: i32 SituationId // 场景ID
}

struct SearchProcessByIdRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: required list<string> ProcessGroupId, //流程定义组Id
    255: base.Base Base
}


struct SearchProcessByNameResponse {
    1: optional list<SearchProcessGroup> ProcessGroupList, // 流程组信息
    255: base.BaseResp BaseResp
}


struct SearchProcessGroup {
    1: string ProcessGroupName, // 流程名字
    2: string ProcessGroupKey, // 流程组定义key
    3: optional list<AllLevelCategoryId> AllLevelCategoryIds,//
    4: optional list<string> KeyWords, // SOP关键词，关键词包含搜索词时赋值,当前仅for商服接入方
}

struct AllLevelCategoryId {
    1: i64 FirstCategoryID, // 一级问题分类ID
    2: i64 SecondCategoryID, // 二级问题分类ID
    3: i64 ThirdCategoryID, // 三级问题分类ID
}

struct SearchOperateHistoryByTaskIdResponse {
     1: optional list<OperateRecord> OperateRecords, //会话纬度的操作记录
     255: base.BaseResp BaseResp
}

struct OperateRecord {
    1: optional ProcessInfo ProcessInfo,
    2: optional list<NodeInfo> NodeInfos,
}

struct ProcessInfo {
    1: optional sop_common.StartProcessType StartProcessType; // 打开方式
    2: optional string ProcessName, //流程 名称
    3: optional string Version, //流程版本
    4: optional bool EndFlag, //流程是否结束
    5: optional i64 CostTime, //秒
    6: optional string ProcessDefId, //流程定义id
    7: optional i64 OpenTime, //打开时间
    8: optional i64 EndTime, // 结束时间
    9: optional string ProcessInstanceId,//流程实例id
}

struct NodeInfo {
    1: optional string NodeId, //节点id
    2: optional string NodeKey, // 节点key
    3: optional string NodeName, // 节点名称
    4: optional sop_common.NodeType NodeType, // 节点类型
    5: optional i64 EventTime, //时间时间
    6: optional sop_common.NodeStatus NodeStatus, // 执行状态
    7: optional string ExecutionResult, // 执行结果，只有判断类型节点才有
    8: optional bool ModifyFlag, //是否有修改
    9: optional string SubProcessName, //子流程节点类型时候使用 子流程名称
    10: optional string SubProcessVersion, //子流程节点类型时候使用 子流程版本
}


struct SearchOperateHistoryByTaskIdRequest {
    1: required string TaskID, //会话id
    255: base.Base Base
}

struct  CategoryProcessInfo {
     1: optional QuestionCategory Category, // 问题分类
     2: optional string ProcessName, //流程 名称
     3: optional string ProcessDefId, //流程定义id
}


struct GetAllBindCategoryProcessRequest {
    1: required sop_common.BaseInfo BaseInfo ,// 基本信息
    255: base.Base Base
}

struct GetAllBindCategoryProcessResponse {
      1: optional list<CategoryProcessInfo> CategoryProcessInfos,
      255: base.BaseResp BaseResp
}


struct GetWorkBenchOpenSopListRequest {
     1: required sop_common.BaseInfo BaseInfo ,// 基本信息
     2: required list<sop_common.Info> InitInfos, //提交的数据
     255: base.Base Base
}

struct ProcessGroupInfo {
    1: string ProcessGroupName, // 流程名字
    2: string ProcessGroupKey, // 流程组定义key
    3: optional list<sop_common.ChannelType> Channels,// 流程绑定的渠道信息
    4: optional string ExtraData, // 前端维护的拉起时的信息
    5: optional string DetailInfoId,
    6: optional i64 UpdateTime
}


struct GetWorkBenchOpenSopListResponse {
     1: required list<ProcessGroupInfo> ProcessGroups
     2: optional string DetailInfoId
     255: base.BaseResp BaseResp
}


struct RemoveWorkBenchSopRequest  {
    1: required sop_common.BaseInfo BaseInfo ,// 基本信息
    2: required list<sop_common.Info> InitInfos, //提交的数据
    3: required list<string> ProcessGroupKeys
    255: base.Base Base
}

struct RemoveWorkBenchSopResponse  {
    255: base.BaseResp BaseResp
}

struct BatchGetProcessGroupInfoRequest {
    1: required list<string> ProcessGroupKeys, //流程定义组id
    255: base.Base Base
}

struct BatchGetProcessGroupInfoResponse {
   1: required list<ProcessGroupInfo> ProcessGroups
   255: base.BaseResp BaseResp
}

struct ContainerCloseRequset {
	1: required string  UserID
	2: required sop_common.ChannelType ChannelType
	3: required string ObjectId
    255: base.Base Base
}

struct ContainerCloseResponse {
       255: base.BaseResp BaseResp
}

struct GetProcessBindingInfosRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: required list<string> ProcessGroupKeys, //流程定义组key
    255: base.Base Base
}

struct GetProcessBindingInfosResponse {
    1: optional list<SearchProcessGroup> ProcessGroupList, // 流程组信息
    255: base.BaseResp BaseResp
}

struct LookupArbitrateSopRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: optional string FirstAfterSaleReasonCode, //一级售后类型原因code
    3: optional list<i64> FeatureIDs, // 特征key列表
    255: base.Base Base
}



struct LookupArbitrateSopResponse {
    1: optional list<ProcessGroupInfo> ProcessList, // 流程信息
    255: base.BaseResp BaseResp
}


struct LookupBindingFeatureSopRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: optional list<i64> FeatureIDs, // 特征key列表
    255: base.Base Base
}

struct LookupBindingFeatureSopResponse {
    1: optional map<i64, ProcessGroupInfo> Feature2Process, // 流程信息
    255: base.BaseResp BaseResp
}

struct LookupSopFilterWithRuleEngineRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: required string EventKey,
    3: required string FactData, #json格式
    255: optional base.Base Base,
}

struct LookupSopFilterWithRuleEngineResponse {
	1: optional list<ProcessGroupInfo> ProcessList, // 流程信息
	255: base.BaseResp BaseResp
}

struct CheckSopAvailableResponse {
	1: optional list<string>  AccessibleSopGroupIdList, // 可访问的流程组id列表
	2: optional list<ProcessGroupInfo> ProcessList, // 可访问的流程组id列表
    255: base.BaseResp BaseResp
}

struct CheckSopAvailableRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: required list<string> SopGroupIdList, //待验证的sop组列表
    255: base.Base Base
}

struct GetSopOfSolutionRequest {
    1: required i64 UserID             // 用户ID
    2: required i64 OrderID            // 订单ID
    3: required i64 KnowledgeID        // 知识点id，如果没有不要传0，可以传-1
    4: required string SopId   // SopGroupId
    5: required i64 Source             // 调用方来源，妙计传8，IM锦囊传1，热线锦囊传4，二线工作台传5
    6: optional i64 AgentID            // 坐席id
    7: optional i64 TaskID             // 会话id，智能和IM工作台传
    8: optional string HotLineID      // 热线ID，热线工作台传

    255: optional base.Base Base
}

struct GetSopOfSolutionResponse {
    1: optional string SopID      // 智能侧为空则表示干预平台没返回方案，人工侧该字段不返回
    2: optional string SopName    // sop名称
    3: optional string SceneCode  // 场景code
    4: optional list<string> SolutionCodeList    // 干预平台方案，数组类型（存在多方案）
    5: required string OriginSopID      // request中的SopGroupId
    6: required string OriginSopName    // sop名称
    7: optional list<SolutionDetail> SolutionDetailList //解决方案详情
    255: base.BaseResp BaseResp
}

struct SolutionDetail {
    1: optional string SolutionCode   // 方案Code
    2: optional i64 RefundAmount  // 退款金额。人民币分
    3: optional i64 AmountPercent // 退款比例。1~100
    4: optional i64 FreeShipping  // 免运费出资方。1=商责,4=达责
    5: optional i64	CompensateBizType // 赔付类型
    6: optional bool NeedCompensate // 是否需赔付
    7: optional i64 Funder  // 仅退款出资方
    8: optional i64 ActionNode // 执行节点
}


struct SearchProcessByDefIdsRequest {
    1: required sop_common.BaseInfo BaseInfo, //基本入参
    2: required list<string> ProcessDefIds, //流程定义Id
    255: base.Base Base
}


struct SearchProcessByDefIdsResponse {
    1: optional list<SimpleProcessGroup> ProcessGroupList, // 流程组信息
    255: base.BaseResp BaseResp
}

struct SimpleProcessGroup {
    1: optional string ProcessGroupName, // 流程名字
    2: optional string ProcessGroupKey, // 流程组定义key
}




service APIService {
    // 开始流程
    sop_common.ProcessResponse StartProcess(1: StartProcessRequest req),
    // 下一步,包含指定回退
    sop_common.ProcessResponse NextStep(1: sop_common.NextStepRequest req),
    // 评论
    CommentResponse Comment(1: CommentRequest req),
    // 查找流程
    LookupResponse Lookup(1: LookupRequest req),
    // 会话关闭
    TaskCloseResponse TaskClose(1: TaskCloseRequset req),
    // 灰度环境用，正式环境勿用
    // 开启新的拦截实验
    NewInterceptionExperimentResponse NewInterceptionExperiment(1: NewInterceptionExperimentRequest req), //开启新的拦截实验

    //根据流程定义组名称搜索
    SearchProcessByNameResponse SearchProcessByName(1: SearchProcessByNameRequest req),

    //根据场景名称搜索场景绑定的sop
    SearchProcessBySituationResponse SearchProcessBySituation(1: SearchProcessBySituationRequest req),

    // 获取干预平台的方案
    GetSopOfSolutionResponse GetSopOfSolution(1: GetSopOfSolutionRequest req)

    //根据流程定义组Id搜索
    SearchProcessByNameResponse SearchProcessById(1: SearchProcessByIdRequest req),

    //根据会话id查询操作快照数据
    SearchOperateHistoryByTaskIdResponse SearchOperateHistoryByTaskId(1: SearchOperateHistoryByTaskIdRequest req),

    //获取全量的绑定标签的sop列表
    GetAllBindCategoryProcessResponse GetAllBindCategoryProcess(1: GetAllBindCategoryProcessRequest req)

    //获取工作台页面打开的Sop列表
    GetWorkBenchOpenSopListResponse  GetWorkBenchOpenSopList(1:GetWorkBenchOpenSopListRequest req)

    //从工作台移除打开的Sop
    RemoveWorkBenchSopResponse RemoveWorkBenchSop(1:RemoveWorkBenchSopRequest req)

    //用于check faq绑定的sop能否被透出
    BatchGetProcessGroupInfoResponse BatchGetProcessGroupInfo (1: BatchGetProcessGroupInfoRequest req)

    //容器（在线、热线、工单、练线）关闭会话通知事件
    ContainerCloseResponse ContainerClose(1: ContainerCloseRequset req)

    //批量获取流程绑定的标签id
    GetProcessBindingInfosResponse GetProcessBindingInfos(1: GetProcessBindingInfosRequest req)

     // 查找仲裁流程
    LookupArbitrateSopResponse LookupArbitrateSop(1: LookupArbitrateSopRequest req),

    // 根据特征查找绑定流程
    LookupBindingFeatureSopResponse LookupBindingFeatureSop(1: LookupBindingFeatureSopRequest req),

     // 根据规则引擎配置的信息查询匹配到的sop列表
    LookupSopFilterWithRuleEngineResponse   LookupSopFilterWithRuleEngine(1:  LookupSopFilterWithRuleEngineRequest req)

	 //批量判断技能组是否有权限使用已全量发布的流程
	CheckSopAvailableResponse   CheckSopAvailable(1:  CheckSopAvailableRequest req)

	//根据流程定义Id获取流程信息
    SearchProcessByDefIdsResponse SearchProcessByDefIds(1: SearchProcessByDefIdsRequest req),

}