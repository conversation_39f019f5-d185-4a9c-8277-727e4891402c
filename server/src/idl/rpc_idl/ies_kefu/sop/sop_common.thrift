include "../../base.thrift"
include "process_definition.thrift"
namespace go ies.cs.sop_engine_common

enum NodeType { // 节点类型,向外暴露的
    StartNode = 1,  // 开始节点
    EndNode = 2, // 结束节点
    SystemJudgmentNode = 3,  // 系统判断节点
    ManualJudgmentNode = 4, // 人工判断节点 = 用户节点 + 排斥网关
    ManualExecutionNode = 5, // 人工执行节点 = 用户节点
    SystemExecutionNode = 6, // 系统执行节点 = 任务节点
    SubProcessNode = 7, // 子流程跳转节点
    ExclusiveGetwayNode = 8 // 排他网关节点
    CommonSysExecutionNode = 9 // 通用系统任务节点
    WaitNode = 10 // 等待节点
    ParallelGatewayNode = 11 // 并行网关节点
}

struct SubProcessNode {
    1: string SubProcessName, // 子流程节点展示名称
    2: string SubProcessDefID, // 子流程定义ID
    3: optional i32 Version, //子流程版本号
}

enum ChannelType {
    IMWorkBench  = 1  // IM工作台
    HotLineWorkBench = 2  // 热线工作台
    TicketWorkBench = 3 // ERS工单工作台
    PlayLine = 4 // 练线IM工作台
    ToBArbitrate = 5, // 仲裁工单工作台
    ToBTicket = 6, // 电商工单工作台
    ToBOneHalfLineTicket = 7 // 一线场景： 商达1.5工单工作台
    TicketI18n   = 10  // 国际化工单
    IMI18n   = 11  // 国际化IM
    ToBSecondLineRisk = 12, //二线场景 ： 风险单 ： 墨提斯工作台
    IML4 = 13// 一线场景： 智能l4托管机器人

    HiAgents = 100 // 大模型hiAgents
    HiQC = 102 // 质检hiQC
}

//所有的 主要为了兼容历史
enum BusinessType {
    ECommerceCustomer = 2, //电商客服
    ECommerceShop = 3, // 电商商
    ECommerceDa = 16, //电商达人
    ECommerceCrossBorderCustomer = 19,//跨境客服
    IesReaders = 7,  //主播
    IesMasterClient = 11, //主端
    LiveBroadCast = 9, //直播工会
    DHJinZhu = 10, //抖火金主
    EnterpriseAccount = 39, //企业号
    LifeServiceCusumer = 40, //生活服务消费者
    LifeServiceShop = 41, //生活服务商家
    LifeServiceDaren = 42, //生活服务达人
    ERSTicketService = 33, //ERS工单
    FinanceConsumer  = 63  // 财经-消金
    FinancePayment   = 64  // 财经-支付
    FinanceAssurance = 65  // 财经-保险
    GlobalSelling = 35
	TouTiaoCreator = 12 // 头条创作者
	XiGuaCreator = 5     // 西瓜创作者
	FanQieCreator = 72    // 番茄创作者
	RetailCustomerService = 74  // 即时零售（客服）
    RetailSellerService = 76  // 即时零售商服

   	DefaultForGlobal = 999 // 国际化 required必传 默认值 无实际意义
}

enum CnBusinessType {
    ECommerceCustomer = 2, //电商客服
    ECommerceShop = 3, // 电商商
    ECommerceDa = 16, //电商达人
    ECommerceCrossBorderCustomer = 19,//跨境客服
    IesReaders = 7,  //主播
    IesMasterClient = 11, //主端
    LiveBroadCast = 9, //直播工会
    DHJinZhu = 10, //抖火金主
    EnterpriseAccount = 39, //企业号
    LifeServiceCusumer = 40, //生活服务消费者
    LifeServiceShop = 41, //生活服务商家
    LifeServiceDaren = 42, //生活服务达人
    ERSTicketService = 33, //ERS工单
	FinanceConsumer  = 63  // 财经-消金
	FinancePayment   = 64  // 财经-支付
	FinanceAssurance = 65  // 财经-保险
	TouTiaoCreator = 12   // 头-创作者
	XiGuaCreator = 5      // 西-创作者
	FanQieCreator = 72    // 番-创作者
	RetailCustomerService = 74  // 即时零售（客服）
    RetailSellerService = 76  // 即时零售商服

}

//https://bytedance.feishu.cn/docx/doxcnpWx4gPyiK8ofc31xGOd3AO
enum GlobalBusinessType {
    TikTokShopSeller = 2, //TikTok Shop Seller Service
    TikTokShopBuyerService = 3, // TikTok Shop Buyer Service
    TikTokShopCreator = 9, //TikTok Shop Creator Service
    FannoBuyer = 10, //Fanno Buyer Service
    FannoSellerService = 11, //Fanno Seller Service
    GlobalSelling = 35, //global seller
    KaSeller = 34, // Partner Enablement KA Seller
    TTFullSeller = 45, //Tiktok Full Service Seller Service
	TokoSeller = 52
	TokoBuyer = 51
}

enum TicketChannelType {
  MAGELLAN_CUSTOMER = 7, // 麦哲伦-买家
  MAGELLAN_SHOP = 8, // 麦哲伦-店铺
  FANS_SHOP = 23, // fans 店铺
  FANS_BUYER = 24, // fans 买家端
  MAGELLAN_CREATOR = 28 // 麦哲伦-达人
}

struct BaseInfo {
    1: required string UserID, // 用户ID， 坐席的UID
    2: required ChannelType ChannelType,// 接入系统， IM工作台，国际化工单，
    3: required BusinessType BusinessType, // 业务类型 兼容不报错后续下掉
    4: optional TicketChannelType TicketChannel, // 国际化业务类型，含义同国内的BusinessType - 已废弃不再使用
    5: optional GlobalBusinessType GlobalBusinessType, // 业务类型 国际化
    6: optional CnBusinessType CnBusinessType,// 国内业务类型
    7: optional string ScenarioUuid, //练线Id
    8: optional string UserName, //用户姓名
    9: optional string Email //邮箱
    10: optional ApplyScene ApplyScene, //流程应用场景 线上数据默认为1 不传默认为1
    11: optional i64 SkillGroupId //ticket所属的技能组
    12: optional string CountryCode  // 国家代码：for 国际化
    13: optional list<i64> StaffSkillGroupIds //技能组列表
    14: optional map<string, string> ExtraData, // eg  SellerType : 1: CB 2:L2L ：for 国际化
}

//流程应用场景 线上数据默认为1 ，新增的应用看具体所属场景
enum ApplyScene {
    FirstLine = 1
    SecondLine = 2
 }

enum ProcessStatus {
    ONGOING = 1,
    COMPLETED = 2, // 结束
}

struct ProcessDetail {
    1: string ProcessInstanceID, // 流程实例ID
    2: string ProcessDefID, //流程定义ID,唯一标识的流程定义
    3: ProcessStatus ProcessStatus,// 流程状态
    4: i32 Progress,// 进度条，百分比
    5: i32 RestStep, //剩余步数
    6: bool Commented,// 是否已经反馈
    7: optional string StarlingInfo, // StarlingInfo
    8: optional i32 Version, //版本号
    9: optional string Instruction //流程说明文档链接
    10: optional string RuleName // 绑定的规则名称
    11: optional string RuleId // 绑定的规则ID
}

struct NodeDetail {
    1: string NodeName, // 节点名称
    2: NodeType NodeType, // 节点类型
    3: optional string ExecutionResult, // 执行结果
    4: string FormKey, //表单key
    5: string NodeID,//节点ID
    6: string NodeKey,// 节点定义ID
    7: bool DisLiked, // 是否已点踩
    10: NodeStatus NodeStatus, // 节点状态
    11: optional SubProcessNode SubProcessNode, //子流程节点时候展示用
    12: optional i64 createTime, //节点的创建时间
    13: optional bool IsNeedHide, //true 需要隐藏
    14: optional list<map<string,list<Info>>> Inputs, // 一个系统节点绑定多个info ，系统节点的入参 取aliasKey
    15: optional list<map<string,Info>> OutPuts, //系统节点的出参 取aliasKey
    16: optional string NodeStageCode, //人工节点配置的阶段
    17: optional string SubProcessStageCode, //当前节点所处子流程配置的阶段
    18: optional list<VariableForDisplay> VariableForDisplay, // 变量展示
    19: optional NodeType NextNodeType, //   下一个节点的类型 （只有当前节点是单分支节点才会有此字段）
    20: optional i64 TimeCost // 节点耗时
    21: optional string ConfigJson // 配置的节点附加信息
    22: optional i32 CommitStatus // 节点提交状态，0：未提交过 1：已提交过（HIQC场景 不能重复提交节点）
    23: optional i64 leaveTime, //节点的离开时间
}

struct VariableForDisplay {
    1: optional string Name // 展示变量的名称
    2: optional string VariableValue, // 展示变量的值
    3: optional bool IsValidate // 变量是否满足配置的规则
    4: optional process_definition.DisplayType DisplayType // 显示节点解释类型
    5: optional string Desc // 变量的详细描述
}


enum DataType{
   Init = 1, // 初始化
   Commit = 2
}

struct ProcessResponse {
    1: required ProcessDetail ProcessDetail, //流程信息
    2: list<NodeDetail> NodeList, // 节点列表
    3: optional map<string, string> CurData,
    4: optional bool ChangedToLatestSopVersion  // 本次是否切换到最新的版本并执行
    254: optional string LogID,// LogID , 方便反馈
    255:base.BaseResp BaseResp
}

struct NextStepRequest {
    1:required BaseInfo BaseInfo, // 透传,用于保存拉起人
    2:required string ProcessInstanceID, // 流程实例ID
    3:optional list<Info> CommitInfos, // 提交的Data
    4:optional string NodeID, //节点实例ID
    5:optional string NodeKey, // 运行态节点定义ID
    6:optional string TaskID, //会话ID
    7:optional string FormSnapShot, //表单快照数据
    8: optional bool ForceNeedWholePath  // 强制返回完整路径，包括配置的隐藏系统判断节点
    255:base.Base Base
}

struct EventMessage {
    1: optional string ProcessInstanceID, //流程实例ID
    2: optional string ProcessDefID, // 流程定义ID
    3: optional string ProcessName, // 流程名称
    4: optional string NodeID, // 节点ID
    5: optional string NodeKey, // 节点定义key
    6: optional string NodeName, //节点名称
    7: optional NodeType NodeType, //节点类型
    8: optional string BusinessKey, //业务标识
    9: required EventType EventType, // 事件类型，
    10: optional bool ReLeave, // 是否重新离开，用于回退，依附于nodeLeave事件
    11: optional string ExecutionResult, // 执行结果，只有判断类型节点才有，依附于nodeLeave事件
    12: required string TaskID,// 会话ID
    13: required string UserID, // 用户ID
    14: optional FeedbackType FeedbackType,
    15: optional NodeStatus NodeStatus, // 节点状态，依附于nodeLeave事件
    16: optional i64 EventTime, // 事件发生时间
    17: optional string DataExtra, //数据埋点需求，extra垃圾桶
    18: optional StartProcessType StartProcessType, //拉起方式，只针对主流程流程打事件有效
    19: optional ChannelType ChannelType,// 接入系统， IM工作台，国际化工单，
    20: optional string ObjectId,// 实体id
    21: optional string ProcessGroupID,// 主流程定义组id
    22: optional list<Info> CommitInfos, // 提交的Data
    23: optional i64 EventTimestamp // 事件发生事件的时间戳
}

enum NodeStatus {
    SUCCESS = 0,
    FAIL = 1,
    ONGOING = 2,
    EMPTYERR = 3,
    CONFIGERR = 4,
}

enum FeedbackType {
    OptimizeProcess = 1, // 优化流程
    OptimizeWords = 2, // 优化话术
    Other = 3, //其他
}

enum EventType {
    ProcessLike    = 1, // 流程点赞
    ProcessDisLike = 2, // 流程点踩
    NodeDisLike    = 3, // 节点点踩
    ProcessOpen    = 4, // 流程打开
    ProcessEnd     = 5, // 流程关闭
    NodeEnter      = 6, // 节点进入
    NodeLeave      = 7, //节点离开
    TaskClose      = 8, //会话关闭
    ContainerClose = 9, //发送关闭通话事件
}

struct Info {
    1: string Key, // 全局唯一的Key
    2: string AliasKey, // 别名
    3: string Value, // value
    4: optional string InfoName, //info名字
}

enum StartProcessType {
    SystemOpen = 1, // 系统推荐人工打开
    ManualOpenByCategory = 2, // 人工打开 -- 通过问题分类
    ManualOpenByFeature = 3, // 人工打开 -- 通过特征
    OtherOpen = 0, //其他
}


struct ActionCallbackRequest {
    1: required string ProcessInstanceId  // 流程实例ID 运行时
    2: required string NodeId             // 节点ID  运行时节点唯一标识
    3: required string NodeKey            // 节点Key 配置时节点唯一标识
    4: optional string ConfigJson         // 节点配置的
    5: optional map<string,string> GlobalData  // 流程执行到当前节点的全局变量集合
    255: base.Base Base
}
struct ActionCallbackResponse {
    1: required map<string,string> Data // 接口返回的字段，可被流程后续节点使用
    255:base.BaseResp BaseResp
}