include "../../base.thrift"
namespace java com.bytedance.ies.router_sim_i18n.thrift
namespace go bytedance.ies.kefu.router_sim_i18n.thrift

struct StartSimRequest {
    1: string Name,

    255: optional base.Base Base,
}

struct StartSimResponse {
    1: string Resp,

    255: base.BaseResp BaseResp,
}

struct StartSimBatchRequest {
    1: optional i64 batchId, // 批次id
    2: optional i64 execTime; // 执行时间
    3: optional string eventKey; // 事件key
    4: optional i64 accessPartyId; // 接入方id
    5: optional string scene; // 场景
    6: optional string creator; // 创建人邮箱
    8: optional string version; // 版本，v1直接使用线上流量，v2使用存储流量
    9: optional i32 expire; // 流量采集时间，v1版本使用
    255: optional base.Base Base,
}

struct StartSimBatchResponse {
    1: string Resp,

    255: base.BaseResp BaseResp,
}

enum DiffResult {
    SAME = 0;
    SAME_BOTH_NULL = 1;
    ONE_NULL_DIFF = 2;
    BASE_RESPONSE_DIFF = 3;
    PASS_INDEX_DIFF = 4;
    HAS_PASSED_DIFF = 5;
    RULE_GROUP_LIST_LENGTH_DIFF = 6;
    RULE_GROUP_ID_DIFF = 7;
    COMPUTE_RESULT_FLAG_DIFF = 8;
    RUNING_TIME_DATA_LENGTH_DIFF = 9
    RUNING_TIME_DATA_DIFF = 10
    SHUNT_DIFF = 11;
    DIFF = 12;
    IMPOSSIBLE = 999;
}

struct QuerySimBatchResponse {
    2: optional i64 batchId,
    3: optional i64 execTime;
    4: optional string eventKey;
    5: optional i64 accessPartyId;
    6: optional string scene;
    7: optional i64 creator;
    8: optional i32 statusRow;
    9: optional i32 statusGcp;
    10: optional i32 statusTTP;
    255: base.BaseResp BaseResp,
}

struct TestRequest {
    1: optional bool test;
    255: optional base.Base Base,
}

struct TestResponse {
    1: optional bool test;
    255: base.BaseResp BaseResp,
}

struct FailDetail {
    1: optional string LogId
}

struct DiffDetail {
    1: optional string LogId
    2: optional string DraftLogId
    3: optional string Version
    4: optional string DraftVersion
    6: optional DiffResult diffResult
    7: optional i64 execTime;
    8: optional string ruleName
    9: optional string draftRuleName;
}

struct GetResultByBatchIdRequest {
    1: optional string BatchId
    2: optional map<string, string> ParamExprMap
    255: base.Base Base
}

struct BatchTestResult {
    1: optional i32 SuccessCount
    2: optional i32 FailCount
    3: optional i32 DiffCount
//    4: optional i32 AverageExecutionTime
//    5: optional i32 MaxExecutionTime
    6: optional list<FailDetail> FailList
    7: optional list<DiffDetail> DiffList
}

struct TaskStatusCheckRequest {
    1: optional bool sendLark
}

struct TaskStatusCheckResponse {
   255: base.BaseResp BaseResp
}

struct GetResultByBatchIdResponse {
    1: optional bool IsEnd
    2: optional BatchTestResult BatchTestResult
    6: optional string EventKey
    7: optional i32 AccessPartyId
    8: optional string scene
   255: base.BaseResp BaseResp
}

struct GetResultByBatchIdMergeRequest {
    1: optional string BatchId
    255: base.Base Base
}

struct GetResultByBatchIdMergeResponse {
    1: optional map<string, GetResultByBatchIdResponse> merge;
   255: base.BaseResp BaseResp
}

struct TestRuleRequest {
    1: optional map<string, string> ParamExprMap
    2: optional string Params
    255: base.Base Base
}

struct TestRuleResponse {
    1: optional string LogId
    2: optional map<string, string> ParamExprMap
    255: base.BaseResp BaseResp
}

struct GetTestBatchRequest {
    1: optional map<string, string> ParamExprMap
    255: base.Base Base
}
struct GetTestBatchResponse {
    1: optional string BatchId
    2: optional map<string, string> ParamExprMap
     255: base.BaseResp BaseResp
}
struct GetSimBatchRecordRequest {
    1: optional string eventKey
    2: optional i64 accessPartyId
    255: base.Base Base
}

struct GetSimBatchRecordResponse {
    1: optional string BatchId
    2: optional string timeRange
    255: base.BaseResp BaseResp
}
struct TerminateAutomatedRuleRequest {
    1: optional string eventKey
    2: optional i64 accessPartyId
    3: optional string batchId
    255: base.Base Base
}

struct TerminateAutomatedRuleResponse {
    255: base.BaseResp BaseResp
}

service RouterSimI18nService {
    StartSimResponse StartSim(1: StartSimRequest req)
    StartSimBatchResponse StartSimBatch(1: StartSimBatchRequest req)
    StartSimBatchResponse StopSimBatch(1: StartSimBatchRequest req)
    GetResultByBatchIdResponse GetResultByBatchId(1: GetResultByBatchIdRequest req)
    TestResponse test(1: TestRequest req)
    TaskStatusCheckResponse TaskStatusCheck(1: TaskStatusCheckRequest req)
    GetResultByBatchIdMergeResponse GetResultByBatchIdMerge(1: GetResultByBatchIdMergeRequest req)
    TestRuleResponse TestRuleRow(1: TestRuleRequest req)
    GetTestBatchResponse openRuleExecuteBatchRow(1: GetTestBatchRequest req);
    GetResultByBatchIdResponse QuerySimRecordRow(1: GetResultByBatchIdRequest req)
    GetSimBatchRecordResponse GetSimBatchRecord(1: GetSimBatchRecordRequest req)
    TerminateAutomatedRuleResponse TerminateAutomatedRule(1: TerminateAutomatedRuleRequest req)
}
