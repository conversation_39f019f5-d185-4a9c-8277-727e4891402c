include "../../base.thrift"
namespace java com.bytedance.agentskillgroup.thrift
namespace go bytedance.ies.kefu.agentskillgroup

enum IdcType {
    ROW = 1,
    TTP = 2,
    GCP = 3,
}
//操作渠道
enum ChannelType {
    IM = 1, //IM
    TICKET = 2, //工单
    PHONE = 3, //电话
    ADMIN = 4, //管理员
    ECOM_TICKET = 5, //电商工单
    BUZZ = 6, //BUZZ工单
    FEEDBACK = 7, // FEEDBACK
    QUALITY_CHECK = 8, // 质检
    IM_OFFLINE_SESSION = 9,//IM离线留言会话
    ACCESS_CALL = 10//触达外呼
    CALLBACK = 11 // 预约回呼
    AFTER_SALE = 12 // 售后

    TT_IM = 101
    TT_TICKET = 102   //TT 工单
    TTLS_TICKET = 103 // TTLS 工单
    TT_CALLBACK = 104 // TT预约回呼
    TT_AFTER_SALE = 105 // TT售后
}
//复制技能组
enum SkillGroupCopyType {
    SKILL_GROUP_IM = 1, //chat skill group
    SKILL_GROUP_TICKET = 2, //ticket skill group
    SKILL_GROUP_AOP = 12, //aop skill group
}
//复制agent最大任务值
enum AgentMaxNum{
    ImMaxTaskNum = 1, //chat Capacity
    TicketMaxTaskNum = 2, //ticket Capacity
    AfterSaleMaxTaskNum = 3, //AOP Capacity
}

enum WorkType {
    FULL_TIME = 1,
    OUTSOURCING = 2,
    TRAINEE = 3,
    THIRD_PARTY = 4, // 第三方派遣
}

enum Status {
    OFF = 0,
    ON = 1,
}

//卡片操作类型
enum HandleType {
    OPEN = 1,
    CLOSE = 2,
    DELETE = 3,
}

enum IncreaseType {
    ADD = 1,
    MINUS = 2,
}

enum TagType{
    FEEDBACK_CHANNEL = 1; //离线技能组渠道
}

enum TagCode{
    FEEDBACK_CHANNEL_EMAIL = 1;
    FEEDBACK_CHANNEL_INAPP = 2;
}

enum ReturnPartialType {
    ALL = 0,                // Return all fields
    BASIC_WITH_ACCESS = 1,  // Return id, name, tenantId, ChannelType, accessPartyId fields
    BASIC_ONLY = 2,        // Return id, name, tenantId fields
    BASIC_WITH_LEADERS = 3, // Return id, name, tenantId, ChannelType, accessPartyId, LeaderIds fields
}

// 工区
struct SiteLocation {
    1: optional i64 ID
    2: optional string Name
    3: optional list<SiteLocation> ChildSiteLocations
}

// 直属上级
struct ReportingLeader {
    1: optional i64 ID
    2: optional string Email
    3: optional string UserName
}
struct BusinessLine{
    1: optional string LineOfBusiness,//一级选项
    2: optional string AgentTag,//二级选项
}

struct Agent {
    1: i64 ID,
    2: i64 TenantId,
    3: i32 WorkType,
    4: string UserName,
    5: string NickName,
    6: i64 UserId,
    7: string UUID,
    8: string Email,
    9: string Mobile,
    10: i64 CompanyId,
    11: list<ChannelType> ChannelTypes,
    12: i32 Status,
    13: i64 CreatedBy,
    14: string CreatedAt,
    15: i64 UpdatedBy,
    16: string UpdatedAt,
    17: string OperatorName,
    18: i64 DepartmentId,
    19: i32 ImMaxTaskNum,
    20: optional i64 PhoneSeatNo,
    21: optional string CountryRegion, // 国家地区
    22: optional i32 FeedbackMaxTaskNum,
    23: optional string NgccServiceLine,
    24: optional i32 QualityCheckMaxTaskNum,

    // 中间的序列号不能用
    31: optional i64 DgTenantId,  //门神租户id
    40: optional i32 TicketMaxTaskNum, //工单最大接待量
    41: optional IdcType IdcType; //机房类型
    42: optional list<i32> Tags,  // 标签列表
    43: optional list<string> OperatingCountries,  // 客服经营国家/地区
    44: optional i32 TtImMaxTaskNum,    // TT IM 最大接线量
    45: optional SiteLocation SiteLocation
    46: optional ReportingLeader ReportingLeader
    47: optional i32 afterSaleMaxTaskNum, // 售后最大接线量
    48: optional list<BusinessLine> BusinessLines,//所属业务线
    49: optional string Organization,//所属组织
    50: optional MemberType memberType, //客服在组中角色
    51: optional string primarySkillGroupName, //主技能组名称
    52: optional i64 primarySkillGroupId, //主技能组ID
    53: optional list<AgentType> agentTypes,
    54: optional string systemLanguage,
    55: optional string readingLanguage,
    56: optional i32 autoTranslation,
    57: optional i64 assignmentSetId,
    58: optional string assignmentSetName,
    59: optional i64 onboardingDate,
    60: optional i64 offboardingDate,
    61: optional i32 attritionType,
    62: optional i32 tenureDays,
    63: optional i32 TtLsTicketMaxTaskNum,
    64: optional i64 evaluationSetId,


    255: map<string, string> Extra, //目前extra里包含的字段 c_n_agent_appid(IM appId),
                                    // c_s_ecom_ticket_role(电商工单系统角色),
                                    // c_n_ecom_ticket_is_super(电商工单是否超级管理员)
                                    // c_s_ecom_ticket_kind(电商工单业务类型)
    }

    struct AgentBaseInfo {
            1: i64 ID,
            2: i64 TenantId,
            3: i32 WorkType,
            4: string UserName,
            5: string NickName,
            6: i64 UserId,
            7: string UUID,
            8: string Email,
            9: string Mobile,
            10: i64 CompanyId,
            11: list<ChannelType> ChannelTypes,
            12: i32 Status,
            13: i64 CreatedBy,
            14: string CreatedAt,
            15: i64 UpdatedBy,
            16: string UpdatedAt,
            17: i64 DepartmentId,
            18: optional string CountryRegion, // 国家地区

            19: optional IdcType IdcType; //机房类型
            20: optional list<AgentType> agentTypes,
            21: optional string systemLanguage,
            22: optional string readingLanguage,
            23: optional i32 autoTranslation,

        }

struct CreateAgentRequest {
    1: required i64 TenantId,
    2: optional i32 WorkType,
    3: optional string UserName,
    4: optional string NickName,
    5: optional i64 UserId,
    6: optional string UUID,
    7: required string Email,
    8: optional string Mobile,
    9: optional list<ChannelType> ChannelTypes,
    10: required i64 OperatorAgentId,
    11: optional i64 CompanyId,
    12: optional map<string, string> Extra,
    13: optional i64 Id,
    14: optional i64 DepartmentId,
    15: optional i32 ImMaxTaskNum,
    16: optional i64 PhoneSeatNo,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FeedbackMaxTaskNum,
    19: optional string NgccServiceLine,
    20: optional i32 QualityCheckMaxTaskNum,

    40: optional i32 TicketMaxTaskNum,
    41: optional IdcType IdcType; //机房类型
    42: optional list<i32> Tags,  // 标签列表
    43: optional list<string> OperatingCountries,  // 客服经营国家/地区
    44: optional i32 TtImMaxTaskNum, // TT IM 最大接线量
    45: optional i64 SiteLocationId,// 工区Id
    46: optional i64 ReportingLeaderId,// 直接上级Id
    47: optional i32 afterSaleMaxTaskNum, // 售后最大接线量
    48: optional list<BusinessLine> BusinessLines,//所属业务线
    49: optional list<AgentType> agentTypes,
    50: optional string systemLanguage,
    51: optional string readingLanguage,
    52: optional i32 autoTranslation,
    53: optional i64 onboardingDate,
    54: optional i64 offboardingDate,
    55: optional i32 attritionType,
    56: optional i64 assignmentSetId,
    57: optional i32 TtLsTicketMaxTaskNum,
    58: optional i64 evaluationSetId,
    59: optional i64 primarySkillGroupId,

    255: optional base.Base Base,
}

//agent type
enum AgentType {
    INVALID = 0, //invalid
    BDEE_SUPPORT = 1, //bdee_support
}

struct CreateAgentResponse {
    1: i64 ID,
    2: optional string email,

    255: base.BaseResp BaseResp,
}

struct BatchCreateAgentRequest {
    1: required list<CreateAgentRequest> CreateAgentRequests,

    255: optional base.Base Base,
}

struct BatchCreateAgentResponse {
    1: list<CreateAgentResponse> CreateAgentResponses,
    2: optional list<CreateAgentResponse> updateAgentResponses,
    3: optional list<CreateAgentResponse> failedAgentResponses,

    255: base.BaseResp BaseResp,
}

enum BatchUpsertType {
    UPDATE = 1,
    CREATE = 2,
}

struct BatchUpsertSkillGroupRequestItem {
    1: required i64 TenantId,
    2: optional list<i64> AccessPartyId,
    3: optional string Name,
    4: optional ChannelType ChannelType,
    5: optional i32 MaxTaskNum,
    6: optional list<i64> QuestionCategoryIds,
    7: optional list<string> TagCodes,
    8: optional i64 OperatorAgentId,
    9: optional map<string, string> Extra,
    10: optional string WorkStartTime,
    11: optional string WorkEndTime,
    12: optional string CountryRegion,
    13: optional i32 FirstHourMaxTaskNum,
    14: optional i32 HourlyMaxTaskNum,
    15: optional i32 DailyMaxTaskNum,
    16: optional i32 MaxFollowUpNum,
    17: optional map<TagType,list<i32>> Tags,
    18: optional IdcType IdcType,
    19: optional bool SupportTransfer,
    20: optional list<i32> SkillTypes,
    21: optional string utc,
    22: optional i32 priority,
    23: optional i32 businessCategory,
    24: optional i32 userCategory,
    25: optional list<i32> customTags,
    26: optional i32 incomingType,
    27: optional list<WeekWorkingTime> weekWorkingTimes,
    28: optional i32 siteCode,
    29: optional i32 serviceType,
    30: optional i32 supportTag,
    31: optional i64 skillGroupId,
    32: optional SkillGroupStatus skillGroupStatus,
    33: optional BatchUpsertType upsertType, // default: update, when it is create, the skillGroupId is for idx to display result, return 0 if not passed
}

struct BatchUpsertSkillGroupRequest {
    1: required list<BatchUpsertSkillGroupRequestItem> batchUpsertSkillGroupRequestItems,

    255: optional base.Base Base,
}

struct BatchUpsertSkillGroupResponse {
    1: optional list<CreateSkillGroupResponse> createSkillGroupResponses,
    2: optional list<CreateSkillGroupResponse> updateSkillGroupResponses, 
    3: optional list<CreateSkillGroupResponse> failedSkillGroupResponses,

    255: base.BaseResp BaseResp,
}

struct BatchDeleteAgentRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 OperatorAgentId,
    4: optional string AccessKey;

    255: optional base.Base Base,
}

struct BatchDeleteAgentResponse {
    255: base.BaseResp BaseResp,
}

struct BatchInsOrUpdtAgentToEsRequest {
    1: required string AccessKey,
    2: required i64 TenantId,

    255: optional base.Base Base,
}

struct BatchInsOrUpdtAgentToEsResponse {
    255: base.BaseResp BaseResp,
}

struct GetLeadersRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,

    255: optional base.Base Base,
}

struct GetLeadersResponse {
    1: required list<i64> LeaderIds,

    255: base.BaseResp BaseResp,
}

struct UpdateAgentRequest {
    1: required i64 Id,
    2: required i64 TenantId,
    3: optional i32 WorkType,
    4: optional string UserName,
    5: optional string NickName,
    6: optional i64 UserId,
    9: optional string Mobile,
    10: optional list<ChannelType> ChannelTypes,
    11: required i64 OperatorAgentId,
    12: optional i64 CompanyId,
    13: optional map<string, string> Extra,
    14: optional i64 DepartmentId,
    15: optional i32 ImMaxTaskNum,
    16: optional i64 PhoneSeatNo,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FeedbackMaxTaskNum,
    19: optional string NgccServiceLine,
    20: optional i32 QualityCheckMaxTaskNum,
    40: optional i32 TicketMaxTaskNum,
    41: optional IdcType IdcType; //机房类型
    42: optional list<i32> Tags,
    43: optional list<string> OperatingCountries,  // 客服经营国家/地区
    44: optional i32 TtImMaxTaskNum, // TT IM 最大接线量
    45: optional i64 SiteLocationId,// 工区Id
    46: optional i64 ReportingLeaderId,// 直接上级Id
    47: optional i32 afterSaleMaxTaskNum, // 售后最大接线量
    48: optional list<BusinessLine> BusinessLines,//所属业务线
    49: optional list<AgentType> agentTypes,
    50: optional string systemLanguage,
    51: optional string readingLanguage,
    52: optional i32 autoTranslation,
    53: optional i64 onboardingDate,
    54: optional i64 offboardingDate,
    55: optional i32 attritionType,
    56: optional i64 assignmentSetId,
    57: optional string email,
    58: optional i32 TtLsTicketMaxTaskNum,
    59: optional i64 evaluationSetId,
    60: optional i64 primarySkillGroupId,

    255: optional base.Base Base,
}

struct UpdateAgentResponse {
    255: base.BaseResp BaseResp,
}

struct AgentUUIDToUpdate{
    1: required string Email,
    2: required string UUID,
}
struct AgentEmailToUpdate{
    1: required string OriginEmail,
    2: required string TargetEmail,
}

struct UpdateAgentAttributeRequest{
    1: required i32 AttributeType,// 0 UUID;1 email
    2: optional list<AgentUUIDToUpdate> AgentUUIDToUpdates,
    3: optional list<AgentEmailToUpdate> AgentEmailToUpdates,
    255: optional base.Base Base,
}
struct UpdateAgentAttributeResponse {
    255: base.BaseResp BaseResp,
}

struct BatchUpdateAgentsProfileRequest{
    1: required list<UpdateAgentRequest> updateAgentsProfile,
    255: optional base.Base Base,
}

struct BatchUpdateAgentsProfileResponse{
    1: optional map<string,base.BaseResp> FailedMsgs,
    255: base.BaseResp BaseResp,
}


struct CopyAgentAttributesRequest{
    1: required i64 TenantId,
    2: required i64 FromAgentId,
    3: required list<i64> ToAgentIds,
    4: optional list<SkillGroupCopyType> SkillGroupCopyTypes,
    5: optional list<AgentMaxNum> AgentMaxNums,
    6: required i64 OperatorAgentId,
    255: optional base.Base Base,
}
struct CopyAgentAttributesResponse{
    255: base.BaseResp BaseResp,
}

struct BatchUpdateAgentRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 OperatorAgentId,
    4: optional i32 ImMaxTaskNum,
    5: optional i32 FeedbackMaxTaskNum,
    6: optional i32 TicketMaxTaskNum,
    7: optional i32 TtImMaxTaskNum,
    8: optional i32 afterSaleMaxTaskNum, // 售后最大接线量
    9: optional i32 QAMaxTaskNum

    255: optional base.Base Base,
}

struct BatchUpdateAgentResponse {
    255: base.BaseResp BaseResp,
}

struct GetAgentDetailRequest {
    1: required i64 TenantId,
    2: required i64 ID,
    3: optional bool timely, //获取实时数据

    255: optional base.Base Base,
}

struct GetAgentDetailResponse {
    1: optional Agent Agent,

    255: base.BaseResp BaseResp,
}

struct GetAgentByUUIDRequest {
    1: required i64 TenantId,
    2: required string UUID,

    255: optional base.Base Base,
}

struct GetAgentByUUIDOrEmailRequest {
    1: required i64 TenantId,
    2: required string UUID,
    3: optional string email,

    255: optional base.Base Base,
}

struct GetAgentByUUIDResponse {
    1: Agent Agent,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByIDsRequest {
    1: required i64 TenantId,
    2: required list<i64> IDs

    255: optional base.Base Base,
}

struct GetAgentListByIDsResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByEmailRequest {
    1: required i64 TenantId,
    2: required list<string> Emails,

    255: optional base.Base Base,
}

struct GetAgentListByEmailResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct EnableAgentRequest {
    1: required i64 TenantId,
    2: required i64 ID,
    3: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct EnableAgentResponse {
    255: base.BaseResp BaseResp,
}

struct DisableAgentRequest {
    1: required i64 TenantId,
    2: required i64 ID,
    3: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DisableAgentResponse {
    255: base.BaseResp BaseResp,
}

struct GetAgentsByConditionRequest {
    1: required i64 TenantId,
    2: optional string Email,
    3: optional string UserName,
    4: optional i32 Status,
    5: required i32 PageNo,
    6: required i32 PageSize,
    7: optional i64 DepartmentId,
    8: optional list<i64> SkillGroupIds,
    9: optional list<i64> DepartmentIds,
    10: optional ChannelType ChannelType,
    11: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName
    12: optional list<i32> Tags,
    13: optional bool CurrentNodeOnly, // 仅显示当前节点下的客服
    14: optional list<i32> IdcTypeValues,// 政策区域，对应idctype
    15: optional list<i64> SiteLocationIds,// 工区id
    16: optional string ReportLeaderEmailOrUsername,// 上级邮箱或name
    17: optional list<BusinessLine> BusinessLines,//所属业务线
    18: optional MemberType memberType,
    19: optional i64 assignmentSetId,
    20: optional i32 workType,
    21: optional string countryRegion, // 国家地区
    22: optional string operatingCountry,  // 客服经营国家/地区
    23: optional i64 companyId,
    24: optional list<string> emails,
    25: optional i32 attritionType,
    26: optional list<i64> primarySkillGroupIds,
    27: optional i64 evaluationSetId,

    255: optional base.Base Base,
}

struct GetAgentsByConditionResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct SearchAgentRequest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: optional string UserName,
    4: optional string Email,
    5: optional i64 PhoneSeatNo,
    6: required i32 PageNo,
    7: required i32 PageSize,
    8: optional i32 Status,

    254: optional map<string,string> extra;

    255: optional base.Base Base,
}

struct SearchAgentResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetAgentsByPermissionRequest {
    1: required i64 OperatorAgentId,
    2: required ChannelType ChannelType,
    3: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAgentsByPermissionResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetAgentsByOperateTimeRequest {
    1: required i64 OperateTime;

    255: optional base.Base Base,
}

struct GetAgentsByOperateTimeResponse {
    1: list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

enum SkillGroupStatus{
    ENABLE=1,
    DISABLE=2,
}

//技能组
struct SkillGroup {
    1: i64 ID,
    2: i64 TenantId,
    3: list<i64> AccessPartyId,
    4: string Name,
    5: ChannelType ChannelType,
    6: i32 MaxTaskNum,
    7: list<i64> QuestionCategoryIds,
    8: list<string> TagCodes,
    9: i64 CreatedBy,
    10: string CreatedAt,
    11: i64 UpdatedBy,
    12: string UpdatedAt,
    13: string OperatorName,
    14: i32 MemberNum;
    15: i32 LeaderNum;
    16: i32 SkillGroupLevel,
    17: optional string WorkStartTime,
    18: optional string WorkEndTime,
    19: optional string CountryRegion, // 国家地区
    20: optional i32 FirstHourMaxTaskNum,
    21: optional i32 HourlyMaxTaskNum,
    22: optional i32 DailyMaxTaskNum,
    23: optional i32 MaxFollowUpNum,

    30: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp  value: TagCode
    31: optional bool AutoAssign, // 是否为自动分单技能组
    32: optional list<i64> LeaderIds,
    33: optional IdcType IdcType; //机房类型
    34: optional bool SupportTransfer; //是否支持人工转交
    35: optional SkillGroupStatus SkillGroupStatus; //技能组状态
    36: optional list<i32> SkillTypes, // 技能类型
    37: optional string utc,// 时区
    38: optional i32 priority,// 优先级
    39: optional SupportTag supportTag, //标签
    40: optional i32 primaryNum; // primary member number
    41: optional i32 businessCategory,
    42: optional i32 userCategory,
    43: optional list<i32> customTags,
    44: optional i32 incomingType,
    45: optional list<WeekWorkingTime> weekWorkingTimes,
    46: optional string businessCategoryName, // return when businessCategory returns
    47: optional string userCategoryName, // return when userCategory returns
    48: optional list<string> customTagsName, // return when customTags returns, order same as customTags
    49: optional string incomingTypeName, // return when incomingType returns
    50: optional map<i32, string> customTagNameMap, // return when customTags returns
    51: optional i32 siteCode,
    52: optional i32 serviceType,

    255: map<string, string> Extra,
}
//In the skill group, we will have a new property called "Automatic Tagging"
  //Switch: On/Off
  //When switch on, show options:
    //"BDEE Support" -> update tagging to BDEE support when ticket assign to this skill group
    //"USDS Support" -> update tagging to USDS support when ticket assign to this skill group
enum SupportTag{
    INVALID = 0; //invalid
    BDEE_SUPPORT = 1;
    USDS_SUPPORT = 2;
}


struct CreateSkillGroupRequest {
    1: required i64 TenantId,
    2: required list<i64> AccessPartyId,
    3: required string Name,
    4: required ChannelType ChannelType,
    5: required i32 MaxTaskNum,
    6: optional list<i64> QuestionCategoryIds,
    7: required list<string> TagCodes,
    8: required i64 OperatorAgentId,
    9: optional map<string, string> Extra,
    10: optional string WorkStartTime,
    11: optional string WorkEndTime,
    12: optional string CountryRegion, // 国家地区
    13: optional i32 FirstHourMaxTaskNum,
    14: optional i32 HourlyMaxTaskNum,
    15: optional i32 DailyMaxTaskNum,
    16: optional i32 MaxFollowUpNum,
    17: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp value: TagCode
    18: optional IdcType IdcType; //机房类型
    19: optional bool SupportTransfer; //是否支持人工转交
    20: optional list<i32> SkillTypes // 技能类型
    21: optional string utc,// 时区
    22: optional i32 priority,// 优先级
    23: optional i32 businessCategory,
    24: optional i32 userCategory,
    25: optional list<i32> customTags,
    26: optional i32 incomingType,
    27: optional list<WeekWorkingTime> weekWorkingTimes,
    28: optional i32 siteCode,
    29: optional i32 serviceType,
    30: optional i32 supportTag, //bdee or usds  support tag

    255: optional base.Base Base,
}

struct WeekWorkingTime {
    1: optional i32 weekDay, //星期几，1-7 表示 周一至周日
    2: optional string workingStartTime,
    3: optional string workingEndTime,
    4: optional i32 bucket, //表示展示位置。便于把相同时间的天聚合在一起展示
}

struct CreateSkillGroupResponse {
    1: i64 ID,

    255: base.BaseResp BaseResp,
}


struct UpdateSkillGroupRequest {
    1: required i64 Id,
    2: required i64 TenantId,
    3: required list<i64> AccessPartyId,
    5: optional ChannelType ChannelType,
    6: optional i32 MaxTaskNum,
    7: optional list<i64> QuestionCategoryIds,
    9: optional list<string> TagCodes,
    11: optional list<i64> AccessPartyIds,
    13: optional i64 OperatorAgentId,
    14: optional map<string, string> Extra,
    15: optional string WorkStartTime,
    16: optional string WorkEndTime,
    17: optional string CountryRegion, // 国家地区
    18: optional i32 FirstHourMaxTaskNum,
    19: optional i32 HourlyMaxTaskNum,
    20: optional i32 DailyMaxTaskNum,
    21: optional i32 MaxFollowUpNum,
    22: optional map<TagType,list<i32>> Tags; // 技能组标签 离线：email inapp  value: TagCode

    40: optional string Name,
    41: optional IdcType IdcType; //机房类型
    42: optional bool SupportTransfer; //是否支持人工转交
    43: optional SkillGroupStatus SkillGroupStatus; //技能组状态
    44: optional list<i32> SkillTypes // 技能类型
    45: optional string utc,// 时区
    46: optional i32 priority,// 优先级
    47: optional i32 supportTag, //bdee or usds  support tag
    48: optional i32 businessCategory,
    49: optional i32 userCategory,
    50: optional list<i32> customTags,
    51: optional i32 incomingType,
    52: optional list<WeekWorkingTime> weekWorkingTimes,
    53: optional i32 siteCode,
    54: optional i32 serviceType,

    255: optional base.Base Base,
}

struct UpdateSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct DeleteSkillGroupRequest {
    1: required i64 ID,
    2: required i64 TenantId,
    4: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DeleteSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupDetailRequest {
    1: required i64 ID,
    2: required i64 TenantId,
    3: optional bool timely, //获取实时数据

    255: optional base.Base Base,
}

struct GetSkillGroupDetailResponse {
    1: SkillGroup SkillGroup

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByIdsRequest {
    1: required i64 TenantId,
    2: required list<i64> Ids,
    3: optional bool IncludeDeletedData,
    4: optional bool IncludeDisabledData,

    255: optional base.Base Base,
}

struct GetSkillGroupsByIdsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByTypeResquest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: required i32 PageNo,
    4: required i32 PageSize,
    5: optional i64 AccessPartyId,
    6: optional list<ChannelType> ChannelTypes,
    7: optional i32 SkillGroupLevel,
    8: optional string SkillGroupName,
    9: optional bool OnlySimpleData, // 是否只需要返回技能组的简单信息，不包含问题标签等关联数据
    10: optional list<i32> ShieldTypes, // 1 测试技能组

    21: optional bool ContainDisable,//废弃，此字段无效
    22: optional list<SkillGroupStatus> SkillGroupStatus,//技能组状态，默认查ENABLE
    23: optional i64 SkillGroupId,// 技能组id

    25: optional list<string> CountryRegions,// 国家区域列表
    26: optional bool onlyLead, //true-current user is a skill group lead
    27: optional i64 agentId

    28: optional list<i32> incomingTypes,
    29: optional list<i32> businessCategories,
    30: optional list<i32> customTags,
    31: optional list<i64> accessPartyIds,
    32: optional list<string> keywords, //模糊搜索key  names or ids,
    33: optional list<string> sortedFields; //排序字段
    34: optional string sortType, // desc | asc
    35: optional list<i32> idcTypeValues,// 政策区域，对应idctype
    36: optional list<i32> tiers,
    37: optional list<i32> serviceTypes,
    38: optional list<i32> siteCodes,
    39: optional list<i32> userCategories,

    255: optional base.Base Base,
}

struct GetSkillGroupsByTypeResponse {
    1: list<SkillGroup> SkillGroups,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByCategoryRequest {
    1: required i64 TenantId,
    2: required i64 CategoryId,

    255: optional base.Base Base,
}

struct GetSkillGroupsByCategoryResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupsByCategoryRequest {
    1: required i64 TenantId,
    2: required list<i64> CategoryIds,

    255: optional base.Base Base,
}

struct BatchGetSkillGroupsByCategoryResponse {
    1: map<i64, list<i64>> CategoryGroupMap,

    255: base.BaseResp BaseResp,
}

struct AddAgentToSkillGroupRequest {
    1: required i64 TenantId,
    2: optional list<i64> AgentIds,
    3: optional i64 SkillGroupId,
    4: required i32 IsGroupLeader,
    5: required i64 OperatorAgentId,
    6: optional MemberType memberType,
    7: optional list<string> emails,
    8: optional bool checkOrg, //check org permission, default:faluse
    9: optional list<i64> skillGroupIds,

    255: optional base.Base Base,
}
//成员类型
enum MemberType {
    Invalid = 0, //无效，无需关注
    Primary = 1,
    Secondary = 2,
//    Lead = 3, //无效，暂时未使用
}

struct AddAgentToSkillGroupResponse {
    1: optional ChannelType misChannelType,
    2: optional list<string> MissChannelTypeEmails,
    3: optional list<string> SkillGroupConflictEmails,

    255: base.BaseResp BaseResp,
}

struct DeleteAgentFromSkillGroupRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required i64 SkillGroupId,
    4: required i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct DeleteAgentFromSkillGroupResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupAgentsRequest {
    1: required i64 TenantId,
    2: required i64 SkillGroupId,
    3: optional string AgentName,
    4: optional i32 IsGroupLeader,
    5: required i32 PageNo,
    6: required i32 PageSize,
    7: optional string AgentEmail,
    8: optional i64 AccessPartyId,
    9: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName
    10: optional i32 priority,
    11: optional MemberType memberType,
    12: optional list<string> emails,

    255: optional base.Base Base,
}

struct GetSkillGroupAgentsResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupAgentsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> GroupList, // 技能组ID列表
    4: optional list<i32> WorkStatusList, // 工作状态列表

    255: optional base.Base Base,
}

struct BatchGetSkillGroupAgentsResponse {
    1: map<i64, list<i64>> GroupToAgentListMap,

    255: base.BaseResp BaseResp,
}

struct UpdateGroupAgentsTaskNumRequest {
    1: required i64 SkillGroupId,
    2: required i64 AccessPartyId,
    3: required list<i64> AgentIds,
    4: required i32 MaxTaskNum,
    5: required i64 TenantId,
    6: optional i64 OperatorAgentId,

    255: optional base.Base Base,
}

struct UpdateGroupAgentsTaskNumResponse {
    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByNameRequest {
    1: required i64 TenantId,
    2: required string SkillGroupName,
    3: optional ChannelType ChannelType,
    4: optional i64 AccessPartyId,
    5: optional list<ChannelType> ChannelTypes,

    21: optional bool ContainDisable,//废弃，此字段无效
    22: optional list<SkillGroupStatus> SkillGroupStatus,//技能组状态，默认查ENABLE

    255: optional base.Base Base,
}

struct GetSkillGroupsByNameResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetAllSkillGroupsRequest {
    1: required i64 TenantId,
    2: optional list<i32> ShieldTypes,  // 1 测试技能组 2管理组 3 session特殊屏蔽组
    3: optional bool onlyLead, //true-current user is a skill group lead
    4: optional i64 agentId,
    5: optional ReturnPartialType returnPartial, // default ALL

    255: optional base.Base Base,
}

struct GetAllSkillGroupsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetAllWorkingSkillGroupsRequest {
    1: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAllWorkingSkillGroupsResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAgentIdRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: optional i64 AccessPartyId; #新增接入方筛选
    4: optional ChannelType channelType, // 新增渠道筛选

    255: optional base.Base Base,
}

struct GetSkillGroupsByAgentIdResponse {
    1: list<SkillGroup> SkillGroups,
    2: map<i64, i32> TaskNumMap,  // skillGroupId -> taskNum
    3: map<i64, i32> LeaderMap,  // 新增是否是组长  key:skillGroupId value 1: 组长, 0 :非组长

    255: base.BaseResp BaseResp,
}

struct BatchGetSkillGroupsByAgentIdsRequest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: optional i64 AccessPartyId,
    4: required list<i64> AgentIds,

    255: optional base.Base Base,
}

struct AgentSkillGroups {
    1: required i64 AgentId,
    2: required list<SkillGroup> SkillGroups,
}

struct BatchGetSkillGroupsByAgentIdsResponse {
    1: required list<AgentSkillGroups> AgentSkillGroups;
    2: optional list<SkillGroupRel> skillGroupRels,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartyRequest {
    1: required i64 TenantId,
    2: required i64 AccessPartyId,
    3: optional list<i32> ShieldTypes, // 1 测试技能组

    21: optional bool LimitManualTransfer,//限制可人工转交的技能组，默认false

    255: optional base.Base Base,
}

struct GetSkillGroupsByAccessPartyResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartiesRequest {
    1: required i64 TenantId,
    2: required list<i64> AccessPartyIds,
    3: optional ChannelType ChannelType,
    4: optional ReturnPartialType returnPartial, // default ALL

    21: optional bool LimitManualTransfer, //限制可人工转交的技能组，默认false
    22: optional list<i32> idcTypeValues,// 政策区域，对应idctype

    255: optional base.Base Base,
}

struct GetSkillGroupsByAccessPartiesResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

struct GetSkillGroupsByAgentAccessPartyRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required i64 AccessPartyId,
    4: optional list<i32> ShieldTypes, // 1 测试技能组

    255: optional base.Base Base,
}

struct GetSkillGroupsByAgentAccessPartyResponse {
    1: list<SkillGroup> SkillGroups,

    255: base.BaseResp BaseResp,
}

//公司
struct AgentCompany {
    1: i64 Id,
    2: i64 TenantId,
    3: string Name,
    4: i32 IsBytedance,
    5: i64 DgTenantId,
    6: string DeletedAt,
    7: i64 CretedBy;
    8: string CreatedAt,
    9: i64 UpdatedBy,
    10: string UpdatedAt,
}

struct GetAllCompanyRequest {
    255: optional base.Base Base,
}

struct GetAllCompanyResponse {
    1: list<AgentCompany> AgentCompanys,

    255: base.BaseResp BaseResp,
}

struct SkillGroupTag {
    1: i64 TenantId,
    2: i64 Id,
    3: string Code,
    4: string DispalyName,
    5: string CreatedAt,
    6: string UpdatedAt,
    7: string OperatrAgentName,
}

struct GetAllSkillGroupTagsRequest {
    1: required i64 TenantId,

    255: optional base.Base Base,
}

struct GetAllSkillGroupTagsResponse {
    1: list<SkillGroupTag> SkillGroupTags

    255: base.BaseResp BaseResp,
}


/*
* 问题卡片相关部分
* */
struct CardQuestionThrift {
    1: required i64 Id,                 //问题ID
    2: required i64 CardId,             //卡片ID
    3: required string QuestionName,    //问题内容
    4: required i64 SkillGroupId,       //技能组ID
    5: required string FullName,        //卡片全称
}

struct AppQuestionCardThrift {
    1: required i64 Id,                 //卡片ID
    2: required i64 TenantId,           //租户ID
    3: required i64 AccessPartyId,      //接入方ID
    4: required i64 AppId,              //应用ID
    5: required string CardName,        //引导话术，用于title展示
    6: required i32 IsOpen,             //是否启用
    7: list<CardQuestionThrift> CardQuestions,//问题列表，list顺序代表C端展示顺序
    8: optional string CardDisplayName, //卡片展示名称 - 新版必传 向下兼容
}

struct UpdateAppQuestionCardThrift {
    1: required i64 Id,                 //卡片ID
    2: required i64 TenantId,           //租户ID
    3: required i64 AccessPartyId,      //接入方ID
    4: required i64 AppId,              //应用ID
    5: required string CardName,        //引导话术，用于title展示
    6: list<UpdateCardQuestion> UpdateCardQuestions,//问题列表，list顺序代表C端展示顺序
    7: optional string CardDisplayName, //卡片展示名称 新版必传 向下兼容
}

struct AddCardQuestion {
    1: required string QuestionName,    //问题内容
    2: required i64 SkillGroupId,       //技能组ID
}

struct UpdateCardQuestion {
    1: optional i64 Id,                 //问题ID
    2: required string QuestionName,    //问题内容
    3: required i64 SkillGroupId,       //技能组ID
}

struct AddAppQuestionCard {
    1: required i64 TenantId,           //租户ID
    2: required i64 AccessPartyId,      //接入方ID
    3: required i64 AppId,              //应用ID
    4: required string CardName,        //引导语，用于title展示
    5: list<AddCardQuestion> AddCardQuestions,//问题列表，list顺序代表C端展示顺序
    6: optional string CardDisplayName, //卡片展示名称 - 新版必传 向下兼容
}

struct GetCardRequest {
    1: required i64 TenantId,           //租户ID
    2: required list<i64> AppIds,      //应用ID列表
    3: optional i64 AccessPartyId,      //接入方ID
    255: optional base.Base Base,
}

struct GetCardResponse {
    1: list<AppQuestionCardThrift> AppQuestionCards,  //问题卡片列表

    255: base.BaseResp BaseResp,
}

struct GetQuestionByCardIdRequest {
    1: required i64 Id,      //卡片id

    255: optional base.Base Base,
}

struct GetQuestionByCardIdResponse {
    1: optional list<CardQuestionThrift> CardQuestions,  //问题卡片

    255: base.BaseResp BaseResp,
}

struct CreateCardRequest {
    1: required AddAppQuestionCard AddAppQuestionCard,    //不带主键ID的问题卡片对象
    2: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct UpdateCardRequest {
    1: required UpdateAppQuestionCardThrift UpdateAppQuestionCardThrift,    //带有主键ID的问题卡片对象
    2: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct HandleCardResponse {
    1: optional i64 Id, // 卡片id
    255: base.BaseResp BaseResp,
}

struct HandleCardRequest {
    1: required i64 CardId,             //卡片ID
    2: required HandleType HandleType,  //操作类型
    3: required i64 AgentId,            //操作人ID

    255: optional base.Base Base,
}

struct Department {
    1: i64 ID,
    2: string Name,
    3: i64 ParentId,
    4: i32 AgentNum,
    5: list<Department> ChildrenDepartments,
    6: i32 Level,
    7: list<i64> LeaderIds,
    8: optional i32 CurrentAgentNum,
    9: string operatingCountry,  // Country Region Code
    10: string baseCountry,      // Base Country Code
    11: i32 baseCity,            // Base City Id
    12: list<string> supportLanguage,  // Support Language Code
    13: string bpoCode,          // Code of BPO
}

struct CreateDepartmentRequest {
    1: required i64 TenantId,
    2: required string Name,
    3: required i64 ParentId,
    4: required i64 OperateAgentId,
    5: optional list<i64> LeaderIds,
    6: optional string operatingCountry,  // Country Region Code
    7: optional string baseCountry,      // Base Country Code
    8: optional i32 baseCity,            // Base City Id
    9: optional list<string> supportLanguage,    // Support Language Code
    10: optional string bpoCode,          // Code of BPO

    255: optional base.Base Base,
}

struct CreateDepartmentResponse {
    1: i64 ID,

    255: base.BaseResp BaseResp,
}

struct UpdateDepartmentRequest {
    1: required i64 TenantId,
    2: required i64 DepartmentId,
    3: optional i64 ParentId,
    4: optional string Name,
    5: required i64 OperateAgentId,
    6: optional list<i64> LeaderIds,
    7: optional string operatingCountry,  // Country Region Code
    8: optional string baseCountry,      // Base Country Code
    9: optional i32 baseCity,            // Base City Id
    10: optional list<string> supportLanguage,    // Support Language Code
    11: optional string bpoCode,          // Code of BPO


    255: optional base.Base Base,
}

struct UpdateDepartmentResponse {
    255: base.BaseResp BaseResp,
}

struct DeleteDepartmentRequest {
    1: required i64 TenantId,
    2: required i64 DepartmentId,
    3: required i64 OperateAgentId,

    255: optional base.Base Base,
}

struct DeleteDepartmentResponse {
    255: base.BaseResp BaseResp,
}

struct GetDepartmentResponse {
    1: optional Department Department,
    255: base.BaseResp BaseResp,
}

struct GetDepartmentRequest {
    1: required i64 Id,
    2: required i64 tenantId,
    255: optional base.Base Base,
}

struct GetDepartmentsRequest {
    1: required i64 TenantId,
    2: optional i32 AgentStatus,
    3: optional string AgentName,
    4: optional string AgentEmail,
    5: optional ChannelType ChannelType,
    6: optional list<i32> Tags,

    14: optional list<i32> IdcTypeValues,// 政策区域，对应idctype
    15: optional list<i64> SiteLocationIds,// 工区id
    16: optional string ReportLeaderEmailOrUsername,// 上级邮箱或name
    17: optional list<BusinessLine> BusinessLines,//业务线
    18: optional string departmantName,

    255: optional base.Base Base,
}

struct GetDepartmentsResponse {
    1: Department rootDepartmentModel,

    255: base.BaseResp BaseResp,
}

struct GetDepartmentsByNameRequest {
    1: required i64 TenantId,
    2: required string DepartmantName,

    255: optional base.Base Base,
}

struct GetDepartmentsByNameResponse {
    1: list<Department> Departments,

    255: base.BaseResp BaseResp,
}

struct FixDepartLevelRequest {
    1: required i64 TenantId,
    255: optional base.Base Base,
}

struct FixDepartLevelResposne {
    255: base.BaseResp BaseResp,
}

struct GetWorkingAgentListRequest {
    1: required i64 TenantId,
    2: required i32 ChannelType,
    255: optional base.Base Base,
}

struct GetWorkingAgentListResponse {
    1: list<i64> AgentList,
    255: base.BaseResp BaseResp,
}

struct GetFirstLoginTimeByAgentListRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> AgentList, // 人员ID列表

    /**
     *所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetFirstLoginTimeByAgentListResponse {
    1: map<i64, string> AgentToFirstLoginTimeMap,

    /**
     * 所有渠道统一工作状态清除结果
     */
    20: optional map<i64, string> UnifiedWorkStatusAgentToFirstLoginTimeMap,
    255: base.BaseResp BaseResp,
}

struct CleanFirstLoginTimeCacheRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required list<i64> AgentList, // 人员ID列表

    /**
     *所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct CleanFirstLoginTimeCacheResponse {
    1: map<i64, bool> CleanResult,

    /**
     * 所有渠道统一工作状态清除结果
     */
    20: optional i64 UnifiedWorkStatus,
    255: base.BaseResp BaseResp,
}

struct GetWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,

    /**
     *所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetWorkStatusResponse {
    1: WorkStatusEnum WorkStatusDetail,
    2: bool IsExist,
    3: i64 StartTime, // 切换到这个状态的时间

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusDurationRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: required i64 StartTime,//开始时间的时间戳（秒）
    5: optional i64 EndTime,//不传默认当前时间（秒）

    /**
     * 所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetWorkStatusDurationResponse {
    1: map<i32,i64>  WorkStatusDuration,//key-工作状态值 , value-该状态持续秒数
    2: optional bool UsingUnifiedWorkStatus,

    255: base.BaseResp BaseResp,
}

struct BatchGetWorkStatusRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required ChannelType ChannelType,

    /**
     * 所有渠道统一工作状态
     */
    4: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct BatchGetWorkStatusResponse {
    1: map<i64, i32> WorkStatusMap,
    2: optional map<i64, string> NotesMap,
    3: optional map<i64, i32> TaskNumMap,

    /**
     * 所有渠道统一工作状态
     */
    20: optional map<i64, i32> UnifiedWorkStatusMap,
    21: optional bool UsingUnifiedWorkStatus,
    255: base.BaseResp BaseResp,
}

// 工作状态详情
struct WorkStatusEnum {
    1: i64 TenantId, // 租户ID
    2: i32 WorkStatus, // 工作状态值
    3: string WorkStatusDesc, // 工作状态描述
    4: string WorkStatusTip, // 工作状态提示
    5: bool Enabled, // 是否启用
    6: i32 ReceptionStatus, // 接线状态（0：不可接线；1: 可接线）
    7: i32 ChannelType, // 渠道类型（1：IM；2：工单；3：电话；4：其他；5：电商工单）
    8: bool IsDefault, // 是否为禁用/启用后默认
    9: string CreatedAt, // 创建时间
    10: bool IsLoginDefault, // 是否为登入后默认
    11: bool IsLogoutDefault, // 是否为登出后默认
    12: bool EnableConfig; // 是否可被配置（启用禁用）
    13: optional string NGCCStatus; // 对应的ngcc的status
    14: optional bool NgccEnable; // ngcc 电话外呼组件是否禁用
    15: optional string WorkStatusKey; //staring key

    /**
    * 所有渠道统一工作状态
    */
    20: optional i64 UnifiedWorkStatus,
    21: optional bool UsingUnifiedWorkStatus,
    22: optional i32 SwitchType;// 0-客服,1-系统
}

struct LoginRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AgentId,

    /**
     *所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct LoginResponse {
    1: WorkStatusEnum WorkStatusDetail, // 登入后的工作状态

    255: base.BaseResp BaseResp,
}

struct LogoutRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AgentId,

    /**
     *所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct LogoutResponse {
    1: WorkStatusEnum WorkStatusDetail, // 登出后的工作状态

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusDetailRequest {
    1: required i64 TenantId,
    2: required i32 WorkStatus,
    3: required ChannelType ChannelType,

    /**
     * 所有渠道统一工作状态
     */
    10: optional i64 UnifiedWorkStatus,
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetWorkStatusDetailResponse {
    1: WorkStatusEnum WorkStatusDetail,

    255: base.BaseResp BaseResp,
}

struct GetWorkStatusOptionsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: optional i64 AgentId,
    4: optional list<i64> AccessPartyIds,
    5: optional bool IsEnabled,

    /**
     * 所有渠道统一工作状态
     */
    11: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetWorkStatusOptionsResponse {
    1: list<WorkStatusEnum> WorkStatusOptions,
    /**
     * 所有渠道统一工作状态
     */
    2: optional bool UsingUnifiedWorkStatus, //如果为true则用新枚举，false用老枚举

    255: base.BaseResp BaseResp,
}


struct WorkStatusConfig {
    1: i64 TenantId, // 租户ID
    2: ChannelType ChannelType, // 坐席类型  废弃
    3: i64 AccessPartyId, // 接入方id
    4: i32 WorkStatus, // 工作状态值
    5: string WorkStatusDesc, // 工作状态描述
    6: bool Enabled, // 是否启用
    7: i32 ReceptionStatus, // 接线状态（0：不可接线；1: 可接线）
    8: i64 UpdaterAgentId,  //更新人id
    9: string updaterAgentName, //更新人名字
    10: string UpdatedAt,  // 更新时间
    11: optional string NGCCStatus, // 对应的ngcc的status
    12: optional bool NgccEnable, // 对应的ngcc电话外呼组件是否启用
    13: optional bool EnableConfig, // 是否可被配置（启用禁用）
    14: optional i32 SwitchType, // 0-客服切换, 1-系统切换
    15: optional string WorkStatusKey; //staring key

    /**
    * 所有渠道统一工作状态
    */
    20: optional i64 UnifiedWorkStatus
}

struct GetWorkStatusConfigsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AccessPartyId,

    21: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct GetWorkStatusConfigsResponse {
    1: list<WorkStatusConfig> WorkStatusConfigs,

    255: base.BaseResp BaseResp,
}

struct UpdateWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: required i32 WorkStatus, // 新的工作状态值
    5: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1 默认为AgentId
    6: optional i64 AccessPartyId;//切换动作所在接入方
    7: optional string Note;// 状态切换备注
    8: optional bool SwitchOfflineIgnoreDownstreamCheck; // 切离线, 但是忽略下游的检查

    /**
     * 所有渠道统一工作状态
     */
    20: optional i64 UnifiedWorkStatus,
    21: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct UpdateWorkStatusResponse {
    1: bool Updated, // 是否已更新

    255: base.BaseResp BaseResp,
}

struct BatchUpdateWorkStatusRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: required ChannelType ChannelType,
    4: required i32 WorkStatus, // 新的工作状态值
    5: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1  默认为AgentId
    6: optional i64 AccessPartyId;//切换动作所在接入方

    /**
     * 所有渠道统一工作状态
     */
    20: optional i64 UnifiedWorkStatus,
    21: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct BatchUpdateWorkStatusResponse {
    255: base.BaseResp BaseResp,
}

struct UpdateWorkStatusConfigRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AccessPartyId,
    4: required i32 WorkStatus,
    5: required bool Enable,
    6: required i64 OperatorAgentId,
    7: optional bool NgccEnable,  // ngcc 外呼组件是否启用，true 为启用

    /**
     * 所有渠道统一工作状态
     */
    10: optional i64 UnifiedWorkStatus,
    11: optional bool SupportUnifiedWorkStatus,  // 0: 不支持 1:支持
    255: optional base.Base Base,
}

struct UpdateWorkStatusConfigResponse {
    255: base.BaseResp BaseResp,
}

struct CreateAgentTemplateRequest {
    1: required i64 TenantId,
    2: required string AccessKey;

    255: optional base.Base Base,
}

struct CreateAgentTemplateResponse {
    1: string FileURL;

    255: base.BaseResp BaseResp,
}

struct FixMissingEndTimeRequest {
    1: required i64 TenantId,
    2: required string AccessKey,
    3: optional string UntilDateTime,

    255: optional base.Base Base,
}

struct FixMissingEndTimeResponse {
    1: list<i64> FailLogIdList,
    2: i32 SuccessCount,
    3: i32 FailCount,
    4: i32 TotalCount,

    255: base.BaseResp BaseResp,
}

struct IncreaseTaskNumRequest{
    1: required i64 SkillGroupId,
    2: required IncreaseType IncreaseType,
    3: required i64 TenantId,
    4: optional i64 OperatorAgentId,
    5: optional i32 IncreaseNum,

    255: optional base.Base Base,
}

struct IncreaseTaskNumResponse{
    255: base.BaseResp BaseResp,
}

struct CheckAgentInSkillGroupRequest{
    1: required i64 TenantId
    2: required i64 AgentId;
    3: required i64 SkillGroupId;

    255: optional base.Base Base,
}

struct CheckAgentInSkillGroupResponse{
    1: bool InSkillGroup = false; //是否在技能组
    2: bool IsLeader = false; //是否是组长

    255: base.BaseResp BaseResp,
}

struct BatchMoveAgentsSkillGroupsRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,
    3: optional list<i64> RemoveSkillGroupIds,
    4: optional list<i64> AddToSkillGroupIds,
    5: optional list<i64> FinalSkillGroupIds,
    6: required i64 OperatorAgentId;
    7: optional ChannelType ChannelType;
    8: optional i32 IsGroupLeader,
    9: optional bool SupportGroupLeader = false,//是否修改isGroupLeader
    10: optional MemberType memberType, //客服在组中角色

    255: optional base.Base Base,
}

struct BatchMoveAgentsSkillGroupsResponse {
    255: base.BaseResp BaseResp,
}

struct CheckSkillGroupAutoAssignRequest {
    1: required i64 TenantId,
    2: required i64 SkillGroupId,

    255: optional base.Base Base,
}

struct CheckSkillGroupAutoAssignResponse {
    1: bool AutoAssign,
    2: list<Agent> UnautoAssignAgents,
    3: i64 SkillGroupAgentsCount,

    255: base.BaseResp BaseResp,
}

struct GetAgentListByUserIdsRequest {
    1: required i64 TenantId,
    2: required list<i64> UserIds

    255: optional base.Base Base,
}

struct GetAgentListByUserIdsResponse {
    1: optional list<Agent> Agents,

    255: base.BaseResp BaseResp,
}

struct UpdateTTPWorkStatusRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: optional i32 CurrWorkStatus, // 原工作状态，提供原子切换能力。可选项
    5: required i32 WorkStatus, // 新的工作状态值
    6: optional i64 OperatorAgentId; //操作者 区分系统和人  系统 -1 默认为AgentId
    7: optional i64 AccessPartyId;//切换动作所在接入方
    8: optional string Note;// 状态切换备注

    /**
     * 所有渠道统一工作状态
     */
    20: optional i64 CurrentUnifiedWorkStatus,//当前状态
    21: optional i64 NewUnifiedWorkStatus,//需要更新的状态
    22: optional bool SupportUnifiedWorkStatus,
    255: optional base.Base Base,
}

struct UpdateTTPWorkStatusResponse {
    255: base.BaseResp BaseResp,
}

struct WorkStatusSyncRequest {
    1: required list<ChannelType> ChannelTypes,

    255: optional base.Base Base,
}

struct WorkStatusSyncResponse {
    255: base.BaseResp BaseResp,
}




struct GetWorkStatusByCacheRequest {
    1: required i64 TenantId,
    2: required i64 AgentId,
    3: required ChannelType ChannelType,
    4: optional bool SupportUnifiedWorkStatus,

    255: optional base.Base Base,
}

struct GetWorkStatusByCacheResponse {
    1: optional i32 WorkStatus,
    2: optional i64 UnifiedWorkStatus,
    3: optional bool UsingUnifiedWorkStatus,

    255: base.BaseResp BaseResp,
}

struct InitUnifiedAgentWorkStatusRequest {
    1: required i64 TenantId,
    2: required list<i64> AgentIds,

    255: optional base.Base Base,
}

struct InitUnifiedAgentWorkStatusResponse {

    255: base.BaseResp BaseResp,
}

struct UnifiedAgentWorkStatusLog {
    1: optional i64 Id
    2: optional i64 TenantId
    3: optional i64 AgentId
    4: optional i32 WorkStatus
    5: optional string StartTime
    6: optional string CreatedAt
    7: optional string UpdatedAt
    8: optional i64 OperatorAgentId
    9: optional string Note
}

struct GetUnifiedAgentWorkStatusLogRequest {
    1: required i64 TenantId
    2: required i64 AgentId
    3: required string StartTime
    4: optional string EndTime
    5: required string AccessKey

    255: optional base.Base Base
}

struct GetUnifiedAgentWorkStatusLogResponse {
    1: optional list<UnifiedAgentWorkStatusLog> Logs

    255: base.BaseResp BaseResp
}

 struct GetSiteLocationListRequest {
     1: required i64 TenantId

     255: optional base.Base Base
 }

 struct GetSiteLocationListResponse {
    1: optional list<SiteLocation> SiteLocations

    255: base.BaseResp BaseResp
 }

 struct ExportField {
     1: required i64 ID
     2: optional string EnName
     3: optional string CnName
     4: optional i32 Orders
 }

 struct GetExportFieldReuqest {
     1: required i64 TenantId
     2: required i64 AgentId
     3: optional i64 AccessPartyId

     255: optional base.Base Base
 }

 struct GetExportFieldResponse {
     1: optional list<ExportField> ExportFields

     255: base.BaseResp BaseResp
 }

struct ExportAgentDataRequest {
    1: required i64 TenantId
    2: required i64 AgentId
    3: optional i64 AccessPartyId
    4: optional string Language // "EN"、"CN"
    5: optional list<i64> ExportFieldIds
    6: optional list<i64> ExportAgentIds // 列表为空时，导出当前用户节点及其子孙节点的成员

    255: optional base.Base Base
}

 struct ExportAgentDataResponse {

     255: base.BaseResp BaseResp
 }

 struct GetSkillGroupsByAgentIdV2Req {
     1: required i64 tenantId,
     2: required i64 agentId,
     3: optional list<string> skillGroupFileds, // 需要查询的字段，不传默认只返回技能组id，参考SkillGroupRel
     4: optional bool needSkillGroupBase, // 需要技能组基础字段
     5: optional list<string> skillGroupBaseFileds, // 需要查询的字段，不传默认只返回技能组id 参考SkillGroupBaseV2
     6: optional i32 status, // 启用 = 1 ，禁用 = 2， 不传位全部，需要筛选状态的从skillBases里面取数据，skillGroupRels不区分是否禁用

     255: optional base.Base Base
 }

 struct GetSkillGroupsByAgentIdV2Resp {
     1: optional list<SkillGroupRel> skillGroupRels,
     2: optional list<SkillGroupBaseV2> skillBases,

     255: base.BaseResp BaseResp
 }

// 技能组人员关联关系
 struct SkillGroupRel {
    1: required i64 skill_group_id,
    2: optional i64 agent_id,
    3: optional bool is_group_leader,
    4: optional i32 max_task_num, // 人员在技能组上最大对客数
    5: optional i32 lower_limit_max_task_num, // 人员在技能组上的对客数下限
    6: optional i64 created_by,
    7: optional string created_at,
    8: optional i64 updated_by,
    9: optional string updated_at,
    10: optional MemberType memberType, //客服在组中角色
 }

 // 新的技能组返回信息
 struct SkillGroupBaseV2 {
    1: required i64 id,
    2: optional i32 channel_type,
    3: optional string name,
    4: optional i32 max_task_num,
    5: optional i32 skill_group_level,
    6: optional string country_region,
    7: optional string work_start_time,
    8: optional string work_end_time,
    9: optional i64 created_by,
    10: optional string created_at,
    11: optional i64 updated_by,
    12: optional string updated_at,
    13: optional i32 idc_type,
    14: optional bool support_transfer,
    15: optional i32 access_party_id, // 接入方id 需要单独获取
    16: optional string utc,// 时区
 }

 struct AgentV2 {
    1: required i64 id,
    2: optional i64 tenant_id,
    3: optional i64 department_id,
    4: optional i32 work_type,
    5: optional string user_name,
    6: optional string nickname,
    7: optional i64 user_id,
    8: optional string uuid,
    9: optional string email,
    10: optional string mobile,
    11: optional i64 company_id,
    12: optional i32 status,
    13: optional string country_region,
    14: optional i64 leader_agent_id,
    15: optional i64 site_location_id,
    16: optional i64 created_by,
    17: optional string created_at,
    18: optional i64 updated_by,
    19: optional string updated_at,
    20: optional i32 idc_type,
 }

 struct GetSkillGroupAgentsV2Request {
     1: required i64 tenantId,
     2: required i64 skillGroupId,
     3: required i32 pageNo,
     4: required i32 pageSize,
     5: optional list<string> agentFileds, // 需要查询的字段，不传默认只返回客服id，参考AgentV2

     255: optional base.Base Base,
 }

 struct GetSkillGroupAgentsV2Response {
     1: optional list<AgentV2> agents,
     2: optional i32 totalSize,

     255: base.BaseResp BaseResp,
 }

 struct QueryAgentFromEsRequest {
     1: optional i64 tenantId,
     2: optional list<i64> skillGroupIds,// 数量限制200
     3: required i32 pageNo,
     4: required i32 pageSize, // 最大限制500
     5: optional ChannelType channelType,
     6: optional list<i64> agentIds, // 数量限制500
     7: optional string email, // 邮箱精确匹配
     8: optional string keyword,// 邮箱和name模糊查找,支持id的精确匹配
     9: optional list<i64> accessPartyIds,
     10: optional list<string> agentFileds, // 需要查询的字段，不传默认只返回客服id，参考AgentV2

     255: optional base.Base Base,
 }

 struct QueryAgentFromEsResponse {
     1: list<AgentV2> Agents,
     2: i32 TotalSize,

     255: base.BaseResp BaseResp,
 }

 struct GetDepartmentTreeRequest {
     1: required i64 TenantId,

     255: optional base.Base Base,
 }

 struct GetDepartmentTreeResponse {
     1: DepartmentNode rootDepartmentNode,

     255: base.BaseResp BaseResp,
 }

 struct DepartmentNode {
     1: i64 ID,
     2: string Name,
     3: i64 ParentId,
     4: list<DepartmentNode> ChildrenDepartments,
     5: i32 Level,
 }

 struct GetLineOfBusinessRequest {
      1: required i64 tenantId,

      255: optional base.Base Base,
  }

  struct GetLineOfBusinessResponse {
      1: list<BusinessLine> BusinessLines,
      2: optional list<string> lineOfBusinesses,
      3: optional list<string> agentTags,
      4: optional list<AgentTagItem> agentTagItems,
      5: optional list<BusinessLineItem> businessLineItems,

      255: base.BaseResp BaseResp,
  }

   struct BusinessLineItem{
       1: optional i32 id,
       2: optional string LineOfBusiness,
   }

   struct AgentTagItem {
       1: optional i32 id,
       2: optional string AgentTag,
   }


  struct QueryAgentPrimaryUpdatedLogsRequest {
     1: required list<QueryAgentPrimaryUpdatedLogsParam> params,
     2: required i64 tenantId,
     3: optional i64 skillGroupId,
    }

    struct QueryAgentPrimaryUpdatedLogsParam {
       1: required i64 agentId,
       2: required i64 startTime, //创建时间Start
       3: required i64 endTime, //创建时间End
    }

    struct QueryAgentPrimaryUpdatedLogsResponse {
      1: optional list<AgentUpdatedLogItem> updatedLogs,
      255: base.BaseResp BaseResp,
    }
  struct AgentUpdatedLogItem {
    1: required i64 id,
    2: optional i64 tenantId,
    3: optional i64 agentId,
    4: optional i64 skillGroupId,
    5: optional i64 operatorId, //操作人id
    6: optional i32 updateType, // 1 - update memberType
    7: optional string originalValue, //变更前 值
    8: optional string updateValue, //变更值
    9: optional i64 createAt, //操作时间
  }


  struct UpdateAgentLanguagesRequest {
        1: required i64 tenantId
        2: required i64 agentId
        3: optional string systemLanguage
        4: optional string readingLanguage
        5: optional i32 autoTranslation
        6: optional i64 operatorId //操作人客服id

       255: optional base.Base Base,
  }

  struct UpdateAgentLanguagesResponse {
     255: base.BaseResp BaseResp,
  }

  struct GetAgentBaseDetailRequest {
          1: required i64 TenantId,
          2: required i64 ID,

          255: optional base.Base Base,
      }

  struct GetAgentBaseDetailResponse {
      1: optional AgentBaseInfo agentBaseInfo,

      255: base.BaseResp BaseResp,
  }

    struct GetAgentIdByPhoneSeatNoRequest {
         1: required i64 tenantId,
         2: optional i64 PhoneSeatNo,

         255: optional base.Base Base,
    }


    struct GetAgentIdByPhoneSeatNoResponse {
        1: optional i64 agentId,
        255: base.BaseResp BaseResp,
    }

   struct QueryAgentPrioritiedGroupsRequest {
       1: required i64 tenantId,
       2: required list<i64> agentIds,
       3: optional i32 channel,

       255: optional base.Base Base,
   }

   struct QueryAgentPrioritiedGroupsResponse {
       1: optional map<i64, list<PrioritiedGroupItem>>  prioritiedSkillGroupsMap,

       255: base.BaseResp BaseResp,
   }

   struct PrioritiedGroupItem {
       1: optional i64 skillGroupId,
       2: optional i32 score,
       3: optional i32 channel,
   }

   struct QueryAgentPrimaryGroupRequest {
       1: required i64 tenantId,
       2: required i64 agentId,

       255: optional base.Base Base,
   }

   struct QueryAgentPrimaryGroupResponse {
       1: optional i64 primaryGroupId,

       255: base.BaseResp BaseResp,
   }

   struct CreateAssignmentSetRequest {
       1: required string name,
       2: optional list<i64> skillGroupIds,
       3: required i64 creator;

       255: optional base.Base Base,
   }

   struct CreateAssignmentSetResponse {

       255: base.BaseResp BaseResp,
   }

   struct SearchAssignmentSetRequest {
       1: required i32 pageNo,
       2: required i32 pageSize,
       3: optional i64 id,
       4: optional string name,
       5: optional i32 status,

       255: optional base.Base Base,
   }

   struct SearchAssignmentSetResponse {
       1: optional list<AssignmentSetItem> assignmentSetItems,
       2: i32 TotalSize,

       255: base.BaseResp BaseResp,
   }

   struct AssignmentSetItem{
       1: optional i64 id,
       2: optional string name,
       3: optional list<i64> skillGroupIds,
       4: optional list<i64> agentIds,
       5: optional i64 creator,
       6: optional string creatorName,
       7: optional i64 createdAt,
       8: optional i64 updator,
       9: optional string updatorName,
       10: optional i64 updatedAt,
       11: optional i32 status,
   }

   struct UpdateAssignmentSetRequest {
       1: required i64 id,
       2: required i64 operator,
       3: optional string name,
       4: optional list<i64> skillGroupIds,
       5: optional AssignmentSetStatus status,

       255: optional base.Base Base,
   }

   enum AssignmentSetStatus {
       ENABLE=1,
       DISABLE=2,
       DELETED=3,
   }

   struct UpdateAssignmentSetResponse {

       255: base.BaseResp BaseResp,
   }

   struct BatchUpdateAgentMultiInfoRequest {
       1: required i64 tenantId,
       2: required list<i64> agentIds,
       3: required i64 operator,
       4: optional i64 assignmentSetId,
       5: optional i64 reportingLeaderAgentId,
       6: optional ChannelType channelType, //更新对应渠道
       7: optional i32 channelCapicity, //渠道对应到容量
       8: optional i64 onboardingDate,
       9: optional i64 offboardingDate,
       10: optional i32 attritionType,
       11: optional string lineOfBusiness,
       12: optional string agentTag,
       13: optional i32 status, //客服状态：1-启用，0-禁用
       14: optional i64 evaluationSetId,
       15: optional i64 primarySkillGroupId,

       255: optional base.Base Base,
   }

   struct BatchUpdateAgentMultiInfoResponse {

       255: base.BaseResp BaseResp,
   }

   struct SearchAssignmentSetGroupsRequest {
       1: required i64 assignmentSetId,

       255: optional base.Base Base,
   }

   struct SearchAssignmentSetGroupsResponse {
       1: optional list<SkillGroup> skillGroup,

       255: base.BaseResp BaseResp,
   }

   struct CheckAgentsOrgRelationRequest {
     1: optional i64 sourceAgentId,
     2: optional list<i64> targetAgentIds,

     255: optional base.Base Base,
   }

   struct CheckAgentsOrgRelationResponse {
     1: optional list<CheckAgentsOrgRelationItem> relationItems,

     255: base.BaseResp BaseResp,
   }
   struct CheckAgentsOrgRelationItem {
     1: optional i64 sourceAgentId,
     2: optional i64 targetAgentId,
     3: optional bool existRelation, //true:存在关联关系，false：不存在关联关系
     4: optional i32 relation, // 1: agent1 是 agent2父级；0：组织相等；-1：agent1 是 agent2 子级

     255: base.BaseResp BaseResp,
   }

   struct ExportGroupAgentsDataRequest {
       1: required i64 tenantId
       2: required i64 agentId
       3: required list<i64> exportGroupIds
       4: optional string Language // "EN"、"CN"
       5: optional list<i64> exportFieldIds


       255: optional base.Base Base
   }

    struct ExportGroupAgentsDataResponse {

        255: base.BaseResp BaseResp
    }
    
    enum ExportGroupDataRequestType {
         MEMBER = 1,
         SKILL_GROUP = 2,
    }

     struct ExportGroupDataRequest {
        1: required i64 tenantId
        2: required i64 agentId
        3: required list<i64> exportGroupIds
        4: optional string Language // "EN"、"CN"
        5: optional ExportGroupDataRequestType requestType // 1: export member, 2: export skill group, default: 1


        255: optional base.Base Base
    }

    struct ExportGroupDataResponse {

        255: base.BaseResp BaseResp
    }

    struct CheckGroupWorkingHourRequest {
        1: required i64 skillGroupId,
        2: optional i64 timestamp, //时间戳；如果不传，则判断当前时间

         255: optional base.Base Base,
     }

     struct CheckGroupWorkingHourResponse {
         1: optional i64 skillGroupId,
         2: optional bool ifInWorkingHour, //是否在工作时间，true: 工作时间， false:非工作时间

         255: base.BaseResp BaseResp,
     }
      struct BatchCheckGroupWorkingHourRequest {
          1: optional list<i64> skillGroupIds,
          2: optional i64 timestamp, //时间戳；如果不传，则判断当前时间

          255: optional base.Base Base,
       }


       struct BatchCheckGroupWorkingHourResponse {
           1: optional list<GroupInWorkingHour> groupInWorkingHours,

           255: base.BaseResp BaseResp,
       }

       struct GroupInWorkingHour {
         1: optional i64 skillGroupId,
         2: optional bool ifInWorkingHour, //是否在工作时间，true: 工作时间， false:非工作时间
      }

     struct MigrateHistoricalDataRequest {
       1: required i32 dataType,

       255: optional base.Base Base,
     }

     struct MigrateHistoricalDataResponse {
       255: base.BaseResp BaseResp,
     }

    struct QueryUpdateRecordRequest {
         1: optional QueryUpdateRecordType queryType,
         2: optional i64 agentId,
         3: optional i64 groupId,
         4: optional string agentIdOrEmail,
         5: optional ChannelType channelType,
         6: optional i64 startTimeMs,
         7: optional i64 endTimeMs,
         8: required i32 PageNo,
         9: required i32 PageSize,
         10: optional list<string> sortedFields; //预留字段：排序字段，无需传入，默认是update_time
         11: optional string sortType, // desc | asc

         255: optional base.Base Base,
      }

      //复制技能组
      enum QueryUpdateRecordType {
          AGENT_BASE = 1,
          SKILL_GROUP_BASE = 2,
          AGENT_CAPACITY = 3,
          SERVING_TASK_NUM = 4,
          AGENT_GROUP_REL = 5,
      }

       struct QueryUpdateRecordResponse {
           1: optional list<UpdateRecordItem> updateRecordItems,
           2: required i32 PageNo,
           3: required i32 PageSize,
           4: optional i32 totalSize,
           5: optional QueryUpdateRecordType queryType,

           255: base.BaseResp BaseResp,
       }

    struct UpdateRecordItem {
          1: optional i64 id,
          2: optional i64 baseId, //agentId or groupId
          3: optional string fieldName,
          4: optional string beforeValue,
          5: optional string afterValue,
          6: optional i64 operatorId,
          7: optional string operatorName,
          8: optional string operatorEmail,
          9: optional i64 operateTimeMs, //操作时间

          //以下为agentChannel需要字段
          10: optional i64 agentId,
          11: optional string agentName,
          12: optional string agentEmail,
          13: optional i64 groupId,
          14: optional string groupName,
          15: optional i32 channelType,
          16: optional string channelTypeName,

          //agent and group rel 需要字段
          17: optional i32 updateType,
          18: optional string updateTypeDesc,
      }

service AgentSkillGroupService {
    // 创建组织架构节点
    CreateDepartmentResponse CreateDepartment(1: CreateDepartmentRequest req)

    // 删除组织架构节点
    DeleteDepartmentResponse DeleteDepartment(1: DeleteDepartmentRequest req)

    // 更新组织架构节点
    UpdateDepartmentResponse UpdateDepartment(1: UpdateDepartmentRequest req)

    // 获取组织架构树
    GetDepartmentsResponse GetDepartments(1: GetDepartmentsRequest req)

    // 获取组织架构树，只包含组织架构树信息
    GetDepartmentTreeResponse GetDepartmentTree(1: GetDepartmentTreeRequest req)

    // 获取部门信息，目前不返回子部门和客服数信息
    GetDepartmentResponse GetDepartment(1: GetDepartmentRequest req)

    // 根据组织架构名称模糊匹配
    GetDepartmentsByNameResponse GetDepartmentsByName(1: GetDepartmentsByNameRequest req)

    // 初始化和修复department中的level字段
    FixDepartLevelResposne FixDepartLevel(1: FixDepartLevelRequest req)

    // 创建agent
    CreateAgentResponse CreateAgent(1: CreateAgentRequest req)

    // 批量创建agent
    BatchCreateAgentResponse BatchCreateAgent(1: BatchCreateAgentRequest req)

    // 批量删除agent
    BatchDeleteAgentResponse BatchDeleteAgent(1: BatchDeleteAgentRequest req)

    // 批量插入和更新人员信息到ES中
    BatchInsOrUpdtAgentToEsResponse BatchInsOrUpdtAgentToEs(1: BatchInsOrUpdtAgentToEsRequest req)

    // 获取人员的leader
    GetLeadersResponse GetLeaders(1: GetLeadersRequest req)

    // 更新agent
    UpdateAgentResponse UpdateAgent(1: UpdateAgentRequest req)

    UpdateAgentAttributeResponse UpdateAgentAttribute(1:UpdateAgentAttributeRequest req)

    //批量更新agent
    BatchUpdateAgentsProfileResponse BatchUpdateAgentsProfile(1: BatchUpdateAgentsProfileRequest req)

    //复制agent的技能组和任务值
    CopyAgentAttributesResponse CopyAgentAttributes(1:CopyAgentAttributesRequest req)

    // 批量更新agent的任务数
    BatchUpdateAgentResponse BatchUpdateAgent(1: BatchUpdateAgentRequest req)

    // 获取id获取agent详情
    GetAgentDetailResponse GetAgentDetail(1: GetAgentDetailRequest req) (api.api_level = '0')

    // 根据UUID获取agent详情
    GetAgentByUUIDResponse GetAgentByUUID(1: GetAgentByUUIDRequest req) (api.api_level = '0')

    // 根据idList获取agent
    GetAgentListByIDsResponse GetAgentListByIDs(1: GetAgentListByIDsRequest req)

    // 根据emailList获取agent,限制数量最多100
    GetAgentListByEmailResponse GetAgentListByEmails(1: GetAgentListByEmailRequest req)

    // 启用客服
    EnableAgentResponse EnableAgent(1: EnableAgentRequest req)

    // 禁用客服
    DisableAgentResponse DisableAgent(1: DisableAgentRequest req)

    // 根据条件，获取agent列表
    GetAgentsByConditionResponse GetAgentsByCondition(1: GetAgentsByConditionRequest req)

    // 根据条件，从ES中查询人员数据，暂时只对电商工单开放
    SearchAgentResponse SearchAgent(1: SearchAgentRequest req)

    // 根据权限获取人员列表
    GetAgentsByPermissionResponse GetAgentsByPermission(1: GetAgentsByPermissionRequest req)

    // 根据操作时间获取人员列表
    GetAgentsByOperateTimeResponse GetAgentsByOperateTime(1: GetAgentsByOperateTimeRequest req)

    // 创建技能组
    CreateSkillGroupResponse CreateSkillGroup(1: CreateSkillGroupRequest req)

    // 更新技能组
    UpdateSkillGroupResponse UpdateSkillGroup(1: UpdateSkillGroupRequest req)

    // 删除技能组
    DeleteSkillGroupResponse DeleteSkillGroup(1: DeleteSkillGroupRequest req)
    
    // batch upsert skill group
    BatchUpsertSkillGroupResponse BatchUpsertSkillGroup(1: BatchUpsertSkillGroupRequest req)


    // 获取技能组详情
    GetSkillGroupDetailResponse GetSkillGroupDetail(1: GetSkillGroupDetailRequest req) (api.api_level = '0')

    // 根据idList获取技能组列表
    GetSkillGroupsByIdsResponse GetSkillGroupsByIds(1: GetSkillGroupsByIdsRequest req) (api.api_level = '0')

    // 根据类型获取技能组列表
    GetSkillGroupsByTypeResponse GetSkillGroupsByType(1: GetSkillGroupsByTypeResquest req) (api.api_level = '0')

    // 根据标签id获取技能组列表
    GetSkillGroupsByCategoryResponse GetSkillGroupsByCategory(1: GetSkillGroupsByCategoryRequest req) (api.api_level = '0')

    // 批量根据标签id获取技能组列表
    BatchGetSkillGroupsByCategoryResponse BatchGetSkillGroupsByCategory(1: BatchGetSkillGroupsByCategoryRequest req)

    // 技能组添加成员
    AddAgentToSkillGroupResponse AddAgentToSkillGroup(1: AddAgentToSkillGroupRequest req)

    // 技能组删除成员
    DeleteAgentFromSkillGroupResponse DeleteAgentFromSkillGroup(1: DeleteAgentFromSkillGroupRequest req)

    // 获取技能组成员
    GetSkillGroupAgentsResponse GetSkillGroupAgents(1: GetSkillGroupAgentsRequest req) (api.api_level = '0')

    // 批量获取技能组成员
    BatchGetSkillGroupAgentsResponse BatchGetSkillGroupAgents(1: BatchGetSkillGroupAgentsRequest req);

    // 更新技能组下成员的对客数
    UpdateGroupAgentsTaskNumResponse UpdateGroupAgentsTaskNum(1: UpdateGroupAgentsTaskNumRequest req)

    // 根据技能组的名字模糊匹配
    GetSkillGroupsByNameResponse GetSkillGroupByName(1: GetSkillGroupsByNameRequest req)

    //获取全量的技能组
    GetAllSkillGroupsResponse GetAllSkillGroups(1: GetAllSkillGroupsRequest req)

    //获取全量的工组中的技能组
    GetAllWorkingSkillGroupsResponse GetAllWorkingSkillGroups(1: GetAllWorkingSkillGroupsRequest req) (api.api_level = '0')

    //根据客服id获取技能组
    GetSkillGroupsByAgentIdResponse GetSkillGroupsByAgentId(1: GetSkillGroupsByAgentIdRequest req) (api.api_level = '0')

    // 批量根据客服id获取技能组
    BatchGetSkillGroupsByAgentIdsResponse BatchGetSkillGroupsByAgentIds(1: BatchGetSkillGroupsByAgentIdsRequest req)

    //根据接入方获取技能组
    GetSkillGroupsByAccessPartyResponse GetSkillGroupsByAccessParty(1: GetSkillGroupsByAccessPartyRequest req) (api.api_level = '0')

    //根据接入方列表获取技能组
    GetSkillGroupsByAccessPartiesResponse GetSkillGroupsByAccessParties(1: GetSkillGroupsByAccessPartiesRequest req) (api.api_level = '0')

    //根据人员id和接入方，获取技能组
    GetSkillGroupsByAgentAccessPartyResponse GetSkillGroupByAgentAccessParty(1: GetSkillGroupsByAgentAccessPartyRequest req)

    //获取所有公司
    GetAllCompanyResponse GetAllCompany(1: GetAllCompanyRequest req)

    GetAllSkillGroupTagsResponse GetAllSkillGroupTags(1: GetAllSkillGroupTagsRequest req)

    //创建问题卡片
    HandleCardResponse createCard(1: CreateCardRequest req);

    //获取问题卡片
    GetCardResponse getCard(1: GetCardRequest req);

    //获取问题卡片
    GetQuestionByCardIdResponse getQuestionByCardId(1: GetQuestionByCardIdRequest req);

    //更新问题卡片
    HandleCardResponse updateCard(1: UpdateCardRequest req);

    //操作卡片(启用、禁用、删除)
    HandleCardResponse handleCard(1: HandleCardRequest req);

    // 获取可以接单的人员列表
    GetWorkingAgentListResponse GetWorkingAgentList(1: GetWorkingAgentListRequest req) (api.api_level = '0')

    // 获取人员的首次登录时间  废弃！！！！！！！！！
    GetFirstLoginTimeByAgentListResponse GetFirstLoginTimeByAgentList(1: GetFirstLoginTimeByAgentListRequest req);

    // 清除人员首次登录时间的缓存  废弃！！！！！！！！
    CleanFirstLoginTimeCacheResponse CleanFirstLoginTimeCache(1: CleanFirstLoginTimeCacheRequest req);

    // 登入  废弃！！！！！！！！
    LoginResponse Login(1: LoginRequest req) (api.api_level = '0')

    // 登出 废弃！！！！！！！
    LogoutResponse Logout(1: LogoutRequest req);

    // 获取工作状态
    GetWorkStatusResponse GetWorkStatus(1: GetWorkStatusRequest req) (api.api_level = '0')

    // 批量获取工作状态
    BatchGetWorkStatusResponse BatchGetWorkStatus(1: BatchGetWorkStatusRequest req);

    // 获取工作状态的详情  无上游调用
    GetWorkStatusDetailResponse GetWorkStatusDetail(1: GetWorkStatusDetailRequest req);

    // 获取工作状态选项列表
    GetWorkStatusOptionsResponse GetWorkStatusOptions(1: GetWorkStatusOptionsRequest req) (api.api_level = '0')

    // 获取工作状态配置
    GetWorkStatusConfigsResponse GetWorkStatusConfigs(1: GetWorkStatusConfigsRequest req);

    // 更新工作状态
    UpdateWorkStatusResponse UpdateWorkStatus(1: UpdateWorkStatusRequest req) (api.api_level = '0')

    // 批量更新工作状态
    BatchUpdateWorkStatusResponse BatchUpdateWorkStatus(1: BatchUpdateWorkStatusRequest req);

    // 生成最新的人员批量上传模板
    CreateAgentTemplateResponse CreateAgentTemplate(1: CreateAgentTemplateRequest req);

    // 更新工作状态配置
    UpdateWorkStatusConfigResponse UpdateWorkStatusConfig(1: UpdateWorkStatusConfigRequest req);

    // 修复缺失endTime的工作状态日志
    FixMissingEndTimeResponse FixMissingEndTime(1: FixMissingEndTimeRequest req);

    //动态修改人员身上taskNum
    IncreaseTaskNumResponse IncreaseTaskNum(1:required IncreaseTaskNumRequest request);

   //判断客服是否在指定技能组
    CheckAgentInSkillGroupResponse CheckAgentInSkillGroup(1:CheckAgentInSkillGroupRequest req);

    //批量转移人员技能组
    BatchMoveAgentsSkillGroupsResponse BatchMoveAgentsSkillGroups(1: BatchMoveAgentsSkillGroupsRequest req);

    //检查技能组自动分单配置
    CheckSkillGroupAutoAssignResponse CheckSkillGroupAutoAssign(1: CheckSkillGroupAutoAssignRequest req);

    // 工作状态及工作日志同步至多机房同步表
    WorkStatusSyncResponse WorkStatusSync(1: WorkStatusSyncRequest req);

    // 根据userIdList获取agent,限制数据量最多100, 不返回 user_id == 0 的客服
    GetAgentListByUserIdsResponse GetAgentListByUserIds(1: GetAgentListByUserIdsRequest req)

    //供row干预ttp人员状态（ttp内部更新不可用此接口）
    UpdateTTPWorkStatusResponse UpdateTTPWorkStatus(1: UpdateTTPWorkStatusRequest req)

    // 计算指定时间开始到当前时间的各个工作状态持续时长
    GetWorkStatusDurationResponse GetWorkStatusDuration(1: GetWorkStatusDurationRequest req)

    // 获取工作状态（仅状态，缓存获取）
    GetWorkStatusByCacheResponse GetWorkStatusByCache(1: GetWorkStatusByCacheRequest req)
    // 初始化人员统一工作状态 （内部初始化人员状态）
    InitUnifiedAgentWorkStatusResponse InitUnifiedAgentWorkStatus(1: InitUnifiedAgentWorkStatusRequest req)

    // 获取统一工作状态的日志流水
    GetUnifiedAgentWorkStatusLogResponse GetUnifiedAgentWorkStatusLog(1: GetUnifiedAgentWorkStatusLogRequest req)

    // 获取工区列表
    GetSiteLocationListResponse GetSiteLocationList(1: GetSiteLocationListRequest req)

    // 获取可导出的字段列表
    GetExportFieldResponse GetExportField(1: GetExportFieldReuqest req)

    // 导出客服数据
    ExportAgentDataResponse ExportAgentData(1: ExportAgentDataRequest req)

    // 根据AgentId获取技能组信息
    GetSkillGroupsByAgentIdV2Resp GetSkillGroupsByAgentIdV2(1: GetSkillGroupsByAgentIdV2Req req)

    // 获取技能组成员V2
    GetSkillGroupAgentsV2Response GetSkillGroupAgentsV2(1: GetSkillGroupAgentsV2Request req)

    // 从es处查询人员信息
    QueryAgentFromEsResponse queryAgentFromEs(1: QueryAgentFromEsRequest req)

    // 查询所属业务线
    GetLineOfBusinessResponse getLineOfBusiness(1:GetLineOfBusinessRequest req)

    //查询客服日志memberType#Primary变更记录
    QueryAgentPrimaryUpdatedLogsResponse QueryAgentPrimaryUpdatedLogs(1:QueryAgentPrimaryUpdatedLogsRequest req)

    //根据phone_seat_no 查询 agentId
    GetAgentIdByPhoneSeatNoResponse GetAgentIdByPhoneSeatNo(1:GetAgentIdByPhoneSeatNoRequest req)

    // 更新agent languages
    UpdateAgentLanguagesResponse UpdateAgentLanguages(1: UpdateAgentLanguagesRequest req)

    // 获取id获取agent 基础详情
    GetAgentBaseDetailResponse GetAgentBaseDetail(1: GetAgentBaseDetailRequest req)

    //查询客服优先级排序后的技能组列表
        QueryAgentPrioritiedGroupsResponse QueryAgentPrioritiedGroups(1: QueryAgentPrioritiedGroupsRequest req)

    //查询客服Primary技能组
    QueryAgentPrimaryGroupResponse QueryAgentPrimaryGroup(1: QueryAgentPrimaryGroupRequest req)

    //新建AssignmenSet
    CreateAssignmentSetResponse CreateAssignmentSet(1: CreateAssignmentSetRequest req)

    //查询AssignmenSet
    SearchAssignmentSetResponse SearchAssignmentSet(1: SearchAssignmentSetRequest req)

    //更新分配集：enable操作、绑定技能组变更、技能组顺序调整
    UpdateAssignmentSetResponse UpdateAssignmentSet(1: UpdateAssignmentSetRequest req)

    //批量更新客服多项信息
    BatchUpdateAgentMultiInfoResponse BatchUpdateAgentMultiInfo(1: BatchUpdateAgentMultiInfoRequest req)

    //查询分配集中的技能组
    SearchAssignmentSetGroupsResponse SearchAssignmentSetGroups(1: SearchAssignmentSetGroupsRequest req)

    //检查客服组织关联关系
    CheckAgentsOrgRelationResponse CheckAgentsOrgRelation(1: CheckAgentsOrgRelationRequest req)

    // 导出技能组内客服
    ExportGroupAgentsDataResponse ExportGroupAgentsData(1: ExportGroupAgentsDataRequest req)

    // 校验是否是技能组工作时间
    CheckGroupWorkingHourResponse CheckGroupWorkingHour(1: CheckGroupWorkingHourRequest req)

    //迁移历史数据
    MigrateHistoricalDataResponse MigrateHistoricalData(1: MigrateHistoricalDataRequest req)

    // 校验是否是技能组工作时间
    BatchCheckGroupWorkingHourResponse BatchCheckGroupWorkingHour(1: BatchCheckGroupWorkingHourRequest req)

    QueryUpdateRecordResponse QueryUpdateRecord(1: QueryUpdateRecordRequest req)

    GetAgentByUUIDResponse GetAgentByUUIDOrEmail(1: GetAgentByUUIDOrEmailRequest req)
    
    // export group data
    ExportGroupDataResponse ExportGroupData(1: ExportGroupDataRequest req)
}
