include "../base.thrift"
namespace py ies.kefu.datamarket
namespace go ies.kefu.datamarket

enum QueryType{
    QUERY,
    INFO_KEY,
    INFO_KEY_V2
}

struct QueryStatistics {
    1: required string CallerPSM,
    2: required QueryType  QueryType,
    3: optional list<QueryResult> QueryResults,
    4: optional list<string> InfoKeys,
    5: optional string Query,
    6: optional string Response,
    7: optional string Variables,
    8: optional map<string, string> KvErrors,
    9: optional string LogID,
}

struct DataQueryRequest {
    1: required string Psm,
    2: required string Token,
    3: optional string Query,
    4: optional string QueryId,
    5: optional string Variables,
	6: optional UserInfo UserInfo
    7: optional bool IsMustCompleteFieldNotNull
    8: optional string NewQueryID

    // 100以后国际化专用
    100: optional CacheConfig CacheConfig

    255: optional base.Base Base,
}

struct QueryByInfoKeysRequest {
     1: required string Psm, //调用者psm
     2: required list<string> InfoKeys,
     3: required string Variables,
     4: optional UserInfo UserInfo
     5: optional bool IsForceReturnLeftValue
     6: optional bool IsMustCompleteFieldNotNull
     7: optional  ScenarioInfo ScenarioInfo
     8: optional string NewQueryID

     // 100以后国际化专用
     100: optional CacheConfig CacheConfig

     255: optional base.Base Base
}
enum OpType {
    Record   //  查询数据，并记录
    PLAY       //  读DM录制完的数据
}
struct ScenarioInfo  {// 剧本信息
     1: required OpType OpType // 操作标识
     2: required string ScenarioKey //  唯一标识的key
}
struct QueryByInfoKeysV2Request {
     1: required string Psm, //调用者psm
     2: required list<string> InfoKeys,
     3: optional map<string, string> Variables,
     4: optional string SessionId
     5: optional string UserId
     6: optional bool enableCache
     7: optional i32 cacheTtl
     8: optional UserInfo UserInfo
     9: optional bool IsForceReturnLeftValue
     10: optional  ScenarioInfo ScenarioInfo
     11: optional string NewQueryID

     // 100以后国际化专用
     100: optional CacheConfig CacheConfig

     255: optional base.Base Base
}

struct CacheConfig {
    1: optional bool Enabled // 必填，是否开启，默认关闭
    2: optional CacheConfigStrategy Strategy // 选填，缓存策略，默认read_only
    3: optional i64 ExpireSecond // 选填， 缓存过期时间，当Strategy为 read_only时，可以不填，其他必填
    4: optional CacheResource CacheResource // 选填，默认共享资源池
    5: optional list<string> InfoKeys // 选填，要开启 cache的INFOKEY 列表，为空则表示全部
    6: optional bool NeedProfilingInfo // 选填，是否需要返回cache的调试信息
}

struct CacheResource {
    1: optional CacheResourceType Type
    2: optional ABase Abase
    3: optional Redis Redis
}

enum CacheResourceType {
    ABASE = 1
    REDIS = 2
}

enum CacheConfigStrategy{
    WirteIfNotExists = 1
    WirteAfterRead = 2
    ReadOnly = 3
}

struct ABase {
    1: optional string ClusterName
    2: optional string Table
}

struct Redis {
    1: optional string ClusterName
}

// cache的调试信息
struct CacheProfilingInfo {
    1: optional bool IsHit // 如果都没有命中缓存， 则false，否则true
    2: optional i64 HitTimeCostMs // 命中的总耗时
    3: optional i64 MissedTimeCostMs // 未命中的总耗时
    4: optional list<string> HitInfoKeys // 命中的infokey，仅针对仅针对QueryByInfoKeys/QueryByInfoKeysV2 有效
}

struct ScenarioDataRewriteRequest {
     1: optional ScenarioInfo ScenarioInfo
     2: optional map<string,string> InfoKVs
     255: optional base.Base Base
}
struct ScenarioDataRewriteResponse {
    255: required base.BaseResp BaseResp,
}
enum InfoValueSource {
    MOCK
    SESSION
    GQL
    DEFAULT
}

struct QueryByInfoKeysV2Response {
    1: optional list<QueryResult> QueryResults,
    2: optional list<string> Errors,

    // 100 以后国际化专用
    100: optional CacheProfilingInfo CacheProfilingInfo,
    255: required base.BaseResp BaseResp,
}

struct QueryResult {
    1: required string InfoKey
    2: optional string InfoValue
    3: optional InfoValueSource ValueSource
}

struct DataQueryResponse {
    1: optional string Data,
    2: optional list<string> Errors,

    // 100 以后国际化专用
    100: optional CacheProfilingInfo CacheProfilingInfo,
    255: required base.BaseResp BaseResp,
}

struct QueryByInfoKeysResponse {
    1: optional string Data,
    2: optional list<string> Errors,

    // 100 以后国际化专用
    100: optional CacheProfilingInfo CacheProfilingInfo,
    255: required base.BaseResp BaseResp,
}


struct WebQueryRequest {
    1: required string RequestString,
    2: optional string VariablesString,
    3: optional string OperationName,
	4: optional UserInfo UserInfo
    255: optional base.Base Base,
}

struct WebQueryResponse {
    1: optional string Data,
    255: required base.BaseResp BaseResp,
}

struct UserInfo{
	1: optional string UserId,
	2: optional string UserName,
	3: optional string Email,
	4: optional i64 AccessPartyId
	5: optional list<ResourceParams> Resources
	6: optional EncryptionParams EncryptionParams  // 加密入参
	7: optional i32 VersionID // 版本id ，用来控制变量
}


//资源相关属性
struct ResourceParams {
     1: optional i64  PictureResizeWidth, // 缩放宽  不传的话不生效
     2: optional i64  PictureResizeHeight,// 缩放高  不传的话不生效
}
// 新服务加解密相关数据（fieldMask ,传递这个参数请求新夫妇）
struct EncryptionParams {
   1: optional string Platform
   3: optional string OperationPage
   4: optional string IssueNo
}

// 添加Query
struct AddQueryRequest {
    // 查询语句
    1: required string Content (go.tag='validate:\"required\"'),
    // 期望的QueryId，不一定会使用此值作为最终QueryId
    2: optional string ExpectQueryId (go.tag='validate:\"omitempty,max=64\"'),
    // 查询参数示例
    3: optional string VariablesExample,
    // 调用方psm
    4: optional string Psm (go.tag='validate:\"omitempty,max=64\"'),
    // 生效环境，默认正式环境
    5: optional list<string> EnvList,
    // idl schema
    6: optional string SchemaId (go.tag='validate:\"omitempty,max=128\"'),

    255: optional base.Base Base,
}

struct AddQueryResponse {
    // 生成的QueryId
    1: required string QueryId (go.tag='json:\"queryId\"'),

    255: required base.BaseResp BaseResp,
}

// 更新Query
struct UpdateQueryRequest {
    // id
    1: required string QueryId (go.tag='validate:\"required,min=1,max=128\"'),
    // 查询语句
    2: required string Content (go.tag='validate:\"required\"'),
    // 查询参数示例
    3: optional string VariablesExample,
    // 生效环境，默认正式环境
    4: optional list<string> EnvList (go.tag='validate:\"required,unique,min=1\"'),
    // idl schema
    5: optional string SchemaId (go.tag='validate:\"omitempty,max=128\"'),

    255: optional base.Base Base,
}

struct UpdateQueryResponse {
    1: required string Version (go.tag='json:\"version\"'),

    255: required base.BaseResp BaseResp,
}

// 获取Query信息
struct GetQueryInfoRequest {
    // id
    1: required string QueryId (go.tag='validate:\"required,min=1,max=128\"'),
    // 生效环境，默认正式环境
    2: optional string Env (go.tag='validate:\"omitempty,max=128\"'),
    // 是否允许使用缓存
    3: optional bool NotUseCache,

    255: optional base.Base Base,
}

struct GetQueryInfoResponse {
    1: optional QueryInfo QueryInfo (go.tag='json:\"queryInfo,omitempty\"'),

    255: required base.BaseResp BaseResp,
}

// 获取Query列表
struct GetQueryListRequest {
    // 分页Number
    1: required i32 Page (go.tag='validate:\"required,min=1\"'),
    // 分页大小
    2: required i32 Limit (go.tag='validate:\"required,min=1\"'),
    // 指定环境，默认返回全部环境的
    3: optional string Env (go.tag='validate:\"omitempty,max=128\"'),
    // 是否返回内容
    4: optional bool NeedContent,
    // 是否返回参数示例
    5: optional bool NeedVariablesExample,
    // QueryId前缀搜索
    6: optional string QueryIdPrefix (go.tag='validate:\"omitempty,max=128\"'),

    255: optional base.Base Base,
}

struct GetQueryListResponse {
    1: optional list<QueryInfo> QueryList (go.tag='json:\"queryList,omitempty\"'),
    2: optional i32 count,

    255: required base.BaseResp BaseResp,
}

struct GetQueryHistoryListRequest {
    // id
    1: required string QueryId (go.tag='validate:\"required,min=1,max=128\"'),
    // 分页Number
    2: optional i32 Page (go.tag='validate:\"required,min=1\"'),
    // 分页大小
    3: optional i32 Limit (go.tag='validate:\"required,min=1\"'),

    255: optional base.Base Base,
}

struct GetQueryHistoryListResponse {
    1: optional list<QueryInfo> QueryList (go.tag='json:\"queryList,omitempty\"'),
    2: optional i32 count,

    255: required base.BaseResp BaseResp,
}

// 将指定版本生效
struct EnableQueryRequest {
    // id
    1: required string QueryId (go.tag='validate:\"required,min=1,max=128\"'),
    // 版本号
    2: required string Version (go.tag='validate:\"required,min=1\"'),
    // 生效环境，默认正式环境
    3: optional list<string> EnvList (go.tag='validate:\"required,unique,min=1\"'),

    255: optional base.Base Base,
}

struct EnableQueryResponse {
    255: required base.BaseResp BaseResp,
}

struct QueryInfo {
    1: required string QueryId (go.tag='json:\"queryId\"'),
    2: required string Env (go.tag='json:\"env\"'),
    3: required string Version (go.tag='json:\"version\"'),
    4: optional string Content (go.tag='json:\"content\"'),
    5: optional string VariablesExample (go.tag='json:\"variablesExample\"'),
    6: optional i64    CreateAt (go.tag='json:\"createAt\"'),
    7: optional string Psm (go.tag='json:\"psm\"'),
}


struct InfoDetail {
    1: required string  infoKey,
    2: optional string subType;   //用户组件子类型
    3: required i32 isList;   //1是 0否
    4: optional string instruction;   //指令
    5: optional string queryPrefix; //查询前缀
    6: optional string initFormat; //初始入参格式
    7: optional string key; //Action Key
    8: optional string aliasName; //Action Key
}

struct CheckInfoKeysIsValidRequest {
    1: required list<InfoDetail> InfoKeys,
    2: optional string Variables,
    3: optional bool IsOnlyCheckQueryValid  //默认执行
    255: optional base.Base Base,
}

struct CheckInfoKeysIsValidResponse {
       1: optional string Data,
       2: optional list<string> Errors,
       3: required string Query
       255: required base.BaseResp BaseResp,
}

struct GenQueryByInfoKeysRequest {
     1: required list<string> InfoKeys,
     2: optional bool IsNeedParentMap  //是否需要字段的父级信息
     255: optional base.Base Base,
}

struct GenQueryByInfoKeysResponse {
    1:  string Query,
    2:  map<string,string> ParentMap
    255: required base.BaseResp BaseResp,
}


struct GetQueryResolverMapRequest {
   1:   string Query,
   255: optional base.Base Base,
}

struct GetQueryResolverMapResp {
   1: list<string> FieldResolverList
   2: map<string,string> FieldResolverMap
   255: required base.BaseResp BaseResp,
}

enum QueryIDType {
    RealQueryID = 1 // 实际的
    AliasName = 2
}
struct QueryByQueryIDReq {
    1: required QueryIDType QueryIDType,
    2: required string QueryID,
    3: required string variables

    255: optional base.Base Base,
}

struct QueryByQueryIDResp {
     1: optional string Data,
     2: optional list<string> Errors,
     3: required string version ,// 正在使用的版本
     255: required base.BaseResp BaseResp,
}

struct QueryByApaasQueryRequest {
    1: optional QueryDTO QueryInfo;
    2: optional map<string,string> ParamMap;
    255: optional base.Base Base,
}

struct QueryByApaasQueryResponse {
     1: optional string Data;
     255: required base.BaseResp BaseResp,
}

struct QueryDTO {
    4: optional string SpaceId;
    5: optional string ReqConfig;
    7: optional string DataSourceConfig;
    8: optional string RespConfig;
    9: optional string ApaasMetaData;
}

struct APaaSQueryExecuteRequest {
    1: required list<APaaSQuery> QueryList, //查询列表
    2: required string AppCode, //应用场景

    255: optional base.Base Base,
}

struct APaaSQuery {
    1: required string QueryCode, //查询Code
    2: required string SpaceId, //查询空间ID
    3: optional list<string> FieldPath, //字段路径
    4: optional map<string, string> Params, //入参
    5: optional string SceneCode,    // 场景
}

struct APaaSQueryExecuteResponse {
    1: required list<APaaSQueryResult> QueryResults, // 查询结果
    2: optional list<string> Errors, //错误

    255: required base.BaseResp BaseResp,
}

struct APaaSQueryResult {
    1: required string FieldPath,
    2: required string FieldPathValue,
}

enum ResultFormat {
    JsonObj = 1,
    KV = 2,
    JSONStr = 3,
}

enum APaaSQueryType {
    QueryDetail = 1, // 详情查询
    QueryMore   = 2, // 分页查询
    QueryAll    = 3, // 列表查询
}

struct CommonAPaaSQueryRequest {
    1: optional string SpaceId,                    // 查询空间ID
    2: optional list<string> OutputFieldPath,      // 需要返回的字段路径
    3: optional map<string, string> InputMap,      // 入参
    4: optional APaasScene Scene,                  // 废弃字段: 旧场景，传入SpaceId + SceneCode
    5: optional APaaSQueryType QueryType,          // 查询类型
    6: optional ResultFormat ResultFormat,         // 返回数据格式
    7: optional bool IsForceReturnLeftValue,       // 强制返回左值
    8: optional string SceneCode,                  // 新场景，格式spaceCode.sceneCode
    255: optional base.Base Base,
}

struct CommonAPaaSQueryResponse {
    1: optional string Output,                      // JSONObject格式的KV
    2: optional list<string> Errors,
    3: optional list<APaaSQueryResult> OutputResults, // list<KV>格式的KV

    255: optional base.BaseResp BaseResp,
}

struct CommonAPaaSQueryReqItem {
    1: optional list<string> OutputFieldPath,      // 需要返回的字段路径
    2: optional map<string, string> InputMap,      // 入参
    3: optional APaaSQueryType QueryType,          // 查询类型
    4: optional bool IsForceReturnLeftValue,       // 强制返回左值
    5: optional string SceneCode,                  // 场景Code
}

struct CommonApaaSQueryRespItem {
    1: optional CommonAPaaSQueryReqItem OriginReqItem, // 原始请求信息
    2: optional string Output,                      // JSONObject格式的KV
    3: optional list<string> Errors,
    4: optional list<APaaSQueryResult> OutputResults, // list<KV>格式的KV
}

struct BatchCommonAPaaSQueryRequest {
    1: optional list<CommonAPaaSQueryReqItem> QueryList,
    255: optional base.Base Base,
}

struct BatchCommonAPaaSQueryResponse {
    1: optional string QueryResultJson,
    255: optional base.BaseResp BaseResp,
}

struct APaasScene {
    1: required string SpaceId,     // APaas空间ID
    2: required string SceneCode,   // 场景Code
}

struct CommonAPaaSActionRequest {
    1: required string SpaceId,      // 项目空间ID
    2: optional string ActionCode,   // 动作Code
    3: optional string ActionParam,  // 动作参数, JSON格式
    255: optional base.Base Base,
}

struct CommonAPaaSActionResponse {
    1: optional string Data,
    2: optional string Result,
    3: optional string ErrorCode,
    4: optional string ErrorMsg,
    255: optional base.BaseResp BaseResp,
}

struct APaaSActionTestRequest {
    1: optional string SpaceId,
    2: optional string ReqConfig,
    3: optional string DataSourceConfig,
    4: optional string RespConfig,
    5: optional string WorkflowStr,
    6: optional string ApaasMetaData,
    7: optional map<string, string> ParamMap,

    255: optional base.Base Base,
}

struct APaaSActionTestResponse {
    1: optional string Data,
    255: optional base.BaseResp BaseResp,
}

struct APaaSWorkflowTestRequest {
    1: optional string Statemachine,
    2: optional string WorkflowConfig,
    3: optional map<string, string> ParamMap,
    255: optional base.Base Base,
}

struct APaaSWorkflowTestResponse {
    1: required bool Success,
    2: optional string Data,
    3: optional string ErrorMsg,
    255: optional base.BaseResp BaseResp,
}

struct APaaSDynamicEnumExecuteRequest {
    1: required string EnumCode,  // format: spaceCode.EnumCode
    2: optional map<string, string> ParamMap, // 参数Map
    255: base.Base Base,
}

struct APaaSDynamicEnumExecuteResponse {
    1: optional list<DynamicEnumData> Data,
    2: optional string DefaultValue,
    255: base.BaseResp BaseResp,
}

struct DynamicEnumData {
    1: optional string Label,
    2: optional string Value,
    3: optional bool Disable,
    4: optional string DisableToolTip,
    5: optional list<DynamicEnumData> Children,
}

service DataMarketService {
    // Query
    DataQueryResponse           Query               (1: DataQueryRequest            request)
    // 通过queryid 或者别名请求
    QueryByQueryIDResp QueryByQueryID(1:QueryByQueryIDReq req)
    // 通过info keys请求下游
    QueryByInfoKeysResponse     QueryByInfoKeys     (1: QueryByInfoKeysRequest      request)
    // 通过info keys请求下游
    QueryByInfoKeysV2Response  QueryByInfoKeysV2     (1: QueryByInfoKeysV2Request      request)
    // WebQuery
    WebQueryResponse            WebQuery            (1: WebQueryRequest             request)
    // 添加查询语句
    AddQueryResponse            AddQuery            (1: AddQueryRequest              request)
    // 修改查询语句
    UpdateQueryResponse         UpdateQuery         (1: UpdateQueryRequest           request)
    // 通过queryId获取配置
    GetQueryInfoResponse        GetQueryInfo        (1: GetQueryInfoRequest          request)
    // 获取query列表
    GetQueryListResponse        GetQueryList        (1: GetQueryListRequest          request)
    // 使指定版本生效
    EnableQueryResponse         EnableQuery         (1: EnableQueryRequest           request)
    // 获取query保存历史列表
    GetQueryHistoryListResponse GetQueryHistoryList (1: GetQueryHistoryListRequest   request)
    // 批量校验InfoKey是否合法
    CheckInfoKeysIsValidResponse  CheckInfoKeysIsValid  (1: CheckInfoKeysIsValidRequest   request)
    // 根据infoKey生成Query
    GenQueryByInfoKeysResponse  GenQueryByInfoKeys  (1: GenQueryByInfoKeysRequest   request)
    //  获取Query关联的ResolverMap
    GetQueryResolverMapResp GetQueryResolverMap (1: GetQueryResolverMapRequest   request)
    //  练线锦囊设置缓存的infokv
    ScenarioDataRewriteResponse ScenarioDataRewrite (1: ScenarioDataRewriteRequest   request)

    // 通过Apaas完整query查询数据
    QueryByApaasQueryResponse QueryByApaasQuery(1:QueryByApaasQueryRequest request)
    // APaaS Query查询
    APaaSQueryExecuteResponse APaaSQueryExecute(1: APaaSQueryExecuteRequest request)
    // APaaS字段 查询
    CommonAPaaSQueryResponse CommonAPaaSQuery(1: CommonAPaaSQueryRequest request);
    // APaaS 动作执行
	CommonAPaaSActionResponse CommonAPaaSAction(1: CommonAPaaSActionRequest request)
	// APaaS 动作测试
	APaaSActionTestResponse APaaSActionTest(1: APaaSActionTestRequest request)
	// APaaS 流程测试
	APaaSWorkflowTestResponse APaaSWorkflowTest(1: APaaSWorkflowTestRequest request)
    // 批量APaaS查询(for jiuquan)
    BatchCommonAPaaSQueryResponse BatchCommonAPaaSQuery(1: BatchCommonAPaaSQueryRequest request)
    // APaaS 动态枚举查询
    APaaSDynamicEnumExecuteResponse APaaSDynamicEnumExecute(1: APaaSDynamicEnumExecuteRequest request)
}
