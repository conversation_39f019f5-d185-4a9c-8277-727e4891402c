namespace go im_model
namespace py im_model
namespace java com.bytedance.im.message

include "base.thrift"

struct EmojiProperty {
    1: optional string icon_url;
    2: optional string text;
}

struct PropertyValue {
    1: optional string required_min_version;
    2: optional i64 type;
    3: optional EmojiProperty emoji_data;
}

// 消息属性
struct PropertyItem {
    1: optional i64 UserdId;
    2: optional string SecUid;
    3: optional i64 CreateTime ;
    4: optional string IdempotentId; //去重Id,相同Id的Item在PropertyItemList中只保留一份
    5: optional string Value ; // 业务方定制，IMCLOUD不理解内容 eg:表情点赞 value:uid
}

struct PropertyItemList {
    1: optional list<PropertyItem> items;
}


struct MessageBody{
    1: i32 ConversationType
    2: string ConversationId
    3: i64 ConversationShortId
    4: i64 ServerMessageId
    5: i32 MsgType
    6: string Content
    7: map<string,string> Ext
    8: i64 Version
    9: i32 Status
    10: i64 CreateTime
    11: i64 Sender
    12: optional binary Bitmap // Deprecated
    13: optional binary ExtraInfo
    14: optional i32 EncryptedType = 0;
    15: optional i32 appID;
    16: optional string SecSender;
    17: optional map<string,  PropertyItemList> properties;
    18: optional i32 BizAppID;
    19: optional map<string, string> UserProfile;
    20: optional i64 IndexInConversationV2 // 新版本的连续自增的index
    21: optional RefMessageInfo RefMsgInfo
    22: optional i64 IndexInConversationV1 // v1

    23: optional binary ContentPb
    24: optional string Scene
    25: optional ConvRankUpdateRule ConvRankUpdateRule

    26: optional i64 SubConversationShortId // B/C客服会话模型-子会话id
}

// ConvRankUpdate is a enum that would ignore the conversation ranking update on specific user side
enum ConvRankUpdateRule {
    IgnoreSenderRankUpdate = 1;
    IgnoreReceiverRankUpdate = 2;
    IgnoreAllRankUpdate = 3;
}

struct RefMessageInfo{
  // 落库的引用信息,预期落库之后就不会发生变化
  1: i64 ReferencedMessageId
  2: string Hint
  3: i64 ReferencedMessageType

  4: MessageStatus Status //注意该状态不会落库,是实时计算出的
  5: i64 SenderUid // who send the original message referenced
  6: optional i64 RootMessageId // 根节点 id
  7: optional i64 RootMessageConversationIndex // 根节点会话 index
}

// 消息状态
enum MessageStatus {
  AVAILABLE = 0; // 消息存在
  NOT_EXIST = 1; // 消息不存在
  INVISIBLE = 2; // 消息（对用户）不可见
  RECALLED = 3; // 消息被撤回
  DELETED = 4;// 消息本身可见，后因删除不可见
}
enum EncryptedType {
    NO_ENCRYPT = 0;
    INNER_ENCRYPT = 1;
    OUTER_ENCRPYT = 2;
}

struct Message{
	1: i64 IndexInConversation // IndexInConversationV1
	2: MessageBody messageBody
	3: optional i64 IndexInUserInbox // IndexInUserInboxV1
    4: i64 IndexInConversationV2 // 优化后的单链index
    5: i64 IndexInUserV2 // 优化后的混链index
}

enum RetentionStatus {
  NOT_STARTED = 0; // 尚未开始清理用户数据
  IN_PROGRESS = 1; // 用户数据清理进行中
  DONE = 2; // 用户数据已经完成清理
  FAILED = 3; // 用户数据清理失败
}

struct RetentionResult{
    1: string UserdId
    2: string Timestamp // 状态修改时间
    3: RetentionStatus Status
}

enum ConversationType {
	ONE_TO_ONE_CHAT = 1,
	GROUP_CHAT = 2,
	LIVE_CHAT = 3,
	BROADCAST_CHAT = 4
	BC_PARENT_CONVERSATION = 10,
	BC_SUB_CONVERSATION = 11,
}

enum GroupRole {
    ORDINARY = 0, // [群成员]
    OWNER = 1, // [群主]
    MANAGER = 2, // [群管理]
    VISITOR = 3, // [访客]
}

enum ActionType {
    TYPE_UNKNOWN = 0                 // 未知动作
    FAVORITE = 1                //  收藏
    PIN = 2                     // 置顶
    UNREAD = 3                  // 未读
}

// 消息链路trace
struct MsgTrace {
    // 消息链路关键节点打点，MsgTraceMetrics->timestamp(us)
   1: map<i32, i64> metrics;
   2: optional MsgTracePath path;
}

enum MsgTraceMetrics {
    // 消息到达im.gateway_go.frontier/im.http.gateway
    GATEWAY = 0;
    // 准备调用callback
    BEFORE_CALLBACK = 1;
    // callback结束
    AFTER_CALLBACK = 2;
    // 写入会话消息队列前
    BEFORE_CONVERSATION_KAFKA =3;
    // 写入会话消息队列后
    AFTER_CONVERSATION_KAFKA = 4;
    // 写入用户消息队列前
    BEFORE_USER_KAFKA = 5;
    // 写入用户消息队列后
    AFTER_USER_KAFKA  = 6;
    // 到达push服务，发给frontier前
    PUSH = 7;
}

enum MsgTracePath {
    // 0留给未知定义来源
    FROM_UNKNOWN = 0;
    // 来自客户端长连接
    FROM_FRONTIER = 1;
    // 来自客户端短连接
    FROM_HTTP = 2;
    // 来自服务端
    FROM_SERVER = 3;
}

enum ContentReviewCode {
  // 风控结果，类型，建议不要修改
  VISIBLE_BY_SELF = -4001,  // 自见
  PUNISH = -4002,   // 惩罚
  REJECT = -4005,  // 拒绝

  PASS = 0,

  // 挂在去风控的路上，类型
  TYPE_NOT_SUPPORT = 1, // 不支持消息类型
  INVALID_REQUEDT = 2,  // 参数错误，可能是少了，错了
  RPC_CONTENT_REVIEW_ERR = 3, // 请求风控下游错误
  CONTENT_EXTRACT_ERR = 4, // 从 content 中抽取文本错误
  CONTENT_INSERT_ERR = 5 // 把文本放回 content 错误
}


/*
 MuteMessageType {
    1;  at
    2;  群主发言
    3;  直播公告/作品同步
    4;  群主发言 且是 at消息
}
*/

struct MuteBadgeCountInfo {
    1: required i32 MuteMessageType
    2: required i32 BadgeCount
}

struct MuteReadBadgeCountInfo {
    1: required i32 MuteMessageType
    2: required i32 ReadBadgeCount
}
