include "../base.thrift"
namespace go aweme.mcn
namespace py aweme.mcn

enum SearchType {
    Uid = 1,
    McnName = 2,
    MainName = 3,
}

enum OperateType {
    MCN = 1,
    Platform = 2,
}

enum RefuseType {
    MCNOvertime = 1,  // mcn处理超时
    AppealOvertime = 2, // 申诉超时
    AuditOvertime = 3  // 审核超时
}

enum InviteStatusEnum {
    NOT_FOUND      = -1 // 未邀请
    INVITING       = 10 // 邀约中
    INVITE_ACCEPT  = 11 // 已签约
    INVITE_REJECT  = 12 // 邀请拒绝
    DELETED        = 13 // 机构将用户移除
}

enum RescissionStatusEnum {
    NOT_FOUND      = -1 // 未申请
    RESCISSION_PROCESSING  = 0 // 处理中
    RESCISSION_ACCEPT  = 1 // 解绑通过
    RESCISSION_REJECT  = 2 // 解绑拒绝
    RESCISSION_AUTO_REJECT  = 3 // 解绑超时未申诉拒绝
    RESCISSION_AUTO_AUDIT_REJECT = 4 // 解绑超时未审核拒绝
}

enum UserRescissionStatusEnum {
    APPLICABLE = 1 // 可申请
    MCN_PROCESSING = 2 // 机构确认中
    MCN_ACCEPT = 3 // 机构通过
    MCN_REJECT = 4 // 机构拒绝
    PLATFORM_PROCESSING = 5 // 平台确认中
    PLATFORM_ACCEPT = 6 // 平台通过
    PLATFORM_REJECT = 7 // 平台拒绝
    PLATFORM_APPEAL_REJECT = 8 // 超时未申诉拒绝
}

struct MCN {
    1: required i64 McnId,  // 机构id
    2: optional string McnName,  // 机构名称
    3: optional string MainName,  // 主体名称
    4: optional string McnAvatar, // 主体头像
    5: optional string Operator, // 操作人员
    6: optional i64 UserId, // 机构抖音uid
    7: optional i32 McnStatus, // MCN 状态 - 0：初始值 1：已入驻 2：已失效 （达人人数小于五） 3：已删除
    8: optional i32 ReviewStatus, // MCN 审核状态 - 0：审核中 1：已通过 2：未通过
    9: optional bool IsBan  // MCN 机构是否被封禁 - true 封禁中 false 未封禁
    10: optional i64 SigningTime,  // 绑定时间
    11: optional i64 ApplyTime, // 机构申请时间
}

struct MCNPrivate {
    1: required i64 McnId,  // 机构id
    2: optional string McnName,  // 机构名称
    3: optional string MainName,  // 主体名称
    4: optional string McnAvatar, // 主体头像
    5: optional string Operator, // 操作人员
    6: optional i64 UserId, // 机构抖音uid
    7: optional i32 McnStatus, // MCN 状态 - 0：初始值 1：已入驻 2：已失效 （达人人数小于五） 3：已删除
    8: optional i32 ReviewStatus, // MCN 审核状态 - 0：审核中 1：已通过 2：未通过
    9: optional bool IsBan  // MCN 机构是否被封禁 - true 封禁中 false 未封禁
    10: optional i64 SigningTime,  // 绑定时间
    11: optional i64 ApplyTime, // 机构申请时间
    12: optional string UnifiedSocialCreditCode, // 企业信用码
    13: optional string License, // 营业执照
    14: optional string Location, // 机构所在地
    15: optional string OperatorPhone,
    16: optional string Wechat,
}

struct MCNLite {
    1: optional string McnName,  // 机构名称
    2: optional string MainName,  // 主体名称
    3: optional string McnAvatar, // 主体头像
    4: optional string Operator, // 操作人员
    5: optional i32 McnStatus, // MCN 状态 - 0：初始值 1：已入驻 2：已失效 （达人人数小于五） 3：已删除
    6: optional i32 ReviewStatus, // MCN 审核状态 - 0：审核中 1：已通过 2：未通过
    7: optional bool IsBan  // MCN 机构是否被封禁 - true 封禁中 false 未封禁
}

struct GetTalentListForStarRequest {
    1: required i64 UserId, // MCN机构注册人使用的抖音uid
    2: required i64 Cursor,
    3: required i16 Count,
    255: required base.Base Base,
}

struct TalentDataStruct {
    1: required i64 UserId,
    2: required i64 CreateTime,
}

struct GetTalentListForStarResponse {
    1: required list<TalentDataStruct> TalentList,
    2: optional i32 Total,
    255: required base.BaseResp BaseResp,
}

struct GetMCNInfoByUserIdRequest {
    1: required i64 UserId,
    255: required base.Base Base,
}

struct GetMCNInfoByUserIdResponse {
    1: required i64 McnId,  // 机构id
    2: required string McnName,  // 机构名称
    3: required string MainName,  // 主体名称
    4: required i32 McnStatus, // MCN 状态 - 0：初始值 1：已入驻 2：已失效 （达人人数小于五） 3：已删除
    5: required i32 ReviewStatus, // MCN 审核状态 - 0：审核中 1：已通过 2：未通过
    6: required bool IsBan  // MCN 机构是否被封禁 - true 封禁中 false 未封禁
    7: required i64 SigningTime,  // 绑定时间
    8: optional i64 McnOwnerUserId,  // 机构绑定登陆uid
    9: optional string UnifiedSocialCreditCode,  // 机构社会信用代码
    255: required base.BaseResp BaseResp,
}

struct MGetMcnInfoByIdRequest {
    1: required list<i64> McnIds,
    255: required base.Base Base,
}

struct MGetMcnInfoByIdResponse {
    1: required map<i64, MCN> McnList,
    255: required base.BaseResp BaseResp,
}

struct McnPrivateSearchResponse {
    1: required list<MCNPrivate> McnList,
    255: required base.BaseResp BaseResp,
}

struct McnSearchRequest {
    1: required string Keyword,
    2: optional SearchType Type,
    255: required base.Base Base,
}

struct McnSearchResponse {
    1: required list<MCN> McnList,
    255: required base.BaseResp BaseResp,
}

struct Rescission {
    1: required i64 McnId,  // 机构id
    2: required i64 UserId, // 抖音uid

    3: optional i64 CreateTime,  // 创建时间
    4: optional i64 UpdateTime,  // 更新时间
    5: optional RescissionStatusEnum McnStatus, // MCN操作状态 - 0：MCN确认中 1：MCN通过 2：MCN拒绝 3：超时拒绝
    6: optional RescissionStatusEnum PlatformStatus, // 平台操作状态，只有在McnStatus是2的情况下，才会进入平台review阶段 - -1：默认值 0：平台确认中 1：平台通过 2：平台拒绝 3：超时拒绝

    7: optional string ApplyReason, // 申请解绑理由
    8: optional string McnRefuseReason, // MCN拒绝理由
    9: optional string PlatformRefuseReason,  // 平台拒绝理由

    10: optional string Extra, // 包括上传的资料地址

    11: optional string Nickname, // 抖音昵称
    12: optional string UniqueId, // 抖音号

    13: optional string AppealReason, // 申诉理由
    14: optional i64 FollowerCount, // 粉丝数
}

struct MCNRescission {
    1: required MCNLite McnInfo,  // 机构信息
    2: optional Rescission RescissionInfo, // 解绑申请信息
    3: required UserRescissionStatusEnum Status // 达人申请状态
}

struct CreateRescissionRequest {
    1: required i64 UserId, // 用户id
    2: required string Reason, // 申请理由
    3: optional string Files, // 解绑材料
    4: optional string FileNames, // 文件名称
    255: required base.Base Base,
}

struct CreateRescissionResponse {
    255: required base.BaseResp BaseResp,
}

struct GetRescissionByUserIdRequest {
    1: required i64 UserId, // 用户id
    255: required base.Base Base,
}

struct GetRescissionByUserIdResponse {
    1: optional MCNRescission Rescission,
    255: required base.BaseResp BaseResp,
}

struct GetRescissionByMCNUserIdRequest {
    1: required i64 UserId, // 用户id
    2: required i64 Cursor,
    3: required i16 Count,
    4: optional string Nickname, // 达人名称
    5: optional string UniqueId, // 抖音号
    6: optional i16 Status, // 处理状态
    255: required base.Base Base,
}

struct GetRescissionByMCNUserIdResponse {
    1: optional list<Rescission> RescissionList,
    2: optional i32 Total,
    3: optional i64 Cursor,
    255: required base.BaseResp BaseResp,
}

struct AcceptRescissionRequest {
    1: required i64 UserId, // 用户id
    2: required OperateType OperateType, // 操作平台 1: mcn 2: platform
    3: required i64 McnId, // 机构id
    4: optional string Operator,
    255: required base.Base Base,
}

struct AcceptRescissionResponse {
    255: required base.BaseResp BaseResp,
}

struct RefuseRescissionRequest {
    1: required i64 UserId, // 用户id
    2: required OperateType OperateType, // 操作平台 1: mcn 2: platform
    3: required string Reason, // 拒绝理由
    4: optional string Files, // 材料信息
    5: required i64 McnId, // 机构id
    6: optional bool AutoRefuse, // 用于区分是脚本处理还是人工处理
    7: optional string Operator,
    8: optional string FileNames, // 文件名称
    9: optional RefuseType RefuseType, // 拒绝类型
    255: required base.Base Base,
}

struct RefuseRescissionResponse {
    255: required base.BaseResp BaseResp,
}

struct CreateAppealRequest {
    1: required i64 UserId, // 用户id
    2: optional string Files, // 材料信息
    3: required string Reason, // 申诉理由
    4: optional string FileNames, // 文件名称
    255: required base.Base Base,
}

struct CreateAppealResponse {
    255: required base.BaseResp BaseResp,
}

struct GetInviteStatusRequest {
    1: required i64 UserId,
    2: required i64 McnId,
    255: required base.Base Base,
}

struct GetInviteStatusResponse {
    1: required InviteStatusEnum InviteStatus = InviteStatusEnum.NOT_FOUND,
    255: required base.BaseResp BaseResp,
}

struct McnSearchV2Request {
    1: required string Keyword,
    2: optional SearchType Type,
    3: optional i64 Cursor,
    4: optional i64 Count,
    255: required base.Base Base,
}

struct McnSearchV2Response {
    1: required list<MCN> McnList,
    2: required i64 Total,
    3: required bool HasMore,

    255: required base.BaseResp BaseResp,
}

struct GetUserListByMcnIdRequest {
    1: required i64 McnId,
    2: optional i64 cursor,
    3: optional i64 count,
    255: required base.Base Base,
}

struct GetUserListByMcnIdResponse {
    1: required list<i64> UserList,
    2: required i64 Total,
    3: required bool HasMore,

    255: required base.BaseResp BaseResp,
}

// 该服务IDL已迁移至 https://code.byted.org/aweme/rpc_idl/blob/master/aweme/mcn_service.thrift，后续修改请移步新仓库进行操作
service IesMcnService {
    GetTalentListForStarResponse GetTalentListForStar(1: GetTalentListForStarRequest request),
    GetMCNInfoByUserIdResponse  GetMCNInfoByUserId (1: GetMCNInfoByUserIdRequest request),
    GetInviteStatusResponse GetInviteStatus(1: GetInviteStatusRequest request),
    MGetMcnInfoByIdResponse MGetMcnInfoById (1: MGetMcnInfoByIdRequest request),
    McnPrivateSearchResponse McnPrivateSearch (1: McnSearchRequest request),
    McnSearchResponse McnSearch (1: McnSearchRequest request),
    McnSearchV2Response McnSearchV2 (1: McnSearchV2Request request),
    CreateRescissionResponse CreateRescission(1: CreateRescissionRequest request), // 创建解绑
    CreateAppealResponse CreateAppeal(1: CreateAppealRequest request), // 创建申诉
    GetRescissionByUserIdResponse GetRescissionByUserId(1: GetRescissionByUserIdRequest request), // 获取用户解绑信息
    GetRescissionByMCNUserIdResponse GetRescissionByMCNUserId(1: GetRescissionByMCNUserIdRequest request), // 获取用户解绑信息
    AcceptRescissionResponse AcceptRescission(1: AcceptRescissionRequest request), // 通过
    RefuseRescissionResponse RefuseRescission(1: RefuseRescissionRequest request), // 拒绝
    GetUserListByMcnIdResponse GetUserListByMcnId(1: GetUserListByMcnIdRequest request),
}
