include "../../base.thrift"

namespace go security.tq.notify.bot

// 返回bot的存储ID的响应
struct BotIDResp {
    1:   required i64           ID,        // 模版ID
    255: optional base.BaseResp BaseResp,
}

// 增加bot的请求
struct AddBotReq {
    1:   required string          AppID,           // 功能模块，用于判断使用哪个机器人
    2:   required string          AppToken,        // 群成员
    3:   required bool            IsPublic,        // 是否为公共bot
    4:   required string          AliasName,        // 别名
    5:   optional list<string>    Users,           // 可用的用户
    6:   required i64             MaxQps,          // 最大QPS
    7:   optional string          Description,     // 描述
    8:   required string          LoginUser,       // 当前登录用户
    255: optional base.Base       Base,
}

// 删除bot的请求
struct DeleteBotReq {
    1:  required i64              ID,      // 需要删除的
    2:  required string           LoginUser,  // 当前登录用户
    255: optional base.Base       Base,
}

struct BotDetail {
    1:  required i64               ID,              // bot的存储ID
    2:  required string            AppID,           // bot应用ID
    3:  required string            AliasName,       // bot的别名
    4:  required string            Owner,           // 创建者
    5:  required i64               MaxQps,          // 最大QPS 
    6:  required i64               RuleNum,         // 关联规则数
    7:  required i64               TemplateNum,     // 关联模板数
    8:  required string            Department,      // 部门信息
    9:  required string            Description,     // 描述信息
    10: required i64               AvQps,           // 近7天平均QPS
    11: required string            CreateAt,        // 创建时间
    12: required string            RiskCoefficient, // 风险系数
    13: optional list<string>      Users,           // 有权使用该bot的用户
}

// 查询bot的请求
struct ListBotReq {
    1:  optional list<i64>        IDs,          // 查询bot的id列表
    2:  required string           LoginUser,    // 当前登录用户
    3:  optional string           AliasName,    // 筛选模版名称（模糊）
    4:  optional string           Creator,      // 筛选创建人（模糊）
    5:  optional bool             IsPublic,     // 是否为公共bot
    6:  optional string           Department,   // 部门
    255: optional base.Base       Base,
}

// 推送消息变量响应
struct ListBotResp {
    1:  required list<BotDetail>  BotList,      // 查询到的bot列表
    2:  required i64              count,        // 符合条件的个数
    255: optional base.BaseResp BaseResp,
}

enum LinkOpt {
    UnknowOpt   = 0
    Add         = 20
    Reduce      = 21
}

struct LinkNumModify {
    1:  optional i64              TemplateNum,  // 管理的模板数，0表示不需要修改
    2:  optional i64              RuleNum,      // 管理的规则数，0表示不需要修改
    3:  required LinkOpt          Opt,          // 增减操作标志
}

// 修改bot的请求
struct ModifyBotReq {
    1:  required i64              ID,           // 需要修改的bot存储ID
    2:  required string           LoginUser,    // 当前登录用户
    3:  optional string           AliasName,    // 别名
    4:  optional string           Description,  // 修改描述
    5:  optional i64              MaxQps,       // 最大一分钟的上传个数
    6:  optional list<string>     Users,        // bot的使用者
    255: optional base.Base       Base,
}

// 修改bot关联的规则和消息模板管理个数
struct ModifyBotLinkReq {
    1:  required i64              ID,           // 需要修改的bot存储ID
    2:  required string           LoginUser,    // 当前登录用户
    3:  optional LinkNumModify    LinkModify,   // 规则和消息模板管理个数修改
    255: optional base.Base       Base,
}
