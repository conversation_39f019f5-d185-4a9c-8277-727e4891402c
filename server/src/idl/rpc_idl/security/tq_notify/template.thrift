include "../../base.thrift"
include "util.thrift"

namespace go security.tq.notify.template

// 推送模式
enum PostMode {
    TIMING   = 1,  // 定时
    REALTIME = 2,  // 实时
}

// 接收者配置类型
enum ReceiverConfigType {
    FIXED       = 1,  // 发送至固定的人或群
    VARIABLE    = 2,  // 发送至角色
    USERDIFINED = 3,  // 随调用方传入接收者列表
}

// 推送状态
enum Status {
    WATING  = 1,  // 待推送
    RUNNING = 2,  // 推送中
    SUCCESS = 3,  // 推送成功
    ERROR   = 4,  // 发生错误
}

enum ReceiverType {
    EMAIL  = 1,  // 邮箱
    CHATID = 2,  // lark群ID
}

// 决策模式
enum DecisionMode {
    MESSAGE = 1,  // 消息通知
    FLOW    = 2,  // 工单流程
}


// 用户信息
struct UserInfo {
    1: required string EmailPrefix,   // 邮箱前缀
    2: required string Email,         // 邮箱
    3: optional string DepartmentID,  // 所在部门ID
}

struct ReceiverConfig {
    1: required ReceiverType Type,   // 类型，邮箱或者群ID
    2: required string       Value,  // 值，如果是发送至角色，该字段传带变量语法，例如$.user_name
    3: optional string       Role,   // 如果是「发送至角色」必传，其他情况不传。通过列表选择
}

// 消息模版结构
struct MessageTemplate {
    1:  optional i64                  ID,                  // ID
    2:  required string               Name,                // 模版名称
    3:  required i64                  Module,              // 功能模块，从TCC获取
    4:  required string               Content,             // 模版内容
    5:  required PostMode             Mode,                // 推送模式
    6:  required DecisionMode         DecisionMode,        // 决策模式
    7:  optional i64                  FirstPostAt,         // 开始推送时间（用于定时推送）
    8:  optional i64                  Interval,            // 间隔周期（用于定时推送），从TCC获取
    9:  required ReceiverConfigType   ReceiverConfigType,  // 接收者配置类型
    10: optional list<ReceiverConfig> ReceiverConfig,      // 接收者配置，选择「随调用方传入接收者列表」不传，其他情况必传
    11: optional string               Creator,             // 创建者(邮箱)
    12: optional i64                  CreatedAt,           // 创建时间
    13: optional i64                  UpdatedAt,           // 更新时间
    14: optional string               FlowName,            // 工单流的唯一标识
    15: optional string               FlowAccount,         // 工单流账号
    16: optional string               FlowToken,           // 工单账号token
    17: optional i64                  UnitTimeH,           // 单位时间
    18: optional i64                  PushTimes,           // 推送次数
}

// 推送历史记录结构
struct NotifierHistory {
    1: required i64                  ID,                // ID
    2: required i64                  TemplateID,        // 模版ID
    3: required string               TemplateName,      // 模版名称
    4: required string               Content,           // 模版内容
    5: required Status               Status,            // 推送状态
    6: required i64                  CreatedAt,         // 创建时间
    7: required list<ReceiverRecord> ReceiverRecords,   // 接收者
    8: optional i64                  FlowID,            // 工单ID
}

// 接收者记录结构
struct ReceiverRecord {
    1: required i64             ID,          // 接收者记录本身ID
	2: required string          MessageID,   // lark返回的消息ID
	3: required i64             StatusCode,  // 状态码，lark返回的状态码+平台自身状态码
	4: required ReceiverConfig  Receiver,    // 只传Type 和 Value
	5: required i64             PostAt,      // 推送时间
}

// 传递模版ID的请求
struct TemplateIDReq {
    1:   required i64       ID,    // 模版ID
    2:   required string         LoginUser,
    255: optional base.Base Base,
}

// 返回模版ID的响应
struct TemplateIDResp {
    1:   required i64            ID,        // 模版ID
    2:   optional util.PermissionInfo PermissionInfo,
    255: optional base.BaseResp BaseResp,
}

// 创建或编辑模版请求
struct UpsertTemplateReq {
    1:   required MessageTemplate Data,
    2:   required UserInfo        User,  // 用户信息
    255: optional base.Base       Base,
}

// 查询模版详情响应
struct TemplateDetailResp {
    1:   optional MessageTemplate Data,
    2:   optional util.PermissionInfo  PermissionInfo,
    255: optional base.BaseResp   BaseResp,
}

// 删除模版响应
struct DeleteTemplateResp {
    1:   optional util.PermissionInfo  PermissionInfo,
    255: optional base.BaseResp   BaseResp,
}

// 查询模版列表请求
struct TemplateListReq {
    1:   required bool         Export,        // 是否为数据导出请求，如果是导出则返回查询条件下全量数据
    2:   optional string       Name,          // 筛选模版名称（模糊）
    3:   optional string       Creator,       // 筛选创建人（模糊）
    4:   optional DecisionMode DecisionMode,  // 筛选决策模式
    5:   optional i64          PerPageItems,  // 分页信息：每一页的数据条数
    6:   optional i64          CurrentPage,   // 分页信息：当前页码，从1开始
    7:   optional list<i64>    IDs,           // 筛选模版ID，管理员身份时传空
    8:   required UserInfo     User,          // 当前操作人信息
    255: optional base.Base    Base,
}

// 模版详情响应
struct TemplateListResp {
    1:   required list<MessageTemplate> Results,   // 数据内容
    2:   required i64                   AllCount,  // 数据总条数，满足请求中查询条件的数据的总数
    3:   optional util.PermissionInfo        PermissionInfo,
    255: optional base.BaseResp         BaseResp,
}

// 查询推送历史记录请求
struct HistoryListReq {
    1:   optional i64          TemplateID,      // 模版ID，前端调用必传，调用方可选
    2:   optional i64          PerPageItems,    // 分页信息：每一页的数据条数
    3:   optional i64          CurrentPage,     // 分页信息：当前页码，从1开始
    4:   required UserInfo     User,            // 当前操作人信息
    5:   optional list<Status> Status,          // 推送状态
    255: optional base.Base    Base,
}

// 查询推送历史记录响应
struct HistoryListResp {
    1:   required list<NotifierHistory> Results,   // 数据内容
    2:   required i64                   AllCount,  // 数据总条数，满足请求中查询条件的数据的总数
    3:   optional util.PermissionInfo        PermissionInfo,
    255: optional base.BaseResp         BaseResp,
}
