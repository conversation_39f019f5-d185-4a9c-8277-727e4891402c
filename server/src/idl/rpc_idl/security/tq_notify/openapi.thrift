include "../../base.thrift"
include "template.thrift"

namespace go security.tq.notify.openapi

// 模版ID和名称
struct Message {
    1:   required i64            TemplateID,  // 模版ID
    2:   required string         Content,     // 模版中需要的变量内容
    3:   optional list<Receiver> Receivers,   // 接收者
}

// 接收者结构
struct Receiver {
    1: required template.ReceiverType Type,   // 接收者类型
    2: required string                Value,  // 邮箱或者lark chat ID
}

// 推送消息变量请求
struct PostMessageReq {
    1:   required Message        Message,  // 推送消息
    255: optional base.Base      Base,
}

// 推送打包消息变量请求
struct PostPacketMessageReq {
    1:   required list<Message>  Message,  // 推送消息
    255: optional base.Base      Base,
}

// 推送消息变量响应
struct PostMessageResp {
    1:   required i64           ID,      // 推送记录的uuid
    255: optional base.BaseResp BaseResp,
}

// 建群请求
struct CreateChatReq {
    1:   required i64          Module,           // 功能模块，用于判断使用哪个机器人
    2:   required list<string> Users,            // 群成员
    3:   required string       ChatName,         // 群聊名称
    4:   optional string       ChatDescription,  // 群聊描述
    255: optional base.Base    Base,
}

// 建群响应
struct CreateChatResp {
    1:   required string        ChatID,   // 群ID
    2:   required list<string>  InvalidUsers,  // 无法拉进群聊的用户
    255: optional base.BaseResp BaseResp,
}


// 邀请人员进去请求
struct AddChatMemberReq {
    1:   required i64          Module,           // 功能模块，用于判断使用哪个机器人
    2:   required list<string> Users,            // 群成员
    3:   required string       ChatOpenID,       // 群聊的open ID
    255: optional base.Base    Base,
}

// 邀请人员进去响应
struct AddChatMemberResp {
    1:   required list<string>  InvalidUsers,  // 无法拉进群聊的用户
    255: optional base.BaseResp BaseResp,
}
