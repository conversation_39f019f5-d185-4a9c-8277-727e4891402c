include "../base.thrift"
include "./common.thrift"
include "../im_cloud/im_model.thrift"
include "../aweme/mcn_service.thrift"
include "../ies_kefu/ies_kefu_pigeon_communication.thrift"
include "../ies_kefu/sop/ies_cs_sop_engine_api.thrift"

namespace py ies.helpdesk.im
namespace go ies.helpdesk.im
namespace java ies.helpdesk.im

struct CustomerCloudContext {
    1: optional i64 TenantID (go.tag = "json:\"tenant_id,omitempty\"") // 租户ID
    2: optional i64 LoginAgentID (go.tag = "json:\"login_agent_id,omitempty\"") // 当前登录客服ID
}

struct TimeRange {
    1: required i64 StartTime,
    2: required i64 EndTime,
}

struct TransferHistory {
    1: required i64 TaskID,
    2: required i64 AccessPartyId,
}

struct QueryTaskRequest {
    1: required i32 HostAppID,
    2: optional i32 Channel,
    3: optional i32 AppID,
    4: optional i64 Uid,
    5: optional i64 StartTime,
    6: optional i64 EndTime,
    7: optional i32 Page,
    8: optional i32 PageSize,
    9: optional list<i32> HostAppIDs,
    10: optional string BizUid,
    11: optional i64 CustomerId,
    12: optional list<i64> ids,
    13: optional i64 ticketID,
    14: optional string ticketNum,
    15: optional bool mustHasTicket,
    16: optional list<i32> statusNot,
    17: optional i64 CreateTimeStart,
    18: optional i64 CreateTimeEnd,
    19: optional i16 TaskType,
    20: optional bool AllTaskType,
    21: optional bool OnlyDirectData,
    22: optional string EnterType,
    23: optional i64 AccessPartyID,
    24: optional list<i64> SceneList,
    25: optional i64 ConversationShortID,
    26: optional i64 TransferNumGte,
    27: optional i64 TransferNumLte,
    28: optional list<i64> SkillGroupIDs,
    29: optional bool FullCategoryData,
    30: optional list<i64> ChatEnded,
    31: optional list<string> FeedBackEntry,
    32: optional bool IsUsBdee,
    33: optional list<i64> EndTimeRange,
    34: optional i64 orderID,
    255: optional base.Base Base,
}

struct QueryTaskResponse {
    1: required i64 TotalCount,
    2: required list<Task> Tasks,
    255: optional base.BaseResp BaseResp,
}

struct QueryTaskMessageRequest {
    1: required i64 TaskID,
    2: optional bool NeedDesensate,
    3: optional i64 AgentID,
    4: optional i64 AccessPartyID,
    5: optional i64 StartTimestamp,
    6: optional i64 EndTimestamp,
    7: optional i64 Cursor,
    8: optional i64 Size,
    9: optional bool MsgNeedDesensate,
    10: optional bool IncludeIntelligentMessages,
    11: optional bool IncludeBlockMessages,
    12: optional bool FilterUsBdeeData,
    13: optional string TranslateLanguage,
    14: optional bool UseCodeText,              //是否使用带code的文本

    255: optional base.Base Base,
}

struct QueryTaskMessageResponse {
    1: required list<im_model.Message> Messages,
    2: optional map<i64,i64> StartMsgIDTaskIDMap,
    3: optional bool HasMore,
    4: optional i32 UserRole, //用户角色，0:未知，1：顾客，2：商家
    255: optional base.BaseResp BaseResp,
}

struct Task {
    1: required i64 TaskID,
    2: required i64 ConversationShortID,
    4: required i64 UID, //用户ID
    5: required string UniqueName, //客服名称
    6: required string Name, //客服昵称
    7: required i64 StartTime, //开始转人工时间
    8: required i64 EndTime, //结束时间，如果没有则是0
    9: required i64 DurationSeconds, // 人工进线咨询时长，如果未结束则为 0
    10: required string ChannelName, //队列名
    11: required i32 Status,//状态
    12: required i32 EndStatus, //结束方式
    13: required string UserName,  // 用户名
    14: required i32 AppID,  // 客服平台AppID
    15: required i32 Channel,  // 客服平台Channel
    16: optional i64 ParentTaskID,  // parent task id
    17: optional i64 SkillGroup,  // 技能组ID
    18: optional i64 TenantID,  // 租户ID
    19: optional i64 AgentID,  // 客服ID
    20: optional i32 EndUser,  // 结束的用户
    21: optional i64 AssignTime,  // 分配客服的时间
    22: optional string AgentEmail,  // 客服Email
    23: optional string AgentUuid, // 客服的门神uuid
    24: optional string SkillGroupName,  // 技能组名称
    25: optional i64 CustomerFirstResponseTime,  // 客服首次回复时间
    26: optional i64 AgentUid,  // 客服Uid
    27: optional i64 IMUserID,  // 客服平台用户ID
    28: optional string BizUserID,  // 业务原始用户ID
    29: optional list<i64> categoryIDs,  // 工单标签列表
    30: optional i64 AccessPartyId,  // 接入方id
    31: optional list<TimeRange> SuspendTimeRanges, // 挂起时间区间列表
    32: optional list<i64> ticketIDs, // 工单id
    33: optional list<string> ticketNums, // 工单编号
    34: optional bool IsReserve, //是否再次触达
    35: optional i64 ChildTaskID, // 流转的下一个taskID
    36: optional i32 HelpdeskChannel,
    37: optional list<TransferHistory> TransferHistories, // 该task全部的历史流转记录, 按流转顺序提供
    38: optional i16 TaskType, // 1-IM会话，2-离线留言
    39: optional i64 HostAppID,
    40: optional i64 TransferNum, // 流转次数（该流转次数是业务方自定义的，非根据parentID计算得来的）
    41: optional bool IsValidTask, // 是否有效会话
    42: optional string Extra, // Extra
    43: optional i64 SessionID, // 随路数据ID
    44: optional list<string> CallIDS, // 通话数据ID
    45: optional list<string> SmsIDS, // 短信ID
    46: optional i64 IsPractice,
    47: optional string EnterType,
    48: optional list<string> IntelligentCallIDS, // 通话数据ID
    49: optional map<i64, list<i64>> TicketCategoryMap, //工单和对应标签
    50: optional i32 ActivityLevel,
    51: optional list<i64> orderIDs,
}

struct GetTaskRequest {
    1: required i64 TaskID,
    2: optional bool HasSuspendTimeRanges, // task 中带上挂起时间区间列表，默认 false
    3: optional bool WithoutTransferHistory,  // 是否忽略会话流转历史，较影响性能，若不需要请设置为true
    255: optional base.Base Base,
}

struct GetTaskResponse {
    1: required Task Task,
    255: optional base.BaseResp BaseResp,
}

struct QueryBizTypeRequest {
    1: required i32 HostAppID,
    255: optional base.Base Base,
}

struct GetBizTypeByAppChannelRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    3: optional i32 TaskID,
    255: optional base.Base Base,
}

struct GetBizTypeByTaskIDRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct GetBizTypeByAppChannelsRequest {
    1: required i32 AppID,
    2: required list<i32> Channel,
    255: optional base.Base Base,
}

struct QueryBizTypesByAccessPartyIDRequest {
    1: required i32 AccessPartyID,
    255: optional base.Base Base,
}

struct BizType {
    1: required i32 ID, // ID
    2: required i32 AppID, //客服平台AppID
    3: required string AppName, //客服平台App Name
    4: required i32 Channel, // 客服平台Channel
    5: required string ChannelName,//客服平台 Channel Name
    6: required i64 HostAppID, // 宿主AppID
    7: required string HostAppName,//宿主App name
    8: required i64 AppBaseID, //标签应用ID
    9: required i64 ResourceID, //资源ID
    10: required i64 SubResourceID, //子资源ID
    11: required i64 AccessPartyID,//接入方ID
    12: optional i64 Scene,//场景
    13: optional string EntranceId,//入口ID
    14: optional string EntranceName,//入口名
}

struct QueryBizTypesResponse {
    1: required list<BizType> BizTypeList,
    255: optional base.BaseResp BaseResp,
}

struct GetBizTypeRequest {
    1: required i32 ID,
    255: optional base.Base Base,
}

struct User {
    1: required string RealUserID,
    2: required string HostUserID,
    3: optional common.BusinessIDEnum BizID, //用户体系
    4: optional i32 AccountType,// 账号体系
}

struct GetBizTypeResponse {
    1: required BizType bizType,
    2: optional User    User,
    3: optional list<common.User>    userList,
    255: optional base.BaseResp BaseResp,
}

struct QueryNoticesRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    255: optional base.Base Base,
}

struct Notice {
    1: required i32 ID,
    2: required string Title,
    3: required string Content,
    4: required bool AutoPopup, // 公告在用户端是否自动弹出，默认false
}

struct QueryNoticesResponse {
    1: required list<Notice> Notices,
    255: optional base.BaseResp BaseResp,
}

struct LineupConfirmRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    255: optional base.Base Base,
}

struct LineupConfirmResponse {
    1: required bool NeedConfirm
    2: required string Position
    3: required string Mins
    4: required string Message
    5: required string ConfirmText
    255: optional base.BaseResp BaseResp,
}

struct QueryLatestTaskRequest {
    1: required i32 AppID,
    2: required i64 ConversationShortID,
    3: optional i16 TaskType,
    255: optional base.Base Base,
}

struct QueryLatestTaskResponse {
    1: optional Task Task,
    255: optional base.BaseResp BaseResp,
}

struct GetWaitingInfoRequest {
    1: required i32 AppID,
    2: required i64 ConversationShortID,
    255: optional base.Base Base,
}

struct NewWaitingInfo {
    1: required string Index
    2: required string WeekTips
    3: required string StrongTipsTittle
    4: required string StrongTips
    5: required string StrongTipsContent
}

struct GetWaitingInfoResponse {
    1: required string position
    2: required i32 mins
    3: required i64 pollingSpan
    4: required string message
    5: optional i64 SkillGroupID
    6: optional i64 ShowEndConversationBtn
    7: optional i64 TipsVersion
    8: optional i64 TaskID
    9: optional i64 Status
    10: optional NewWaitingInfo NewWaitingInfo

    255: optional base.BaseResp BaseResp,
}

struct GipReplyRequest {
    1: required i32 Source,
    2: required i64 Uid,
    3: required string Operator,
    4: required string Content,
    5: required string ImageUrl, // 图片 tos uri
    255: optional base.Base Base,
}

struct GipReplyResponse {
    1: required i64 TaskID
    2: required i64 ServerMessageID
    255: optional base.BaseResp BaseResp,
}


struct GipSyncUserMessageRequest {
    1: required i32 Source,
    2: required i64 Uid,
    3: required string Content,
    4: required string ImageUrl, // 图片 tos uri
    255: optional base.Base Base,
}

struct GipSyncUserMessageResponse {
    1: required i64 TaskID
    2: required i64 ServerMessageID
    255: optional base.BaseResp BaseResp,
}

struct GetUnreadMessageRequest {
    1: required i32 AppID,
    2: required i64 Uid,
    3: optional i32 Source,
    255: optional base.Base Base,
}

struct GetUnreadMessageResponse {
    1: required i32 UnreadMessageCount
    255: optional base.BaseResp BaseResp,
}

struct LabelInfo {
     1: required i64 Id,
     2: required string Name,
}

struct MGetMessageLabelsRequest {
    1: required list<i64> ServerMessageIds,
    255: optional base.Base Base,
}

struct MGetMessageLabelsResponse {
    1: map<i64,list<LabelInfo> >  LabelMap,
    255: optional base.BaseResp BaseResp,
}

struct AppV2 {
     1: required i32 AppID, // app_id
     2: required string AppName, // app_name
     3: required i32 ProductID, // product_id
     4: required bool InNewRoute, // 是否使用了新路由，true 为使用了
}

struct ListAllAppV2Request {
    255: optional base.Base Base,
}

struct ListAllAppV2Response {
    1: required list<AppV2> NewRouteAppList,
    255: optional base.BaseResp BaseResp,
}

struct GetProductListResponseItem {
	1: required i64    ID          (go.tag = "json:\"id\"")
	2: required i32    AppID       (go.tag = "json:\"app_id\"")
	3: required i64    UserID      (go.tag = "json:\"user_id\"")
	4: required string Description (go.tag = "json:\"description\"")
	5: required string Config      (go.tag = "json:\"config\"") // map[string]interface{}序列化后的json串
}

struct GetProductListRequest {
    255: optional base.Base Base,
}

struct GetProductListResponse {
	1: required list<GetProductListResponseItem> List     (go.tag = "json:\"list\"")
    255: optional base.BaseResp BaseResp,
}

struct Agent {  // 客服人员 helpdest_agent entity
    1: required i64 ID, // primary id
    2: required byte Type, // 客服类型 0 普通 1 管理员
    3: required byte Status, // 客服状态 0 禁用 1 启用
    4: required byte WorkStatus, // 工作状态
    5: required byte LoginGroup, // 工作组
    6: required i32 TaskNum, // 目前分配任务
    7: required i32 TaskMaxNum, // 最大分配组
    8: required i64 ProductID, // 产品信息
    9: required i64 UserID, // 客服 userID
    10: required i64 TenantID, // 租户 ID
    11: required i64 CreatedAt, // 创建时间
    12: required i64 UpdatedAt, // 更新时间
    13: required string UUID, // 用户唯一标识
    14: required string Name, // 客服昵称
    15: required string UniqueName, // 客服姓名
    16: required string Email, // 客服邮箱
}

struct ListAgentByIDSRequest {
    1: required list<i64> AgentIDList,
    255: optional base.Base Base,
}

struct ListAgentByIDSResponse {
    1: required list<Agent> AgentList,
    255: optional base.BaseResp BaseResp,
}

struct BatchSetWorkStatusByIDSRequest {
    1: required list<i64> AgentIDList,
    2: required byte WorkerStatus, // 0 小休，1 接线
    3: optional i64 AgentID, // 请求的客服ID（操作人）
    255: optional base.Base Base,
}

struct BatchSetWorkStatusByIDSResponse {
    1: optional bool IsASync,       // 是否是异步执行任务
    2: optional list<i64> failedAgentIDS,
    255: required base.BaseResp BaseResp,
}

struct SkillGroup {
	1: required string Name                 (go.tag = "json:\"name\"")
	2: required i64    TaskMaxNum           (go.tag = "json:\"task_max_num\"")
	3: required bool   OfflineSessionEnable (go.tag = "json:\"offline_session_enable\"")
}

struct HelpdeskAgent {
	1:  required i8     Type              (go.tag = "json:\"type\"")         // 客服类型 0 普通 1 管理员
	2:  required i8     Status            (go.tag = "json:\"status\"")       // 客服状态 0 禁用 1 启用
	3:  required i8     WorkStatus        (go.tag = "json:\"work_status\"")  // 工作状态
	4:  required i8     LoginGroup        (go.tag = "json:\"login_group\"")  // 工作组
	5:  required i32    TaskNum           (go.tag = "json:\"task_num\"")     // 目前分配任务
	6:  required i32    TaskMaxNum        (go.tag = "json:\"task_max_num\"") // 最大分配组
	7:  required i64    Id                (go.tag = "json:\"id\"")           // 唯一标示
	8:  required i64    ProductId         (go.tag = "json:\"product_id\"")   // 产品信息
	9:  required i64    UserId            (go.tag = "json:\"user_id\"")      // 客服user_id
	10: required i64    TenantId          (go.tag = "json:\"tenant_id\"")    // 租户id
	11: required i64    CreatedAt         (go.tag = "json:\"created_at\"")   // 创建时间
	12: required i64    UpdatedAt         (go.tag = "json:\"updated_at\"")   // 更新时间
	13: required i64    DeletedAt         (go.tag = "json:\"deleted_at\"")   // 删除时间 type:*time.Time;time
	14: required string Uuid              (go.tag = "json:\"uuid\"")         // 用户唯一标识
	15: required string Name              (go.tag = "json:\"name\"")         // 客服名称
	16: required string UniqueName        (go.tag = "json:\"unique_name\"")  // 客服唯一名称
	17: required string Email             (go.tag = "json:\"email\"")        // 用户邮箱
	18: required string IDStr             (go.tag = "json:\"id_str\"")
	19: required string ProductIDStr      (go.tag = "json:\"product_id_str\"")
	20: required string UserIDStr         (go.tag = "json:\"user_id_str\"")
	21: required string TenantIDStr       (go.tag = "json:\"tenant_id_str\"")
	22: required i64    WorkSecs          (go.tag = "json:\"work_secs\"")
	23: required i64    RestSecs          (go.tag = "json:\"rest_secs\"")
	24: required i32    RestTimes         (go.tag = "json:\"rest_times\"")
	25: required i32    InvalidTaskMaxNum (go.tag = "json:\"invalid_task_max_num\"")
	26: required list<i64> SkillGroupIds     (go.tag = "json:\"skill_group_ids\"")
	27: required map<i64, SkillGroup> SkillGroups       (go.tag = "json:\"skill_groups\"")
	28: required map<i8, i64> WorkStatusSecs (go.tag = "json:\"work_status_secs,omitempty\"")
	29: optional i32 StaffType  (go.tag = "json:\"staff_type\"")
}

struct GetAgentByIDRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetAgentByIDResponse {
    1: required HelpdeskAgent Agent,
    255: optional base.BaseResp BaseResp,
}

struct GetAgentIMCloudTokenRequest {
    1: required i64 AgentUserID,
    2: optional i64 FlowControlGroupKey,
    3: optional string FlowControlGroup
    255: optional base.Base Base,
}

struct GetAgentIMCloudTokenResponse {
	1: required string Token      (go.tag = "json:\"token\"")
	2: required i64    VendorType (go.tag = "json:\"vendor_type\"")
    255: optional base.BaseResp BaseResp,
}

struct SuspendTaskRequest {
    1: required i64 TaskID, // 人工会话ID
    2: optional common.SuspendOperationEnum OperationType, // 操作方

    254: optional CustomerCloudContext CustomerCloudContext, // 请求上下文
    255: optional base.Base Base,
}

struct SuspendTaskResponse {
    255: required base.BaseResp BaseResp,
}

struct ContinueTaskRequest {
    1: required i64 TaskID, // 人工会话ID
    2: optional string event, // 事件类型
    3: optional common.SuspendOperationEnum OperationType, // 操作方

    254: optional CustomerCloudContext CustomerCloudContext, // 请求上下文
    255: optional base.Base Base,
}

struct ContinueTaskResponse {
    255: required base.BaseResp BaseResp,
}

struct IsSensitiveMessagesRequest {
    1: required list<MessageSensitive> MessageList,
    2: optional common.SensitiveReqFrom ReqFrom,
    3: optional bool AbuseCheck,
    4: optional string NameSpace,
    255: optional base.Base Base,
}

struct CheckSensitiveText {
    1: optional string OriginText,
    2: optional string TranslationText,
    3: optional string TranslationLanguage,
}

struct MessageSensitive {
    1:  required i32 AppID,
    2:  required i32 Channel,
    3:  required i64 ConversationShortID,
    4:  required i64 TaskID,
    5:  required i64 UserId,
    6:  required string Content,
    7:  required string Ext,
    8:  optional i64 MessageID,
    9:  required i32 AgentID,
    10: optional CheckSensitiveText CheckText,
    11: optional i32 MessageType,
}

struct IsSensitiveMessagesResponse {
    1: required list<MessageIsSensitiveData> Data (go.tag = "json:\"data\""),
    255: required base.BaseResp BaseResp,
}

struct MessageIsSensitiveData {
    1:required i32 SensitiveType (go.tag = "json:\"sensitive_type\""),
    2:required list<BlockContent> BlockList (go.tag = "json:\"block_list\""),
    3:required i64 MessageID (go.tag = "json:\"message_id\""),
    4:required string BlockListStr (go.tag = "json:\"block_list_str\""),
    5:optional list<BlockContent> TranslationBlockList (go.tag = "json:\"translation_block_list\""),
    6:optional string BlockText (go.tag = "json:\"block_text\""),//拦截敏感词的提示文案
}

struct BlockContent{
   1:required i32 LeftPosition  (go.tag = "json:\"left_position\""),
   2:required i32 RightPosition (go.tag = "json:\"right_position\""),
}

struct MessageInfo {
     1:  required i32 AppID,
     2:  required i32 Channel,
     3:  required i64 ConversationShortID,
     4:  required i64 TaskID,
     5:  required i64 UserId,
     6:  required string Content,
     7:  required string Ext,
     8:  required i64 CreateTime,
     9:  required i64 ID,
     10: required i64 AgentID,
}

struct GetBlockHistoryMessagesRequest {
      1:   required i64 TaskID,
      255: optional base.Base Base,
}

struct GetBlockHistoryMessagesResponse {
      1:   required  list<MessageInfo> Messages,
      255: required  base.BaseResp BaseResp,
}

struct GetMessagesByOldConversationRequest {
    1: required  string conversation_short_id,   // 当前会话 ShortID
    2: required  string next_cursor,             // 获取消息的位点，默认 0，从最新的一条消息开始
    3: optional  string message_id,              // 前一条消息的ID
    4: optional string flow_control_group,         // vendor配置分组
    5: optional i64 flow_control_group_key
    255: optional base.Base Base,
}

struct IMessageBody {
    1: required  string conversation_id,                          // im_model.MessageBody ConversationId
    2: required  i32 conversation_type,                          // im_model.MessageBody ConversationType
    3: required  string conversation_short_id,                          // im_model.MessageBody ConversationShortId
    4: required  string server_message_id,                          // im_model.MessageBody ServerMessageId
    5: required  i32 message_type,                          // im_model.MessageBody MsgType
    6: required  string sender,                          // im_model.MessageBody Sender
    7: required  string content,                          // im_model.MessageBody Content
    8: required  map<string,string> ext,                          // im_model.MessageBody Ext
    9: required  string create_time,                          // im_model.MessageBody CreateTime
    10: required  string version,                          // im_model.MessageBody Version
    11: required  i32 status,                          // im_model.MessageBody Status
    12: optional  string index_in_conversation,                          // im_model.MessageBody IndexInConversationV2
    13: optional  string sec_sender,                          // im_model.MessageBody SecSender
}

struct GetOldMessagesByConversationResponse {
    1: required  bool has_more,                          // 是否有更多的历史消息，true 为有
    2: required  string next_cursor,                        // 消息的位点，分页用，分页数量 TCC 中配置
    3: required  list<IMessageBody> message_list,    // 消息列表
    255: optional base.BaseResp BaseResp,
}

struct GetConversationRequest {
    1: required  i64 ConversationShortID,   // 当前会话 ShortID
    255: optional base.Base Base,
}

struct GetOldConversationResponse {
    1: required  i64 OldConversationShortID,                          // 老会话ID
    255: optional base.BaseResp BaseResp,
}

// 用户引导
struct Guidance{
    1: required i64 ID
    2: required string Name
    3: optional bool Visited
}

struct GetGuidanceRequest {
    1: required string GuidanceName,
    2: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetGuidanceResponse {
    1: required Guidance Guidance,
    255: optional base.BaseResp BaseResp,
}

struct SetGuidanceVisitedRequest {
    1: required i32 GuidanceID,
    2: required i64 AgentID,
    255: optional base.Base Base,
}

struct SetGuidanceVisitedResponse {
    255: optional base.BaseResp BaseResp,
}

struct ListAgentAccessPartyRequest {
    1: required string AgentID,
    2: required string Email,
    3: required i64 TenantID,
    255: optional base.BaseResp BaseResp,
}

struct AccessParty{
    1: optional i64 ID,
    2: string Name,                                 //接入方名称
    3: optional list<AccessParty> SubAccessParty,   //二级接入方
    4: i32 EnableFlag,                              //是否可用
}

struct ListAgentAccessPartyResponse {
     1:required list<AccessParty> AccessPartyList
     255: base.BaseResp BaseResp,
}

struct IMUser {
    1: required i64 IMUserID, //IM用户ID
    2: required common.BusinessIDEnum BizID, //用户体系
    3: required string RealUserId, //用户体系下的用户ID
}

struct GetTaskIMUserDetailRequest {
    1: required i64 TaskID, //人工会话ID
    255: optional base.Base Base,
}

struct GetTaskIMUserDetailResponse {
    1: optional IMUser IMUser, //IM用户详情
    255: optional base.BaseResp BaseResp,
}

struct GetOnlineInfoByAppIdRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    255: optional base.Base Base,
}

struct GetOnlineInfoByAppIdResponse {
    1: optional OnlineInfo OnlineInfo, //在线信息详情
    255: optional base.BaseResp BaseResp,
}

struct OnlineInfo {
    1: optional bool IsOnline,
    2: optional list<list<i32>> OnlineTime,
    255: optional base.BaseResp BaseResp,
}

struct IsSkillGroupInWorkingTimeRequest {
    1: required i64 SkillGroupID,
    255: optional base.Base Base,
}

struct IsSkillGroupInWorkingTimeResponse {
    1: required bool IsOnline,
    255: optional base.BaseResp BaseResp,
}

struct GetMcnInfoRequest {
    1: optional i64 McnID,// mcnid
    2: optional i64 AwemeID, // 抖音 id
    4: optional string AwemeUniqId,// 抖音号
    3: optional i64 TaskID, // 人工会话 id
    255: optional base.BaseResp BaseResp,
}


struct GetMcnInfoResponse {
    1: required string EntranceName, // 反馈入口
    2: required string McnName, // 机构名称
    3: required string MainName, // 主体名称
    4: required i64 AwemeID, // 抖音 id
    5: required string AwemeUniqId, // 抖音号
    6: required string Nickname, // 昵称
    7: required string Avatar, // 头像
    8: required string Status, // 签约状态
    9: required bool HasTag, // 是否含有万粉标签
    10: optional mcn_service.InviteStatusEnum InviteStatus, // 签约状态枚举
    255: optional base.BaseResp BaseResp,
}

struct GetUserPublishItemListRequest {
    1: required i64 UserID,
    2: optional string RealUserID,
    3: optional list<string> ItemIDS,
    4: optional i32 Count,
    5: optional string Cursor,
    255: optional base.Base Base,
}

struct GetUserPublishItemListResponse {
    1: optional string Data,
    2: optional list<AwemeItem> Items,
    3: optional bool HasMore,
    4: optional string Cursor,
    254: optional base.BaseResp BaseResp,
    255: optional base.Base Base,
}

struct AwemeItem {
    1: required string ID,
    2: required string Title,
    3: optional string PosterUrl,
    4: optional i32 Status,
    5: optional i64 CreateTime,
    6: optional string ReviewStatusDesc,
    7: optional string ItemID,
    8: optional string VideoID,
    9: optional string Score,
    10: optional i64 UserId,
}

struct ClearTaskTimerRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct ClearPigeonUnreadMsgRequest {
    1: required i64 ConversationShortID,
    255: optional base.Base Base,
}

struct ClearTaskTimerResponse {
    255: required base.BaseResp BaseResp,
}

struct ClearPigeonUnreadMsgResponse {
    255: required base.BaseResp BaseResp,
}

struct BatchSetAgentLoginStatusRequest {
    1: required list<i64> AgentIDList,
    2: required i32 LoginStatus, // 0 离线
    3: optional i64 AgentID, // 请求的客服ID（操作人）
    255: optional base.Base Base,
}

struct BatchSetAgentLoginStatusResponse {
    1: optional bool IsASync,       // 是否是异步执行任务
    2: optional list<i64> failedAgentIDS,
    255: required base.BaseResp BaseResp,
}

struct BatchGetMessagesRequest {
    1: required map<i64,list<i64>> TaskIDMapToMessageID,
    255: optional base.Base Base,
}

struct IMMessage {
    1: required i64 ServerMessageID,
    2: required string Content,
    3: required i32 MsgType,
    4: required i64 CreateTime,
}

struct BatchGetMessagesResponse {
    1: required map<i64,list<IMMessage>> TaskIDMapToMessage,
    2: optional list<i64> FailedTaskIDS,
    255: optional  base.BaseResp BaseResp,
}

struct GetTaskExtraReq {
    1: required i64 TaskID,

    255: optional base.Base Base,
}

struct TaskExtra{
    1: optional string Key,
    2: optional string Value,
    3: optional string Type,
}

struct GetTaskExtraResp {
    1: optional list<TaskExtra> Extra,

    255: optional base.BaseResp BaseResp,
}

struct BatchGetTasksFromEsRequest {
    1: required list<i64> IDS,
    2: optional string AgentId, // 发起查询操作的客服ID
    255: optional base.Base Base,
}

struct BatchGetTasksFromEsResponse {
    1: required list<ESTask> Tasks,
    255: optional base.BaseResp BaseResp,
}

struct ESTask {
    1: required i64 TaskID,
    2: required i64 ConversationShortID,
    3: required i64 UID, //用户ID
    4: required i64 StartTime, //开始转人工时间
    5: required i64 EndTime, //结束时间，如果没有则是0
    6: required i32 Status,//状态
    7: required i32 EndStatus, //结束方式
    8: required i32 AppID,  // 客服平台AppID
    9: required i32 Channel,  // 客服平台Channel
    10: optional i64 ParentTaskID,  // parent task id
    11: optional i64 SkillGroup,  // 技能组ID
    12: optional i64 AgentID,  // 客服ID
    13: optional i32 EndUser,  // 结束的用户
    14: optional i64 AssignTime,  // 分配客服的时间
    15: optional i64 CustomerFirstResponseTime,  // 客服首次回复时间
    16: optional i64 IMUserID,  // 客服平台用户ID
    17: optional string RealUserID,  // 业务用户ID
    18: optional list<i64> categoryIDs,  // 工单标签列表
    19: optional i64 AccessPartyId,  // 接入方id
    20: optional list<i64> ticketIDs, // 工单id
    21: optional list<string> ticketNums, // 工单编号
    22: optional bool IsReserve, //是否再次触达
    23: optional i16 TaskType, // 1-IM在线会话 2-离线留言
    24: optional string Token, // 加密token
}

struct GetLinkChatURLReq {
    1: required i64 TaskID,

    255: optional base.Base Base,
}

struct GetLinkChatURLResp {
    1: required string Url,

    255: optional base.BaseResp BaseResp,
}

struct GetPicVidOrderOfMessageRequest {
    1: required i64 TaskID,
    2: optional string UserEmail,
    3: optional string UserTenantID,

    255: optional base.Base Base,
}

struct GetPicVidOrderOfMessageResponse {
    1: optional list<PicAndVidItem> PicAndVidList,
    2: optional string OrderID,
    3: optional string CertificateID

    255: optional base.BaseResp BaseResp,
}

struct PicAndVidItem {
    1: required string Type, // 判断是图片还是视频
    2: required string Url, // 图片或视频 url
    3: required string ID, // 图片或视频 id
    4: optional bool UseImagex,
    5: optional string Thumnail, // 视频封面
    6: optional string Size, // 视频大小
}

struct CanReserveRequest {
    1: required i64 AgentID,
    2: required list<i64> TaskID,
    3: optional i64 AccessPartyID,
    255: optional base.Base Base,
}

struct CanReserveResponse {
    1: required bool CanReserve, //控制「再次触达」按钮是否展示（从门神获取）
    2: optional string Reason,
    255: optional base.BaseResp BaseResp,
}

struct ReserveRequest {
    1: required i64 AgentID,
    2: required list<i64> TaskID,
    3: optional i64 AccessPartyID,
    255: optional base.Base Base,
}

struct ReserveResponse {
    1: required bool IsReserved,
    2: required i64 TaskID,
    3: required string Reason,
    255: optional base.BaseResp BaseResp,
}

struct HiServiceABTestRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct HiServiceABTestResponse {
    1: required bool ABInfo,
    255: optional base.BaseResp BaseResp,
}

struct QueryTaskAndIntelligentMessagesRequest {
    1: required i64 TaskID,
    2: optional bool NeedDesensate,
    3: optional i64 AgentID,
    255: optional base.Base Base,
}

struct QueryTaskAndIntelligentMessagesResponse {
    1: required list<im_model.Message> Messages,
    255: optional base.BaseResp BaseResp,
}

struct GetABConfigRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetABConfigResponse {
	1: required string Data (go.tag = "json:\"data\"") // map[string]interface{}序列化后的json串
    255: optional base.BaseResp BaseResp,
}

struct GetTipsReq {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct TipTag {
    1: required string Name,        // 标签名称
    2: required string ID,          // 标签ID
}

struct GetTipsResponse {
    1: optional string SelectedOrderID,     // 客服当前选中的订单ID
    2: optional string OrderID,             // 最新识别到的订单ID
    3: optional list<TipTag> SelectedTag,   // 客服选中的问题标签
    4: optional list<TipTag> Tag,           // 最新识别到的问题标签
    5: optional string AlgoResult,          // 识别到的算法结果
    6: optional list<ies_cs_sop_engine_api.ProcessDescription> ProcessList,         // 识别到的SOP列表
    7: optional string Abstract,            // 算法识别的摘要
    8: optional string SolutionKey,         // sop or faq id
    9: optional string SolutionName,
    10: optional string Intent,             // 意图(诉求)
    11: optional string Question,           // 问题
    12: optional map<string, string> Extra, // extra信息
    13: optional bool IsFeatureSop,         // 是否命中sop
    14: optional string FeatureID,
    15: optional string FeatureName,
    255: optional base.BaseResp BaseResp,
}

struct SetTipsReq {
    1: required i64 TaskID,                 // taskID
    2: optional string OrderID,             // 客服选中的订单ID
    3: optional list<TipTag> Tag,           // 客服选中的问题标签
    4: optional string OperationType,       // 操作类型（该值用过打点使用，后端直接透传）
    255: optional base.Base Base,
}

struct SetTipsResponse {
    255: optional base.BaseResp BaseResp,
}

struct GetTaskNoticeReq {
    1: required i64 TaskID,

    255: optional base.Base Base,
}

struct GetTaskNoticeResponse {
    1: required i64 FromAccessPartyID,
    2: required string FromAccessPartyName,
    3: required i64 FromSkillGroupID,
    4: required string FromSkillGroupName,
    5: required i64 FromAgentID,
    6: required string FromAgentName,
    7: required i64 AccessPartyID,
    8: required string AccessPartyName,
    9: required i64 SkillGroupID,
    10: required string SkillGroupName,
    11: required i64 AgentID,
    12: required string AgentName,
    13: required string TransferRemark,
    14: optional i32 TransferRemarkType,

    255: optional base.BaseResp BaseResp,
}

struct StashTaskRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct StashTaskResponse {
    255: optional base.BaseResp BaseResp,
}

struct Conversation {
    1: required i64 TaskID,
    2: required i64 ConversationID,
    3: required AgentTask Task,
    4: optional i64 BaseConversationID,
}

struct GroupChatConversation {
    1: required i64 ConversationID,
    2: optional i64 BaseConversationID,
}

struct AgentTask {
    1: required i64 AccessPartyID,
    2: required string AssignTime,
    3: required i64 CustomerID,
    4: required i64 EndStatus,
    5: required string EndTime,
    6: required i64 EndUser,
    7: required string RealUserID,
    8: required i64 SkillGroupID,
    9: required string StartTime,
    10: required i64 Status,
    11: required i64 TaskType,
    12: required i64 UserID,
}

struct GetAgentConversationsRequest {
    1: required i64 TaskType,
    2: required i64 Page,
    3: required i64 PageSize,
    4: required i64 AgentID,
    5: required i64 Cursor,
    6: optional i64 FinishTimeStart,
    7: optional i64 FinishTimeEnd,
    8: optional i64 Status,
    9: optional list<i64> StatusList,
    10: optional list<i64> EndStatusList,
    11: optional i64 IsPractice,
    255: optional base.Base Base,
}

struct GetAgentConversationsResponse {
    1: required list<Conversation> Conversations,
    2: required i64 Total,
    255: optional base.BaseResp BaseResp,
}

struct GetGroupChatConversationsRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetGroupChatConversationsResponse {
    1: required list<GroupChatConversation> Conversations,
    2: required i64 Total,
    255: optional base.BaseResp BaseResp,
}

struct GetUserConversationsRequest {
    1: required string RealUserID,   // 业务用户ID
    2: required i64 CreateStartTime, // 会话创建时间
    3: required i64 CreateEndTime,   // 会话结束时间
    4: optional i32 Page,            // 分页数，默认1
    5: optional i32 PageSize,        // 每页数量，默认/最大均为100
    6: optional i64 AccessPartyID,   // 接入方过滤
    7: optional string AgentId,      // 发起查询操作的客服ID
    8: optional i64 ConversationShortID,
    9: optional i64 ShopID,
    10: optional bool IsUsBdee,
    11: optional string PartnerId
    12: optional bool IsPartnerIsolation

    255: optional base.Base Base,
}

struct GetUserConversationsResponse {
    1: required list<UserConversation> Conversations, // 用户会话列表
    2: required i64 Total,                            // 总记录数
    3: optional i64 AccessPartyID // 接入方id
    255: optional base.BaseResp BaseResp,
}

struct UserConversation {
    1: required i64 TaskID,                  // 人工会话id
    2: required i64 SkillGroupID,            // 技能组id
    3: required i64 CustomerID,              // 分配客服id，若未分配则为0
    4: required i64 EnqueueTimeTimeStamp,    // 进入排队时间，单位秒
    5: required list<i64> HistoryTaskIDList, // 历史流转会话id，从首次进线到最新进线的完整列表，按流转顺序排序；可以用list最后一个元素是否与当前会话taskID相等来判断当前会话是否最新一次进线；此list长度至少为1（必包含当前会话）
    6: required list<i64> TicketTagIDs,      // 会话工单标签id
    7: optional string Token,                // 加密token
}

// 单据选择器
struct ReceiptSelector {
    1: optional string Type,
    2: optional string CustomType,
    3: optional string Text, // 按钮文案
    4: optional string GuideText, // 引导文案
}

struct GetDoFilterRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    3: required i64 UserID,
    4: required i64 ConversationShortID,
    5: required i64 QuestionID,
    6: required string TransType,
    7: required i64 QuestionSkillGroupID,
    8: optional i64 IntelligentTaskID,
    9: optional string QuestionDescription, // 用户问题描述
    10: optional string ServiceProgressID,// 服务进度ID
    11: optional AlgorithmRoute Algorithm
    12: optional map<string,string> Extra,
    255: optional base.Base Base,
}


struct AlgorithmRoute  {
	1: optional string SkillName
	2: optional i64 Confidence
	3: optional double Score
	//1: 有订单且订单没有售后单
	//0. 有订单且订单存在售后单
	//-1: 没有订单
	4: optional string HasProcessType0
}

struct GetDoFilterResponse {
    1: required i64 SkillGroupID,
    2: required list<i64> OverflowSkillGroupIDS,
    3: required i32 OverflowThreshold,
    4: optional i32 TaskType,
    5: optional i32 EnableLeaveMessage, // 是否开启转人工前展示问题描述弹窗
    6: optional i32 EnableReceiptSelector, // 是否开启单据选择器
    7: optional list<ReceiptSelector> ReceiptSelectors, // 单据选择器列表
    8: optional string GuideText,
    9: optional map<string,string> Extra// 扩展字段
    255: optional base.BaseResp BaseResp,
}

struct CanLogoutRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct CanLogoutResponse {
    1: required bool CanLogout,
    2: required string Reason,
    255: optional base.BaseResp BaseResp,
}

struct IsTaskUserOnlineRequest {
    1: required i64 TaskID,
    255: optional base.Base Base,
}

struct IsTaskUserOnlineResponse {
    1: required bool IsOnline,
    255: optional base.BaseResp BaseResp,
}

struct ReportUserActionRequest {
    1: required i32 AppID,
    2: required i32 Channel,
    3: required i64 IMUserID,
    255: optional base.Base Base,
}

struct ReportUserActionResponse {
    255: optional base.BaseResp BaseResp,
}

enum FeatureSourceType {
    INTEllIGENT = 1; // 智能客服
}

struct Feature {
    1: required string Key,
    2: required string value,
}

struct UpdateSessionAssociatedDataRequest {
    1: required FeatureSourceType SourceType, // 调用方类型
    2: required i64 SessionID,
    3: required list<Feature> Features,
    255: optional base.Base Base,
}

struct UpdateSessionAssociatedDataResponse {
    255: optional base.BaseResp BaseResp,
}

struct ReserveWithMessageRequest {
    1: required i64 TaskID,
    2: required i64 Channel, // 渠道，目前枚举：工单-1 服务进度-2 工单(满意度平台)-3 服务进度(满意度平台)-4
    3: required string Type, // 触达类型，目前枚举："unfinished_ticket"-未完结工单, "finished_service_progress"-已完结服务进度
    4: optional map<string, string> Ext

    255: optional base.Base Base,
}

struct ReserveWithMessageResponse {
    1: required string Reason
    255: optional base.BaseResp BaseResp,
}

struct GetAgentConversationRelationInfoRequest {
    1: required i64 AgentID,
    255: optional base.Base Base,
}

struct GetAgentConversationRelationInfoResponse {
    1: required map<i64, RelationInfo> RelationInfo, // 会话列表
    255: optional base.BaseResp BaseResp,
}

struct RelationInfo {
    1: optional ies_kefu_pigeon_communication.ConversationInfo ShopConversationInfo,
}

struct GetFriendListRequest {
    1: required i64 AgentID,

    255: optional base.Base Base,
}

struct GetFriendListResponse {
    1: optional list<FriendInfo> Friends,

    255: optional base.BaseResp BaseResp,
}

struct FriendInfo {
    1: required i64  UserID,
    2: required i64  AgentID,
    3: required i64  ConversationID,
    4: required i64  TaskID,
    5: required bool IsBind,
    6: required i64  BaseConversationShortID,
}


struct BindFriendWithAgentEmailRequest {
    1: required string Email,
    2: required i64 BizID,
    3: required string UserID,
    4: required i64 AccessPartyID,


    255: optional base.Base Base,
}

struct BindFriendWithAgentEmailResponse {
    1: required bool IsSuccess,
    2: required string Reason,
    3: optional bool IsNewCreate,

    255: optional base.BaseResp BaseResp,
}

struct GetUserInfoRequest {
    1: required i64 UserID,
    2: required i64 AgentID,
    3: required i64 TaskID,
    4: required i64 ConversationShortID,
    5: optional string RealUserID,

    255: optional base.Base Base,
}

struct GetUserInfoResponse {
    1: required string UserID,
    2: required string Name,
    3: required string AvatarURL,
    4: required string Extra,
    5: required string ExtraList,
    6: required TaskInfo Task,
    7: required string UserCenterReq,
    8: required map<string, bool> UserTags,
    9: optional string Country,

    255: optional base.BaseResp BaseResp,
}

struct UnBindFriendWithAgentEmailRequest {
    1: required string Email,
    2: required i64 BizID,
    3: required string UserID,
    4: required i64 AccessPartyID,


    255: optional base.Base Base,
}

struct UnBindFriendWithAgentEmailResponse {
    1: required bool IsSuccess,
    2: required string Reason,

    255: optional base.BaseResp BaseResp,
}

struct TaskInfo {
    1: required i64 SkillGroupId,
    2: required string SkillGroupName,
    3: optional i64 AccessPartyId,
}

struct CancelStashedTaskRequest {
    1: required i64 TaskID,
    2: required i64 AgentID,

    255: optional base.Base Base,
}

struct CancelStashedTaskResponse {
    255: optional base.BaseResp BaseResp,
}

// 分单降级
struct ListQueueTaskRequest {
    1: required i64 Offset,  // 排队中 TaskId 起始位置
    2: required i64 Limit,   // 每次返回的 Task 数量
    3: optional i64 SkillGroupId,
    4: optional i64 TaskType, // 分单侧TaskType

    255: base.Base Base,
}

struct ListQueueTaskResponse {
    1: optional map<i64, i64> QueueTaskList, // 排队中的 taskId map，key => taskId，val => skillGroupId
    2: optional i64 Offset, // List中最小的 TaskId

    255: base.BaseResp BaseResp,
}

struct MatchTaskToAgentRequest {
    1: required i64 TaskId,  // 匹配到的 taskId
    2: required i64 AgentId,   // 匹配到的 agent Id
    3: required i64 SkillGroupId // 所在技能组
    4: required i64 TaskType // 分单侧TaskType
    255: base.Base Base,
}

struct MatchTaskToAgentResponse {
    255: base.BaseResp BaseResp,
}


struct TriggerPersonalTaskAutoCloseRequest {
    1: required i64 TaskID,
    2: required i64 TargetTimeStamp,

    255: optional base.Base Base,
}
struct TriggerPersonalTaskAutoCloseResponse {
    255: optional base.BaseResp BaseResp,
}

struct IsPersonalCustomerConversationRequest {
    1: required i64 ConversationShortID,

    255: optional base.Base Base,
}
struct IsPersonalCustomerConversationResponse {
    1: required bool IsPersonal,
    2: optional i64 PersonalCustomerTaskID

    255: optional base.BaseResp BaseResp,
}

struct GetSkillGroupTransferConfRequest {
    1: required i64 TaskID,

    255: optional base.Base Base,
}
struct GetSkillGroupTransferConfResponse {
    1: required i64 TransferNum,
    2: required string TransferTips,
    3: required bool CanTransfer,

    255: optional base.BaseResp BaseResp,
}

struct GetCanTransferAgentsRequest {
    1: required i64 TaskID,
    2: required common.TransferAgentReqFromEnum ReqFrom,
    255: optional base.Base Base,
}
struct GetCanTransferAgentsResponse {
    1: required list<TransferAgent> Agents,
    255: base.BaseResp BaseResp,
}
struct TransferAgent {
    1: required i64 ID,
    2: required string UniqueName,
    3: required string Email,
    4: required string Avatar,
    5: required bool CanTransfer,
    6: required string TransferTips,
}

struct Transfer2agentRequest {
    1: required i64 TaskID,    // 流转taskID
    2: required i64 AgentID,   // 流转客服
    3: required string Remark, // 流转备注
    4: required common.TransferAgentReqFromEnum ReqFrom,
    5: optional i32 RemarkType,// 流转备注类型
    255: optional base.Base Base,
}
struct Transfer2agentResponse {
    255: base.BaseResp BaseResp,
}
struct ContactUserRequest {
    1: required i64 AccessPartyID
    2: optional i64 AgentID
    3: optional i64 SkillGroupID
    4: optional i64 IntelligentTaskID
    5: optional map<string, string> Ext
    255: optional base.Base Base
}

struct ContactUserResponse {
    1: optional i64 TaskID
    2: optional bool IsReserved
    3: optional string Reason
    255: optional base.BaseResp BaseResp,
}

struct IsContactUserAvailableRequest {
    1: required i64 AccessPartyID
    2: optional i64 AgentID
    3: optional i64 SkillGroupID
    4: optional i64 IntelligentTaskID
    5: optional map<string, string> Ext
    255: optional base.Base Base
}

struct IsContactUserAvailableResponse {
    1: required bool Available
    2: optional string Reason
    255: optional base.BaseResp BaseResp,
}

struct GetCurrentTaskFeaturesByConversationRequest {
    1: required i32 AppID
    2: optional i32 Channel
    3: optional i64 UserID
    4: optional i64 ConversationShortID
    255: optional base.Base Base
}

struct GetCurrentTaskFeaturesByConversationResponse {
    1: required i64 TaskID
    2: optional map<string, string> Features
    255: optional base.BaseResp BaseResp
}

struct GetSessionLastBindingByTaskRequest {
    1: required i64 TaskID
    255: optional base.Base Base
}

struct GetSessionLastBindingByTaskResponse {
    1: optional map<string, string> Binding
    255: optional base.BaseResp BaseResp
}

struct CloseVoipTaskRequest {
    1: required string VoipUniqID
    2: optional i64 TaskID
    255: optional base.Base Base
}

struct CloseVoipTaskResponse {
    255: optional base.BaseResp BaseResp
}

struct MarkVoipTaskTransferRequest{
    1: required i64 TaskID
    255: optional base.Base Base,
}
struct MarkVoipTaskTransferResponse {
    255: optional base.BaseResp BaseResp
}

struct VoipTaskTransferRequest{
    1: required i64 TaskID
    2: required string UniqueID
    3: required i64 AgentID
    255: optional base.Base Base,
}
struct VoipTaskTransferResponse {
    1: required i64 NewTaskID
    255: optional base.BaseResp BaseResp
}

struct ClearVoipTaskRequest {
    1: required i64 ConversationShortID
    2: optional string VoipUniqID
    255: optional base.Base Base
}
struct ClearVoipTaskResponse {
    1: required i64 ClearCount
    255: optional base.BaseResp BaseResp
}

struct TaskOrder {
    1: required i32 Stage // 1 智能阶段，2 人工阶段
    2: required string From // user: 用户，agent: 客服，system: 系统
    3: required string OrderID
}

struct GetTaskOrdersRequest {
    1: required string TaskID
    2: optional i32 Stage // 1 智能阶段，2 人工阶段，0 表示所有
    255: optional base.Base Base
}

struct GetTaskOrdersResponse {
    1: optional list<TaskOrder> Orders // 按照时间排序
    255: optional base.BaseResp BaseResp
}

struct AppIdChannel {
    1: required string AppIDAndChannel // appId-channel
    2: required string ChannelName
}
struct ListAppIdChannelRequest {
    255: optional base.Base Base
}

struct ListAppIdChannelResponse {
    1: required list<AppIdChannel> ChannelList
    255: optional base.BaseResp BaseResp
}

struct ShouldShowOfflineTabRequest {
    1: required i64 AgentID
    255: optional base.Base Base
}

struct ShouldShowOfflineTabResponse {
    1: required bool Show
    255: optional base.BaseResp BaseResp
}

struct SendMessageByTaskIDRequest {
    1: required i64 TaskID
    2: required i32 MsgType
    3: required string Content
    4: optional map<string, string> Ext
    5: optional string StrategyKey
    6: optional string SendUniqueKey
    255: optional base.Base Base
}

struct SendMessageByTaskIDResponse {
    1: optional i64 MessageID
    2: optional i64 ConversationShortID
    255: optional base.BaseResp BaseResp
}

struct GetHelpdeskSettingsConfigRequest {
    1: required string Namespace //配置的命名空间
    2: required string Module    //配置的模块
    3: required i64 UniqueType   //唯一键类型，客服类型为0，使用其他类型请联系zhangyanjun.666
    4: required i64 UniqueID     //唯一键ID
    255: optional base.Base Base
}

struct GetHelpdeskSettingsConfigResponse {
    1: required string Config //json; 具体配置，是直接覆盖的。如果需要merge应用层自己实现
    255: optional base.BaseResp BaseResp
}

struct UpsertHelpdeskSettingsConfigRequest {
    1: required string Namespace //配置的命名空间
    2: required string Module    //配置的模块
    3: required i64 UniqueType   //唯一键类型，客服类型为0，使用其他类型请联系zhangyanjun.666
    4: required i64 UniqueID     //唯一键ID
    5: required string Config    //json; 具体配置，是直接覆盖的。如果需要merge应用层自己实现
    255: optional base.Base Base
}

struct UpsertHelpdeskSettingsConfigResponse {
    255: optional base.BaseResp BaseResp
}

struct SearchLatestArtificialTaskIDsByFeatureRequest {
    1: required string FeatureKey
    2: required string FeatureValue
    255: optional base.Base Base
}

struct SearchLatestArtificialTaskIDsByFeatureResponse {
    1: required list<i64> TaskIDs
    255: optional base.BaseResp BaseResp
}

struct DelayEvent {
    1: required string EventType
    2: required string EventKey
    3: optional string Payload
    4: optional map<string,string> Ext
}

struct TriggerDelayEventRequest {
    1: optional DelayEvent event
    255: optional base.Base Base
}

struct TriggerDelayEventResponse {
    255: optional base.BaseResp BaseResp
}

struct GetSuggestionsRequest {
    1: optional i64 TaskID
    2: optional i64 AgentID
    3: optional i64 Cursor
    4: optional i64 MsgID
    5: optional string InputText
    255: optional base.Base Base
}

struct GetSuggestionsResponse {
    1: optional list<SuggestionsItem> Suggestions
    255: optional base.BaseResp BaseResp
}

struct GetSuggestionsNewRequest {
    1: optional i64 TaskID
    2: optional i64 AgentID
    3: optional i64 Cursor
    4: optional i64 MsgID
    5: optional string InputText
    255: optional base.Base Base
}

struct GetSuggestionsNewResponse {
    1: optional list<TagSuggestions> Suggestions
    255: optional base.BaseResp BaseResp
}

struct TagSuggestions {
    1: required string TagName
    2: required list<TagSuggestionsItem> Replies
}

struct TagSuggestionsItem {
    1: optional string Content
    2: optional map<string,string> Ext
    3: optional list<string> Keywords
}

struct SuggestionsItem {
    1: optional string Content
    2: optional list<SuggestionsTag> Tags
    3: optional map<string,string> Ext
    4: optional list<string> Keywords
}

struct SuggestionsTag {
    1: optional string Key
    2: optional string Name
}

struct TriggerTaskEsUpdateRequest {
    1: optional i64 TaskID
    2: optional i64 ConversationShortID
    255: optional base.Base Base
}

struct TriggerTaskEsUpdateResponse {
    255: optional base.BaseResp BaseResp
}

struct GetGuideConfigRequest {
    1: required string Namespace
    2: required string Scene
    255: optional base.Base Base
}

struct GetGuideConfigResponse {
    1: required list<GuideConfig> Guide
    255: optional base.BaseResp BaseResp
}

struct GuideConfig {
    1: optional string Title
    2: optional string Key
    3: optional bool Enabled
    4: optional i64 Priority
    5: optional list<GuideInfo> Guides
}

struct GuideInfo {
    1: optional string Title
    2: optional string Key
    3: optional i64 Priority
    4: optional list<string> Modules
    5: optional list<GuideContent> Content
    6: optional RangeConfig Range
}

struct RangeConfig {
    1: optional list<i64>  AgentIds
    2: optional list<i64>  BlockId
    3: optional list<i64>  AccessPartyIds
    4: optional list<i64> SkillGroupIds
    5: optional list<i64>  ExcludeSkillGroupIds
}

struct GuideContent {
    1: optional string Title
    2: optional string Content
    3: optional string Image
    4: optional string Webp

}

struct GetTaskTransferDetailRequest {
    1: required i64 TaskID
    255: optional base.Base Base
}

struct GetTaskTransferDetailResponse {
    1: optional i64 ParentTaskID
    2: optional i64 ParentTaskSkillGroupID
    3: optional string TransferRemark
    4: optional i64 TransferType
    5: optional i64 TransferNum
    6: optional bool CanTransfer
    7: optional bool EnableDefaultSkillGroup
    8: optional list<i64> DefaultSkillGroupIDs
    9: optional i64 SkillGroupID
    10: optional i32 TransferRemarkType,
    255: optional base.BaseResp BaseResp
}

struct AnalyzeMessageHistoryRequest {
    1: required i64 ConversationShortID
    2: required i32 Limit
    3: optional i64 StartMillis
    4: optional i64 EndMillis
    5: optional list<string> AnalyzeList
    255: optional base.Base Base
}

struct AnalyzeMessageHistoryResponse {
    1: optional map<string,string> ResultMap
    255: optional base.BaseResp BaseResp
}

struct EnableStructuralRequest {
    1: optional i64 TaskID
    2: optional i64 AgentID
    3: optional i32 TaskType
    4: optional i32 TaskScene
    5: optional string Extra
    255: optional base.Base Base
}

struct EnableStructuralResponse {
    1: required bool Enable
    255: optional base.BaseResp BaseResp
}

struct StartTaskXPlusYTimerRequest {
    1: required i64 TaskID
    2: required bool StartX
    3: required bool StartY
    255: optional base.Base Base
}

struct StartTaskXPlusYTimerResponse {
    255: optional base.BaseResp BaseResp
}

struct StartTaskXPlusYTimerPureRequest {
    1: required i64 TaskID
    2: required bool StartX
    3: required bool StartY
    255: optional base.Base Base
}

struct StartTaskXPlusYTimerPureResponse {
    255: optional base.BaseResp BaseResp
}

struct QueryMarkInfoRequest {
    1: required i64 TaskID
    2: optional i64 MsgID
    3: optional string Entitytype
    4: optional string Flag
    255: optional base.Base Base
}

struct MarkInfo {
    1: optional string infoKey
    2: required string infoName
    3: required string infoValue
}

struct QueryImMarkInfoResponse {
    1: optional list<MarkInfo> infos
    2: optional list<MarkInfo> cots
    255: optional base.BaseResp BaseResp
}


struct ProcessOrdersRequest {
    1: required i64 Uid,
    2: optional i32 AppID,
    3: optional i64 TaskID,
    4: optional string OrderID,
    255: optional base.Base Base
}

struct ProcessOrdersResponse {
    1: optional list<OrderRecord> OrderRecordList
    255: optional base.BaseResp BaseResp
}

struct OrderRecord {
    1: optional i32 AppID
    2: optional i64 Uid
    3: optional i64 ConversationShortID
    4: optional i64 TaskID
    5: optional string OrderID
    6: optional string UserIP
}

struct TransferToHumanReq {
    1: required i64 TaskId,
    2: required list<string> ReasonOptions,
    3: required string Note,

    255: optional base.Base Base
}

struct TransferToHumanResp {
    255: optional base.BaseResp BaseResp
}

struct ProcessImTaskReq {
    1: required i64 TaskId,
    2: required i32 FromState
    3: required i32 ToState
    4: required i64 AccessPartyID
    5: required i64 SkillGroupID
    6: required i64 AgentID
    7: optional bool IsTransfer
    8: optional i32 EndUser //完结方式

    255: optional base.Base Base
}

struct ProcessImTaskResp {
    255: optional base.BaseResp BaseResp
}

struct ModifyL4BotManageMessageReq {
    1:  required i64 ConversationShortId,
    2:  required i64 TaskId,
    3:  required i64 MessageId,
    4:  required i64 UserId
    5:  required string Ext,
}

struct ModifyL4BotManageMessageResponse {
    255: optional base.BaseResp BaseResp
}

struct ModifyMessageExtReq {
    1:  required i64 ConversationShortId,
    2:  required i64 TaskId,
    3:  required i64 MessageId,
    4:  required i64 UserId
    5:  required map<string, string> Ext,
}

struct ModifyMessageExtResponse {
    255: optional base.BaseResp BaseResp
}

struct GetTransferStatisticRequest{
    1: required i64 AgentID
    2: required i64 skillGroupId
    255: optional base.Base Base
}

struct GetTransferStatisticResponse{
    1: required TransferStatistic AgentStatistic
    2: required TransferStatistic SkillGroupStatistic
    255: optional base.BaseResp BaseResp
}

struct TransferStatistic{
    1: required i64 FirstTransCount
    2: required i64 SecondTransCount
    3: required double TransferRate
}


struct QueryFormatedMessagesRequest {
   1: required i64 TaskID,      // 任务ID
   2: optional i64 QuerySize,   // 对话轮次
   3: optional i64 MaxLen,      // 单句话最大长度
   255: required base.Base Base,
}

struct QueryFormatedMessageResponse {
   1: required string Content,
   255: optional base.BaseResp BaseResp,
}

struct IMConversationCallbackRequest {
    1: required string Event,
    2: optional string JsonStr,

    255: optional base.Base Base
}

struct IMConversationCallbackResponse {

    255: optional base.BaseResp BaseResp
}

struct IMConversationDealNewRouteCallbackRequest {
    1: required i64 TaskID,
    2: required i64 AgentID,
    3: required i64 AccessPartyID,

    255: optional base.Base Base
}

struct IMConversationDealNewRouteCallbackResponse {

    255: optional base.BaseResp BaseResp
}

struct IMConversationAddParticipantCallbackRequest {
    1: required i64 TaskID,
    2: required i64 AgentID,
    3: required i64 AccessPartyID,
    4: required i64 Scene, // 1 进线 2 流转 3 再次触达

    255: optional base.Base Base
}

struct IMConversationAddParticipantResponse {

    255: optional base.BaseResp BaseResp
}

struct GetSkillGroupIDAndAgentForOpRequest {
    1: required i64 ConversationShortID,
    2: required string uuid,

    255: optional base.Base Base
}

struct GetSkillGroupIDAndAgentForOpResponse {
    1: required i64 SkillGroupID,
    2: required i64 AgentID,

    255: optional base.BaseResp BaseResp
}

struct GetStatisticalDataRequest{
    1: required i16 TimeZone,
    2: required i64 AgentID,
    255: optional base.Base Base
}

struct StatisticalData{
    1: required string Name,
    2: required i64 QueueCount,
    3: required i64 ReceiveCount,
    4: required i64 AgentTransferCount,
    5: required i64 AutoTransferCount,
    6: required i64 TotalTransferCount,
}

struct GetStatisticalDataResponse{
    1: required map<i64, StatisticalData> skillGroupIdToData,
    255: optional base.BaseResp BaseResp
}

struct AgentFirstReplyRequest{
    1: required i64 TaskID,

    255: optional base.Base Base
}

struct AgentFirstReplyResponse{
    255: optional base.BaseResp BaseResp
}

struct GetTaskDetailForListRequest{
    1: required i64 TaskID,

    255: optional base.Base Base
}

//直接返回一个json，json中包含task的详情
struct GetTaskDetailForListResponse{
    1: optional string TaskDetailJson

    255: optional base.BaseResp BaseResp
}

struct GetTaskDetailForHistoryRequest{
    1: required i64 TaskId,
    2: required i64 AgentId //坐席id

    255: optional base.Base Base
}

struct GetTaskDetailForHistoryResponse{
    1: optional string TaskDetailJson

    255: optional base.BaseResp BaseResp
}



struct GetValidCustomerSystemsRequest {
    1: required i64 TaskID,

    255: optional base.Base Base
}
struct GetValidCustomerSystemsResponse {
    1:   required list<GetValidCustomerSystemsLinkChatConfig> List
    255: optional base.BaseResp BaseResp
}

struct GetValidCustomerSystemsImage {
    1: required string Src
    2: required string Alt
}

struct GetValidCustomerSystemsLinkChatConfig {
    1: required GetValidCustomerSystemsImage Image
    2: required string name
    3: required string workStartTime
    4: required string workEndTime
    5: required string workEndTimePrefix
    6: required string description
    7: required string transferReply
    8: required i32 appID
    9: required i32 channel
    10: required string entrance
    11: required string url
    12: required string urlGenStrategy
    13: required i32 id
    14: required i32 businessLineID
    15: required string env
    16: required i32 channelID
    17: required i32 uidType
    18: required i32 douAppID
    19: required i32 source
    20: required bool needOriginUrl
    21: required string mockUrl
    22: required string accountType
    23: required string taskId
    24: required bool canTransToOtherBU
    25: required i64 accessPartyId // 接入方
    26: required bool asTransfer // 当做流转处理
    27: required i64 uniqueID // unique-ID，全局保持唯一
}


// PSM ies.helpdesk.im
service CustomerCloudService {
    // 消费分单后回调
    IMConversationDealNewRouteCallbackResponse IMConversationDealNewRouteCallback(1: IMConversationDealNewRouteCallbackRequest req)
   // 拉群后回调
    IMConversationAddParticipantResponse IMConversationAddParticipantCallback(1: IMConversationAddParticipantCallbackRequest req)
    // 获取群聊的技能组ID和坐席ID
    GetSkillGroupIDAndAgentForOpResponse GetSkillGroupIDAndAgentForOp(1: GetSkillGroupIDAndAgentForOpRequest req)


    // App映射相关
    GetBizTypeResponse GetBizType(1: GetBizTypeRequest req), //根据ID获取业务类型
    GetBizTypeResponse GetBizTypeByAppChannel(1: GetBizTypeByAppChannelRequest req), //根据AppID+Channel获取业务类型
    GetBizTypeResponse GetBizTypeByTaskID(1: GetBizTypeByTaskIDRequest req), //根据TaskID获取业务类型
    QueryBizTypesResponse GetBizTypeByAppChannels(1: GetBizTypeByAppChannelsRequest req), //根据AppID+多个Channel获取业务类型
    QueryBizTypesResponse QueryBizTypes(1: QueryBizTypeRequest req), //根据宿主AppID查询业务类型
    QueryBizTypesResponse QueryBizTypesByAccessPartyID(1: QueryBizTypesByAccessPartyIDRequest req), //根据接入方ID(AccessPartyID)查询所有业务类型
    QueryBizTypesResponse QueryBizTypesByAccessPartyIDNew(1: QueryBizTypesByAccessPartyIDRequest req), //根据接入方ID(AccessPartyID)查询所有业务类型(通过接入方获取入口列表，根据入口列表再查询业务类型)
    ListAllAppV2Response ListAllAppV2(1: ListAllAppV2Request req), // 获取所有使用 app 2.0 列表

    // 获取产品线列表
    GetProductListResponse GetProductList(1: GetProductListRequest req),

    //Task相关
    GetTaskResponse GetTask(1: GetTaskRequest req), //获取Task详情
    QueryTaskResponse QueryTasks(1: QueryTaskRequest req), //查询人工进线列表
    QueryTaskMessageResponse QueryTaskMessages(1: QueryTaskMessageRequest req), //根据TaskID查询聊天记录
    QueryLatestTaskResponse QueryLatestTask(1: QueryLatestTaskRequest req), // 根据conversationID查询最近一条会话
    QueryLatestTaskResponse QueryLatestValidTask(1: QueryLatestTaskRequest req), // 根据conversationID查询最近一条成功转人工的会话
    QueryTaskAndIntelligentMessagesResponse QueryTaskAndIntelligentMessages(1:QueryTaskAndIntelligentMessagesRequest req), // 根据TaskID查询智能及人工消息
    GetABConfigResponse GetABConfig(1: GetABConfigRequest req),  // 根据客服id获取ab配置

    //Misc
    QueryNoticesResponse QueryNotices(1: QueryNoticesRequest req), //查询IM页面Nocie列表
    LineupConfirmResponse LineupInfo(1: LineupConfirmRequest req), //转人工二次确认信息

    // 排队
    GetWaitingInfoResponse GetWaitingInfo(1: GetWaitingInfoRequest req), //查询排队信息

    //GIP
    GipReplyResponse GipReply(1: GipReplyRequest req), // Gip运营回复用户时调用该 RPC，同步运营的回复到客服平台
    GipSyncUserMessageResponse GipSyncUserMessage(1: GipSyncUserMessageRequest req), // Gip收到用户消息时，通过该RPC将用户的消息同步至客服平台

    //消息
    GetUnreadMessageResponse GetUnreadMessage(1: GetUnreadMessageRequest req), // 获取用户未读的客服消息数
    GetOldMessagesByConversationResponse GetOldMessagesByConversation(1: GetMessagesByOldConversationRequest req), // 根据当前会话ID获取前会话的聊天记录
    GetOldConversationResponse GetOldConversation(1: GetConversationRequest req), // 根据当前会话ID获取对应的老会话ID
    GetPicVidOrderOfMessageResponse GetPicVidOrderOfMessage(1: GetPicVidOrderOfMessageRequest req), // 根据TaskID获取用户消息中的所有图片、视频以及最新的订单ID
    SendMessageByTaskIDResponse SendMessageByTaskID(1: SendMessageByTaskIDRequest req), // 根据TaskID发送消息

    //批量获取标签信息
    MGetMessageLabelsResponse MGetMessageLabels(1: MGetMessageLabelsRequest req),

    // 客服人员相关
    ListAgentByIDSResponse ListAgentByIDS(1: ListAgentByIDSRequest req),  // 根据 agent ids 批量获取客服人员信息(最大1000个)
    BatchSetWorkStatusByIDSResponse BatchSetWorkStatusByIDS(1: BatchSetWorkStatusByIDSRequest req), // 根据 agent ids 批量设置工作状态(最大1000个)
    GetAgentByIDResponse GetAgentByID(1: GetAgentByIDRequest req),  // 根据agent.id获取客服信息, new_admin_api/v3/i的rpc版本, 终态是全部调用rpc
    GetAgentIMCloudTokenResponse GetAgentIMCloudToken(1: GetAgentIMCloudTokenRequest req),  // 获取IM token

    // 挂起
    SuspendTaskResponse SuspendTask(1: SuspendTaskRequest request) // 挂起会话
    ContinueTaskResponse ContinueTask(1: ContinueTaskRequest request) // 取消挂起

    IsSensitiveMessagesResponse IsSensitiveMessages(1: IsSensitiveMessagesRequest req),
    GetBlockHistoryMessagesResponse GetBlockHistoryMessages(1: GetBlockHistoryMessagesRequest req),

    GetGuidanceResponse GetGuidance(1: GetGuidanceRequest request)
    SetGuidanceVisitedResponse SetGuidanceVisited(1: SetGuidanceVisitedRequest request)

    // 接入方相关
    ListAgentAccessPartyResponse ListAgentAccessParty(1: ListAgentAccessPartyRequest req)

    // 拉取人工会话的IM用户
    GetTaskIMUserDetailResponse GetTaskIMUserDetail(1: GetTaskIMUserDetailRequest req)

    // 获取客服在线时间信息
    GetOnlineInfoByAppIdResponse GetOnlineInfoByAppId(1: GetOnlineInfoByAppIdRequest req)

    // 判断技能组是否在工作时间
    IsSkillGroupInWorkingTimeResponse IsSkillGroupInWorkingTime(1: IsSkillGroupInWorkingTimeRequest req)

    // 获取 mcn 信息
    GetMcnInfoResponse GetMcnInfo(1:GetMcnInfoRequest req)

    // 获取用户作品列表
    GetUserPublishItemListResponse GetUserPublishItemList(1: GetUserPublishItemListRequest req)

    // 清除会话的定时器(超时自动结束等)
    ClearTaskTimerResponse ClearTaskTimer(1: ClearTaskTimerRequest req)

    // 清除jumanji未读消息
    ClearPigeonUnreadMsgResponse ClearPigeonUnreadMsg(1: ClearPigeonUnreadMsgRequest req)

    // 批量获取消息内容
    BatchGetMessagesResponse BatchGetMessages(1: BatchGetMessagesRequest req)

    // 获取 task 的一些信息，如： entrance、技能组
    GetTaskExtraResp GetTaskExtra(1: GetTaskExtraReq req)

    // 批量更新客服登陆状态
    BatchSetAgentLoginStatusResponse BatchSetAgentLoginStatus(1: BatchSetAgentLoginStatusRequest req)

    // 批量获取task
    BatchGetTasksFromEsResponse BatchGetTasksFromEs(1: BatchGetTasksFromEsRequest req)

    // 获取 link chat 链接
    GetLinkChatURLResp GetLinkChatURL(1:GetLinkChatURLReq req)

    //是否可以再次触达
    CanReserveResponse CanReserve(1:CanReserveRequest req)

    //是否可以再次触达
    ReserveResponse Reserve(1:ReserveRequest req)

    //Hi服务AB实验
    HiServiceABTestResponse HiServiceABTest(1:HiServiceABTestRequest req)

    // 锦囊
    GetTipsResponse GetTipsResult(1:GetTipsReq req)
    SetTipsResponse SetTipsResult(1:SetTipsReq req)

    //获取会话扩展信息
    GetTaskNoticeResponse GetTaskNotice(1:GetTaskNoticeReq req)
    // 暂存
    StashTaskResponse StashTask(1:StashTaskRequest req)
    // 获取客服的会话列表
    GetAgentConversationsResponse GetAgentConversations(1: GetAgentConversationsRequest req)

    // 获取客服的会话列表
    GetGroupChatConversationsResponse GetGroupChatConversations(1: GetGroupChatConversationsRequest req)
    // 获取C端用户的会话列表
    GetUserConversationsResponse GetUserConversations(1: GetUserConversationsRequest req)
    // 获取路由结果
    GetDoFilterResponse GetDoFilter(1: GetDoFilterRequest req)

    // 是否可以交班
    CanLogoutResponse CanLogout(1:CanLogoutRequest req)

    // 判断task对应用户是否在线
    IsTaskUserOnlineResponse IsTaskUserOnline(1: IsTaskUserOnlineRequest req)
    // 上报用户行为，用于判断用户是否在线
    ReportUserActionResponse ReportUserAction(1: ReportUserActionRequest req)

    // 更新随路数据
    UpdateSessionAssociatedDataResponse UpdateSessionAssociatedData(1: UpdateSessionAssociatedDataRequest req)


    // 主动向用户推送消息
    ReserveWithMessageResponse ReserveWithMessage(1:ReserveWithMessageRequest req)

    // 获取taskrelationinfo
    GetAgentConversationRelationInfoResponse GetAgentConversationRelationInfo(1: GetAgentConversationRelationInfoRequest req)

    // GetFriendList
    GetFriendListResponse GetFriendList(1: GetFriendListRequest req)

    // BindFriend
    BindFriendWithAgentEmailResponse BindFriendWithAgentEmail(1: BindFriendWithAgentEmailRequest req)

    // UnBindFriend
    UnBindFriendWithAgentEmailResponse UnBindFriendWithAgentEmail(1: UnBindFriendWithAgentEmailRequest req)

    // 专属客服会话自动关闭
    TriggerPersonalTaskAutoCloseResponse TriggerPersonalTaskAutoClose(1: TriggerPersonalTaskAutoCloseRequest req)

    // 判断Conversation是否绑定专属客服Task
    IsPersonalCustomerConversationResponse IsPersonalCustomerConversation(1: IsPersonalCustomerConversationRequest req)

    // 获取用户信息
    GetUserInfoResponse GetUserInfo(1: GetUserInfoRequest req)
    // 取消暂存
    CancelStashedTaskResponse CancelStashedTask(1: CancelStashedTaskRequest req)

    // 手动获取排队中的 task，降级场景使用
    ListQueueTaskResponse ListQueueTask(1: ListQueueTaskRequest req)

    // 手动分配 task，降级场景使用
    MatchTaskToAgentResponse MatchTaskToAgent(1: MatchTaskToAgentRequest req)
    // 获取技能组流转的相关信息
    GetSkillGroupTransferConfResponse GetSkillGroupTransferConf(1: GetSkillGroupTransferConfRequest req)

    GetCanTransferAgentsResponse GetCanTransferAgents(1: GetCanTransferAgentsRequest req)
    Transfer2agentResponse Transfer2agent(1: Transfer2agentRequest req)
    // 主动触达接口 (注意不是再次触达, 概念不同 两个接口)
    ContactUserResponse ContactUser (1: ContactUserRequest req)

    // 判断当前状态是否可以主动触达
    IsContactUserAvailableResponse IsContactUserAvailable (1: IsContactUserAvailableRequest req)

    // 获取当前Conversation对应的Task信息
    GetCurrentTaskFeaturesByConversationResponse GetCurrentTaskFeaturesByConversation(1: GetCurrentTaskFeaturesByConversationRequest req)

    // 根据Task获取Session最后的绑定信息
    GetSessionLastBindingByTaskResponse GetSessionLastBindingByTask (1: GetSessionLastBindingByTaskRequest req)

    // Voip挂断
    CloseVoipTaskResponse CloseVoipTask(1: CloseVoipTaskRequest req)
    // 标记Voip转接
    MarkVoipTaskTransferResponse MarkVoipTaskTransfer(1: MarkVoipTaskTransferRequest req)
    // Voip转接
    VoipTaskTransferResponse VoipTaskTransfer(1: VoipTaskTransferRequest req)
    // Voip会话清理
    ClearVoipTaskResponse ClearVoipTask(1: ClearVoipTaskRequest req)

    // 根据Task获取订单列表
    GetTaskOrdersResponse GetTaskOrders(1:GetTaskOrdersRequest req)

    // 获取appId 和 channel列表
    ListAppIdChannelResponse ListAppIdChannel(1:ListAppIdChannelRequest req)

    // 是否有处理中的离线留言
    ShouldShowOfflineTabResponse ShouldShowOfflineTab(1:ShouldShowOfflineTabRequest req)

    // 通用工作台配置 - 获取配置
    GetHelpdeskSettingsConfigResponse GetHelpdeskSettingsConfig(1:GetHelpdeskSettingsConfigRequest req)

    // 通用工作台配置 - 修改/创建配置
    UpsertHelpdeskSettingsConfigResponse UpsertHelpdeskSettingsConfig(1:UpsertHelpdeskSettingsConfigRequest req)
    // 根据特征搜索最新的人工Task
    SearchLatestArtificialTaskIDsByFeatureResponse SearchLatestArtificialTaskIDsByFeature(1:SearchLatestArtificialTaskIDsByFeatureRequest req)

    // 延迟队列回调
    TriggerDelayEventResponse TriggerDelayEvent(1: TriggerDelayEventRequest req)

    // 输入联想
    GetSuggestionsResponse GetSuggestions(1: GetSuggestionsRequest req)

    // 话术推荐
    GetSuggestionsNewResponse GetSuggestionsNew(1: GetSuggestionsNewRequest req)

    // 触发会话ES更新
    TriggerTaskEsUpdateResponse TriggerTaskEsUpdate(1: TriggerTaskEsUpdateRequest req)

    GetGuideConfigResponse GetGuideConfig(1: GetGuideConfigRequest req)

    // 会话流转详细信息
    GetTaskTransferDetailResponse GetTaskTransferDetail(1: GetTaskTransferDetailRequest req)

    // 聊天记录分析
    AnalyzeMessageHistoryResponse AnalyzeMessageHistory(1: AnalyzeMessageHistoryRequest req)

    // 工作台是否启动服务结构化
    EnableStructuralResponse EnableStructural(1: EnableStructuralRequest req)

    // 为人工会话启动x+y
    StartTaskXPlusYTimerResponse StartTaskXPlusYTimer(1: StartTaskXPlusYTimerRequest req)

    // 启动x+y纯净版
    StartTaskXPlusYTimerPureResponse StartTaskXPlusYTimerPure(1: StartTaskXPlusYTimerPureRequest req)

    // 获取Info信息
    QueryImMarkInfoResponse QueryImMarkInfo(1: QueryMarkInfoRequest req)

    // 处理订单ID列表
    ProcessOrdersResponse ProcessOrderRecords(1: ProcessOrdersRequest req)

    // L4托管转人工
    TransferToHumanResp TransferToHuman(1: TransferToHumanReq req)

    // 工单会话完结时回调im处理
    ProcessImTaskResp ProcessImTask(1: ProcessImTaskReq req)

    // L4托管修改消息
    ModifyL4BotManageMessageResponse ModifyL4BotManageMessage(1: ModifyL4BotManageMessageReq req)

    // 修改消息ext
    ModifyMessageExtResponse ModifyMessageExt(1: ModifyMessageExtReq req)

    // 获取流转数据统计
    GetTransferStatisticResponse GetTransferStatistic(1: GetTransferStatisticRequest req)

    // 格式化会话内容 财经
    QueryFormatedMessageResponse QueryFormatedMessages(1: QueryFormatedMessagesRequest req)

     // 会话服务回调
    IMConversationCallbackResponse IMConversationCallback(1: IMConversationCallbackRequest req)

    // 获取客服的相关统计数据
    GetStatisticalDataResponse GetAgentStatisticalData(1: GetStatisticalDataRequest req)

    //更新Task首响时间
    AgentFirstReplyResponse AgentFirstReply(1:AgentFirstReplyRequest req)

    // 获取可流转外部系统
    // new_admin_api 服务 /v1/conversations/valid-customer-systems 接口的rpc版
    GetValidCustomerSystemsResponse GetValidCustomerSystems( 1:GetValidCustomerSystemsRequest req)

    // 获取task列表页详情，时间紧任务重，只能从customer_cloud获取
    GetTaskDetailForListResponse GetTaskDetailForList(1: GetTaskDetailForListRequest req)

    // 获取历史会话详情
    GetTaskDetailForHistoryResponse GetTaskDetailForHistory(1: GetTaskDetailForHistoryRequest req)
}
