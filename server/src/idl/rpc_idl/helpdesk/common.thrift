include "../base.thrift"

namespace go helpdesk.common.common
namespace py helpdesk.common.common
namespace java helpdesk.common.common

enum HelpdeskType {
    Artificial = 1,  // 人工客服
    Intelligent = 2, // 智能客服
    Feedback = 3,    // 用户反馈
}

enum SensitiveReqFrom {
    ResourceProxy = 1,  // 资源代理服务
}

// 用户体系枚举
enum BusinessIDEnum {
    AWEME_SHOP_TALENT     = 10101, // dy电商达人
    HOTSOON_SHOP_TALENT   = 10102, // 火山电商达人
    XIGUA_SHOP_TALENT     = 10103, // 西瓜电商达人
    TOUTIAO_SHOP_TALENT   = 10104, // 头条电商达人
    FEISHU_SHOP_TALENT    = 10105, // 飞书电商达人
    FEISHU_ECOM_SHOP      = 10106, // 飞书电商商家
    ECOM_COMPASS         = 10107, // 抖店罗盘PC
    BUYIN_SHOP_MECHANISM  = 10111, // 百应机构
    AWEME_ENTERPRISE     = 10112, // dy企业号PC
    
    MP_XIGUA_CREATOR    = 10201, // MP西瓜创作者
    MP_TOUTIAO_CREATOR  = 10202, // MP头条创作者
    XIGUA_CREATOR      = 10211, // 西瓜创作者
    
    AURORA  = 10301, // 极光
    
    AWEME  = 10401, // dy客服业务
    
    UNION  = 10501, // 3700 直播开放平台

    ECOM_SHOP = 10601, // 3513 channel 1,2 电商商家
    ECOM_SHOP_GUEST = 10602, //电商商家游客

    LIVE_RECHARGE_WAP     = 10701, // 直播官网充值H5
    LIVE_RECHARGE_WEB     = 10702, // 直播官网充值PC

    VIDEO_CUT = 10801, // 剪映
}


// 用户账号组体系枚举
enum AccountEnum {
    Aweme     = 1, // dy
    Toutiao   = 2, // 头条
    Hotsoon     = 3, // 火山
    WebcastPlatform = 4, // 直播开放平台 uid
    Shop = 5,// 电商商服
    FaceU = 6, // faceu
    Aurora = 7, // 小荷
    SearchRefinement = 8, //
    Phone = 9, // 手机号
    Email = 10, // 邮箱
    Guest = 11, // 游客
    SellerCode = 12, // 货主code,一个货主供应多个shop
    Buyin = 51, // 百应机构、达人
    MP = 52, // gip 创作者
    AwemeOpenPlatform = 53, // 开放平台
    EcomCompass = 54, // 罗盘
    Ep = 55,// 学浪
    EpGuest = 56, // 学浪游客
    Daren = 57, // 达人（头西达人和抖火达人，通过是否拥有达人权限判断）
    DeviceID = 60, // 设备 id
    UserID = 62, // 用户ID，无 appID ，所以需要遍历所有可能的
    DecryptPhone = 63,// 加密电话号码
    Xigua = 70,// 西瓜
    MusicPlatform = 71,// 方舟音乐平台
    GamePlayFun = 72,// 游戏摸摸鱼
    Hotline = 73, // 热线工作台
    Ocean = 74, // 商业化ID
    AwemeUniqueID = 75 // dy号、头条号
    MainOcean = 76, // 主账号商业化ID
    LifeAccount = 77, // dy来客 新商家 account id
    LifeUID = 78, // dy来客 新商家 uid 废弃
    Mechanism = 79, // 机构
    AdvID = 80, // 广告主 id
    Outsider = 81, // 沸寂
    Novel = 82 ,// 小说业务
    Super = 83, // 皮皮虾
    QQ = 84 // qq
    BDCrmSeller = 86 // bd crm 员工 id
    OGC = 87 //OGC开放平台
    WebcastPlatformAccount = 90, // 直播开发平台 工会ID
    Partner = 91, // 服务商行业 id
    PartnerGuest = 92, // 服务商游客ID
    BSMAccount = 93
    EcomMember = 94 //商家组织下成员 id
    EcomOrg = 95 // 商家组织 id
    LifeOrg = 96  // 本地生活达人机构 Id
    Retouch = 171 // 醒图 aid:2515
    DouHot = 100 // 热点宝
    MaskPhone = 101
    Seller = 200
    GlobalSeller = 210
    Oec = 211
    Tiktok = 212
    Deliverer = 233 // 即时零售-骑手id
    Novelread = 234 // 红果短剧
    Globalfinance = 300 // 国际化支付、财经
    GlobalSellingUnSettled = 301 // gs 未入驻
    GlobalSellingSettled = 302 // gs 已入驻
    IdentifyCode         = 304 //身份证号
    QWUser = 400
    RetouchGuest = 401 // Retouch醒图游客
    TokoBuyer = 402 // Toko买家
    FanqieKol = 413 // 番茄达人
    VolcEngine = 414 // 火山引擎
    VolcEngineMain = 415 //火山引擎主账号
    VolcEngineChild = 416 // 火山引擎子账号
    VolcEngineGuest = 417 // 火山引擎游客
    CatBox = 420 // 猫箱
    XiGuaVideoUnLogin = 421 // 西瓜视频-未登录进线
    Test = 10000

}

struct User {
    1: required string UserID,
    2: required AccountEnum AccountType,
    3: optional map<string,string> Extra,
}

struct HumanAgentSystemSetting {

}
struct ImOptionSetting {
    1: required string ApiUrl,
}
struct FrontierOptionSetting {
    1: required i32 Aid,
    2: required i32 Fpid,
    3: required i32 Service,
    4: required string AppKey,
}
struct AppSetting  {
    1: required string UserInfoURL,
    2: required HumanAgentSystemSetting HumanAgentSystem,
    3: required ImOptionSetting ImOption,
    4: required FrontierOptionSetting FrontierOptionSetting,
}

struct HelpdeskContext {
    1: required i32 AppID,
    2: required i64 TicketID,
    3: required i64 ConversationShortID,
    4: required i64 TaskID,
    5: optional i64 HelpdeskConversationLogID,
    6: optional bool AgentIsOnline, //客服是否在线
}

struct HelpdeskConversationLog {
     1:  required i64 ID,
     2:  required i32 AppId,
     3:  required i64 UserId,
     4:  required i32 channel, //渠道
     5:  required i32 CurrentSubSystem, // 当前处理的子系统，0:初始值 1:智能客服 2:人工客服
     6:  required i32 Status, //进线状态，0:进行中，1:已结束
     7:  optional i64 ConversationShortId, //会话id
     8:  optional i64 IntelligentBeginMillis, // 智能客服开始时间(毫秒)
     9:  optional i64 IntelligentResolveMillis, // 智能客服结束时间
     10: optional i64 IntelligentTaskId, // 智能客服taskID
     11: optional i64 TransferToArtificialMillis, //转人工客服的时间
     12: optional i64 ArtificialTaskId, //人工客服taskID
     13: optional i64 ArtificialResolveMillis, //人工客服结束时间
     14: optional i64 CreateMiilis, //创建时间
     15: optional string Extra, // 附加字段
     16: optional i64 TenantID // 租户ID
     17: optional i64 AccessPartyID // 接入方ID
}

struct ConversationLogExtra {
	1: optional i64 AccessPartyID (go.tag = "json:\"access_party_id\"")
	2: optional i64 VipLevel (go.tag = "json:\"vip_level\"")
	3: optional i64 CreatorLevel (go.tag = "json:\"creator_level\"")
	4: optional i64 FollowerCount (go.tag = "json:\"follower_count\"")
	5: optional i64 AllConsumptionAmount (go.tag = "json:\"all_consumption_amount\"")
	6: optional i32 IsOpen (go.tag = "json:\"is_open\"")
	7: optional i64 BotID (go.tag = "json:\"bot_id\"")
	8: optional string RuntimeData (go.tag = "json:\"runtime_data\"")
	9: optional i64 IsCriticalComplaint (go.tag = "json:\"is_critical_complaint\"")
	10: optional i64 AnchorType (go.tag = "json:\"anchor_type\"")
	11: optional string OrderID (go.tag = "json:\"order_id\"")
	12: optional string Entrance (go.tag = "json:\"entrance\"")
}

enum TransferAgentReqFromEnum {
    REQ_FROM_MONITOR = 1 // 场控
}

enum SuspendOperationEnum{
    System = 1 // 系统
    Agent = 2 // 客服
}
