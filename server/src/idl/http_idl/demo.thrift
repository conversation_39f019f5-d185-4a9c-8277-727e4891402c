include "./base.thrift"
namespace js ies.fe.route_manage_i18n

struct QueryBizTypesByAccessPartyIDRequest {
  1: required string AccessPartyId,
}
// 组件类型
enum FieldOptionType {
    //单选
    SINGLE_CHOOSE = 1;
    //多选
    MULTI_CHOOSE = 2;
    //混合(多选+输入框)
    MIX = 3;
    //时间控件
    DATE = 4;
    //输入框
    INPUT = 5;
    //批量输入
    BATCH_INPUT = 6;
    //树
    TREE = 7;
    //级联
    CASCADER = 8;
    //多行文本
    TEXT = 9;
    //数字
    INT = 10;
    //常量
    CONSTANT = 11;
    //富文本
    RICH_TEXT = 12;
    //多语言文本
    MULTI_LANG = 13;
    // 时间点类型
    DATE_TIME_POINT = 14;
    //条件级联下拉列表
    CONDITION_CASCADER_LIST = 16;
    // 浮点数输入框
    INPUT_FLOAT = 17;
    //树状结构-只选择当前节点不选择子节点
    TREE_TO_LIST = 701;
}
struct BizType {
    1: required i32 ID, // ID
    2: required i32 AppID, //客服平台AppID
    3: required string AppName, //客服平台App Name
    4: required i32 Channel, // 客服平台Channel
    5: required string ChannelName,//客服平台 Channel Name
    6: required i64 HostAppID, // 宿主AppID
    7: required string HostAppName,//宿主App name
    8: required i64 AppBaseID, //标签应用ID
    9: required i64 ResourceID, //资源ID
    10: required i64 SubResourceID, //子资源ID
    11: required i64 AccessPartyID,//接入方ID
    12: optional i64 Scene,//场景
    13: optional string EntranceId,//入口ID
    14: optional string EntranceName,//入口名
}

struct QueryBizTypesByAccessPartyIDResponse {
  1: required i32 code = 0
  2: required list<BizType> data
  3: required string message
}



struct GetSkillGroupsByAgentIdRequest {
  1: required list<string> AccessPartyIds,
  2: optional ChannelType channelType
}
//操作渠道
enum ChannelType {
  IM = 1, //IM
  TICKET = 2, //工单
  PHONE = 3, //电话
  ADMIN = 4, //管理员
  ECOM_TICKET = 5, //电商工单
  BUZZ = 6, //BUZZ工单
  FEEDBACK = 7, // FEEDBACK
  QUALITY_CHECK = 8 // 质检
  IM_OFFLINE_SESSION = 9,//IM离线留言会话
  ACCESS_CALL = 10//触达外呼
  CALLBACK = 11 // 预约回呼
  AFTER_SALE = 12 // 售后
}
struct SkillGroup {
  1: i64 ID,
  2: i64 TenantId,
  3: list<i64> AccessPartyId,
  4: string Name,
  5: ChannelType ChannelType,
  6: i32 MaxTaskNum,
  7: list<i64> QuestionCategoryIds,
  8: list<i64> CategoryIds,
  9: i64 CreatedBy,
  10: string CreatedAt,
  11: i64 UpdatedBy,
  12: string UpdatedAt,
}

struct GetSkillGroupsByAgentIdResponse {
  1: required i32 code = 0
  2: required list<SkillGroup> data
  3: required string message
}



struct GetCardRequest {
  1: required string AccessPartyId,
  2: list<i64> AppIds,      //应用ID列表
}
struct CardQuestionThrift {
  1: required i64 Id,                 //问题ID
  2: required i64 CardId,             //卡片ID
  3: required string QuestionName,    //问题内容
  4: required i64 SkillGroupId,       //技能组ID
}
struct AppQuestionCardThrift {
  1: required i64 Id,                 //卡片ID
  2: required i64 TenantId,           //租户ID
  3: required i64 AccessPartyId,      //接入方ID
  4: required i64 AppId,              //应用ID
  5: required string CardName,        //卡片名称，用于title展示
  6: required i32 IsOpen,             //是否启用
  7: list<CardQuestionThrift> CardQuestions,//问题列表，list顺序代表C端展示顺序
}

struct GetCardResponse {
  1: required i32 code = 0
  2: required list<AppQuestionCardThrift> data  //问题卡片列表
  3: required string message
}



struct AddCardQuestion {
  1: required string QuestionName,    //问题内容
  2: required i64 SkillGroupId,       //技能组ID
}
struct AddAppQuestionCard {
  3: required i64 AppId,              //应用ID
  4: required string CardName,        //卡片名称，用于title展示
  5: list<AddCardQuestion> AddCardQuestions,//问题列表，list顺序代表C端展示顺序
}
struct CreateCardRequest {
  1: required AddAppQuestionCard AddAppQuestionCard,    //不带主键ID的问题卡片对象
  2: required string AccessPartyId,
}
struct CreateCardResponse {
  1: required i32 code = 0
  2: required string message
}


struct UpdateCardQuestion {
    1: optional i64 Id,                 //问题ID
    2: required string QuestionName,    //问题内容
    3: required i64 SkillGroupId,       //技能组ID
}


struct UpdateAppQuestionCardThrift {
  1: required i64 Id,                 //卡片ID
  2: required i64 TenantId,           //租户ID
  3: required i64 AccessPartyId,      //接入方ID
  4: required i64 AppId,              //应用ID
  5: required string CardName,        //卡片名称，用于title展示
  6: list<UpdateCardQuestion> UpdateCardQuestions,//问题列表，list顺序代表C端展示顺序，更新跟创建结构相同，用于兼容同时又问题增删的情况
  7: optional string CardDisplayName, //卡片展示名称 新版必传 向下兼容
}
struct UpdateCardRequest {
  1: required UpdateAppQuestionCardThrift UpdateAppQuestionCardThrift,    //带有主键ID的问题卡片对象
  2: required string AccessPartyId,
}
struct UpdateCardResponse {
  1: required i32 code = 0
  2: required string message
}



//卡片操作类型
enum HandleType {
  OPEN = 1,
  CLOSE = 2,
  DELETE = 3,
}
struct HandleCardRequest {
  1: required i64 CardId,             //卡片ID
  2: required HandleType HandleType,  //操作类型
  3: required string AccessPartyId,
}
struct HandleCardResponse {
  1: required i32 code = 0
  2: required string message
}



struct SubResource{
  1: optional i64 Id,
  2: string Name,
  6: i32 EnableFlag,//是否可用
}
struct Resource{
  1: optional i64 Id,
  2: string Name,
  4: string DockingName,//对接人名称
  5: i64 AccessPartyId,//接入方id
  6: i32 SubFlag,//是否开启子资源 0-未开启 1-开启
  7: i32 EnableFlag,//是否可用
  8: list<SubResource> SubList
}
struct GetResourceListRequest {
  1: required string AccessPartyId,
}
struct GetResourceListResponseData {
  1: list<Resource> ResourceList,
}
struct GetResourceListResponse {
  1: required i32 code = 0
  2: required GetResourceListResponseData data
  3: required string message
}



struct Category{
  1: optional i64 Id,//标签id 新增为0
  2: optional i64 ResourceId,//资源id
  3: optional i64 SubResourceId,//子资源id
  4: string Name,//标签名称
  5: optional string Path,//标签全路径
  6: optional i64 ParentId,//父标签id
  7: optional i32 Level,//层级
  8: i32 OrderIndex,//当前排序
  9: optional list<Category> SubCategoryList,//子标签集合
  10: optional bool IsBindApp//是否绑定app
  11: optional i32 EnableFlag,//
}
struct GetCategoryListRequest {
  1: required i64 ResourceId,//资源id
  2: required i64 SubResourceId,//子资源ID
}
struct GetCategoryListResponseData {
  1: list<Category> CategoryList,
}
struct GetCategoryListResponse {
  1: required i32 code = 0
  2: required GetCategoryListResponseData data
  3: required string message
}



struct FieldValueItem {
  1: required string name,
  2: required i32 value,
}
struct GetFieldValuesRequest {
  1: required i64 FieldId,
  2: required i32 OperatorId,
  3: required string AccessPartyId,
  4: optional list<string> AppIds,
  5: optional string Operator,
}
struct GetFieldValuesResponseData {
  1: required i32 FieldValueType,
  2: required list<FieldValueItem> FieldValueList,
  3: required string FieldValues,
}
struct GetFieldValuesResponse {
  1: required i32 code = 0
  2: required GetFieldValuesResponseData data
  3: required string message
}


struct FieldCondition {
  1: required i64 FieldId,
  2: required string FieldDisplayName, //字段的前端展示名称
  3: required string FieldMapName, //字段的数据库存储名称
  4: required list<i32> OperatorIds,
  5: required map<string, list<GetFieldValuesResponseData>> Fieldvalues,
}
struct GetFieldListRequest {
  1: required string AccessPartyId,
  2: required string EventId,
  3: optional list<string> AppIds,
}
struct GetFieldListResponse {
  1: required i32 code = 0
  2: required list<FieldCondition> data
  3: required string message
}



//规则任务状态
enum RuleTaskStatus {
  HAS_RUNNING = 1,//运行中
  NO_RUNNING = 0,//无运行中
}
//规则禁用状态
enum RuleStopStatus {
  DISABLED = 1,//禁用
  USING = 0,//启用
}
//规则状态
enum EntityStatus {
  ENABLE = 1,//可用
  UNABLE = 0,//不可用
}
//单个字段条件
struct FilterUnit {
    1: optional i64 FieldId,
    2: required string FieldMapName,//todo 自定义字段前缀
    3: required i32 OperatorId,//todo 前端映射ID
    4: required string FieldValue,
}
//条件操作类型
enum FilterOperateType {
    UNION = 0,//求并集
    INTERSECTION = 1,//求交集
}
//条件
struct Filter {
    1: required FilterOperateType OperateType,
    2: required list<FilterUnit> FilterUnitList,
}
struct AdminRule {
  1: required i64 Id, //ID
  2: required string DisplayName, //名称
  3: required i32 Priority,  //优先级
  4: required Filter Filter, //条件
  5: required string Value, //动作信息 json, list查询时填充''，get查询时填充
  6: required RuleStopStatus StopStatus,  //禁用状态
  7: required EntityStatus Status,  //状态
  8: required i32 AppId,    //应用类型，如0：触发器、1：SLA、2：后台
  9: required i64 TenantId, //租户ID
  10: required i64 AccessPartyId, //接入方ID
  11: optional i64 SourceId,    //业务标识，区分工单、电商工单
  12: required string CreatedAt,  //创建时间
  13: required string UpdatedAt,  //更新时间
  14: required string Extra,  //附加信息
  15: required i64 CreatorAgentId,  //创建人ID
  16: required i64 UpdaterAgentId,  //更新人ID
  17: optional RuleTaskStatus TaskStatus,  //规则任务状态 get查询时填充
  18: optional string UpdaterAgentName,  //规则任务状态 get查询时填充
}
struct GetAdminRuleListRequest {
  1: required string AccessPartyId,
  2: optional string DisplayNameLike,
  3: required string EventId,
  4: required string SourceId,
  5: required i32 AppId,
  6: optional RuleStopStatus StopStatus,
}
struct GetAdminRuleListResponse {
  1: required i32 code = 0
  2: required list<AdminRule> data
  3: required string message
}

struct CreateAdminRuleRequest {
  1: required string AccessPartyId,
  2: required string DisplayName,
  3: required i32 Priority,
  4: required Filter Filter,
  5: required string Value,
  6: required string EventId,
  7: required string SourceId,
  8: required i32 AppId,
  9: optional CreateCardRequest CreateCardRequest
  10: optional bool Disable
}
struct CreateAdminRuleResponse {
  1: required i32 code = 0
  2: required string message
}


struct UpdateAdminRuleRequest {
  1: required string AccessPartyId,
  2: optional string DisplayName,
  3: optional i32 Priority,
  4: optional Filter Filter,
  5: optional string Value,
  6: required i64 Id,
  7: required string EventId,
  8: required string SourceId,
  9: required i32 AppId,
  10: optional RuleStopStatus StopStatus,
  11: optional UpdateCardRequest UpdateCardRequest //卡片的修改请求，未修改时可不传
}
struct UpdateAdminRuleResponse {
  1: required i32 code = 0
  2: required string message
  3: optional AdminRule data
}

struct DeleteAdminRuleRequest {
  1: required string AccessPartyId,
  2: required i64 Id,
  3: required string SourceId,
  4: required i32 AppId,
}
struct DeleteAdminRuleResponse {
  1: required i32 code = 0
  2: required string message
}

struct AdminRuleSimple {
  1: required i64 Id,
  2: required i32 Priority,  //优先级
}
struct BatchUpdateAdminRuleRequest {
  1: required string AccessPartyId,
  2: required list<AdminRuleSimple> AdminRules,
  3: required string EventId,
  4: required string SourceId,
  5: required i32 AppId,
}
struct BatchUpdateAdminRuleResponse {
  1: required i32 code = 0
  2: required string message
}

struct GetSLAAimMetaSimpleListRequest {
  1: optional i64 AccessPartyId //接入方ID
  2: optional i64 SourceId    //业务标识，区分工单、电商工单
}

struct SLAAimMetaSimple {
  1: required i64 Id, //ID
  2: required string Name, //名称 首响、完结
  3: required string Extra, //附加信息
}

struct GetSLAAimMetaSimpleListResponse {
    1: required i32 code = 0
    2: required string message
    3: required list<SLAAimMetaSimple> data
}
// 新增部分
//规则状态
enum RuleStatus {
    ENABLE = 1,//启用
    UNABLE = 0,//禁用
    DRAFT = 2,//草稿
    DELETE = 3,//已删除
}
// 规则优先级
struct RulePriority {
    1: required i64 Id, //ID
    2: required i32 Priority,  //优先级
}
// 规则生效环境
enum RuleEnv {
    PPE = 0,//PPE环境
    PROD = 1,//线上环境
}

//条件组合操作类型
enum OpGroup {
    AND = 1,//求交集
    OR = 2,//求并集
    NOT = 3, //求反计算
}
//运算型参数表达式
struct MathExpr {
    1: required string opMath, //数学运算符
    2: required Expr Lhs,
    3: required Expr Rhs,
}
//方法型参数表达式
struct FuncExpr {
    1: required string FuncName, //方法名
    2: optional map<string, Expr> ParamExprMap, //参数
}
//特征型参数表达式
struct FeatureExpr {
    1: required string FeatureName, //特征名，为字符串，如"ticket.status"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//参数表达式
struct Expr {
    1: optional list<Expr> ExprList, //元素可以为任意一种Expr
    2: optional list<Expr> SubstringList, //元素可以为任意一种Expr，用于拼成字符串
    3: optional MathExpr MathExpr,
    4: optional FuncExpr FuncExpr,
    5: optional FeatureExpr FeatureExpr, //特征型参数表达式，如"ticket.assignee_agent.status()"
    6: optional string VarExpr, //变量型参数表达式，如"$id"
    7: optional list<string> ConstantList, //元素可以为字符串，数字，布尔值中任意一种常量
    8: optional string Constant, //常量型参数表达式：字符串，如\"abc\"，\"300\"；数字，如100.1；布尔值，如true
}
//条件表达式
struct ConditionExpr {
    1: required string OpCheck,
    2: required Expr Lhs, //运算左值
    3: optional Expr Rhs, //运算右值
}
//条件组合
struct ConditionGroupExpr {
    1: required string OpGroup,
    2: optional list<ConditionExpr> Conditions,
    3: optional list<ConditionGroupExpr> ConditionGroups,//conditions和conditionGroups有且只有一个非空
}
//动作表达式
struct ActionExpr {
    1: required string ActionName, //动作名，为字符串，如"abc"
    2: optional map<string, Expr> ParamExprMap, //参数map，key为字符串，如"abc"
}
//动作组合
struct ActionGroupExpr {
    1: required bool Sequential,
    2: required bool ContinueOnFail,
    3: required list<ActionExpr> Actions,
}
//延时步骤
struct DelayStepExpr {
    1: required ActionGroupExpr ActionGroup;//即时动作
    2: required string FilterAim;//即时条件名，为字符串，如"abc"
    3: required Expr DelayTime;//延时参数表达式
}
//规则返回
struct AimExpr {
    1: optional list<DelayStepExpr> DelaySteps,//延时步骤
    2: optional ActionGroupExpr ActionGroup,//即时动作
    3: optional Expr ReturnValue,//路由返回 delaySteps/actionGroup/returnValue有且只有一个非空
}

//规则元数据
struct Rule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional ConditionGroupExpr Expression, //规则条件部分
    5: optional AimExpr ActionInfo,  //动作部分
    6: required i64 RuleGroupId,  //规则组id
    7: required RuleStatus Status,  //规则状态
    8: optional string Description, //规则描述
    9: required string CreatedAt,  //创建时间
    10: required string UpdatedAt,  //更新时间
    11: required string CreatorAgentId,  //创建人ID
    12: required string UpdaterAgentId,  //更新人ID
    13: required i32 DraftEditType, //草稿编辑类型 1-新增 2-编辑 0-未修改
    14: required string UpdaterAgentName // 更新人名字
}
// 创建规则
struct CreateRuleV2Request {
    1: required string EventKey, //事件key
    2: optional i64 RuleGroupId, //规则组ID
    3: required string DisplayName, //规则名称
    4: required i32 Priority, // 规则优先级
    5: required ConditionGroupExpr Expression, //条件-DSL
    6: required AimExpr ActionInfo,  //动作部分
    7: optional bool Enable, // 是否启用规则，不传默认为禁用
    8: optional string Extra, //扩展字段，前端可用于复现规则
    9: required string Description, //规则描述
    10: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    11: required i64 AccessPartyId,
    12: required string Version, // 接口版本 UI传v1
    14: optional map<string,string> ExtraInfo, // 额外信息
}
struct CreateRuleV2Response {
    1: optional Rule Rule,
    2: required i32 code = 0,
    3: required string message;
}

// 更新规则
struct UpdateRuleRequest {
    1: required i64 Id, //规则ID
    2: optional string DisplayName, //规则名称
    3: optional ConditionGroupExpr Expression, //条件-DSL
    4: optional AimExpr ActionInfo, //路由的结果部分，用于对结果的特殊处理，比如绑定技能组
    5: optional string Extra, //扩展字段，前端可用于复现规则
    6: optional string Description, //规则描述
    7: required string Version, // // 接口版本 UI传v1
    8: optional string PermCode // 门神权限点 code
    9: optional i32 Priority, // 规则优先级
    10: optional map<string,string> ExtraInfo, // 额外字段
}
struct UpdateRuleResponse {
    1: optional Rule Rule,
    2: required i32 code = 0,
    3: required string message;
}

// 启用/禁用/删除规则
struct UpdateRuleStatusV2Request {
    1: required list<i64> Ids, //规则ID的list
    2: required RuleStatus RuleStatus, //建RuleStatus定义
    3: required string Version, // // 接口版本 UI传v1
    4: optional string PermCode // 门神权限点 code
    5: optional bool Draft, //是否为草稿规则
    6: optional i64 ruleGroupId,
    7: optional i32 operateGroupAllRules, // 1 - 需要处理，0 -仅处理部分
}
struct UpdateRuleStatusV2Response {
  1: required i32 code = 0,
  2: required string message;
}

//调整规则优先级
struct UpdateRulePriorityV2Request {
    1: required list<RulePriority> Rules, //所有规则的优先级信息
    2: required string Version, // 接口版本 UI传v1
}
struct UpdateRulePriorityV2Response {
    1: optional i64 RuleGroupId, //调整优先级会生成新的规则组版本(不包括草稿)
    2: optional map<i64,i64> newRuleIds, //调整优先级会生成新的ruleId(不包括草稿)
    3: required i32 code = 0,
    4: required string message;
}

// 根据id获取规则
struct GetRuleByIdRequest {
    1: required i64 Id,
}
struct GetRuleByIdResponse {
    1: optional Rule Rule,
    2: required i32 code = 0,
    3: required string message;
}

//获取规则列表
struct GetRuleListV2Request {
    1: required string EventKey, //事件Key
    2: required i64 AccessPartyId, //接入方ID
    3: optional i64 RuleGroupId, //规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
    4: optional i32 IsDraft,//是否获取草稿箱里的规则1-是
    5: optional map<string,string> ExtraInfo, // 额外查询字段
    6: optional i32 Page,
    7: optional i32 PageSize,
}
struct GetRuleListV2Response {
    1: optional list<Rule> data, //规则
    2: required i32 code = 0,
    3: required string message;
    4: optional i32 existRulesIfNotFilter,
}
struct PublishRuleGroupV2Request {
    1: required i64 RuleGroupId, //规则组id
    2: optional string PermCode // 权限点
    3: optional list<string> RuleIds, //规则ID集合
    4: optional string eventKey,
}

struct PublishRuleGroupV2Response {
    1: optional i64 RuleGroupId, //新的规则组id
    2: optional map<i64,i64> newRuleIds, //发布会生成新的ruleId
    3: required i32 code = 0,
    4: required string message;
}

struct CopyRuleGroupV2Request {
    1: required i64 RuleGroupId, //规则组id
    // 2: required string UpdaterAgentId, // 更新人
}

struct CopyRuleGroupV2Response {
    1: required i32 code = 0,
    2: required string message;
}
struct GetRuleListV3Response{
  1: required i32 code = 0
}
struct GetRuleListV3Request {
  1: required string name = '1213'
}

//获取规则列表
struct GetRuleListV4Request {
    1: required string EventKey, //事件Key
    2: required i64 AccessPartyId, //接入方ID
    3: optional i64 RuleGroupId, //规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
    4: optional i32 IsDraft,//是否获取草稿箱里的规则1-是
    5: optional map<string,string> ExtraInfo, // 额外查询字段
    6: optional i32 Page,
    7: optional i32 PageSize,
    8: optional list<i32> statusList,  //状态，1-启动，0-禁用
    9: optional string ruleName, //规则名称
}
struct GetRuleListV4Response {
    1: optional list<Rule> data, //规则
    2: required i32 code = 0,
    3: required string message,
    4: optional i32 existRulesIfNotFilter,
}

struct Agent {
    1: required i64 ID,
    2: required i64 TenantId,
    3: required i32 WorkType,
    4: required string UserName,
    5: required string NickName,
    6: required i64 UserId,
    7: required string UUID,
    8: required string Email,
    9: required string Mobile,
    10: required i64 CompanyId,
    11: required list<ChannelType> ChannelTypes,
    12: required i32 Status,
    13: required i64 CreatedBy,
    14: required string CreatedAt,
    15: required i64 UpdatedBy,
    16: required string UpdatedAt,
    17: required string OperatorName,
    18: required i64 DepartmentId,
    19: required i32 ImMaxTaskNum,
    20: optional i64 PhoneSeatNo,
    21: optional string CountryRegion, // 国家地区
    22: optional i32 FeedbackMaxTaskNum,
    23: optional string NgccServiceLine,

    255: map<string, string> Extra, //目前extra里包含的字段 c_n_agent_appid(IM appId),
                                    // c_s_ecom_ticket_role(电商工单系统角色),
                                    // c_n_ecom_ticket_is_super(电商工单是否超级管理员)
                                    // c_s_ecom_ticket_kind(电商工单业务类型)
    }

struct CheckSkillGroupAutoAssign {
  1: optional bool autoAssign,
  2: optional list<Agent> unautoAssignAgents,
  3: optional i64 skillGroupAgentsCount,
  4: optional i64 skillGroupId
}
struct CheckSkillGroupAutoAssignRequest {
    1: required list<i64> skillGroupIdList,
}

struct CheckSkillGroupAutoAssignResponse {
    // 1: optional bool autoAssign,
    // 2: optional list<Agent> unautoAssignAgents,
    // 3: optional i64 skillGroupAgentsCount,
    1: optional list<CheckSkillGroupAutoAssign> checkSkillGroupAutoAssign
    2: required i32 code = 0,
    3: required string message;
}

enum ConditionUpdateType {
    OptionsUpdate = 1, //选项更新
    DELETE = 2, // 条件删除
    ADD = 3, // 条件新增
}

enum OperateRuleItemType {
    STAUTS = 1, // 更新状态
    NAME = 2, // 更新名字
    PRIORITY = 3, // 更新优先级
    CONDITION = 4, // 更新条件
    CONDITION_OPTIONS = 5, // 更新条件选项
    CONDITION_RELATION = 6, // 更新条件间关系
    CONDITION_GROUP_RELATION = 7, // 更新条件组间关系
    RETURN_VALUE = 8 // 更新规则返回值
    ROUTE_TYPE = 9, // 路由时机变更
    OVERFLOW = 10, // 溢出设置变更
}

enum RuleOperationType {
    CREATE_ENABLE = 1, // 创建规则并启用
    CREATE_DISABLE = 2, // 创建规则并禁用
    STATUS_CHANGE = 3, // 规则状态变更, 启用/禁用/发布
    PRIORITY_CHANGE = 4, // 规则优先级变更
    UPDATE = 5, // 规则内容变更
}

struct ConditionGroupChange {
    1: required i32 ConditionGroupOrder, // 条件组序号
    2: optional list<ConditionChange> ConditionChangeList, // 条件变更列表
    3: optional string Relation, // 条件关系变更后结果
    4: optional list<string> ConditionNameList, // 条件关系变更对应的条件列表
}

struct ConditionChange {
    1: required string ConditionName, // 条件名字
    2: required ConditionUpdateType ConditionUpdateType, // 条件更新类型
    3: optional list<string> AddOptions, // 条件选项新增
    4: optional list<string> DeleteOptions, // 条件选项删除
}

struct OperateRuleItemLog {
    1: required OperateRuleItemType OperateItemType,
    2: optional string BeforeValue,
    3: optional string AfterValue, //字符串或json结构
    4: optional list<ConditionGroupChange> ConditionGroupChangeList,
}


struct RuleOperationLog {
    1: required i64 Id,
    2: required RuleOperationType OperationType,
    3: optional list<OperateRuleItemLog> OperateRuleItemLogs,
    4: required string OperatorAgentId,
    5: required string CreatedAt,
}
struct GetRuleOperationLogsRequest {
    1: required i64 ruleId,
    2: required string eventId,
    3: required string accessPartyId
    4: required i32 page,
    5: required i32 pageSize,
}
struct BaseResp {
  1: string StatusMessage = "",
  2: i32 StatusCode = 0,
  3: optional map<string, string> Extra,
}
struct retutnValue{
  1: optional string value,
  2: optional i32 percent,
  3: optional string skillGroupName,
}
struct changeItem {
  1: optional string groupIndex,
  2: optional string conditionName,
  3: optional string updateValueType,
  4: optional string options,
  5: optional string updateConditionsType,
  6: optional i32 OperateItemType,
}
struct logList {
  1: optional string status,
  2: optional list<retutnValue> retutnValue,
  3: optional string priority,
  4: optional string name,
  5: optional string groupsRelationChange,
  6: optional i32 OperateItemType,
  7: optional list<changeItem> ConditionRelationChange,
  8: optional list<changeItem> ValueChange,
  9: optional list<changeItem> ConditionsChange,
}
struct RuleOperationLog {
  1: optional string agentId,
  2: optional string agentName,
  3: optional string title,
  4: optional string updateTime,
  5: optional list<logList> logList
}
struct GetRuleOperationLogsResponse {
    1: optional list<map<string,string>> RuleOperationLogList,
    2: optional list<map<string,string>> oldres,
    3: optional i64 totalCount,
    255: required BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartiesResponse {
    1: list<SkillGroup> SkillGroups,

    255: optional BaseResp BaseResp,
}

struct GetSkillGroupsByAccessPartiesRequest {
  1: required i64 TenantId,
  2: required list<i64> AccessPartyIds,
  3: optional ChannelType ChannelType,
}

// 问题匪类卡片rule
struct ClientRule {
    1: required i64 Id, //ID
    2: required string DisplayName, //名称
    3: required i32 Priority,  //优先级
    4: optional ConditionGroupExpr Expression, //规则条件部分
    5: optional AimExpr ActionInfo,  //动作部分
    6: required i64 RuleGroupId,  //规则组id
    7: required RuleStatus Status,  //规则状态
    8: optional string Description, //规则描述
    9: required string CreatedAt,  //创建时间
    10: required string UpdatedAt,  //更新时间
    11: required string CreatorAgentId,  //创建人ID
    12: required string UpdaterAgentId,  //更新人ID
    13: required i32 DraftEditType, //草稿编辑类型 1-新增 2-编辑 0-未修改
    14: required string UpdaterAgentName // 更新人名字
    15: optional AppQuestionCardThrift cardInfo
}

struct GetRuleGroupListByEventKeyRequest {
    1: required string EventKey,
    2: optional string RuleGroupDisplayName, //名称
    3: optional list<RuleStatus> RuleGroupStatus, //状态
    4: optional list<string> CreatorAgentId, //创建人
    5: optional list<string> CreatorTime, //创建时间
    6: optional list<string> UpdaterAgentId, //更新人
    7: optional list<string> UpdaterTime, //更新时间
    8: required i64 AccessPartId,
    9: optional i64 SkillGroupId,
    10: required i32 Page,
    11: required i32 PageSize,
}

struct GetRuleGroupListByEventKeyResponse {
    1: optional list<RuleGroupDetail> RuleGroupList, //规则组的list
    2: optional i32 Count,  // 数量
    3: required i32 code = 0,
    4: required string message,
}

// 规则组详情
struct RuleGroupDetail {
    1: required i64 RuleGroupId, // 规则组id - 跳转详情页使用
    2: required i64 OriginId, // 原始id
    3: required string RuleGroupDisplayName, // 名称
    4: required RuleStatus RuleGroupStatus, // 状态
    5: required i32 Version, // 版本号
    6: required list<AntlrRule> RuleList, // 规则集合
    7: optional i64 DraftRuleGroupId, // 草稿版本id - 跳转编辑页使用
    8: optional string CreatorAgentId, // 创建人
    9: optional string CreatorTime, // 创建时间
    10: optional string UpdaterAgentId, // 更新人
    11: optional string UpdaterTime, // 更新时间
    12: optional bool HaveReleaseVersion, // 是否有线上生效版本
    13: optional map<string,string> ExtraInfo, // 规则类型
}

// 规则
struct AntlrRule {
    1: required string DisplayName, //规则名称
    2: required i32 Priority, // 规则优先级
    3: required ConditionGroupExpr Expression, //条件-DSL
    4: required AimExpr ActionInfo,  //动作部分
    5: optional string CreatorAgentId, //创建人id
    6: optional string Description, //规则描述
    7: optional i64 Id  // 规则ID
    8: optional i64 OriginId    // 规则原始ID
    9: optional string Extra // 用于前端复现规则
}

struct GetSkillGroupsByTypeResquest {
    1: required i64 TenantId,
    2: optional ChannelType ChannelType,
    3: required i32 PageNo,
    4: required i32 PageSize,
    5: optional i64 AccessPartyId,
    6: optional list<ChannelType> ChannelTypes,
    7: optional i32 SkillGroupLevel,
    8: optional string SkillGroupName,
    9: optional bool OnlySimpleData, // 是否只需要返回技能组的简单信息，不包含问题标签等关联数据
    10: optional i64 BizLineEnum, // 业务线枚举
    11: optional bool ImOfflineSessionEnable, // 是否打开IM离线会话开关
    12: optional list<i64> AccessPartyIds,
    13: optional bool OnlyId, // 是否只返回技能组的ID以及技能组名称,只支持查询条件包含channelType和接入方ID的简单批量查询，如果需要按照SkillGroupLevel等字段进行筛选，请使用OnlySimpleData
    14: optional bool HasCode, // 是否只返回有技能Code的技能组

}

struct GetSkillGroupsByTypeResponse {
    1: list<SkillGroup> SkillGroups,
    2: i32 TotalSize,
    3: required i32 code = 0,
    4: required string message;

}

struct GetExtraInfoRequestV2 {
    1: required string Scenes,
    2: required string EventKey,
    3: optional list<string> ExtraKeys,
    4: optional i64 AccessPartId,
}

struct GetExtraInfoResponseV2 {
    1: optional list<RuleGroupRelationStruct> RuleGroupRelations
    2: required i32 code = 200,
    3: required string message,
}

struct RuleGroupRelationStruct {
    1: required i64 RuleGroupId,
    2: required map<string, string> extraInfos,
    3: required i64 OriginId
}

struct GetFieldListRequest {
  1: required string AccessPartyId,
  2: required string EventId,
  3: optional list<string> AppIds,
  3: optional string ZjOtherAccessPartyId,
}
struct GetFieldListResponse {
  1: required i32 code = 0
  2: required list<FieldCondition> data
  3: required string message
}

struct BatchCreateRuleGroupResponse {
    1: optional list<i64> RuleGroupIds,
    2: required i32 code = 0,
    3: required string message,
    
}

struct BatchCreateRuleGroupRequest {
    1: required list<CreateRuleGroupRequest> RuleGroups,
}
// 创建规则组
struct CreateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required string GroupDisplayName, //规则组名称
    3: required string CreatorAgentId, //创建人id
    4: required string Product, //产品
    5: required list<AntlrRule> RuleList, // 规则集合
    6: optional bool Enable, // 是否启用规则，不传默认为待发布（草稿），点保存-传false，发布传true
    7: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    8: required i64 AccessPartyId, //接入方ID
    9: optional list<ExtraInfoStruct> ExtraInfo,
    10: optional i64 SkillGroupId, //技能组
}

struct ExtraInfoStruct {
    1: required string DisplayName,
    2: required string Key,
    3: required i32 Status,
    4: required i32 Type,
    5: required string Value
}

struct GetWorkStatusConfigsRequest {
    1: required i64 TenantId,
    2: required ChannelType ChannelType,
    3: required i64 AccessPartyId,
}
struct GetWorkStatusConfigsResponse {
    1: list<WorkStatusConfig> WorkStatusConfigs,
    2: required i32 code = 0,
    3: required string message,
}
struct WorkStatusConfig {
    1: i64 TenantId, // 租户ID
    2: ChannelType ChannelType, // 坐席类型
    3: i64 AccessPartyId, // 接入方id
    4: i32 WorkStatus, // 工作状态值
    5: string WorkStatusDesc, // 工作状态描述
    6: bool Enabled, // 是否启用
    7: i32 ReceptionStatus, // 接线状态（0：不可接线；1: 可接线）
    8: i64 UpdaterAgentId,  //更新人id
    9: string updaterAgentName, //更新人名字
    10: string UpdatedAt,  // 更新时间
    11: optional string NGCCStatus, // 对应的ngcc的status
    12: optional bool NgccEnable, // 对应的ngcc电话外呼组件是否启用
    13: optional bool EnableConfig, // 是否可被配置（启用禁用）
}

// 启用/禁用/删除规则
struct UpdateRuleGroupStatusRequest {
    1: required i64 RuleGroupId, //规则组id
    2: required RuleStatus RuleStatus, // 状态
    3: required string UpdaterAgentId, // 更新人
}

// 结果
struct UpdateRuleGroupStatusResponse {
    1: optional bool Success, //成功失败
    2: required i32 code = 200,
    3: required string message,
}

struct BatchUpdateRuleGroupResponse {
    1: optional bool Success, //成功失败
    2: required i32 code = 0,
    3: required string message,
}

// 修改规则组
struct UpdateRuleGroupRequest {
    1: required string EventKey, //事件key
    2: required i64 RuleGroupId, //规则组ID
    3: required i64 OriginId, // 原始id
    4: required string GroupDisplayName, //规则组名称
    5: required i32 Version, // 版本号
    6: required string UpdaterAgentId, //创建人id
    7: required string Product, //产品
    8: required list<AntlrRule> RuleList, // 规则集合
    9: optional bool Enable, // 是否启用规则，不传默认为禁用
    10: optional RuleEnv RuleEnv, //规则生效环境，不传默认为线上
    11: required i64 AccessPartyId, //接入方ID
    12: optional RuleType RuleType,
    13: optional i64 SkillGroupId, //技能组id
}


enum RuleType {
    DOCUMENTARY_RULE   = 1  // 跟单规则
    GENERAL_RULE       = 2  // 通用规则
    SEND_DOWN          = 3  // 下送
    UPGRADE            = 4  // 升级
    FINISH             = 5  // 完结
}

struct BatchUpdateRuleGroupRequest {
    1: required list<UpdateRuleGroupRequest> RuleGroups,
    
}

struct SearchSamBindRequest {
    1: optional i64 sellerId, // 商家id
    2: optional string sellerCountryCode, // 国家码
    3: optional i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: optional i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: optional i64 operateAgentId, // 更新人id
    8: optional i64 updateTimeStart, // 更新时间-start
    9: optional i64 updateTimeEnd, // 更新时间-end
    10: optional i64 createTimeStart, // 创建时间-start
    11: optional i64 createTimeEnd, // 创建时间-end
    12: optional i64 pageNum, // 页码，从1开始
    13: optional i64 pageSize, // 每页的数量
    14: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct SearchSamBindData {
    1: required i64 sellerId, // 商家id
    2: optional string sellerImg, // 商家头像地址
    3: optional string sellerName, // 商家名称
    4: optional string imGroupName, // 绑定IM技能组名称
    5: optional string imAgentName, //  绑定IM客服名称
    6: optional string imAgentEmail, //  绑定IM客服邮箱
    7: optional string ticketGroupName, // 绑定工单技能组名称
    8: optional string ticketAgentName, //  绑定工单客服名称
    9: optional string ticketAgentEmail, // 绑定工单客服邮箱
    10: optional string note, // 备注
    11: optional string operateImg, // 操作人头像
    12: optional string operateName, // 操作人名字
    13: optional i64 updateTime, // 更新时间
    14: optional i64 createTime, // 创建时间
    15: optional i64 imGroupId, //  绑定IM技能组
    16: optional i64 imAgentId, //  绑定IM客服
    17: optional i64 ticketGroupId, //  绑定工单技能组
    18: optional i64 ticketAgentId, //  绑定工单客服
    19: optional i64 operateAgentId, // 更新人id
    20: optional string sellerCountry, // 更新人id
    21: optional i64 id, // 商家id
    22: optional string geo, // 合规区域


}
struct SearchSamBindResponse {
    1: required list<SearchSamBindData> searchSamBindDataList,
    2: required i64 totalSize,


    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}
struct CreateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct CreateSamBindRequest {
    1: required i64 sellerId, // 商家id
    2: required string sellerCountryCode, // 商家国家/地区
    3: required i64 imGroupId, //  绑定IM技能组
    4: required i64 imAgentId, //  绑定IM客服
    5: required i64 ticketGroupId, //  绑定工单技能组
    6: required i64 ticketAgentId, //  绑定工单客服
    7: optional string note, // 备注
    10: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct UpdateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct UpdateSamBindRequest {
    1: required i64 sellerId, // 商家id
    2: required string sellerCountryCode, // 商家国家/地区
    3: required i64 imGroupId, //  绑定IM技能组
    4: required i64 imAgentId, //  绑定IM客服
    5: required i64 ticketGroupId, //  绑定工单技能组
    6: required i64 ticketAgentId, //  绑定工单客服
    7: required i32 isDel, //  0 未删除 1删除
    8: optional string note, // 备注
    9: required i64 id, // id
    12: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchCreateSamBindResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchCreateSamBindRequest {
    1: required string excelUrl, // 文件地址
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchDelSamBindByExcelResponse {

    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchDelSamBindByExcelRequest {
    1: required string excelUrl, // 文件地址
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}



struct BatchTransferSamBindRequest {
    1: required list<i64> sellerIdList, // 商家id list
    2: required i64 imGroupId, //  绑定IM技能组
    3: required i64 imAgentId, //  绑定IM客服
    4: required i64 ticketGroupId, //  绑定工单技能组
    5: required i64 ticketAgentId, //  绑定工单客服
    255: optional base.Base Base,
}

struct BatchTransferSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchDelSamBindRequest {
    1: required list<i64> sellerIdList, // 商家id list
    4: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchDelSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct BatchExportSamBindRequest {
    1: optional i64 sellerId, // 商家id
    2: optional string sellerCountryCode, // 国家码
    3: optional i64 imGroupId, //  绑定IM技能组
    4: optional i64 imAgentId, //  绑定IM客服
    5: optional i64 ticketGroupId, //  绑定工单技能组
    6: optional i64 ticketAgentId, //  绑定工单客服
    7: optional i64 operateAgentId, // 更新人id
    8: optional i64 updateTimeStart, // 更新时间-start
    9: optional i64 updateTimeEnd, // 更新时间-end
    10: optional i64 createTimeStart, // 创建时间-start
    11: optional i64 createTimeEnd, // 创建时间-end
    13: optional string geo, // 合规区域
    255: optional base.Base Base,
}

struct BatchExportSamBindResponse {
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}

struct GetSellerInfoRequest {
    1: required i64 sellerId, // 商家id
    255: optional base.Base Base,
}

struct GetSellerInfoResponse {
    1: required string sellerCountryCode, // 国家码
    2: required i32 bindStatus, // 能否绑定：1 能绑定 0：不能绑定
    255: required base.BaseResp BaseResp,// status:0 成功 其他：失败
}
struct GetAllSkillGroupsRequest {
    1: optional list<i32> ShieldTypes,  // 1 测试技能组 2管理组 3 session特殊屏蔽组
    2: optional string accessPartyId,

    255: optional base.Base Base,
}
struct GetAllSkillGroupsResponse {
    1: map<string,list<SkillGroup>> SkillGroupMap,

    255: base.BaseResp BaseResp,
}
struct GetSkillGroupAgentsRequest {
    1: optional i64 TenantId,
    2: optional i64 SkillGroupId,
    3: optional string AgentName,
    4: optional i32 IsGroupLeader,
    5: optional i32 PageNo,
    6: optional i32 PageSize,
    7: optional string AgentEmail,
    8: optional i64 AccessPartyId,
    9: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName
    10: optional i32 priority,

    255: optional base.Base Base,
}
struct GetSkillGroupAgentsResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

struct GetAgentsByConditionRequest {
    1: optional i64 TenantId,
    2: optional i32 Status,
    3: optional i32 PageNo,
    4: optional i32 PageSize,
    5: optional ChannelType ChannelType,
    6: optional string Keyword, //模糊搜索key  email or name, 优先级高于email/userName
    255: optional base.Base Base,
}
struct GetAgentsByConditionResponse {
    1: list<Agent> Agents,
    2: i32 TotalSize,

    255: base.BaseResp BaseResp,
}

service Demo {
  QueryBizTypesByAccessPartyIDResponse queryBizTypesByAccessPartyID(1: QueryBizTypesByAccessPartyIDRequest req) (api.get = "/route_management/api/queryBizTypesByAccessPartyID", api.group = "customerCloud")

  GetSkillGroupsByAgentIdResponse getSkillGroupsByAgentId(1: GetSkillGroupsByAgentIdRequest req) (api.get = "/route_management/api/getSkillGroupsByAgentId", api.group = "agentSkillGroup")
  GetCardResponse getCard(1: GetCardRequest req) (api.post = "/route_management/api/getCard", api.group = "agentSkillGroup")
  CreateCardResponse createCard(1: CreateCardRequest req) (api.post = "/route_management/api/createCard", api.group = "agentSkillGroup")
  UpdateCardResponse updateCard(1: UpdateCardRequest req) (api.post = "/route_management/api/updateCard", api.group = "agentSkillGroup")
  HandleCardResponse handleCard(1: HandleCardRequest req) (api.get = "/route_management/api/handleCard", api.group = "agentSkillGroup")
  CheckSkillGroupAutoAssignResponse checkSkillGroupAutoAssign(1: CheckSkillGroupAutoAssignRequest req)(api.post = "/route_management/api/checkSkillGroupAutoAssign", api.group = "agentSkillGroup");

  GetResourceListResponse getResourceList(1: GetResourceListRequest req) (api.get = "/route_management/api/getResourceList", api.group = "category")
  GetCategoryListResponse getCategoryList(1: GetCategoryListRequest req) (api.get = "/route_management/api/getCategoryList", api.group = "category")

  GetSLAAimMetaSimpleListResponse getSLAAimMetaSimpleList(1: GetSLAAimMetaSimpleListRequest req) (api.get = "/route_management/api/getSLAAimMetaSimpleList", api.group = "routeRule")
  GetFieldListResponse getFieldList(1: GetFieldListRequest req) (api.post = "/route_management/api/getFieldList", api.group = "routeRule")
  GetFieldValuesResponse getFieldValues(1: GetFieldValuesRequest req) (api.get = "/route_management/api/getFieldValues", api.group = "routeRule")

  GetAdminRuleListResponse getAdminRuleList(1: GetAdminRuleListRequest req) (api.get = "/route_management/api/getAdminRuleList", api.group = "routeRule")
  CreateAdminRuleResponse createAdminRule(1: CreateAdminRuleRequest req) (api.post = "/route_management/api/createAdminRule", api.group = "routeRule")
  UpdateAdminRuleResponse updateAdminRule(1: UpdateAdminRuleRequest req) (api.post = "/route_management/api/updateAdminRule", api.group = "routeRule")
  DeleteAdminRuleResponse deleteAdminRule(1: DeleteAdminRuleRequest req) (api.get = "/route_management/api/deleteAdminRule", api.group = "routeRule")
  BatchUpdateAdminRuleResponse batchUpdateAdminRule(1: BatchUpdateAdminRuleRequest req) (api.post = "/route_management/api/batchUpdateAdminRule", api.group = "routeRule")

  CreateRuleV2Response createRule(1: CreateRuleV2Request req) (api.post = "/route_management/apiv2/createRule", api.group = "routeRule")
  UpdateRuleResponse updateRuleById(1: UpdateRuleRequest req) (api.post = "/route_management/apiv2/updateRuleById", api.group = "routeRule")
  GetRuleListV2Response getNewRuleList(1: GetRuleListV2Request req) (api.get = "/route_management/apiv2/getNewRuleList", api.group = "routeRule")
  GetRuleListV4Response getNewRuleListV4(1: GetRuleListV4Request req) (api.post = "/route_management/apiv2/getNewRuleListV4", api.group = "routeRule")
  GetRuleListV3Response getNewRuleListV3(1: GetRuleListV3Request req) (api.get = "/route_management/apiv2/getNewRuleListV3", api.group = "routeRule")
  UpdateRuleStatusV2Response UpdateRuleStatus(1: UpdateRuleStatusV2Request req) (api.post = "/route_management/apiv2/UpdateRuleStatus", api.group = "routeRule")
  UpdateRulePriorityV2Response UpdateRulePriority(1: UpdateRulePriorityV2Request req) (api.post = "/route_management/apiv2/UpdateRulePriority", api.group = "routeRule")
  PublishRuleGroupV2Response PublishRuleGroup(1: PublishRuleGroupV2Request req) (api.post = "/route_management/apiv2/PublishRuleGroup", api.group = "routeRule")
  CopyRuleGroupV2Response CopyRuleGroup(1: CopyRuleGroupV2Request req) (api.get = "/route_management/apiv2/CopyRuleGroup", api.group = "routeRule")
  GetRuleOperationLogsResponse GetRuleOperationLogs(1: GetRuleOperationLogsRequest req)(api.post = "/route_management/api/getRuleOperationLogs", api.group = "routeRule")
  GetSkillGroupsByAccessPartiesResponse  GetSkillGroupsByAccessParties(1: GetSkillGroupsByAccessPartiesRequest req)(api.post = "/route_management/api/getSkillGroupsByAccessParties", api.group = "routeRule")
  // 规则列表查询
  GetRuleGroupListByEventKeyResponse GetRuleGroupListByEventKey(1: GetRuleGroupListByEventKeyRequest req) (api.post = "/route_management/apiv2/getRuleGroupListByEventKey", api.group = "routeRule")
  // 根据类型获取技能组列表 -- 编辑页选择技能组
  GetSkillGroupsByTypeResponse GetSkillGroupsByType(1: GetSkillGroupsByTypeResquest req) (api.post = "/route_management/api/getSkillGroupsByType", api.group = "agentSkillGroup")
  // 获取已配置信息
  GetExtraInfoResponseV2 GetExtraInfoV2(1: GetExtraInfoRequestV2 req) (api.post = "/route_management/apiv2/getExtraInfoV2", api.group = "routeRule")
  GetFieldListResponse getNewFieldList(1: GetFieldListRequest req) (api.post = "/route_management/api/getNewFieldList", api.group = "routeRule")
    // 批量创建规则
  BatchCreateRuleGroupResponse BatchCreateRuleGroup(1: BatchCreateRuleGroupRequest req) (api.post = "/route_management/apiv2/batchCreateRuleGroup", api.group = "routeRule")
    // 获取工作状态配置
  GetWorkStatusConfigsResponse GetWorkStatusConfigs(1: GetWorkStatusConfigsRequest req) (api.get = "/route_management/api/getWorkStatusConfigs", api.group = "agentSkillGroup")
    // 修改规则组状态 启用禁用
  UpdateRuleGroupStatusResponse UpdateRuleGroupStatus(1: UpdateRuleGroupStatusRequest req) (api.post = "/route_management/apiv2/updateRuleGroupStatus", api.group = "routeRule")
   // 批量更新规则
  BatchUpdateRuleGroupResponse BatchUpdateRuleGroup(1: BatchUpdateRuleGroupRequest req) (api.post = "/route_management/apiv2/batchUpdateRuleGroup", api.group = "routeRule")
  //  搜索绑定配置信息
  SearchSamBindResponse SearchSamBind(1: SearchSamBindRequest req) (api.post = "/route_management/api/searchSamBind", api.group = "bindingConfig")
  // 查询商家信息
  GetSellerInfoResponse GetSellerInfo(1: GetSellerInfoRequest req) (api.post = "/route_management/api/getSellerInfo", api.group = "bindingConfig")
  // 批量导出绑定配置信息
  BatchExportSamBindResponse BatchExportSamBind(1: BatchExportSamBindRequest req) (api.post = "/route_management/api/batchExportSamBind", api.group = "bindingConfig")
  // 批量删除绑定配置信息
  BatchDelSamBindResponse BatchDelSamBind(1: BatchDelSamBindRequest req) (api.post = "/route_management/api/batchDelSamBind", api.group = "bindingConfig")
  // 批量转移绑定配置信息
  BatchTransferSamBindResponse BatchTransferSamBind(1: BatchTransferSamBindRequest req) (api.post = "/route_management/api/batchTransferSamBind", api.group = "bindingConfig")
  // 批量导入删除绑定配置信息
  BatchDelSamBindByExcelResponse BatchDelSamBindByExcel(1: BatchDelSamBindByExcelRequest req) (api.post = "/route_management/api/batchDelSamBindByExcel", api.group = "bindingConfig")
  // 批量新增绑定配置信息
  BatchCreateSamBindResponse BatchCreateSamBind(1: BatchCreateSamBindRequest req) (api.post = "/route_management/api/batchCreateSamBind", api.group = "bindingConfig")
  // 编辑绑定配置信息
  UpdateSamBindResponse UpdateSamBind(1: UpdateSamBindRequest req) (api.post = "/route_management/api/updateSamBind", api.group = "bindingConfig")
  // 新增绑定配置信息
  CreateSamBindResponse CreateSamBind(1: CreateSamBindRequest req) (api.post = "/route_management/api/createSamBind", api.group = "bindingConfig")
  // 获取全量技能组
  GetAllSkillGroupsResponse GetAllSkillGroups(1: GetAllSkillGroupsRequest req) (api.post = "/route_management/api/getAllSkillGroups", api.group = "agentSkillGroup")
      // 获取技能组成员
  GetSkillGroupAgentsResponse GetSkillGroupAgents(1: GetSkillGroupAgentsRequest req) (api.post = "/route_management/api/getSkillGroupAgents", api.group = "agentSkillGroup")
  // 根据条件获取人员
  GetAgentsByConditionResponse GetAgentsByCondition(1: GetAgentsByConditionRequest req) (api.post = "/route_management/api/getAgentsByCondition", api.group = "agentSkillGroup")

}