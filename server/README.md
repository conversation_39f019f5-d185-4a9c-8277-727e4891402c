# route_manage
## How to start
Start the project

``` shell
    npm install -g @ies/eden --registry=http://bnpm.byted.org
    npm install --registry=http://bnpm.byted.org
	npm run start
```
## It needs to be done before development
- Execute the command before running the project.`npm install --registry=http://bnpm.byted.org`
- If you're using*vscode*
	- Install the Eden Development Environment plug-in on vscode


- If you're using*Other editors*
		- Please make sure that relevant plug-ins such as eslint can be installed on the editor


- Read it well
			- [Front-end security specification](https://bytedance.feishu.cn/space/doc/doccn1ka86myxVgooVnk3DF4nfd)
			- [JavaScript code specification](https://bytedance.feishu.cn/space/doc/doccnM9RRoHjZhiPi2hHjZHPydg)
			- [TypeScript Code Specification](https://bytedance.feishu.cn/space/doc/doccnSa3gbdCJhWOBIwIkhzuW4a)
			- [React code specification](https://bytedance.feishu.cn/space/doc/doccnn7g7b118fyFedFdapoOV0g)

## How to deploy
### Basic configuration modification
- SCM build correlation
The relevant configuration has been configured according to the project name at the time of initialization.
- TCE Service Launch Related
	- Basic mirror image:`toutiao.nodejs:latest`
	- Service port: 3000
### Resource application and allocation
- To apply for yours
	- Code base
	- SCM
[Novice documentation](https://bytedance.feishu.cn/space/doc/doccn8wGtFJItdsVVuQIQrODJXf)
*If you don't know the above environment configuration, please consult your team members.*
### Service goes live
Reference:[https://cloud.bytedance.net/scm/detail/31154/versions](https://cloud.bytedance.net/scm/detail/31154/versions)
## All orders

``` bash
# root
# install dependencies
eden fastinstall

# build
npm run build

# start test environment
npm run start

# start debug mode
npm run debug

```
## Directory

```
├─README.md                  // read me
├─bootstrap.sh               // Gulu boot script
├─build.sh                   // SCM build script
├─commitlint.config.js       // commit lint
├─eden.pipeline.js           // eden pipeline config
├─nodemon.json               // local development nodemon config
├─package.json
├─settings.py                // TCE Runtime config
├─tsconfig.json              // TypeScript config
├─src                        // Gulu
|   ├─app.ts
|   ├─bootstrap.ts
|   ├─config                 // Gulu config
|   |   ├─config.boe.ts
|   |   ├─config.default.ts
|   |   ├─config.dev.ts
|   |   ├─config.prod.ts
|   |   ├─config.test.ts
|   |   ├─security.config.ts
|   |   └session.config.ts
|   ├─app                    // Gulu codes
|   |  ├─router.ts           // router config
|   |  ├─utils
|   |  ├─service             // Service
|   |  ├─middleware          // middleware
|   |  |     └error.ts
|   |  ├─extension
|   |  |     └context.ts
|   |  ├─controller
|   |  |     └home.ts
|   |  ├─constants
|   |  |     └index.ts
```

## Local Debugging

### OptionA: Debug in vscode（Recommanded）

Files watching, real-time compiling and debugging in vscode are supported.

(1) `F5` start debugging

(2) set breackpoints in source file to debug

### OptionB：Debug in browser using node-inspector（If u are not using vscode）

(1) [Install browseer node-inspect plugin](https://chrome.googl.com/webstore/detail/nodejs-v8-inspector-manag/gnhhdgbaldcilmgcpfddgdbkhjohddkj)

(2) Run `npm run debug` to restart server

# Scaffolding for content security business
Systematic Construction of Landing Audit Side to Improve R & D Quality and Development Efficiency
[Detailed description of the document](https://bytedance.feishu.cn/wiki/wikcnyiSZA37vvDUxpgfbWqTtIg)
