#!/usr/bin/env bash
set -e

# specify node version
source /etc/profile && nvm use node 12 && echo 'node version is ' && node -v

INSTANCE_NAME="eden"
CURRENT_DIR="$(cd -P "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROUTE_IP=`ip route |grep "default via"|awk -F " " '{print $3}'`

line=$(head -1 current_revision)
revision=$(echo $line | awk -F: '{print $2}')

# 通过脚本启动项目，注入 BUILD_BASE_COMMIT_HASH 参数


cd $CURRENT_DIR

# service port
PORT=3000

# If Docker container's network mode is Host, get valid port from environment variable, and ROUTE_IP is 127.0.0.1
if [ "$IS_HOST_NETWORK" = "1" ]; then
    ROUTE_IP=127.0.0.1
    PORT=$PORT0
fi

# ENV VARIABLE
# GULU_ENV is defined in toutiao:latest mirroring,  including BOE enviroment
if [ "$GULU_ENV" = "" ]; then
    GULU_ENV=prod
fi

# start service
sudo -u tiger -H sh -c "cd $CURRENT_DIR"

# set process env, they will be automatically injected into our script by pm2
export GULU_ENV=$GULU_ENV
export ROUTE_IP=$ROUTE_IP
export PORT=$PORT
export BUILD_BASE_COMMIT_HASH=$revision

pm2 start bootstrap.js -n $INSTANCE_NAME --no-daemon -i 2 --log-date-format "YYYY-MM-DD HH:mm:ss"
