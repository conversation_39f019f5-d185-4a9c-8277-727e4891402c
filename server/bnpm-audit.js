/**
 * @file bnpm audit
 * <AUTHOR>
 */

const axios = require('axios');

async function audit(pkg, lock) {
  const res = await axios.post('https://bnpm.byted.org/-/open/v1/security/audits', {
    ['package.json']: pkg,
    ['package-lock.json']: lock,
  });
  return res.data;
}

const pkg = require('./package.json');
const lock = require('./package-lock.json');

audit(pkg, lock).then(data => {
  console.log(data);
});
