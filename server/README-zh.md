# route_manage

## 如何启动

启动工程

```shell
    npm install -g @ies/eden --registry=http://bnpm.byted.org
    npm install --registry=http://bnpm.byted.org
		npm run start
```

## 开发前需做

- 在运行项目前先执行命令 `npm install --registry=http://bnpm.byted.org`
- 如果你在用 *vscode*
  - 在 vscode 上安装 Eden Develop Environment 插件
- 如果你在用 *其他编辑器*
  - 请确保编辑器上可安装 eslint 等相关插件
- 熟读
  - [前端安全规范](https://bytedance.feishu.cn/space/doc/doccn1ka86myxVgooVnk3DF4nfd)
  - [JavaScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnM9RRoHjZhiPi2hHjZHPydg)
  - [TypeScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnSa3gbdCJhWOBIwIkhzuW4a)
  - [React 代码规范](https://bytedance.feishu.cn/space/doc/doccnn7g7b118fyFedFdapoOV0g)

## 如何部署
### 基础配置修改
- SCM构建相关
    相关配置在初始化时已根据 项目名称 配置
- TCE服务启动相关
    - 基础镜像：`toutiao.nodejs:latest`
    - 服务端口：3000

### 资源申请和配置
- 申请你的
  - 代码库
  - SCM

[新手文档](https://bytedance.feishu.cn/space/doc/doccn8wGtFJItdsVVuQIQrODJXf)

*以上环境配置不了解的请咨询你的团队成员*

### 服务上线
参考：https://cloud.bytedance.net/scm/detail/31154/versions


## 所有命令

```bash
# 根目录
# 安装依赖
eden fastinstall

# 生成打包文件
npm run build

# 启动测试环境
npm run start

# 启动 debug 模式
npm run debug

```
## 目录

```
├─README.md                  // 必读
├─bootstrap.sh               // Gulu 启动脚本
├─build.sh                   // SCM 打包脚本
├─commitlint.config.js       // 提交 lint
├─eden.pipeline.js           // eden pipeline配置
├─nodemon.json               // 本地开发 nodemon 配置
├─package.json
├─settings.py                // TCE Runtime 配置
├─tsconfig.json              // TypeScript 配置
├─src                        // Gulu 目录
|   ├─app.ts
|   ├─bootstrap.ts
|   ├─config                 // Gulu 配置目录
|   |   ├─config.boe.ts
|   |   ├─config.default.ts
|   |   ├─config.dev.ts
|   |   ├─config.prod.ts
|   |   ├─config.test.ts
|   |   ├─security.config.ts
|   |   └session.config.ts
|   ├─app                    // Gulu 业务代码目录
|   |  ├─router.ts           // 路由配置
|   |  ├─utils
|   |  ├─service             // Service
|   |  ├─middleware          // 中间件
|   |  |     └error.ts
|   |  ├─extension
|   |  |     └context.ts
|   |  ├─controller
|   |  |     └home.ts
|   |  ├─constants
|   |  |     └index.ts
```

## 本地调试

### 方式一：vscode中调试（推荐）

支持在vscode中进行断点调试，并且支持代码热更新

(1) 点击按钮`F5`启动调试

(2) 在源文件中打断点，进行调试

### 方式二：Node-inspector在浏览器中调试（非vscode用户）

(1) [安装浏览器插件](https://chrome.google.com/webstore/detail/nodejs-v8-inspector-manag/gnhhdgbaldcilmgcpfddgdbkhjohddkj)

(2) 使用 `npm run debug` 启动服务


# 内容安全业务脚手架

落地审核侧体系化建设，提高研发质量及开发效率
[详细介绍文档](https://bytedance.feishu.cn/wiki/wikcnyiSZA37vvDUxpgfbWqTtIg)
