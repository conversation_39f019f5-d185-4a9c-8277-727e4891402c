/**
 * @file 编译时替换CDN
 * <AUTHOR>
 */

const fs = require('fs');
const path = require('path');
const htmlPath = path.resolve(__dirname, '../output/app/view/index.html');
const targetPath = path.resolve(__dirname, '../output/app/view/va');

const content = fs.readFileSync(htmlPath).toString();

const CDN_SG_ONLINE = process.env.CDN_OUTER_SG;
const CDN_VA_ONLINE = process.env.CDN_OUTER_VA;
const CDN_SG_PPE = process.env.CDN_INNER_SG;
const CDN_VA_PPE = process.env.CDN_INNER_VA;

fs.writeFileSync(
  `${targetPath}/index.html`,
  content.replace(new RegExp(CDN_SG_ONLINE, 'g'), CDN_VA_ONLINE).replace(new RegExp(CDN_SG_PPE, 'g'), CDN_VA_PPE)
);
