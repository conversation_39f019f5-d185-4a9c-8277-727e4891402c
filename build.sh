set -e

source /etc/profile
nvm use 12

# client install
cd ./client
eden fastinstall
sh ./build.sh

# 根目录删除已有的 output_resource 和 output 文件夹
cd ..
rm -rf output_resource
rm -rf output

# 删除 client/build 目录下 .js.map 后缀的文件
find client/build -name "*.js.map" | xargs rm -rf

# 根目录创建 output_resource 文件
mkdir output_resource

# server intall
# git submodule init
cd server 
eden fastinstall

npm i -g typescript@4.5.2

echo "BUILD_REGION is set to: $BUILD_REGION"
echo "BUILD_TYPE is set to: $BUILD_TYPE"

# 只在boe和ppe 非ttp环境上报
if [ "$BUILD_TYPE" = "offline" -o "$BUILD_TYPE" = "test" ] && [ "$BUILD_REGION" != "oci" ]; then
    npm i @ecom/coverage-uploader@1.0.3-beta.13
    npm i @ecom/huatuo-cli@1.0.2-alpha.2
    npx huatuo-cli coverage -C "src/app" \
        -I "node_modules,output,*.d.ts,*.config.js,eden.*.js,dist,.jest" \
        -N "ies/route_manage_i18n" \
        -E "src/bootstrap.ts" \
        && tsc --build ./tsconfig.json
else
    tsc --build ./tsconfig.json
fi

# 根目录创建 output/app/view/ 文件夹
cd ..
mkdir -p output/app/view

# 将 server 目录下的文件拷贝到 output 目录下
cp -r server/output/* output/
cp -r server/bootstrap.sh server/settings.py server/node_modules output/
cp -r server/src/app/idl output/app/

# idl文件夹复制到output 运行时依赖idl
cp -r server/src/idl output/

# 将 client/build/template/index.html 文件拷贝到 output/app/view/ 目录下
cp -r client/build/template/* output/app/view/

# 将 client/build/template/ 目录下的文件拷贝到 output_resource/ 目录下
cp -r client/build/* output_resource/
