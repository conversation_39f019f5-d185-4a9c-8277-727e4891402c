# route_manage

## 如何启动

### 启动整个工程
```shell
  npm install -g @ies/eden --registry=http://bnpm.byted.org
  npm install  --registry=http://bnpm.byted.org
  npm run start
```

### 启动 server 端
```shell
  npm run dev-server
```

### 启动 client 端
```shell
  npm run dev-client
```

### 打包
```shell
  npm run build
```

### 清除打包生成的文件
```shell
  npm run clean
```

## 开发前需要做
- 在运行项目前先执行命令 `npm run install`
- 如果你在用 *vscode*
  - 在 vscode 上安装 Eden Develop Environment 插件
- 如果你在用 *其他编辑器*
  - 请确保编辑器上可安装 eslint 等相关插件
- 熟读
  - [前端安全规范](https://bytedance.feishu.cn/space/doc/doccn1ka86myxVgooVnk3DF4nfd)
  - [JavaScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnM9RRoHjZhiPi2hHjZHPydg)
  - [TypeScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnSa3gbdCJhWOBIwIkhzuW4a)
  - [React 代码规范](https://bytedance.feishu.cn/space/doc/doccnn7g7b118fyFedFdapoOV0g)
- 修改 server/src/const 和 client/src/const 中的 projectName

### 资源申请和配置
- 申请你的
  - 代码库
  - SCM

[新手文档](https://bytedance.feishu.cn/space/doc/doccn8wGtFJItdsVVuQIQrODJXf)

*以上环境配置不了解的请咨询你的团队成员*

### 服务上线
参考：https://cloud.bytedance.net/scm/detail/31154/versions

## 各种服务接入

以下服务需要在其他平台申请接入，得到某些信息后填入配置文件才能生效。

以下提到的配置文件，没有特别说明均指 config.default.ts。

### slardar

详见 client/src/common/constants/config.ts

### tea

详见 client/src/common/constants/config.ts

### starling

详见 client/src/common/constants/config.ts

### 犀鸟工单

详见 client/src/common/constants/config.ts

### 天穹

平台 https://ti.bytedance.net/

接入文档 https://bytedance.feishu.cn/docs/doccnJVHGp0acr4OImUvZkGCQLh#BOZ9Uv

参考 https://bytedance.feishu.cn/docs/doccncm7eVfOTkmtGMveOeknpae

配置文件字段：tqLog

### Ferry

Ferry 是一个致力于提高前后端协作开发效率的通信框架。
[介绍](https://ferry.bytedance.net/overview/introduction/)

修改 `http_idl` 目录下的thrift文件，然后执行 `npm run ferry-gen`,即可生成前后端调用代码。
server端业务逻辑在`controller`目录下，client端调用代码在  `client/src/http_idl`下。
这里采用的是前后端统一一个thrift文件的形式，不支持多个thrift文件，原因是node端只支持一个入口文件。