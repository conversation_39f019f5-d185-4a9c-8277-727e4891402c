module.exports = {
  idlFetch: [
    // {
    //   outDir: 'idl',
    //   type: 'git',
    //   source: '**************:lancewuz/fetch-idl.git',
    //   entry: 'test/idl/!(error|index).thrift',
    //   branch: 'master',
    //   rootDir: '.',
    // }
  ],
  ignore: {},
  customHttpRequest: {
    // api: 'axiosFetch',
    // importCode: 'import { default as axiosFetch } from "@byted-ferry/axios-fetch"',
    // useFunctionGeneric: true,
  },
  whiteApi: {},
  pageWhiteApi: {},
  customDefault: {},
  mock: {},
};
