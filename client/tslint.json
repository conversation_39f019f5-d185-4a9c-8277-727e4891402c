{
  "extends": ["tslint:recommended", "tslint-react", "tslint-config-prettier"],
  "linterOptions": {
    "exclude": [
      "**/node_modules/**",
      "**/dist/**",
      "**/output/**",
      "**/output_resource/**",
      "**/test/**",
      "**/typings/**"
    ]
  },
  "rules": {
    "no-console": false,
    "import-spacing": true,
    "max-classes-per-file": false,
    "interface-name": false,
    "no-any": false,
    "variable-name": [true, "ban-keywords", "check-format", "allow-pascal-case", "allow-leading-underscore"],
    "no-empty-interface": false,
    "object-literal-sort-keys": false,
    "no-shadowed-variable": false,
    "no-empty": false,
    "no-var-requires": false,
    "semicolon": [true, "always"],
    "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"],
    "curly": true,
    "no-arg": true,
    "no-unused-expression": true,
    "prefer-const": true,
    "max-line-length": [true, 120],
    // tslint-react
    "jsx-no-lambda": false,
    "jsx-no-multiline-js": false,
    "jsx-curly-spacing": [
      true,
      {
        "when": "never",
        "allowMultiline": false
      }
    ],
    "ordered-imports": [
      false,
      {
        "import-sources-order": "any",
        "named-imports-order": "case-insensitive"
      }
    ],
    "jsx-boolean-value": false,
    "member-access": false,
    "no-bitwise": false
  }
}
