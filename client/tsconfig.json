{"compilerOptions": {"target": "es6", "module": "esnext", "lib": ["es7", "dom"], "sourceMap": true, "allowJs": true, "jsx": "react", "moduleResolution": "node", "experimentalDecorators": true, "rootDir": "./", "baseUrl": "./src", "paths": {"@/*": ["*"], "@context/*": ["context/*"], "@api/*": ["api/*"], "@components/*": ["components/*"], "@http_idl/*": ["http_idl/*"], "@constants/*": ["common/constants/*"], "@pages/*": ["pages/*"], "@common/*": ["common/*"], "@stores/*": ["stores/*"], "@services/*": ["services/*"], "@types/*": ["types/*"], "@sdks/*": ["sdks/*"], "@locale/*": ["locale/*"], "@hooks/*": ["hooks/*"]}, "forceConsistentCasingInFileNames": true, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": false, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "typeRoots": ["node", "./node_modules/@types", "./typings"], "exclude": ["src/**/*.js"], "include": ["typings", "src"]}