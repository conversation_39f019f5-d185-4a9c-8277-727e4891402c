/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-22 14:49:52
 * @LastEditTime: 2019-10-22 16:21:33
 * @LastEditors: Please set LastEditors
 */
const path = require('path');
const SlardarWebpackPlugin = require('@slardar/webpack-plugin');
// const TsConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const ReplacePlugin = require('@ies/replace-plugin');
const CoverageUploadPlugin = require('@ecom/coverage-upload-plugin');
// 项目标识
const coverageVariable = 'route_manage_i18n';
// 需要插桩 boe || ppe
const needCoverage = process.env.BUILD_TYPE === 'test' || process.env.BUILD_TYPE === 'offline';

const resolve = dir => path.join(__dirname, './', dir);
const FILE_EXTENSIONS = ['.scss', '.ts', '.tsx', '.js', '.json', '.jsx'];

const babelPlugin = needCoverage ? [
  ['module:@ecom/babel-coverage-plugin', 
    {
      coverageVariable: coverageVariable
    }
  ]
] : [];

const typingsForCssModulesLoader = {
  loader: 'typings-for-css-modules-loader',
  options: {
    modules: true,
    namedExport: true,
    camelCase: true,
    sass: true,
    localIdentName: '[local]--[hash:base64:5]',
  },
};

const CDN_PREFIX = 'ies/route_manage_i18n';

module.exports = {
  projectType: 'app',
  dev: {
    deploy: {
      // eden start 时自动启动 server 脚本，可以参考 package.json 中 scripts 中 server 配置
      // preScript: 'server',

      // node-server 本地接口
      receiver: `http://localhost:${process.env.DEV_SERVER_PORT}/eden/upload`,

      // 开启模板文件上传，模板文件会被上传到相对 server cwd 的对应路径
      templatePath: 'src/app/view',

      // 这里可以配置需要上传的模板的匹配规则, 默认匹配扩展名 .html
      templateRegExps: [/\.html/],

      // 默认不上传静态文件，开启后会把静态资源上传到相对 server cwd 的对应路径
      // staticPath: 'src/static',
    },
    proxy: {
      verbose: true,
      preserveHost: true,
      urlRewrite: {
        ['http://csp-bos.byteoversea.net/route_management']: `http://localhost:${process.env.DEV_SERVER_PORT}/route_management`,
        ['https://csp-bos.byteoversea.net/route_management']: `http://localhost:${process.env.DEV_SERVER_PORT}/route_management`,
      },
    },
    // 自动打开浏览器
    openBrowser: {
      enabled: true,
      url: 'http://csp-bos.byteoversea.net/united/setting-route_management/ticket_routing_v2',
    },
    // dev-server port
    port: Number(process.env.DEV_CLIENT_PORT),
  },
  // 自定义entry，非SPA不需要填input
  input: path.resolve(__dirname, 'src/index.tsx'),

  // 自定义 publishPath
  output: {
    libraryTarget: 'umd',
    jsonpFunction: 'imRouteManagementWebpackJsonp', // 每个子应用自己使用独特的名字
    publicPath:
      process.env.NODE_ENV === 'production' ?
        `//CDN-TOS-PREFIX/${CDN_PREFIX}/resource/` :
        `http://localhost:${process.env.DEV_CLIENT_PORT}/`,
  },

  resolve: {
    extensions: FILE_EXTENSIONS,
    alias: {
      '@http_idl': resolve('./src/http_idl'),
    },
    // plugins: [
    //   new TsConfigPathsPlugin({
    //     configFile: resolve('tsconfig.webpack.json'),
    //     extensions: FILE_EXTENSIONS
    //   })
    // ]
  },

  // 开启不同的能力
  abilities: {
    dll: false,
    react: {
      antd: false,
      hot: true,
    },
    semi: {
      theme: '@ies/semi-theme-byte-desk',
      prefixCls: 'united_route_manage',
      srcSvgPaths: [resolve('./src/images')],
    },
    css: true,
    sass: {
      cssLoader: {
        modules: true,
        localIdentName: '[local]--[hash:base64:5]',
      },
      sassLoader: {
        includePaths: [path.join(__dirname, 'src/common/styles')],
      },
    },
    define: {
      // 读取 SCM 构建版本
      PATROL_RELEASE_ID: JSON.stringify(process.env.PATROL_RELEASE_ID),
    },
    babel: { plugins: babelPlugin },
    ts: {
      babel: { plugins: babelPlugin }
    },
    checkes6: false,
  },

  // 原生配置 对应 webpack.config.js
  raw: options => {
    // 排除ant样式 css module
    let sassLoaders = options.module.rules.filter(loader => loader.test.test('.sass'));
    sassLoaders.map(item => {
      if (item.exclude) {
        item.exclude = [item.exclude, path.resolve('./node_modules')];
      }
      let index = item.use.findIndex(loader => loader.loader.indexOf('css-loader') > -1);
      item.use[index] = typingsForCssModulesLoader;
      return item;
    });
    // patrol release 插件，用于自动上传 map 文件
    if (process.env.NODE_ENV === 'production') {
      options.plugins.push(
        new SlardarWebpackPlugin({
          bid: 'ies_kefu_route_manage_i18n', // 站点bid,必传
          include: ['./build/resource/common', './build/resource'], // sourcemap文件所在的目录,必传,可填多个
          release: process.env.PATROL_RELEASE_ID, // 可选参数,没有则传空
        })
      );
    }
    options.plugins.push(new ReplacePlugin());

    if (needCoverage) {
      options.plugins.push(new CoverageUploadPlugin({
        gitRepo: 'ies/route_manage_i18n',
        isDiffSend: true,
        interval: 2000,
        diffSendFileNum: 50,
        retryLimit: 5,
        coverageVariable: coverageVariable,
        customEntry: []
      }));
    }

    return options;
  },
  other: {
    envMode: 'strict',
    edenRuntimeDependencies: ['@babel/core', '@babel/preset-react', '@babel/traverse', 'babel-loader']
  },
  // 自定义插件
  edenPlugins: [],
};
