# route_manage

## 如何启动

启动工程

```shell
    npm install -g @ies/eden --registry=http://bnpm.byted.org
    npm install  --registry=http://bnpm.byted.org
    npm run start
```

## 开发前需做

- 在运行项目前先执行命令 `npm run install:all`
- 如果你在用 *vscode*
  - 在 vscode 上安装 Eden Develop Environment 插件
- 如果你在用 *其他编辑器*
  - 请确保编辑器上可安装 eslint 等相关插件
- 熟读
  - [前端安全规范](https://bytedance.feishu.cn/space/doc/doccn1ka86myxVgooVnk3DF4nfd)
  - [JavaScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnM9RRoHjZhiPi2hHjZHPydg)
  - [TypeScript 代码规范](https://bytedance.feishu.cn/space/doc/doccnSa3gbdCJhWOBIwIkhzuW4a)
  - [React 代码规范](https://bytedance.feishu.cn/space/doc/doccnn7g7b118fyFedFdapoOV0g)

## 如何部署
### 基础配置修改
- SCM构建相关
    相关配置在初始化时已根据 项目名称 配置
- TCE服务启动相关
    - nginx.conf  修改域名、静态资源部署路径）
    ```javascript
           server {
                listen 80;
                server_name ase2.bytedance.net; // 此处需修改

                root /opt/tiger/ies/fe/route_manage/build/; // 此处需手动修改，与eden init时配置的项目名称相同

                location / {
                    #root   html;
                    index   index.html   /template/index.html ;
                    try_files $uri $uri/ /template/index.html;
                    add_header Cache-Control no-store;
                }
            }
    ```

### 资源申请和配置
- 申请你的
  - 代码库
  - SCM

[新手文档](https://bytedance.feishu.cn/space/doc/doccn8wGtFJItdsVVuQIQrODJXf)

*以上环境配置不了解的请咨询你的团队成员*

### 服务上线
参考：https://cloud.bytedance.net/scm/detail/31154/versions


## 所有命令

```bash
# 根目录
# 安装依赖
eden fastinstall

# 生成打包文件
npm run build

# 启动测试环境
npm start

```
## 目录

```
├──  src
│    ├── api
│    │   ├── config.ts // 统一拦截处理请求
│    │   └── index.ts
│    ├── app.tsx
│    ├── common
│    │   ├── constants
│    │   │   ├── config.ts // 第三方工具配置（重要）
│    │   │   └── property.ts
│    │   ├── images
│    │   ├── styles
│    │   └── utils
│    │       └── hook.ts // 待完善，Hook Component 模拟 ClassComponent的生命周期钩子
│    ├── components
│    │   ├── global
│    │   │   ├── error
│    │   │   └── loading
│    │   │   └── RouteRender 路由鉴权
│    │   └── layout
│    │       ├── content
│    │       ├── footer
│    │       ├── header
│    │       └── nav
│    ├── index.html
│    ├── index.scss
│    ├── index.scss.d.ts
│    ├── index.tsx
│    ├── pages
│    │   ├── guide
│    │   ├── home
│    │   └── no_match // 404默认页面
│    ├── router.tsx
│    ├── sdk
│    │   ├── hornbill.ts// 犀鸟工单系统
│    │   ├── i18n.tsx  // HOC，用于注入文案
│    │   ├── slardar.ts // slardar异常监控上报
│    │   └── tea.ts // tea上报示例
│    └── stores // 共享数据
│        └── index
├── .npmrc
├── eden.config.js         // eden 配置
├── tsconfig.webpack.json
├── .commitlintrc.js           // 提交 lint
├── .gitignore
├── .npmrc
├── CHANGELOG.md               // 发大版本时，请在此处写入版本特性
├── README.md                  // 必读
├── build.sh                   // SCM 打包脚本
├── sw.js                      // ServiceWorder崩溃、卡顿上报
├── package-lock.json
├── package.json
├── scm_build_resource.sh      // SCM 上传 CDN 脚本
└── tsconfig.json              // 根目录 TypeScript 配置
```


# 内容安全业务脚手架

落地审核侧体系化建设，提高研发质量及开发效率
[详细介绍文档](https://bytedance.feishu.cn/space/doc/doccnnhZ1xS73uUMi4kwz1WO2rc)