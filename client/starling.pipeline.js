/**
 * Power By Starling
 * About: https://bytedance.feishu.cn/docs/doccnSbrp2NYvDDCwO5qL9rkLbc#G5mG0Q
 */
module.exports = {
  //  {zh} 公共参数 {en}  Common context
  context: {
    mode: 'auto',
    i18nopsRemote: {
      repoName: 'ies/route_manage_i18n',
    },
    publish: {
      env: 'prod',
    },
  },
  //  {zh} 生命周期卡点  {en} Life cycle point plugin
  stage: {
    setup: ['starling-i18nops-remote-plugin'],
    scan: {
      beforeScan: [],
      afterScan: ['starling-translate-plugin', 'starling-upload-plugin'],
    },
    replace: {
      beforeReplace: [],
      afterReplace: ['starling-publish-plugin'],
    },
    emit: [],
  },
};
