/**
 * @file
 * <AUTHOR>
 */

export {};
declare global {
  // 操作渠道
  enum ChannelType {
    IM = 1, // IM
    TICKET = 2, // 工单
    PHONE = 3, // 电话
    OTHER = 4,
  }

  export interface Agent {
    ID: string;
    TenantId: string;
    WorkType: number;
    UserName: string;
    NickName: string;
    UserId: string;
    UUID: string;
    Email: string;
    Mobile: string;
    CompanyId: string;
    ChannelTypes: Array<ChannelType>;
    Status: number;
    CreatedBy: string;
    CreatedAt: string;
    UpdatedBy: string;
    UpdatedAt: string;
    OperatorName: string;
    DepartmentId: string;
    ImMaxTaskNum: number;
    Extra: Record<string, string>;
  }

  export interface User {
    uuid: string;
    unique_name: string;
    username: string;
    email: string;
    user_id: string;
    platform: number;
    tenant_key: string;
    avatar: string;
    create_time: string;
  }

  export interface AccessPartyMap {
    name: string;
    allow_access_party_list: number[];
  }

  export interface Tcc {
    skill_group_allow_access_party: {
      [index: number]: AccessPartyMap;
    };
  }
  interface Window {
    __PROWER_BY_GAR__: boolean;
    $pageData: {
      env: 'prod' | 'boe' | 'dev';
      tcc: Tcc;
      agent: Agent;
      user: User;
    };
    firstPaint: number;
    Slardar: Record<string, any>;
    secsdk: Record<string, Record<string, any>>;
  }
  let __webpack_public_path__: string; // webpack public path 动态路径
}
