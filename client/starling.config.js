/**
 * Power By Starling
 * About: https://bytedance.feishu.cn/docs/doccnSbrp2NYvDDCwO5qL9rkLbc#YTaebl
 */
module.exports = {
  //  {zh} 收集入口  {en}  Collection entrance
  entry: './src',

  //  {zh} 剔除路径(glob)  {en} Remove path (glob)
  exclude: ['**/locale/**/*.*'],

  //  {zh} 环境设置  {en} Environment setting
  env: process.env.NODE_ENV === 'production' ? 'prod' : 'dev',

  //  {zh} 预览设置  {en} Preview settings
  preview: {
    profile: true,
    browser: true,
    output: {
      path: './starling',
      type: ['json', 'xlsx', 'html'],
    },
  },

  //  {zh} Starling项目  {en} Starling project
  starling: {
    api_key: '7528ec60f66811eb91e659796e4d90c0',
    accessKey: 'e8242c2b537375ebc9a93a4b105e7c83',
    secretKey: '68a5b97a92eeaeac47abc3637b0b1c1c',
    projectId: 'route_manage_i18n',
    namespace: ['route_manage_i18n'],
    mode: 'normal',
    source: true,
    download: {
      path: './locales',
    },
    upload: {
      path: './starling/starling.xlsx',
      target: true,
    },
  },

  //  {zh} 文件加载器  {en} File loader
  loaders: [
    {
      name: 'starling-js-loader',
      options: {
        rules: [/formatMessage\(.*\)/i, /\s*i18n\.t\(.+\)\s*/i],
        comment: true,
      },
    },
    {
      name: 'starling-css-loader',
      options: {
        comment: true,
      },
    },
  ],

  //  {zh} 生命周期plugin  {en} life cycle plugin
  plugins: [
    {
      name: 'starling-after-replace-plugin',
      options: {
        autoImport: 'import { I18n } from "@ies/starling_intl";',
      },
    },
    {
      name: 'starling-key-generator-plugin',
      options: {
        remote: {
          source: true,
        },
        auto: {
          type: 'machineTranslate',
        },
      },
    },
    {
      name: 'starling-code-generator-plugin',
      options: {
        statement: 'I18n.t("$key", $variable, "$defaultMessage")',
      },
    },
  ],

  //  {zh} 扩展命令  {en} Extended command
};
