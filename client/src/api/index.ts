import http from './config';

export type UserInfo = {
  uuid: string;
  unique_name: string;
  username: string;
  email: string;
  user_id: string;
  platform: number;
  tenant_key: string;
  avatar: string;
  create_time: string;
};

export interface StrategyItem {
  strategyKey: string;
  strategyName: string;
  enable: boolean;
}

export interface GetStrategyListRequest {
  strategyKey: string[];
  lang: string;
}

export interface GetStrategyListsResponse {
  message: string;
  status: number;
  data: {
    skillGroupStrategy: StrategyItem[];
    changeAccessPartyStrategy: StrategyItem[];
  };
}
function checkStatus(response) {
  if (response && response.status >= 200 && response.status < 300) {
    return response;
  } else {
    const error = new Error(response.statusText);
    error.response = response;
    throw error;
  }
}

export const getUserInfo = () => http.get('/api/user');

export const logout = () => {
  location.href = `${window.origin}/route_management/api/logout?returnTo=${encodeURIComponent(location.href)}`;
};

export const getStrategyList = req => {
  const uri = `${window.origin}/bench/route/strategyList`;
  const headers = { 'Content-Type': 'application/json' };
  const body = JSON.stringify(req);
  return fetch(uri, {
    method: 'POST',
    headers,
    body,
    credentials: 'same-origin',
  })
    .then(checkStatus)
    .then(response => response.json && response.json());
};
