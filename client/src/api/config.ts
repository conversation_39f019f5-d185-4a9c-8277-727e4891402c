import { I18n } from '@ies/starling_intl';
import axios, { AxiosRequestConfig } from 'axios';
import { PATH_PREFIX } from '../const';

// Default configuration settings
const DEFAULTCONFIG = {
  baseURL: `${PATH_PREFIX}/`,
};

type HTTPMethods = 'get' | 'post' | 'put' | 'delete';

interface IClientRequestConfig extends AxiosRequestConfig {
  startTime?: Date;
  successCallback?: (...args: any[]) => any;
  errorCallback?: (...args: any[]) => any;
}

class HTTPClient {
  get<D, T>(url: string, data?: D, config?: IClientRequestConfig): Promise<T> {
    return this._invokeMethod<D, T>('get', url, data, config);
  }
  post<D, T>(url: string, data?: D, config?: IClientRequestConfig): Promise<T> {
    return this._invokeMethod<D, T>('post', url, data, config);
  }
  put<D, T>(url: string, data?: D, config?: IClientRequestConfig): Promise<T> {
    return this._invokeMethod<D, T>('put', url, data, config);
  }
  delete<D, T>(url: string, data?: D, config?: IClientRequestConfig): Promise<T> {
    return this._invokeMethod<D, T>('delete', url, data, config);
  }

  // When the code is not 0, please output msg
  private _isSuccess = (res): boolean => res.code === 0;
  private _resFormat = (res): any => res.data || res.response || {};
  private _invokeMethod<D, T>(v: HTTPMethods, url: string, data: D, config: IClientRequestConfig = {}): Promise<T> {
    const axiosConfig: IClientRequestConfig = Object.assign(
      {
        baseURL: DEFAULTCONFIG.baseURL,
        headers: {},
        method: v,
        url,
      },
      config
    );

    const instance = axios.create(DEFAULTCONFIG);
    // Request interception
    instance.interceptors.request.use(
      cfg => {
        cfg.params = Object.assign({}, cfg.params);
        return cfg;
      },
      error => Promise.reject(error)
    );
    // Return to intercept
    instance.interceptors.response.use(
      response => {
        let rdata = null;
        if (typeof response.data === 'object' && !isNaN(response.data.length)) {
          rdata = response.data[0];
        } else {
          rdata = response.data;
        }
        if (!this._isSuccess(rdata)) {
          const _err = {
            code: rdata.code || rdata.errCode,
            message: rdata.message || I18n.t('unknown_cause__service_exception', {}, '未知原因, 服务异常'),
          };
          return Promise.reject(_err);
        }
        return rdata;
      },
      error => {
        const _err = {
          message: error.response.message || I18n.t('unknown_cause__service_exception', {}, '未知原因, 服务异常'),
          code: error.response.code || '-1',
        };
        return Promise.reject(_err);
      }
    );
    if (v === 'get') {
      axiosConfig.params = data;
    } else if (v === 'post') {
      axiosConfig.data = data;
    } else if (data instanceof FormData) {
      axiosConfig.data = data;
    } else {
      axiosConfig.data = JSON.stringify(data);
    }
    axiosConfig.startTime = new Date();

    return instance
      .request<T>(axiosConfig)
      .then(res => {
        if (typeof config.successCallback === 'function') {
          config.successCallback(res);
        }
        return this._resFormat(res);
      })
      .catch(err => {
        // Not logged in, unified jump to login
        if (err.code === 401) {
          location.href = '/login';
        } else if (typeof config.errorCallback === 'function') {
          config.errorCallback(err);
        }
        return Promise.reject(err);
      });
  }
}

export default new HTTPClient();
