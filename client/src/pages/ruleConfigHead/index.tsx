import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import { useHistory } from 'react-router-dom';
import { locationItemType } from './typeDefinition';
import * as styles from './index.scss';
import { ROUTE_PATH } from '@/common/constants/property';
import { routeType } from '@/const/enums';

const locationArr: Array<locationItemType> = [
  {
    router: '/client_routingV2',
    name: I18n.t('client_issue_classification_card', {}, '客户端问题分类卡片'),
    tips: I18n.t(
      'when_the_user_problem_classification_cannot_be_obtained__the_problem_classificat_4300c4aeb7931ac6d6d8691e4e0c2b4c',
      {},
      '当获取不到用户问题分类时，可在前端配置问题分类，引导用户进行信息选择，从而更便捷地匹配到相应技能组'
    ),
  },
  {
    router: '/service_routing_v2',
    name: I18n.t('im_manual_routing_rules', {}, 'IM人工路由规则'),
    tips: I18n.t(
      'the_routing_rules_under_the_current_entry_can_be_configured__and_they_can_be_jud_d4688e468145de95f4ad81abbf8efb05',
      {},
      '可配置当前入口下路由规则，按照编号顺序依次判断，其中兜底技能组为系统默认配置'
    ),
  },
  {
    router: '/service_create_rule',
    name: I18n.t('im_manual_routing_rules', {}, 'IM人工路由规则'),
    // Tips: 'You can configure the routing rules under the current entrance, and judge them in sequence according to the number order, where the bottom skill group is the default configuration of the system',
  },
  {
    router: '/bot_routing_v2',
    name: I18n.t('im_intelligent_routing_rules', {}, 'IM智能路由规则'),
    tips: I18n.t(
      'routing_rules_can_be_configured_to_customize_the_flow_trigger_process',
      {},
      '路由规则可以配置定制流转触发流程'
    ),
  },
  {
    router: '/ticket_routing_v2',
    name: I18n.t('work_order_routing_rules', {}, '工单路由规则'),
    tips: I18n.t(
      'configure_the_work_order_allocation_rules_under_the_current_access_party__and_ju_4e9d613c8a0645f0096b8a092cf6ee7c',
      {},
      '配置当前接入方下的工单分配规则，按照编号顺序依次判断'
    ),
  },
  {
    router: '/offline_routing_v2',
    name: I18n.t('offline_routing_rules', {}, '离线路由规则'),
    tips: I18n.t(
      'configure_the_offline_allocation_rules_under_the_current_access_party__and_judge_96720140bde4d52fb6733136ea186218',
      {},
      '配置当前接入方下的离线分配规则，按照编号顺序依次判断'
    ),
  },
  {
    router: '/quality_check_routing_v2',
    name: I18n.t('quality_inspection_routing_rules', {}, '质检路由规则'),
    tips: I18n.t(
      'can_configure_the_routing_rules_under_the_current_service__and_judge_them_in_seq_e7a942e65e36a8bb2b48179ba70198dd',
      {},
      '可配置当前业务下路由规则，按照编号顺序依次判断，其中兜底技能组规则为系统默认配置'
    ),
  },
  {
    router: '/bot_create_rule',
    name: I18n.t('im_intelligent_routing_rules', {}, 'IM智能路由规则'),
    // Tips: 'You can configure the routing rules under the current business, and judge them in sequence according to the numbering order, where the rules of the bottom skill set are the default configuration of the system',
  },
  {
    router: '/offline_create_rule',
    name: I18n.t('offline_routing_rules', {}, '离线路由规则'),
    // Tips: 'You can configure the routing rules under the current business, and judge them in sequence according to the numbering order, where the rules of the bottom skill set are the default configuration of the system',
  },
  {
    router: '/accessparty_create_rule',
    name: I18n.t('access_party_shunt_rule', {}, '接入方分流规则'),
  },
  {
    router: '/qualitycheck_create_rule',
    name: I18n.t('quality_inspection_routing_rules', {}, '质检路由规则'),
    // Tips: 'You can configure the routing rules under the current business, and judge them in sequence according to the numbering order, where the rules of the bottom skill set are the default configuration of the system',
  },
  {
    router: '/ticket_create_rule',
    name: I18n.t('work_order_routing_rules', {}, '工单路由规则'),
    // Tips: 'You can configure the routing rules under the current business, and judge them in sequence according to the numbering order, where the rules of the bottom skill set are the default configuration of the system',
  },
  {
    router: '/quality_check_extract_rule',
    name: I18n.t('drawing_rules', {}, '抽单规则'),
    // Tips: 'You can configure the routing rules under the current business, and judge them in sequence according to the numbering order, where the rules of the bottom skill set are the default configuration of the system',
  },
  {
    router: '/accessparty_split_flow',
    name: I18n.t('access_party_shunt', {}, '接入方分流'),
    tips: I18n.t(
      'configure_the_shunt_rules_of_the_same_data_source_in_different_access_parties__a_861a5e42e067ac5da88827a865e35ee5',
      {},
      '配置同一份数据源在不同接入方的分流规则，按照编号顺序依次判断'
    ),
  },
  {
    router: '/flexible_art',
    name: I18n.t('flexible_configuration', {}, '弹性配置'),
    tips: I18n.t(
      'when_there_is_a_supply_and_demand_imbalance__the_supply_and_demand_situation_can',
      {},
      '当出现供需失衡时，可通过预先设定的实时调控规则，通过透传供需情况给各应用模块，还请谨慎配置'
    ),
  },
  {
    router: '/create_dynamic_rule',
    name: I18n.t('editing_rules', {}, '编辑规则'),
    tips: I18n.t(
      'set_the_trigger_conditions_for_dynamically_adjusting_the_number_of_seats_to_gues',
      {},
      '基于新技能组维度设定动态调整坐席对客数触发条件，以及人员溢出上限。'
    ),
  },
  {
    router: `/${ROUTE_PATH.IM_MANUAL_ACCESS_PARTY}`,
    name: I18n.t('im_manual_shunt_configuration', { }, 'IM manual shunt configuration'),
    tips: I18n.t('shunt_configuration_ability_affects_online_data__please_operate_with_caution!', { }, 'Shunt configuration ability affects online data, please operate with caution!'),
  },
  {
    router: `/${ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY}`,
    name: I18n.t('ticket_access_party_shunt_configuration', { }, 'ticket access party shunt configuration'),
    tips: I18n.t('shunt_configuration_ability_affects_online_data__please_operate_with_caution!', { }, 'Shunt configuration ability affects online data, please operate with caution!'),
  },
  {
    router: `/${routeType.im_accessparty_routing}`,
    name: I18n.t('im_manual_shunt_rule', { }, 'IM manual shunt rule'),
  },
  {
    router: `/${routeType.ticket_accessparty_routing}`,
    name: I18n.t('ticket_access_party_shunt_rule', { }, 'ticket access party shunt rule'),
  },
];


const Header: React.FC = () => {
  const history = useHistory();
  const location = history.location.pathname;
  let locationItem: locationItemType;
  for (let i = 0; i < locationArr.length; i++) {
    if (locationArr[i].router === location) {
      locationItem = locationArr[i];
    }
  }
  const content = (
    <>
      <p className={styles.routerPageTitle}>{locationItem.name}</p>
      {locationItem.router === '/accessparty_split_flow' && (
        <div className={`${styles.routerHeaderTips} ${styles.remind}`}>
          {I18n.t(
            'the_traffic_diversion_from_the_access_side_directly_affects_online_data_please_be_careful_when_performing_operations',
            {},
            '接入方分流直接影响线上数据，操作请务必谨慎！！'
          )}
        </div>
      )}
      <div className={styles.routerHeaderTips}>{locationItem.tips}</div>
    </>
  );
  return content;
};

export default Header;
