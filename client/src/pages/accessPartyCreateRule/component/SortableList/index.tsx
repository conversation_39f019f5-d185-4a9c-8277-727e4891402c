import { I18n } from '@ies/starling_intl';
import React from 'react';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { Icon, Switch } from '@ies/semi-ui-react';
import * as styles from './index.scss';

const DragHandle = SortableHandle(() => (
  <Icon
    type="handle"
    style={{
      color: 'var(--semi-color-text-2)',
      marginRight: '16px',
      cursor: 'move',
    }}
  />
));

const SortableItem = SortableElement(({ value, idx, onChange, ashes }) => (
  <div className={styles.skillGroupStrategyItem}>
    <DragHandle />
    <div className={styles.skillGroupName}>{value.strategyName}</div>
    <Switch
      onChange={checked => onChange({ idx, checked })}
      checked={value.enable === 1}
      disabled={ashes}
    />
  </div>
));

const SortableList = SortableContainer(({ items, onChange, disabled }) => (
  <div className={styles.skillGroupStrategy}>
    <div className={styles.skillGroupInfo}>{I18n.t('priority_is_determined_from_top_to_bottom', { }, '优先级由从上到下的顺序决定')}</div>
    {items.map((value, index) => (
      <SortableItem
        key={value.strategyKey}
        index={index}
        idx={index}
        value={value}
        onChange={onChange}
        disabled={disabled}
        ashes={disabled}
      />
    ))}
  </div>
));

export default SortableList;

