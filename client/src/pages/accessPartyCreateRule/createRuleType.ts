import { StrategyItem } from '@/api';
import { I18n } from '@ies/starling_intl';

export interface RuleFormInit {
  DisplayName: string;
  OperateType: number;
  opGroup: string;
  SkillGroupId?: string;
  Enable: number;
  RuleEnv: number;
  Expression: {
    opGroup: string;
    conditionGroups: Array<RuleGroup>;
  };
  accessPartyId: string;
  skillGroupRouteStrategyList: StrategyItem[];
  changeAccessPartyCondition: string;
  changeAccessPartyId: string;
  [key: string]: any;
}


export type CreateRuleHeaderProps = {
  name: string;
  platform: string;
  handleGoBack: () => void;
};

export type CreateRuleProps = {
  Priority: number; // Rule priority
  RuleGroupId?: number; // Rule group ID
  ruleId?: string; // Rule ID,
  plaformTag: string; // Platform identity
  Enable: boolean;
  viewType: string; // Edit or New
  isLast: boolean;
  EventKey: string;
  ruleLength: number;
  accessPartyStrategy: StrategyItem[];
  skillGroupStrategy: StrategyItem[];
};

export type RuleItem = {
  Lhs: any;
  OpCheck: string;
  Rhs: any;
  [propName: string]: any;
};

export interface RuleGroup {
  OpGroup: string;
  Conditions: Array<RuleItem>;
}

export interface RuleGroupClient {
  opGroup: string;
  conditions: Array<RuleItem>;
}

export interface SelectOption {
  label: string;
  value: string;
  otherKey: string;
  [propName: string]: any;
}

export interface ReturnValue {
  ReturnValue: {
    Constant: string;
  };
}

export interface ActionInfoData {
  accessPartyId: string;
  skillGroupRouteStrategyList?: StrategyItem[];
  changeAccessPartyStrategy?: {
    condition: string[];
    newAccessPartyId: string;
  };
}
export interface ActionInfoProps {
  values: any;
}

export interface SubmitProps {
  ruleLists: any;
  fieldList: any;
}

export const ruleOpcheckArr = [
  { serveOp: '==', clientOp: 1, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 2, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: 'CONTAINS', clientOp: 3, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: '>', clientOp: 4, opType: 'math', clientName: () => I18n.t('greater_than', {}, 'greater than') },
  { serveOp: '<', clientOp: 5, opType: 'math', clientName: () => I18n.t('less_than', {}, 'less than') },
  { serveOp: '>=', clientOp: 6, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, 'no less than') },
  { serveOp: '<=', clientOp: 7, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, 'no more than') },
  { serveOp: 'START_WITH', clientOp: 8, opType: 'fun', clientName: () => I18n.t('started_with', {}, 'started at') },
  { serveOp: 'END_WITH', clientOp: 9, opType: 'fun', clientName: () => I18n.t('ended_in', {}, 'ended at') },
  { serveOp: 'IS_NULL', clientOp: 10, opType: 'fun', clientName: () => I18n.t('empty', {}, 'empty') },
  { serveOp: 'IS_NOT_NULL', clientOp: 11, opType: 'fun', clientName: () => I18n.t('not_empty', {}, 'not empty') },
  { serveOp: 'LIST_IN', clientOp: 12, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: 'STRING_CONTAINS', clientOp: 13, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: '==', clientOp: 14, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 15, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: '==', clientOp: 16, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 17, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: '>', clientOp: 18, opType: 'math', clientName: () => I18n.t('greater_than', {}, 'greater than') },
  { serveOp: '<', clientOp: 19, opType: 'math', clientName: () => I18n.t('less_than', {}, 'less than') },
  { serveOp: '>=', clientOp: 20, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, 'no less than') },
  { serveOp: '<=', clientOp: 21, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, 'no more than') },
  { serveOp: 'LIST_NOT_IN', clientOp: 22, opType: 'fun', clientName: () => I18n.t('not_included', {}, 'not include') },
  { serveOp: 'LIST_EQUAL', clientOp: 30, opType: 'fun', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: 'LIST_NOT_EQUAL', clientOp: 31, opType: 'fun', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  {
    serveOp: 'STRING_NOT_CONTAINS',
    clientOp: 32,
    opType: 'fun',
    clientName: () => I18n.t('not_included', {}, 'not include'),
  },
  { serveOp: 'LIST_RETAIN', clientOp: 33, opType: 'fun', clientName: () => I18n.t('intersect', {}, 'intersect') },
  { serveOp: 'NOT LIST_IN', clientOp: 34, opType: 'fun', clientName: () => I18n.t('not_included', {}, 'not include') },
  { serveOp: 'BETWEEN_ALL_CLOSE', clientOp: 35, opType: 'fun', clientName: () => I18n.t('belong_to', {}, 'belong to') },
  {
    serveOp: 'NOT BETWEEN_ALL_OPEN',
    clientOp: 36,
    opType: 'fun',
    clientName: () => I18n.t('do_not_belong', {}, 'do not belong'),
  },
  {
    serveOp: 'NOT LIST_RETAIN',
    clientOp: 37,
    opType: 'fun',
    clientName: () => I18n.t('no_intersection', {}, 'no intersection'),
  },
];
