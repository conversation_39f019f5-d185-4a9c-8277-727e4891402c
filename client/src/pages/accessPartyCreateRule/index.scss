.create-rule-content {
  padding: 24px 0 0 25px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  width: 760px;
  margin: 0 auto;
}

.form-content {
  flex: 1;
  height: calc(100vh);
}

.create-rule-bottom {
  height: 56px;
  border-top: 2px solid rgba(28, 31, 35, .08);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}


.radioIcon {
  margin-left: 4px;
  color: rgba(28, 31, 35, .6);
}

.create-rule-form-minwidth {
  width: 450px;
}

.addRuleListBox {
  display: flex;
  padding: 14px;
  border: 1px dashed rgba(var(--grey-1), 1);
}

.addRuleListBtn {
  display: flex;
  align-items: center;
  color: #0077fa;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.errorTipBox {
  display: flex;
  align-items: center;
  margin-top: 4px;
  color: var(--color-danger);
  font-size: 14px;
  line-height: 20px;
}

.lastTip {
  font-size: 14px;
  line-height: 20px;
  color: #1c1f23;
}

.action-log-title {
  line-height: 20px;
  margin: 4px 0;
  font-weight: 700;
  color: var(--color-text-0);
  font-size: 14px;
}

.time-line-box {
  width: 100%;
  height: 300px;
  overflow: auto;
  margin-bottom: 100px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 12px;
}

.label-text {
  font-weight: 700;
  color: var(--color-text-0);
  font-size: 14px;
  line-height: 20px;
}

.actions-log-header {
  font-weight: 600;
  color: #222727;
  font-size: 14px;
  line-height: 20px;
  margin-left: 8px;
}

.actions-log-text {
  color: var(--color-text-2) !important;
  font-size: 14px;
  line-height: 20px;
  margin-left: 8px;
}

.access-party-condition {
  margin: 5px 0 20px;
  display: flex;
  align-items: center;
  padding: 4px 16px;
  border-radius: 6px;
  border: 1px solid var(--color-border);

  .condition-info {
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: rgba(28, 31, 35, 0.8);
    width: 160px;
  }
}

.access-party-header {
  flex: 1;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  color: #222727;
  margin-top: 24px;
}