import { I18n } from '@ies/starling_intl';
import React from 'react';
import { useHistory } from 'react-router-dom';
import RuleLists from '@/components/ruleLists';
import { Icon, Form, Button } from '@ies/semi-ui-react';
import { getRequiredRule } from '@common/rules';
import CreateRuleHeader from '@/components/createRuleHeader';
import { StrategyItem } from '@/api';
import { getAllAccessPartyList } from '@/common/utils/hook';
import { formApi } from '@ies/semi-ui-react/form';
import { useSubmit, useHandleGoBack } from './common/hooks';
import { useCreateRuleHooks } from './hooks';
import { tempRouterParams, TIPS_NAME_MAP, TRIGER_DESC_MAP } from './common/constants';
import { CreateRuleProps } from './createRuleType';
import SortableList from './component/SortableList';
import * as styles from './index.scss';

let ruleFormApi: formApi = null;

const Index: React.FC = () => {
  const Browerhistory = useHistory();
  const { handleGoBack } = useHandleGoBack({ ruleFormApi });
  const routerParams: CreateRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});
  const locationType = Browerhistory?.location?.pathname?.slice?.(1);
  const plaformTag = locationType;

  const typeName = I18n.t('access_party_shunt', { }, 'Access party shunt');
  const {
    initFormDetail,
    ruleLists,
    fieldList,
    ruleListIsError,
    sortedSkillGroupStrategy,
    addRuleGroup,
    changeConditionsJoinWay,
    setRuleFormRef,
    deleteRuleItem,
    handleFormValueChange,
    handleOpRadioChange,
    checkRuelAllWrite,
    showContent,
    changeFormValuesByRuleList,
    deleteGroup,
    changeSortedSkillGroupStrategy
  } = useCreateRuleHooks(routerParams.ruleId, plaformTag, routerParams.isLast, routerParams);

  const { handleSubmit } = useSubmit({ ruleLists, fieldList });

  const submitInfo = async () => {

    if (!ruleFormApi) {
      return;
    }
    // Check if the rules are valid
    if (!checkRuelAllWrite(routerParams.isLast)) {
      return;
    }

    const values = await ruleFormApi.validate();

    // submit changes
    handleSubmit(values);
  };

  const content = (
    <div className={styles.createRuleContent}>
      <CreateRuleHeader platform={plaformTag} name={TIPS_NAME_MAP[plaformTag]?.title()} handleGoBack={handleGoBack} />
      <div className={styles.formContent}>
        <Form
          disabled={routerParams?.viewType === 'view'}
          onValueChange={handleFormValueChange}
          getFormApi={formApi => {
            ruleFormApi = formApi;
            setRuleFormRef(formApi);
          }}
          initValues={{
            ...initFormDetail,
          }}
        >
          {({ values, formApi }) => (
            <>
              <Form.Input
                className={styles.createRuleFormMinwidth}
                label={`${I18n.t('{typename}_rule_name', { typeName }, '{typeName} Rule name')}`}
                field="DisplayName"
                rules={[
                  getRequiredRule(
                    `${I18n.t('please_fill_in_{typename}_rule_name', { typeName }, 'Please fill in the {typeName} Rule name')}`
                  ),
                ]}
              />
              {routerParams.isLast ? (
                <>
                  <Form.Slot label={{ text: I18n.t('trigger_condition', {}, 'Trigger condition') }}>
                    <div className={styles.lastTip}>
                      {TRIGER_DESC_MAP[plaformTag]?.title?.() || I18n.t('unknown_rule_type', {}, 'Unknown rule type')}
                    </div>
                  </Form.Slot>
                </>
              ) : (
                <>
                  <Form.RadioGroup
                    field="OperateType"
                    label={I18n.t('trigger_condition', {}, 'Trigger condition')}
                    onChange={handleOpRadioChange}
                    type="card"
                    style={{ marginBottom: '10px' }}
                  >
                    <Form.Radio
                      value={1}
                      style={values.OperateType === 0 ? { border: '0.5px solid rgba(34, 39, 39, 0.35)' } : null}
                    >
                      {I18n.t('meet_all_of_the_following_conditions', {}, 'Meet all of the following conditions')}
                    </Form.Radio>
                    <Form.Radio
                      value={0}
                      style={values.OperateType === 1 ? { border: '0.5px solid rgba(34, 39, 39, 0.35)' } : null}
                    >
                      {I18n.t('meet_any_of_the_following_conditions', {}, 'Meet any of the following conditions')}
                    </Form.Radio>
                  </Form.RadioGroup>
                  {ruleLists.map((item, index) => {
                    const content = (
                      <React.Fragment key={index}>
                        <RuleLists
                          changeFormValuesByRuleList={changeFormValuesByRuleList}
                          changeJoinWay={changeConditionsJoinWay}
                          values={values}
                          formApi={formApi}
                          groupInfo={item}
                          parentIndex={index}
                          deleteItem={deleteRuleItem}
                          fieldList={fieldList}
                          deleteGroup={deleteGroup}
                          viewType={routerParams.viewType}
                          disabledGroup={ruleLists?.length === 1}
                        />
                      </React.Fragment>
                    );
                    return content;
                  })}
                  {ruleListIsError ? (
                    <div className={styles.errorTipBox}>
                      <Icon type="clear" style={{ marginRight: '4px' }} />
                      {`${I18n.t('please_fill_in_the_{typename}_rule', { typeName }, 'Please fill in the {typeName} rule')}`}
                    </div>
                  ) : null}
                  <div className={styles.addRuleListBox}>
                    <Button
                      className={styles.addRuleListBtn}
                      icon="plus_circle"
                      theme="borderless"
                      onClick={addRuleGroup}
                      disabled={routerParams.viewType === 'view'}
                      style={{ margin: '0 auto' }}
                    >
                      {I18n.t('route_Starling_1', {}, 'Add a Condition-group')}
                    </Button>
                  </div>
                </>
              )}
              <div style={{ flexGrow: 1 }}>
                <Form.Select
                  field="accessPartyId"
                  label={I18n.t('the_shunt_access_party_is', { }, 'The shunt access party is')}
                  optionList={getAllAccessPartyList()}
                  style={{ width: '400px' }}
                  filter={true}
                  rules={
                    [getRequiredRule(I18n.t('please_select', { }, 'Select'))]
                  }
                />
              </div>
              <Form.Slot label={<div>{I18n.t('skill_set_selection_strategy', { }, 'skill set selection strategy')}<span style={{ color: 'rgba(28, 31, 35, 0.6)' }}>{I18n.t('(optional)', { }, '(可选)')}</span></div>} />
              <SortableList
                items={sortedSkillGroupStrategy}
                onSortEnd={({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }): void => {
                  const sortList = sortedSkillGroupStrategy.map(item => ({ ...item }));
                  const fieldOptions: StrategyItem[] = sortList.splice(oldIndex, 1) || [];
                  sortList.splice(newIndex, 0, fieldOptions[0]);
                  changeSortedSkillGroupStrategy(sortList);
                }}
                onChange={({ idx, checked }) => {
                  // Create a new element object and set a new value
                  const updatedItem = { ...sortedSkillGroupStrategy[idx], enable: checked === true ? 1 : 0 };
                  // Create a new array object and replace the elements to be updated
                  const updatedItems = [
                    ...sortedSkillGroupStrategy.slice(0, idx),
                    updatedItem,
                    ...sortedSkillGroupStrategy.slice(idx + 1)
                  ];
                  changeSortedSkillGroupStrategy(updatedItems);
                }}
                disabled={routerParams.viewType === 'view'}
              />
              <div className={styles.accessPartyHeader}>{I18n.t('access_party_change_policy', { }, 'access party change policy')}<span style={{ color: 'rgba(28, 31, 35, 0.6)' }}>{I18n.t('(optional)', { }, '(可选)')}</span></div>
              <div className={styles.accessPartyCondition}>
                <Form.Select
                  field="changeAccessPartyCondition"
                  noLabel
                  placeholder={I18n.t('the_change_condition_is', { }, 'The change condition is')}
                  optionList={routerParams.accessPartyStrategy.map(item => ({
                    value: item.strategyKey,
                    label: item.strategyName,
                  }))}
                  style={{ width: '200px' }}
                  filter={true}
                />
                <div className={styles.conditionInfo}>{I18n.t('change_the_access_party_to', { }, 'Change the access party to')}</div>
                <Form.Select
                  field="changeAccessPartyId"
                  noLabel
                  placeholder={I18n.t('select_access_party', { }, 'Select access party')}
                  optionList={getAllAccessPartyList()}
                  style={{ width: '300px' }}
                  filter={true}
                />
              </div>
              {['create', 'clone'].includes(routerParams.viewType) && (
                <>
                  <Form.RadioGroup
                    field="Enable"
                    label={I18n.t('effective_status', {}, 'Effective status')}
                    onChange={handleOpRadioChange}
                  >
                    <Form.Radio value={0}>{I18n.t('disable', {}, 'Disable')}</Form.Radio>
                    <Form.Radio value={1}>{I18n.t('enable', {}, 'Enable')}</Form.Radio>
                  </Form.RadioGroup>
                  <Form.RadioGroup
                    field="RuleEnv"
                    label={I18n.t('production_environment', {}, 'Production environment')}
                    onChange={handleOpRadioChange}
                  >
                    <Form.Radio value={1}>{I18n.t('online', {}, 'Online')}</Form.Radio>
                  </Form.RadioGroup>
                </>
              )}
            </>
          )}
        </Form>
      </div>
      <div className={styles.createRuleBottom} style={{ display: routerParams.viewType === 'view' ? 'none' : 'flex' }}>
        <Button type="tertiary" onClick={handleGoBack}>
          {I18n.t('cancel', {}, 'Cancel')}
        </Button>
        <Button style={{ marginLeft: 8 }} type="secondary" theme="solid" onClick={submitInfo}>
          {routerParams.viewType === 'edit' ? I18n.t('update', {}, 'Update') : I18n.t('new', {}, 'Create')}
        </Button>
      </div>
    </div>
  );
  return <>{showContent ? content : null}</>;
};

export default Index;
