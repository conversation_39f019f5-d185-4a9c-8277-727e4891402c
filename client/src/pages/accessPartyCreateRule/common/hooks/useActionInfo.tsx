import { AimExpr } from '@http_idl/demo';
import { ActionInfoData, ActionInfoProps, ReturnValue } from '../../createRuleType';

export const useActionInfo = (): { getActionInfo: (val: ActionInfoProps) => AimExpr } => {

  const getActionInfo = ({ values }: ActionInfoProps): ReturnValue => {

    if (!Object.keys(values || {})?.length) {
      return;
    }

    const { accessPartyId, sortedGroupRouteStrategyList, changeAccessPartyCondition, changeAccessPartyId } = values;
    const action: ActionInfoData = {
      accessPartyId,
    };

    if (sortedGroupRouteStrategyList && sortedGroupRouteStrategyList?.length) {
      action.skillGroupRouteStrategyList = sortedGroupRouteStrategyList;
    }

    if (changeAccessPartyId && changeAccessPartyCondition) {
      action.changeAccessPartyStrategy = {
        condition: [changeAccessPartyCondition],
        newAccessPartyId: changeAccessPartyId,
      };
    }

    return {
      ReturnValue: {
        Constant: `\"${JSON.stringify(action)}\"`,
      },
    };
  };

  return {
    getActionInfo,
  };
};
