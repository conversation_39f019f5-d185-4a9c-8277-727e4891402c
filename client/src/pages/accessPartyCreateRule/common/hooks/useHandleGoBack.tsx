import React from 'react';
import I18n from '@ies/starling_intl';
import { useHistory } from 'react-router-dom';
import { formApi as FormApi } from '@ies/semi-ui-react/form';
import { Icon, Modal } from '@ies/semi-ui-react';
import { CreateRuleProps } from '../../createRuleType';
import { REDIRECT_URL_MAP, tempRouterParams } from '../constants';

export const useHandleGoBack = ({
  ruleFormApi,
}: {
  ruleFormApi: FormApi;
}): {
    handleGoBack: () => Promise<void>;
  } => {
  const Browerhistory = useHistory();
  const plaformTag = Browerhistory?.location?.pathname?.slice?.(1);
  const routerParams: CreateRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});
  const typeName = I18n.t('manual_integration', { }, 'Manual integration');
  const displayName = ruleFormApi?.getValue('DisplayName');

  const handleGoBack = async () => {
    let confirmed = true;
    let title: string;
    if (routerParams?.viewType === 'view') {
      const url = REDIRECT_URL_MAP[plaformTag];
      Browerhistory.replace({
        pathname: url,
        state: {
          jumpPublish: true,
          eventKey: routerParams?.EventKey,
        },
      });
      return;
    }
    switch (routerParams?.viewType) {
      case 'create':
        const titleNameCreate =
          displayName || `${I18n.t('unnamed_{typename}_rule', { typeName }, 'Unnamed {typeName} rule')}`;
        title = `${I18n.t('abandon_new__{titlenamecreate}_', { titleNameCreate }, 'Abandon new「{titleNameCreate}」')}`;
        break;
      case 'edit':
        const titleNameEdit =
          displayName || `${I18n.t('unnamed_{typename}_rule', { typeName }, 'Abandon new {typeName} rule')}`;
        title = `${I18n.t('abandon_editing_of__{titlenameedit}_', { titleNameEdit }, 'Abandon editing of「{titleNameEdit}」')}`;
        break;
      default:
        title = I18n.t('abandon_the_current_operation?', {}, 'Abandon the current operation?');
        break;
    }
    confirmed = await new Promise(resolve => {
      Modal.confirm({
        icon: <Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />,
        title,
        okButtonProps: {
          type: 'danger',
        },
        cancelText: I18n.t('cancel', {}, 'Cancel'),
        okText: I18n.t('ok', {}, 'OK'),
        content: I18n.t(
          'once_abandoned__the_data_will_not_be_recovered__please_operate_with_caution',
          {},
          'Be cautious! The data will not be recovered once abandoned.'
        ),
        onCancel() {
          resolve(false);
        },
        onOk() {
          resolve(true);
        },
      });
    });

    if (confirmed) {
      const url = REDIRECT_URL_MAP[plaformTag];
      Browerhistory.replace({
        pathname: url,
        state: {
          jumpPublish: true,
          eventKey: routerParams?.EventKey,
        },
      });
      // Need to jump
    }
  };
  return {
    handleGoBack,
  };
};

export default useHandleGoBack;
