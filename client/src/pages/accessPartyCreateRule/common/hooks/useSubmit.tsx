import { useHistory } from 'react-router';
import { I18n } from '@ies/starling_intl';
import { Toast } from '@ies/semi-ui-react';
import { demoClient, CreateRuleV2Request, UpdateRuleRequest } from '@http_idl/demo';
import { ErrorCodeNoPermission } from '@/const';
import { useActionInfo } from './useActionInfo';
import { REDIRECT_URL_MAP, tempRouterParams } from '../constants';
import { getServerRuleData } from '../../hooks';
import { CreateRuleProps, RuleFormInit, SubmitProps } from '../../createRuleType';

export const useSubmit = ({
  ruleLists,
  fieldList,
}: SubmitProps): {
    handleSubmit: (val: RuleFormInit) => void;
  } => {
  const { getActionInfo } = useActionInfo();
  const Browerhistory = useHistory();
  const locationType = Browerhistory?.location?.pathname?.slice?.(1);
  const plaformTag = locationType;
  const routerParams: CreateRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});

  const updateRule = async (values, ruleInfo, isLast) => {
    const updateReq: UpdateRuleRequest = {
      Id: routerParams.ruleId,
      Version: 'v1',
      DisplayName: values.DisplayName,
      Expression: ruleInfo,
      ActionInfo: getActionInfo({
        values,
      }),
      PermCode: '',
    };

    const res = await demoClient.updateRuleById(updateReq);
    if (res.code !== 0) {
      if (res.code === ErrorCodeNoPermission) {
        Toast.error(I18n.t('no_editing_permission ', {}, ' no edit permission'));
      } else {
        Toast.error(res.message);
      }
      return;
    }
    Toast.success(
      I18n.t(
        'update_successful__return_to_the_rules_page_after_2s ',
        {},
        ' update successfully, return to the rules page after 2s'
      )
    );
    const url = REDIRECT_URL_MAP[plaformTag];
    Browerhistory.replace({
      pathname: url,
      state: {
        newRuleId: res.Rule.Id,
        jumpPublish: true,
        eventKey: routerParams?.EventKey,
      },
    });
  };

  const handleSubmit = async values => {
    const rulesInfo = getServerRuleData(ruleLists, fieldList);

    const sendRule = rulesInfo;

    const saveData: CreateRuleV2Request = {
      Version: 'v1',
      RuleEnv: values.RuleEnv,
      Enable: values.Enable === 1,
      EventKey: routerParams.EventKey,
      DisplayName: values.DisplayName,
      Description: '',
      AccessPartyId: '0',
      Priority: routerParams.Priority || 0,
      RuleGroupId: routerParams.RuleGroupId === -1 ? undefined : routerParams.RuleGroupId.toString(),
      Expression: sendRule,
      ActionInfo: getActionInfo({
        values
      }),
    };

    if (routerParams.viewType === 'edit') {
      updateRule(values, sendRule, routerParams.isLast);
      return;
    }

    const res = await demoClient.createRule(saveData);

    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }

    Toast.success(
      I18n.t(
        'create_successfully__return_to_the_rules_page_after_2s ',
        {},
        ' created successfully, return to the rules page after 2s'
      )
    );
    const url = REDIRECT_URL_MAP[plaformTag];
    Browerhistory.replace({
      pathname: url,
      state: {
        jumpPublish: true,
        newRuleId: res.Rule.Id,
        eventKey: routerParams.EventKey,
      },
    });
  };

  return {
    handleSubmit,
  };
};

export default useSubmit;
