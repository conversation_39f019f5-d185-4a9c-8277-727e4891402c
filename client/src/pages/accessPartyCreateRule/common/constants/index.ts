import { I18n } from '@ies/starling_intl';
import { routeType } from '@/const/enums';
import { CreateRuleProps } from '../../createRuleType';

export const tempRouterParams: CreateRuleProps = {
  Priority: 0,
  RuleGroupId: -1,
  ruleId: '',
  plaformTag: '',
  Enable: false,
  viewType: 'create',
  isLast: false,
  EventKey: '',
  ruleLength: 0,
  accessPartyStrategy: [],
  skillGroupStrategy: [],
};

export const TIPS_NAME_MAP = {
  [routeType.im_accessparty_routing]: {
    title: (): string => I18n.t('return_to_im_manual_offload_configuration_list', { }, 'Return to IM manual offload configuration list'),
  },
  [routeType.ticket_accessparty_routing]: {
    title: (): string => I18n.t('return_to_the_ticket_access_party_offload_configuration_list', { }, 'Return to the ticket access party offload configuration list'),
  },
};

export const TRIGER_DESC_MAP = {
  [routeType.im_accessparty_routing]: {
    title: I18n.t('when_no_im_manual_shunt_matching_rules_are_obtained', { }, 'When no IM manual shunt matching rules are obtained'),
  },
  [routeType.ticket_accessparty_routing]: {
    title: I18n.t('when_no_ticket_access_party_shunt_matching_rules_are_obtained', { }, 'When no ticket access party shunt matching rules are obtained'),
  },
};

export const REDIRECT_URL_MAP = {
  [routeType.im_accessparty_routing]: '/im_manual_access_party_routing',
  [routeType.ticket_accessparty_routing]: '/ticket_manual_access_party_routing',
};
