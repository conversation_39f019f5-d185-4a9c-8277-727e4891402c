import { I18n } from '@ies/starling_intl';
import { ConditionGroupExpr } from '@http_idl/demo';
import { useEffect, useState } from 'react';
import { numberToTime, timeToNumber } from '@/common/utils/dateTimeField';
import { timeToMillisecond, millisecondToTime } from '@/common/utils/date';
import { Toast } from '@ies/semi-ui-react';
import { isArray } from 'lodash';
import { formatNum } from '@/common/utils/inputFloat';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import { RuleFormInit, RuleGroup, RuleItem } from './createRuleType';
import { formApi } from '@ies/semi-ui-react/form';
import { StrategyItem } from '@/api';

export const numbetToStropreatorMap = {
  1: 'AND',
  0: 'OR',
};
const strToNumberOpMap = {
  AND: 1,
  OR: 0,
};

let ruleCreateFormApi: formApi = null;

function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min)) + min; // No maximum value, no minimum value
}

const getInItData = fieldList => {
  const detail: RuleFormInit = {
    DisplayName: '',
    OperateType: 1,
    Enable: 0,
    RuleEnv: 1,
    opGroup: 'AND',
    Expression: {
      opGroup: 'AND',
      conditionGroups: [],
    },
    accessPartyId: '',
    changeAccessPartyCondition: '',
    changeAccessPartyId: '',
    skillGroupRouteStrategyList: [],
  };

  const opcheck = fieldList[0].OperatorList[0];
  const lhs = fieldList[0].FieldName;
  const rhs = fieldList[0].OperatorFieldvalues[opcheck]?.FieldValueList[0]?.value;
  detail.item_opCheck_0_0 = opcheck;
  detail.item_lhs_0_0 = lhs;
  detail.item_rhs_0_0 = [rhs];
  return detail;
};

// Parsing server-side data based on current type
const parseServerAction = (action, detail) => {
  const accessPartyId = action?.accessPartyId;
  const newAccessPartyId = action?.changeAccessPartyStrategy?.newAccessPartyId;
  detail.accessPartyId = accessPartyId !== undefined ? String(accessPartyId) : '';
  detail.changeAccessPartyCondition = action?.changeAccessPartyStrategy?.condition?.[0] || '';
  detail.changeAccessPartyId = newAccessPartyId !== undefined ? String(newAccessPartyId) : '';
  detail.skillGroupRouteStrategyList = action?.skillGroupRouteStrategyList || [];
};

// Condition group data processing
const getRuleItemValue = (values, valueType, platform, typeTag) => {
  if (typeTag === 1) {
    values = platform === 'server' ? [values] : isArray(values) ? values[0] : values;
  }
  if (typeTag === 801) {
    return platform === 'server' ?
      {
        ConstantList: timeToMillisecond(values),
      } :
      millisecondToTime(values);
  } else if (typeTag === 17) {
    return platform === 'server' ?
      {
        Constant: formatNum(values),
      } :
      values;
  } else if (isArray(values)) {
    for (let i = 0; i < values.length; i++) {
      if (valueType === 3) {
        values[i] = platform === 'server' ? `\"${values[i]}\"` : values[i].slice(1, values[i].length - 1);
      } else if (valueType === 2) {
        values[i] = String(values[i]);
      } else if (valueType === 5) {
        values[i] = platform === 'server' ? String(values[i]) : Number(values[i]);
      } else if (valueType === 6) {
        values[i] = platform === 'server' ? String(values[i]) : values[i] === 'true';
      } else if (valueType === 1) {
        values[i] = String(values[i]);
      }
      if (typeTag === 8) {
        values[i] =
          platform === 'server' ? JSON.stringify(timeToNumber(values[i])) : 'Mon Oct 11 2021 00:54:00 GMT+0800';
      }
    }
    return platform === 'server' ?
      {
        ConstantList: values,
      } :
      values;
  } else {
    if (valueType === 3) {
      values = platform === 'server' ? `\"${values}\"` : values.slice(1, values.length - 1);
    } else if (valueType === 2 || valueType === 5) {
      values = platform === 'server' ? String(values) : Number(values);
    } else if (valueType === 6) {
      values = platform === 'server' ? String(values) : values === 'true';
    } else if (valueType === 1) {
      values = String(values);
    }
    if (typeTag === 8) {
      if (values.length > 8) {
        values = new Date(values);
      }
      values = platform === 'server' ? JSON.stringify(timeToNumber(values)) : numberToTime(values);
    }
    if ([4, 14].includes(typeTag)) {
      const dateTime = Number(values) || values;
      values = platform === 'server' ? JSON.stringify(new Date(dateTime).getTime()) : Number(dateTime);
    }

    return platform === 'server' ?
      {
        Constant: values,
      } :
      values;
  }
};

// Get the ID of the option selected by the left side of the ruleitem
const getSelectRuleInfo = (id, fieldList) => {
  if (!fieldList || !fieldList.length) {
    return;
  }
  for (let i = 0; i < fieldList.length; i++) {
    if (fieldList[i].FieldName === id) {
      // debugger;
      return fieldList[i];
    }
  }
  return null;
};

// Turn back-end Expression into ruleList
const getClientRuleList = (serverData: ConditionGroupExpr, fieldList) => {
  const addInfo = {
    OperateType: -1,
  };
  const { ConditionGroups, OpGroup } = serverData;
  addInfo.OperateType = strToNumberOpMap[OpGroup] || 0;
  const ruleLists = [];
  // debugger;
  for (let i = 0; i < ConditionGroups.length; i++) {
    ruleLists.push({
      conditions: [],
      opGroup: ConditionGroups[i].OpGroup,
    });
    const len = ruleLists.length - 1;
    for (let z = 0; z < ConditionGroups[i].Conditions.length; z++) {
      const temp = ConditionGroups[i].Conditions[z];

      ruleLists[len].conditions.push({
        lhs: '',
        opCheck: '',
        rhs: '',
        key: getRandomInt(1, 1000000),
      });

      const info = getSelectRuleInfo(temp.Lhs.VarExpr || temp.Lhs.FeatureExpr.FeatureName, fieldList);
      if (!info) {
        continue;
      }

      addInfo[`item_lhs_${i}_${z}`] = temp.Lhs.VarExpr || temp.Lhs.FeatureExpr.FeatureName;
      addInfo[`item_opCheck_${i}_${z}`] = ConditionGroups[i].Conditions[z].OpCheck;
      const rhsList = temp.Rhs.Constant || temp.Rhs.ConstantList;
      addInfo[`item_rhs_${i}_${z}`] = getRuleItemValue(
        rhsList,
        info.FieldDataType || '',
        'client',
        info.OperatorFieldvalues[temp.OpCheck]?.FieldValueType
      );
      addInfo[`item_opGroup_${i}_0`] = ConditionGroups[i].OpGroup;
    }
  }

  for (let i = 0; i < ruleLists.length; i++) {
    if (ruleLists[i].conditions.length === 0) {
      ruleLists.splice(i, 1);
      i -= 1;
    }
  }
  const res = {
    formValue: addInfo,
    ruleListValues: ruleLists,
  };
  return res;
};

// Turning rules into the data structures needed for the back end
export const getServerRuleData = (ruleLists, fieldList) => {
  if (!ruleCreateFormApi) {
    return;
  }
  const values = ruleCreateFormApi.getValues();

  const rule = [];
  const tempOpGroup = numbetToStropreatorMap[values.OperateType];
  const serverData: ConditionGroupExpr = {
    OpGroup: tempOpGroup === 'WU' ? 'AND' : tempOpGroup,
    ConditionGroups: rule,
  };
  for (let i = 0; i < ruleLists.length; i++) {
    const checkGroup = values[`item_opGroup_${i}_0`];
    const obj: RuleGroup = {
      Conditions: [],
      OpGroup: checkGroup === 'WU' ? 'AND' : checkGroup,
    };
    for (let z = 0; z < ruleLists[i].conditions.length; z++) {
      const info = getSelectRuleInfo(values[`item_lhs_${i}_${z}`], fieldList);
      const lhsKey = values[`item_lhs_${i}_${z}`].startsWith('$') ? 'VarExpr' : 'FeatureExpr';
      const lhs = {};
      if (lhsKey === 'FeatureExpr') {
        lhs[lhsKey] = { FeatureName: values[`item_lhs_${i}_${z}`] };
      } else {
        lhs[lhsKey] = values[`item_lhs_${i}_${z}`];
      }
      const tempOpCheck = values[`item_opCheck_${i}_${z}`];
      // Tree select selected value
      const targetValue = Array.isArray(values[`item_rhs_${i}_${z}`]) ?
        [...values[`item_rhs_${i}_${z}`]] :
        values[`item_rhs_${i}_${z}`];
      const value = getRuleItemValue(
        targetValue,
        info.FieldDataType,
        'server',
        info.OperatorFieldvalues[tempOpCheck].FieldValueType
      );
      // Current semi component problem
      const item: RuleItem = {
        Lhs: lhs,
        OpCheck: tempOpCheck === 'WU' ? 'AND' : tempOpCheck,
        Rhs: value,
      };
      obj.Conditions.push(item);
    }
    rule.push(obj);
  }
  return serverData;
};

// Check if the rule writing is filled in
const checkRuleInit = ruleList => {
  if (!ruleCreateFormApi) {
    return false;
  }
  const values = ruleCreateFormApi.getValues();
  for (let i = 0; i < ruleList.length; i++) {
    for (let z = 0; z < ruleList[i].conditions.length; z++) {
      const lhr = (values[`item_lhs_${i}_${z}`] || '').toString();
      if (!lhr || !lhr.length) {
        return false;
      }
      const op = (values[`item_opCheck_${i}_${z}`] || '').toString();
      if (!op) {
        return false;
      }
      const rhs = (String(values[`item_rhs_${i}_${z}`]) || '').toString();
      if (!rhs || !rhs.length) {
        return false;
      }
    }
  }
  return true;
};

export const useCreateRuleHooks = (ruleId = '', type: string, isLast, routeInfo) => {
  const { fieldLists: fieldList, skillGroupStrategy } = routeInfo;
  const [ruleLists, setRuleLists] = useState([
    {
      conditions: [
        {
          lhs: '',
          opCheck: '',
          rhs: '',
          key: getRandomInt(1, 1000000),
        },
      ],
      opGroup: 'AND',
    },
  ]);
  const [showContent, setShowContent] = useState(() => ruleId.length === 0);
  const [ruleListIsError, setRuleListIsError] = useState(false);
  const [initFormDetail, setInitFormDetail] = useState(() => getInItData(fieldList));

  const [sortedSkillGroupStrategy, setSortedSkillGroupStrategy] = useState<StrategyItem[]>([]);

  const changeSortedSkillGroupStrategy = list => {
    setSortedSkillGroupStrategy(list);

    const nowValues = safeJSONParse(JSON.stringify(ruleCreateFormApi.getValues()));
    const values = Object.assign({}, nowValues, {
      sortedGroupRouteStrategyList: list,
    });
    ruleCreateFormApi.setValues(values, { isOverride: true });
  };

  useEffect(() => {
    const serverSkillGroupStrategy = initFormDetail.skillGroupRouteStrategyList || [];
    // Skill Group List
    if (skillGroupStrategy?.length > 0 && ruleCreateFormApi) {
      // And the skill group list returned by the server
      const unique = skillGroupStrategy
        .filter(item => !serverSkillGroupStrategy.find(i => i.strategyKey === item.strategyKey));

      changeSortedSkillGroupStrategy(serverSkillGroupStrategy.map(item => ({
        ...item,
        strategyName: skillGroupStrategy.find(i => i.strategyKey === item.strategyKey)?.strategyName || item.strategyName,
      })).concat(unique.map(item => ({
        ...item,
        enable: 0,
      }))));
    }
  }, [skillGroupStrategy, initFormDetail, ruleCreateFormApi]);

  // Add rules to a whole chunk
  const addRuleGroup = () => {
    const nowList = safeJSONParse(JSON.stringify(ruleLists));
    nowList.push({
      conditions: [
        {
          lhs: '',
          opCheck: '',
          rhs: '',
          key: getRandomInt(1, 1000000),
        },
      ],
      opGroup: 'AND',
    });
    // debugger;
    setRuleLists(nowList);
  };
  // Set form ref
  const setRuleFormRef = formApi => {
    ruleCreateFormApi = formApi;
  };

  // Check if all the rules are filled in
  const checkRuelAllWrite = isLast => {
    // Is this the last one
    if (!ruleLists.length && !isLast) {
      setRuleListIsError(true);
      return false;
    }
    const tag = checkRuleInit(ruleLists);
    setRuleListIsError(!tag);
    return tag;
  };

  // When the trigger condition changes
  const handleOpRadioChange = e => {
    const nowValues = safeJSONParse(JSON.stringify(ruleCreateFormApi.getValues()));
    const opNumber = e.target.value;
    if (opNumber === -1) {
      return;
    }
    const temp = {};
    for (let i = 0; i < ruleLists.length; i++) {
      temp[`opGroup_${i}`] = numbetToStropreatorMap[opNumber];
    }
    const values = Object.assign({}, nowValues, temp);
    ruleCreateFormApi.setValues(values);
  };

  // When rule form options change
  const handleFormValueChange = (values, changedValue) => {
    // console.log(values, changedValue);
  };
  const getInitOpGroup = opKey => {
    const temp = {};
    for (let i = 0; i < ruleLists.length; i++) {
      temp[`opGroup_${i}`] = opKey === 1 ? 'AND' : 'OR';
      temp[`item_opGroup_${i}_0`] = ruleLists[i].opGroup;
    }
    return temp;
  };
  // Reset the value of the form when the rule changes
  const changeFormValuesByRuleList = () => {
    if (!ruleCreateFormApi) {
      return;
    }
    const nowValues = safeJSONParse(JSON.stringify(ruleCreateFormApi.getValues()));
    const temp = {};
    for (let i = 0; i < ruleLists.length; i++) {
      temp[`opGroup_${i}`] = nowValues.OperateType === 1 ? 'AND' : 'OR';
      temp[`item_opGroup_${i}_0`] = nowValues[`item_opGroup_${i}_0`] || 'AND';
    }
    const newFormValues = Object.assign({}, nowValues, temp);
    ruleCreateFormApi.setValues(newFormValues);
  };
  // Change how each subitem is linked
  const changeConditionsJoinWay = (parentIndex: number, myIndex: number, op: string) => {
    const nowRuleLists = safeJSONParse(JSON.stringify(ruleLists));
    // debugger;
    const ruleRef = nowRuleLists[parentIndex].conditions || [];
    if (op === 'WU') {
      // Choose None
      ruleRef.splice(myIndex + 1, ruleRef.length);
    }
    const temp = {};
    if (op === 'AND' || op === 'OR') {
      nowRuleLists[parentIndex].opGroup = op;
      if (nowRuleLists[parentIndex].conditions.length - 1 === myIndex) {
        ruleRef.push({
          lhs: '',
          opCheck: '',
          rhs: '',
          key: getRandomInt(1, 1000000),
        });
        const opcheck = fieldList[0]?.OperatorList[0];
        const lhs = fieldList[0]?.FieldName;
        const rhs = fieldList[0]?.OperatorFieldvalues[opcheck]?.FieldValueList[0]?.value;
        temp[`item_opCheck_${parentIndex}_${myIndex + 1}`] = opcheck;
        temp[`item_lhs_${parentIndex}_${myIndex + 1}`] = lhs;
        temp[`item_rhs_${parentIndex}_${myIndex + 1}`] = [rhs];
      }
    }
    setRuleLists(nowRuleLists);
    ruleCreateFormApi.setValues({ ...ruleCreateFormApi.getValues(), ...temp }, { isOverride: true });
  };

  const deleteGroup = groupIndex => {
    let nowList = safeJSONParse(JSON.stringify(ruleLists));
    if (!nowList[groupIndex]) {
      return;
    }
    const formValues = safeJSONParse(JSON.stringify(ruleCreateFormApi.getValues()));
    const nextFormValues = {};
    const tempParent = nowList.filter((item, index) => index !== groupIndex);
    for (let i = groupIndex + 1; i < nowList.length; i++) {
      const tempCondtion = nowList[i].conditions;
      for (let k = 0; k < tempCondtion.length; k++) {
        nextFormValues[`item_opCheck_${i - 1}_${k}`] = formValues[`item_opCheck_${i}_${k}`];
        nextFormValues[`item_lhs_${i - 1}_${k}`] = formValues[`item_lhs_${i}_${k}`];
        nextFormValues[`item_rhs_${i - 1}_${k}`] = formValues[`item_rhs_${i}_${k}`];
      }
    }
    nowList = tempParent;
    const newFormValues = Object.assign({}, formValues, nextFormValues);
    setRuleLists(nowList);
    setTimeout(() => {
      ruleCreateFormApi.setValues(newFormValues, { isOverride: true });
    }, 0);
  };

  // Delete single subrule
  const deleteRuleItem = (parentIndex: number, myIndex: number) => {
    let nowRuleLists = safeJSONParse(JSON.stringify(ruleLists));
    if (!nowRuleLists[parentIndex]) {
      return;
    }
    const temp = nowRuleLists[parentIndex].conditions;
    const formValues = safeJSONParse(JSON.stringify(ruleCreateFormApi.getValues()));
    const nextFormValues = {};
    for (let i = myIndex; i < temp.length - 1; i++) {
      // `item_opCheck_${parentIndex}_${index}`
      nextFormValues[`item_opCheck_${parentIndex}_${i}`] = formValues[`item_opCheck_${parentIndex}_${i + 1}`];
      nextFormValues[`item_lhs_${parentIndex}_${i}`] = formValues[`item_lhs_${parentIndex}_${i + 1}`];
      nextFormValues[`item_rhs_${parentIndex}_${i}`] = formValues[`item_rhs_${parentIndex}_${i + 1}`];
    }
    const nextValues = temp.filter((item, index) => index !== myIndex);
    nowRuleLists[parentIndex].conditions = nextValues;
    if (nowRuleLists[parentIndex].conditions.length === 0) {
      const tempParent = nowRuleLists.filter(item => item.conditions.length !== 0);
      for (let i = parentIndex + 1; i < nowRuleLists.length; i++) {
        const tempCondtion = nowRuleLists[i].conditions;
        for (let k = 0; k < tempCondtion.length; k++) {
          nextFormValues[`item_opCheck_${i - 1}_${k}`] = formValues[`item_opCheck_${i}_${k}`];
          nextFormValues[`item_lhs_${i - 1}_${k}`] = formValues[`item_lhs_${i}_${k}`];
          nextFormValues[`item_rhs_${i - 1}_${k}`] = formValues[`item_rhs_${i}_${k}`];
        }
      }
      nowRuleLists = tempParent;
    }
    const newFormValues = Object.assign({}, formValues, nextFormValues);
    setRuleLists(nowRuleLists);
    setTimeout(() => {
      ruleCreateFormApi.setValues(newFormValues, { isOverride: true });
    }, 0);
  };
  // Get data according to ID
  const getRuleInfoById = async serverRule => {
    const nowFormDetail = safeJSONParse(JSON.stringify(initFormDetail));
    const serverData = serverRule;
    // debugger;
    if (!serverData.ActionInfo) {
      Toast.error(I18n.t('data_acquisition_failed__please_exit_and_try_again', {}, 'Data acquisition failed, please exit and try again'));
      return;
    }
    let actionJSONStr = '';
    let percentStr = '';
    if (serverData?.ActionInfo?.ReturnValue?.FuncExpr) {
      actionJSONStr = serverData?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.substring(
        1,
        serverData?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.length - 1
      );
      percentStr = serverData?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.substring(
        1,
        serverData?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.length - 1
      );
    } else {
      actionJSONStr = serverData?.ActionInfo?.ReturnValue?.Constant || '';
      actionJSONStr = (serverData?.ActionInfo?.ReturnValue?.Constant || '')?.slice(1, actionJSONStr?.length - 1);
    }
    let action = {};
    try {
      const percentList = safeJSONParse(percentStr) || [];
      action = { ...safeJSONParse(actionJSONStr), percentList };
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getRuleInfoById' });
    }

    delete serverData?.ActionInfo;

    const nextDetail = Object.assign({}, nowFormDetail, serverData);
    nextDetail.opGroup = numbetToStropreatorMap[nextDetail.OperateType];
    parseServerAction(action, nextDetail);
    const { Expression } = serverRule;
    let ruleServerInfo = Expression;
    if (Expression.ConditionGroups.length) {
      for (let i = 0; i < Expression.ConditionGroups.length; i++) {
        if (Expression.ConditionGroups[i].ConditionGroups) {
          ruleServerInfo = Expression.ConditionGroups[i];
        }
      }
    }
    const changeInfo = getClientRuleList(ruleServerInfo, fieldList);
    setRuleLists(changeInfo.ruleListValues);
    const opGroupTemp = getInitOpGroup(nextDetail.OperateType);
    const nextValues = Object.assign({}, nextDetail, opGroupTemp, safeJSONParse(JSON.stringify(changeInfo.formValue)));
    if (fieldList.length === 0) {
      return;
    }
    setInitFormDetail(nextValues);
    setShowContent(true);
  };

  useEffect(() => {
    if (ruleId) {
      getRuleInfoById(routeInfo.ruleInfo);
    }
  }, [fieldList]);

  useEffect(() => {
    changeFormValuesByRuleList();
  }, [ruleLists]);

  return {
    initFormDetail,
    ruleLists,
    fieldList,
    ruleListIsError,
    showContent,
    sortedSkillGroupStrategy,
    addRuleGroup,
    changeConditionsJoinWay,
    setRuleFormRef,
    deleteRuleItem,
    handleFormValueChange,
    handleOpRadioChange,
    checkRuelAllWrite,
    changeFormValuesByRuleList,
    deleteGroup,
    changeSortedSkillGroupStrategy
  };
};
