import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Icon, Button, Modal, Toast } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { KefuPageEmptyContent } from '@ies/kefu-components';
import { useHistory } from 'react-router-dom';
import styles from './index.scss';
import RuleList from '@components/ForecastRule';
import { context } from '../index';
import { routeType } from '@/const/enums';
import { UserContext } from '@/context/user';
import * as t from '@http_idl/demo';
import { ROUTE_PATH, SERVICE_ROUTE } from '@constants/property';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { safeJSONParse } from '@/common/utils';
import { useDoorAuthority } from '@/hooks';
import { errorReporting } from '@/common/utils/errorReporting';
import RouteFilter from '@/components/RouteFilter';

function Index(props) {
  const [timer, changeTimer] = useState(null);
  const history = useHistory();
  const {
    changeDefaultItemKey,
    PreReleaseRulesList,
    onlineRuleList,
    newRuleId,
    changePreReleaseRulesList,
    skillGroups,
    bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    fieldList,
    ticketSkillGroups,
    changeSkillGroupList,
    changeRuleName,
    changeStatusList,
    statusList,
    skillGroupList,
    ruleName,
    existRulesIfNotFilter
  } = useContext(context);
  const user = useContext(UserContext);
  const { hasEditAuth } = useDoorAuthority();

  // New route
  const addNewRouteRule = () => {
    if (fieldList.length > 0 && skillGroups.length > 0) {
      history.push({
        pathname: '/service_create_rule',
        state: {
          plaformTag: routeType.service_routing,
          viewType: 'create',
          Priority: 1,
          EventKey: SERVICE_ROUTE,
          ruleLength: 0,
          skillGroups,
          fieldLists: fieldList,
          bizTypeList,
          ruleName,
          statusList,
          skillGroupList
        },
      });
    } else {
      Toast.warning(
        I18n.t('_please_check_the_skill_group_and_routing_condition_values_', {}, '「请检查技能组与路由条件值」')
      );
    }
  };

  const [searchData, changeListData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Get search data
  const findData = val => {
    if (val) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(PreReleaseRulesList);
    }
  };
  useEffect(() => {
    if (inputValue) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(inputValue));
      changeListData(safeJSONParse(JSON.stringify(newListData)));
    } else {
      changeListData(safeJSONParse(JSON.stringify(PreReleaseRulesList)));
    }
  }, [PreReleaseRulesList]);
  const onRelease = async () => {
    const RuleIds = PreReleaseRulesList.map(item => item.Id);
    const res = await t.demoClient.PublishRuleGroup({
      RuleGroupId: PreReleaseRulesList[0].RuleGroupId,
      RuleIds,
      eventKey: SERVICE_ROUTE,
    });
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    changeDefaultItemKey('onlineRule');
    // 创建一个新的状态对象，更新其中的特定属性
    const newState = { ...history.location.state, jumpPublish: false };

    // 使用 history.replace() 更新状态，但不刷新页面
    history.replace(ROUTE_PATH.IM_SERVICE, newState);
    await getPreReleaseRuleList();
    await getOnlineRuleList();
    Toast.success(I18n.t('rule_group_released_successfully', {}, '规则组发布成功'));
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        setInputValue(val);
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };
  const onCancle = async () => {
    try {
      setConfirmLoading(true);
      const res = await t.demoClient.UpdateRuleStatus({
        Ids: PreReleaseRulesList?.map(val => val?.Id),
        RuleStatus: 3,
        Version: 'v1',
        Draft: true,
        ruleGroupId: PreReleaseRulesList[0]?.RuleGroupId,
        operateGroupAllRules: 1,
      });
      if (res.code !== 0) {
        Toast.error(res.message);
        return;
      }
      setModalVisible(false);
      setModalVisible(false);
      changeDefaultItemKey('onlineRule');
      // 创建一个新的状态对象，更新其中的特定属性
      const newState = { ...history.location.state, jumpPublish: false };
  
      // 使用 history.replace() 更新状态，但不刷新页面
      history.replace(ROUTE_PATH.IM_SERVICE, newState);
      await getPreReleaseRuleList();
      await getOnlineRuleList();
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'onCancle' });
    }
  };
  return (
    // <Spin spinning={preIsLoading}>
    <div className={styles.allContainer}>
      <RouteFilter
        skillGroupList={skillGroupList}
        ruleName={ruleName}
        skillGroups={ticketSkillGroups}
        statusList={statusList}
        changeStatusList={changeStatusList}
        changeRuleName={changeRuleName}
        changeSkillGroupList={changeSkillGroupList}
      />

      {searchData.length ? (
        <>
          <div className={styles.routerList}>
            <RuleList
              bizTypeList={bizTypeList}
              newRuleId={newRuleId}
              getPreReleaseRuleList={getPreReleaseRuleList}
              ruleList={searchData}
              skillGroups={skillGroups}
              changePreReleaseRulesList={changePreReleaseRulesList}
              ruleType={routeType.service_routing}
              fieldList={fieldList}
              ticketAllSkillGroups={ticketSkillGroups}
              ruleName={ruleName}
              statusList={statusList}
              skillGroupList={skillGroupList}
            />
          </div>
          <div className={styles.foot}>
            <div className={styles.footBtn}>
              <Button
                onClick={() => setModalVisible(true)}
                disabled={isTTP() || havePermission()}
                className={styles.btnCancle}
              >
                {I18n.t('cancel', {}, '取消')}
              </Button>
              <Modal
                title={I18n.t('route_Starling_12', {}, '请问您是否确认要取消本次修改？')}
                visible={modalVisible}
                onOk={onCancle}
                cancelText={I18n.t('cancel', {}, '取消')}
                okText={I18n.t('ok', {}, '确定')}
                onCancel={() => setModalVisible(false)}
                okType="danger"
                confirmLoading={confirmLoading}
                icon={<Icon style={{ color: 'var(--color-danger)' }} type="alert_circle" size="extra-large" />}
              >
                {I18n.t('route_Starling_13', {}, '一旦取消，数据将无法恢复，请谨慎操作')}
              </Modal>
              <Button
                onClick={() => {
                  onRelease();
                }}
                disabled={isTTP() || havePermission() || !hasEditAuth}
                theme="solid"
                type="primary"
              >
                {I18n.t('release_online', {}, '发布上线')}
              </Button>
            </div>
          </div>
        </>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_pre_release_routing_rules', {}, '暂无预发布路由规则')}
        >
          {onlineRuleList.length || existRulesIfNotFilter ? null : (
            <Button
              icon="plus"
              theme="solid"
              type="primary"
              style={{ width: '100%' }}
              disabled={isTTP() || havePermission() || !hasEditAuth}
              onClick={addNewRouteRule}
            >
              {I18n.t('new_routing_rule', {}, '新建路由规则')}
            </Button>
          )}
        </KefuPageEmptyContent>
      )}
    </div>
    // </Spin>
  );
}

export default Index;
