import { ServiceAdminRule } from '@/const/types';
import { ChannelType } from '@http_idl/demo';
import { SERVICE_ROUTING_EVENT_ID, SERVICE_ROUTE } from '@constants/property';
import { useSkillGroups, useBizTypeList, useFieldList } from '@hooks/index';
import { useRuleList } from '@hooks/newUseRuleList';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
const filterRuleList = list =>
  (list || [])
    .map(o => {
      try {
        let newStr = '';
        let val = {} as any;
        if (o?.ActionInfo?.ReturnValue?.FuncExpr) {
          newStr = o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.substring(
            1,
            o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.length - 1
          );
          val = safeJSONParse(unescape(newStr));
          if (o?.ActionInfo?.ReturnValue?.FuncExpr?.FuncName === 'diversion') {
            o.isShunt = 1;
          } else {
            o.isShunt = val?.isShunt;
          }
          o.isAutoShunt = val?.isAutoShunt || 0;
          o.autoShuntSkillList = val?.autoShuntSkillList;
          o.skillGroupOverflowList = val?.skillGroupOverflowList;
          o.queueOverflowCount = val?.overflow_threshold;
          // o.
          o.SupportOverflow = val?.support_overflow;
          const skillListsStr = o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.substring(
            1,
            o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.length - 1
          );
          if (!skillListsStr && val?.isShunt) {
            o.skillList = val?.skillList;
            o.isAutoShunt = val?.isAutoShunt;
            o.autoShuntSkillList = val?.autoShuntSkillList;
          } else {
            if (!skillListsStr) {
              o.skillList = undefined;
            } else {
              o.skillList = safeJSONParse(unescape(skillListsStr));
            }
          }
        } else {
          newStr = o?.ActionInfo?.ReturnValue?.Constant?.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
          val = safeJSONParse(unescape(newStr));
          o.isShunt = val?.isAutoShunt;
          o.skillList = val?.skillList;
          o.isAutoShunt = val?.isAutoShunt;
          o.autoShuntSkillList = val?.autoShuntSkillList;
          o.skillGroupOverflowList = val?.skillGroupOverflowList;
          o.queueOverflowCount = val?.overflow_threshold;
          o.SupportOverflow = val?.support_overflow;
        }
        o.UpdatedAt = `${o?.UpdatedAt?.substring(0, o?.UpdatedAt?.indexOf?.('T'))} ${o?.UpdatedAt?.substring(
          o?.UpdatedAt?.indexOf?.('T') + 1,
          o?.UpdatedAt?.length
        )}`;
        o.SkillGroupOverflow = (val?.skill_group_overflow || [])?.map(o => o?.id);
        o.SkillGroupId = val?.skill_group?.id;
        o.OverflowThreshold = val?.overflow_threshold;
        o.IsOpen = val?.is_open;
        o.BotId = val?.bot_id?.id;
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
        o.SkillGroupId = '';
        o.SupportOverflow = 0; // 1: support overflow; 0: not supported
        o.OverflowThreshold = 0;
        o.SkillGroupOverflow = [];
        o.IsOpen = 0; // 1: supported; 0: not supported
        o.BotId = '';
        console.error(error);
      }

      return o;
    })
    ?.sort((a, b) => a.Priority - b.Priority);

export const useRoutingRule = (defaultItemKey: string, ruleName?: string, skillGroupList?: string[], statusList?: number[]) => {
  const { bizTypeList, loaded: bizTypeListLoaded } = useBizTypeList();
  const { skillGroups, ticketSkillGroups } = useSkillGroups({ channelType: ChannelType.IM, routerKey: defaultItemKey });
  // const { cardList } = useCardList({
  //   when: bizTypeListLoaded,
  //   appids: bizTypeList.map(v => v.ID.toString()),
  // });
  const { fieldList } = useFieldList({
    when: bizTypeListLoaded,
    eventId: SERVICE_ROUTING_EVENT_ID,
    appids: bizTypeList.map(v => v.ID.toString()),
  });
  const {
    loading: loadingRuleList,
    loaded: loadedRuleList,
    filterSearch,
    setInputValue,
    inputValue,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    existRulesIfNotFilter
  } = useRuleList<ServiceAdminRule>({
    eventKey: SERVICE_ROUTE,
    filterRuleList,
    TabKey: defaultItemKey,
    ruleName,
    skillGroupList,
    statusList,
  });

  return {
    loadingRuleList,
    loadedRuleList,
    bizTypeList,
    skillGroups,
    // cardList,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    inputValue,
    setInputValue,
    fieldList,
    filterSearch,
    ticketSkillGroups,
    existRulesIfNotFilter,
  };
};
