import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane, Spin } from '@ies/semi-ui-react';
import * as styles from './index.scss';
import ServiceOnlineRules from '../newServiceRouting/onlineRules';
import ServicePreReleaseRules from '../newServiceRouting/preReleaseRules';
import { useRoutingRule } from './use';
import { useHistory } from 'react-router-dom';
import { UserContext } from '@/context/user';
export const context = React.createContext(null);

const RouterPage: React.FC = () => {
  const history = useHistory();
  const user = useContext(UserContext);

  const { 
    jumpPublish,
    newRuleId,
    ruleName: ruleNameState,
    statusList: statusListState,
    skillGroupList: skillGroupListState,
    accessPartyId: accessPartyIdState
  } = Object.assign(
    { jumpPublish: false, newRuleId: '', ruleName: '', statusList: [], skillGroupList: [], accessPartyId: '' },
    history.location.state || {}
  );

  const [defaultItemKey, changeDefaultItemKey] = useState('onlineRule');
  const [ruleName, changeRuleName] = useState((accessPartyIdState === user?.accessPartyId) ? ruleNameState : '');
  const [skillGroupList, changeSkillGroupList] = useState((accessPartyIdState === user?.accessPartyId) ? skillGroupListState : []);
  const [statusList, changeStatusList] = useState((accessPartyIdState === user?.accessPartyId) ? statusListState : []); 

  const {
    ruleList,
    preRuleList,
    loadingRuleList,
    // loadedRuleList,
    bizTypeList,
    skillGroups,
    getPreReleaseRuleList,
    getOnlineRuleList,
    // inputValue,
    setInputValue,
    fieldList,
    // filterSearch,
    // botList,
    ticketSkillGroups,
    existRulesIfNotFilter
  } = useRoutingRule(defaultItemKey, ruleName, skillGroupList, statusList);
  const [isLoading, setLoading] = useState(false);
  const [onlineRuleList, changeOnlineRuleList] = useState(ruleList);
  const [PreReleaseRulesList, changePreReleaseRulesList] = useState(preRuleList);

  const changeActiveKey = key => {
    changeDefaultItemKey(key);
    changeSkillGroupList([]);
    changeStatusList([]);
    changeRuleName('');
  };

  const jumpToPpublish = () => {
    if (jumpPublish) {
      changeDefaultItemKey('readyToRelease');
    }
  };
  useEffect(() => {
    jumpToPpublish();
  }, []);
  useEffect(() => {
    changePreReleaseRulesList(preRuleList);
  }, [preRuleList]);
  useEffect(() => {
    changeOnlineRuleList(ruleList);
  }, [ruleList]);
  useEffect(() => {
    setLoading(loadingRuleList);
  }, [loadingRuleList]);

  const clearFilter = () => {
    changeSkillGroupList([]);
    changeStatusList([]);
    changeRuleName('');
  };

  const content = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        <Tabs
          onChange={key => changeActiveKey(key)}
          defaultActiveKey={defaultItemKey}
          activeKey={defaultItemKey}
          type="line"
          keepDOM={false}
        >
          {/* <context.Provider value={{defaultItemKey, changeDefaultItemKey }}> */}
          <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
            <context.Provider
              value={{
                defaultItemKey,
                changeDefaultItemKey,
                onlineRuleList,
                changeOnlineRuleList,
                changePreReleaseRulesList,
                skillGroups,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                // botList,
                fieldList,
                ticketSkillGroups,
                changeRuleName,
                changeSkillGroupList,
                changeStatusList,
                ruleName,
                skillGroupList,
                statusList,
                existRulesIfNotFilter
              }}
            >
              {/* <ServiceOnlineRules /> */}
              {/* {platform == platformMap.bot_routingV2? (<BotOnlineRules></BotOnlineRules>):
                  platform == platformMap.offline_routingV2? (<OfflineOnlineRules></OfflineOnlineRules>):
                  platform == platformMap.quality_check_routingV2? (<QualityCheckOnlineRules></QualityCheckOnlineRules>):
                  platform == platformMap.service_routingV2? (<ServiceOnlineRules></ServiceOnlineRules>):
                  platform == platformMap.ticket_routingV2? (<TicketOnlineRules></TicketOnlineRules>): null
                } */}
              <ServiceOnlineRules />
            </context.Provider>
          </TabPane>
          <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
            <context.Provider
              value={{
                newRuleId,
                defaultItemKey,
                PreReleaseRulesList,
                onlineRuleList,
                changeDefaultItemKey,
                changePreReleaseRulesList,
                changeOnlineRuleList,
                skillGroups,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                // botList,
                fieldList,
                ticketSkillGroups,
                changeRuleName,
                changeSkillGroupList,
                changeStatusList,
                ruleName,
                skillGroupList,
                statusList,
                existRulesIfNotFilter    
              }}
            >
              {/* {platform == platformMap.bot_routingV2? (<BotPreReleaseRules></BotPreReleaseRules>):
              platform == platformMap.offline_routingV2? (<OfflinePreReleaseRules></OfflinePreReleaseRules>):
              platform == platformMap.quality_check_routingV2? (<QualityCheckPreReleaseRules></QualityCheckPreReleaseRules>):
              platform == platformMap.service_routingV2? (<ServicePreReleaseRules></ServicePreReleaseRules>):
              platform == platformMap.ticket_routingV2? (<TicketPreReleaseRules></TicketPreReleaseRules>): null
              } */}
              <ServicePreReleaseRules />
            </context.Provider>
          </TabPane>
          {/* </context.Provider> */}
          {/* < TabPane tab = {I18n.t ('historical_version ', {}, ' Historical Version') } itemKey = "historyRule" >
             < HistoryRule/>
           </TabPane > */}
        </Tabs>
      </div>
    </Spin>
  );
  return content;
};
export default RouterPage;
