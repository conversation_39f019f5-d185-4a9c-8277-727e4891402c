import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Button, Empty, Toast } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import styles from './index.scss';
import RuleList from '@components/newRuleList';
import { context } from '../index';
import { routeType } from '@/const/enums';
import * as t from '@http_idl/demo';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import RouteFilter from '@/components/RouteFilter';

function Index() {
  const [timer, changeTimer] = useState(null);
  const {
    changeDefaultItemKey,
    onlineRuleList,
    // defaultItemKey,
    // PreReleaseRulesList,
    // changePreReleaseRulesList,
    // changeOnlineRuleList,
    skillGroups,
    // bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    ticketSkillGroups,
    // setInputValue,
    fieldList,
    changeRuleName,
    changeSkillGroupList,
    changeStatusList,
    ruleName,
    skillGroupList,
    statusList
    // botList,
  } = useContext(context);
  const [searchData, changeListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const copyRouterRule = async () => {
    try {
      setLoading(true);
      const res = await t.demoClient.CopyRuleGroup({
        RuleGroupId: onlineRuleList[0].RuleGroupId,
      });
      if (res?.code === 200) {
        setLoading(false);
        Toast.success(I18n.t('rule_group_replication_successful', {}, '规则组复制成功'));
        await getPreReleaseRuleList();
        changeDefaultItemKey('readyToRelease');
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
    } finally {
      setLoading(false);
    }

    // history.push({pathname:'/service_routingV2', state: {readyToRelease: 'readyToRelease'}})
  };
  useEffect(() => {
    changeListData(safeJSONParse(JSON.stringify(onlineRuleList)));
  }, [onlineRuleList]);
  // const ruleType = 'service_routing';
  // Get search data
  const findData = val => {
    if (val) {
      const newListData = onlineRuleList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(onlineRuleList);
    }
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };
  return (
    <div className={styles.allContainer}>
      <div className={styles.topHead}>
        <RouteFilter 
          skillGroupList={skillGroupList}
          ruleName={ruleName}
          skillGroups={ticketSkillGroups}
          statusList={statusList}
          changeStatusList={changeStatusList}
          changeRuleName={changeRuleName}
          changeSkillGroupList={changeSkillGroupList}
        />
        <Button
          disabled={!searchData.length || isTTP() || havePermission()}
          theme="solid"
          type="primary"
          icon="plus"
          loading={loading}
          onClick={copyRouterRule}
        >
          {I18n.t('copy_to_pre_release_rules', {}, '复制到预发布规则')}
        </Button>
      </div>
      {searchData.length ? (
        <div className={styles.routerList}>
          <RuleList
            ruleList={searchData}
            skillGroups={skillGroups}
            fieldList={fieldList}
            ruleType={routeType.service_routing}
            ticketAllSkillGroups={ticketSkillGroups}
          />
        </div>
      ) : (
        <Empty image={NoContent} description="" title={I18n.t('no_online_routing_rules', {}, '暂无线上路由规则')} />
      )}
    </div>
  );
}
export default Index;
