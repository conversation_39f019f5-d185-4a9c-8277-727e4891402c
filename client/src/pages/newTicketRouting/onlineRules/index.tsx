import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Input, Icon, Button, Empty, Toast } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import * as t from '@http_idl/demo';
// import NoContentDark from '@ies/semi-illustrations/noContent-dark.svg';
import styles from './index.scss';
import RuleList from '@components/newRuleList';
import { context } from '../index';
import { routeType } from '@/const/enums';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import RouteFilter from '@/components/RouteFilter';
function Index() {
  const [timer, changeTimer] = useState(null);
  const [loading, setLoading] = useState(false);
  const {
    changeDefaultItemKey,
    onlineRuleList,
    loadedRuleList,
    loadingRuleList,
    defaultItemKey,
    PreReleaseRulesList,
    changePreReleaseRulesList,
    changeOnlineRuleList,
    skillGroups,
    bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    setInputValue,
    fieldList,
    botList,
    ticketSkillGroups,
    changeRuleName,
    changeSkillGroupList,
    changeStatusList,
    ruleName,
    skillGroupList,
    statusList
  } = useContext(context);
  // const recordList = safeJSONParse(JSON.stringify(onlineRuleList))
  const isShow = !onlineRuleList || !onlineRuleList.length;
  const [searchData, changeListData] = useState([]);
  const copyRouterRule = async () => {
    try {
      setLoading(true);
      const res = await t.demoClient.CopyRuleGroup({
        RuleGroupId: onlineRuleList[0].RuleGroupId,
      });
      if (res?.code === 200) {
        setLoading(false);
        Toast.success(I18n.t('rule_group_replication_successful', {}, '规则组复制成功'));
        await getPreReleaseRuleList();
        changeDefaultItemKey('readyToRelease');
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
    } finally {
      setLoading(false);
    }

    // history.push({pathname:'/service_routingV2', state: {readyToRelease: 'readyToRelease'}})
  };
  useEffect(() => {
    changeListData(safeJSONParse(JSON.stringify(onlineRuleList)));
  }, [onlineRuleList]);
  // New route
  // Get search data
  const findData = val => {
    if (val) {
      const newListData = onlineRuleList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(onlineRuleList);
    }
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };
  return (
    <div className={styles.allContainer}>
      <div className={styles.topHead}>
        <RouteFilter 
          skillGroupList={skillGroupList}
          ruleName={ruleName}
          skillGroups={ticketSkillGroups}
          statusList={statusList}
          changeStatusList={changeStatusList}
          changeRuleName={changeRuleName}
          changeSkillGroupList={changeSkillGroupList}
        />
        <Button
          theme="solid"
          disabled={!searchData?.length || isTTP() || havePermission()}
          type="primary"
          icon="plus"
          loading={loading}
          onClick={copyRouterRule}
        >
          {I18n.t('copy_to_pre_release_rules', {}, '复制到预发布规则')}
        </Button>
      </div>
      {searchData?.length ? (
        <div className={styles.routerList}>
          <RuleList
            ruleList={searchData}
            skillGroups={skillGroups}
            fieldList={fieldList}
            ruleType={routeType.ticket_routing}
            ticketAllSkillGroups={ticketSkillGroups}
          />
        </div>
      ) : (
        <Empty image={NoContent} description="" title={I18n.t('no_online_routing_rules', {}, '暂无线上路由规则')} />
      )}
    </div>
  );
}

export default Index;
