import { AdminRule, ChannelType, RuleType } from '@http_idl/demo';
import { useIsEcomAccessParty, useTicketRuleBaseParam, useNewTicketRuleBaseParam } from '@hooks/useTicketRuleBaseParam';

import { useSkillGroups, useBizTypeList, useFieldList, useBotList } from '@hooks/index';
import { useRuleList } from '@hooks/newUseRuleList';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
const filterRuleList = list =>
  (list || [])
    ?.map(o => {
      try {
        let newStr = '';
        let val = {} as any;
        if (o?.ActionInfo?.ReturnValue?.FuncExpr) {
          newStr = o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.substring(
            1,
            o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.originData?.Constant?.length - 1
          );
          val = safeJSONParse(unescape(newStr));
          if (o?.ActionInfo?.ReturnValue?.FuncExpr?.FuncName === 'diversion') {
            o.isShunt = 1;
          } else {
            o.isShunt = val?.isShunt;
          }
          o.isAutoShunt = val?.isAutoShunt;
          o.autoShuntSkillList = val?.autoShuntSkillList;
          o.skillGroupOverflowList = val?.skillGroupOverflowList;
          o.queueOverflowCount = val?.overflow_threshold;
          // o.
          o.SupportOverflow = val?.support_overflow;
          const skillListsStr = o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.substring(
            1,
            o?.ActionInfo?.ReturnValue?.FuncExpr?.ParamExprMap?.percentList?.Constant?.length - 1
          );
          if (!skillListsStr && val?.isShunt) {
            o.skillList = val?.skillList;
            o.isAutoShunt = val?.isAutoShunt;
            o.autoShuntSkillList = val?.autoShuntSkillList;
          } else {
            if (!skillListsStr) {
              o.skillList = undefined;
            } else {
              o.skillList = safeJSONParse(unescape(skillListsStr));
            }
          }
        } else {
          newStr = o?.ActionInfo?.ReturnValue?.Constant?.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
          val = safeJSONParse(unescape(newStr));
          o.isShunt = val?.isAutoShunt;
          o.skillList = val?.skillList;
          o.isAutoShunt = val?.isAutoShunt;
          o.autoShuntSkillList = val?.autoShuntSkillList;
          o.skillGroupOverflowList = val?.skillGroupOverflowList;
          o.queueOverflowCount = val?.overflow_threshold;
          o.SupportOverflow = val?.support_overflow;
        }
        o.UpdatedAt = `${o?.UpdatedAt?.substring(0, o?.UpdatedAt?.indexOf?.('T'))} ${o?.UpdatedAt?.substring(
          o?.UpdatedAt?.indexOf?.('T') + 1,
          o?.UpdatedAt?.length
        )}`;
        o.SkillGroupId = val?.skill_group?.id;
        o.IsOpen = val?.is_open;
        o.BotId = val?.bot_id?.id;
        (o.SupportReclaim = val?.support_reclaim), (o.RouteType = val?.route_type);
        o.RouteTypeName = val?.route_type_name;
        o.ReclaimCondition = val?.reclaim_config?.[0]?.aimMetaId;
        o.ReclaimConditionName = val?.reclaim_config_name;
        const minutes = val?.reclaim_config?.[0]?.aimSteps?.[0]?.actionTime || 0;
        o.ReclaimMinutes = minutes % 60;
        o.ReclaimHours = (minutes - (minutes % 60)) / 60;
        o.ReclaimType = val?.reclaim_config?.[0]?.reclaimType;
        o.SkillGroupReclaim = val?.skill_group_reclaim?.[0]?.id;
        o.Expression.ConditionGroups = o?.Expression?.ConditionGroups?.filter(
          v => v?.Conditions?.[0]?.Lhs?.VarExpr !== '$upgrade_time'
        );
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
        o.SkillGroupId = '';
        o.SupportOverflow = 0; // 1: support overflow; 0: not supported
        o.OverflowThreshold = 0;
        o.SkillGroupOverflow = [];
        o.IsOpen = 0; // 1: supported; 0: not supported
        o.BotId = '';
        (o.SupportReclaim = 0), (o.RouteType = undefined);
        o.RouteTypeName = '';
        o.ReclaimCondition = '';
        o.ReclaimConditionName = '';
        o.ReclaimHours = 0;
        o.ReclaimMinutes = 30;
        o.SkillGroupReclaim = [];
      }
      return o;
    })
    ?.sort((a, b) => a.Priority - b.Priority);

export const useRoutingRule = (
  tabKey?: string,
  defaultItemKey?: string,
  ruleName?: string,
  skillGroupList?: string[],
  statusList?: number[]
) => {
  const isEcomAccessParty = useIsEcomAccessParty();
  const { EventKey } = useNewTicketRuleBaseParam(tabKey);
  const { appId, eventId, sourceId } = useTicketRuleBaseParam(tabKey);
  const { bizTypeList, loaded: bizTypeListLoaded } = useBizTypeList();
  const getChannelType = () => {
    let channelType = 0;
    if (tabKey === '') {
      return channelType;
    }
    if (tabKey === 'afterSalesRoute') {
      channelType = ChannelType.AFTER_SALE;
    } else {
      if (isEcomAccessParty) {
        channelType = ChannelType.ECOM_TICKET;
      } else {
        channelType = ChannelType.TICKET;
      }
    }
    return channelType;
  };

  const { skillGroups, ticketSkillGroups } = useSkillGroups({
    channelType: getChannelType(),
    routerKey: tabKey,
  });
  const { botList } = useBotList();
  // const { cardList } = useCardList({
  //   when: bizTypeListLoaded,
  //   appids: bizTypeList.map(v => v.ID.toString()),
  // });

  const { fieldList } = useFieldList({
    when: bizTypeListLoaded,
    eventId,
    appids: bizTypeList.map(v => v.ID.toString()),
  });
  const {
    loading: loadingRuleList,
    loaded: loadedRuleList,
    filterSearch,
    setInputValue,
    inputValue,
    ruleList,
    preRuleList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    existRulesIfNotFilter,
  } = useRuleList({
    eventKey: EventKey,
    filterRuleList,
    TabKey: defaultItemKey,
    ruleName,
    skillGroupList,
    statusList,
  });
  return {
    loadingRuleList,
    loadedRuleList,
    bizTypeList,
    skillGroups,
    // cardList,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    inputValue,
    setInputValue,
    fieldList,
    filterSearch,
    botList,
    ticketSkillGroups,
    existRulesIfNotFilter,
  };
};
