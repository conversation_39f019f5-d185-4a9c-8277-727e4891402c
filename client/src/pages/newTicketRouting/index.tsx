import { I18n } from '@ies/starling_intl';
import React, { useContext, useState, useEffect } from 'react';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane, Spin } from '@ies/semi-ui-react';
import * as styles from './index.scss';
import TicketOnlineRules from '../newTicketRouting/onlineRules';
import TicketPreReleaseRules from '../newTicketRouting/preReleaseRules';
import { useRoutingRule } from './use';
import { UserContext } from '@context/user';
import { useHistory } from 'react-router-dom';
import { AOP_TICKET_ROUTE, ACCESS_PARTY } from '@/common/constants/property';
export const context = React.createContext(null);
// This is route mapping
const platformMap = {
  service_routingV2: 'service_create_rule',
  bot_routingV2: 'bot_create_rule',
  offline_routingV2: 'offline_create_rule',
  quality_check_routingV2: 'qualitycheck_create_rule',
  ticket_routingV2: 'ticket_create_rule',
};

const RouterPage: React.FC = () => {
  const history = useHistory();
  const user = useContext(UserContext);

  const {
    jumpPublish,
    newRuleId,
    eventKey,
    ruleName: ruleNameState,
    statusList: statusListState,
    skillGroupList: skillGroupListState,
    accessPartyId: accessPartyIdState
  } = Object.assign(
    { jumpPublish: false, newRuleId: '', eventKey: '', ruleName: '', statusList: [], skillGroupList: [], accessPartyId: '' },
    history.location.state || {}
  );

  const [isLoading, setLoading] = useState(false);
  const [defaultItemKey, changeDefaultItemKey] = useState('onlineRule');
  const [defaultItemKeyOne, setDefaultItemKeyOne] = useState('');
  const [ruleName, changeRuleName] = useState((accessPartyIdState === user?.accessPartyId) ? ruleNameState : '');
  const [skillGroupList, changeSkillGroupList] = useState((accessPartyIdState === user?.accessPartyId) ? skillGroupListState : []);
  const [statusList, changeStatusList] = useState((accessPartyIdState === user?.accessPartyId) ? statusListState : []);

  const clearFilter = () => {
    changeSkillGroupList([]);
    changeStatusList([]);
    changeRuleName('');
  };

  const changeActiveKey = key => {
    changeDefaultItemKey(key);
    clearFilter();
  };
  const {
    ruleList,
    preRuleList,
    loadingRuleList,
    loadedRuleList,
    bizTypeList,
    skillGroups,
    getPreReleaseRuleList,
    getOnlineRuleList,
    // inputValue,
    setInputValue,
    fieldList,
    // filterSearch,
    botList,
    ticketSkillGroups,
    existRulesIfNotFilter
  } = useRoutingRule(defaultItemKeyOne, defaultItemKey, ruleName, skillGroupList, statusList);
  const [onlineRuleList, changeOnlineRuleList] = useState(ruleList);
  const [PreReleaseRulesList, changePreReleaseRulesList] = useState(preRuleList);
  const { accessPartyId } = user;

  const jumpToPpublish = () => {
    if (jumpPublish) {
      let tabKey = 'ticketRoute';
      if (
        [ACCESS_PARTY.GLOAB_SELLING, ACCESS_PARTY.SELLWE_SERVICE_HOSTING].includes(accessPartyId) &&
        eventKey === AOP_TICKET_ROUTE
      ) {
        tabKey = 'afterSalesRoute';
      }
      setDefaultItemKeyOne(tabKey);
      changeDefaultItemKey('readyToRelease');
    } else {
      setDefaultItemKeyOne('ticketRoute');
    }
  };
  useEffect(() => {
    jumpToPpublish();
  }, []);
  useEffect(() => {
    changePreReleaseRulesList(preRuleList);
  }, [preRuleList]);
  useEffect(() => {
    changeOnlineRuleList(ruleList);
  }, [ruleList]);
  useEffect(() => {
    setLoading(loadingRuleList);
  }, [loadingRuleList]);

  const changeActiveKeyOne = key => {
    changeOnlineRuleList([]);
    changePreReleaseRulesList([]);
    setDefaultItemKeyOne(key);
    clearFilter();
  };

  // const history = useHistory();
  const location = history.location.pathname.slice(1);
  const platform = platformMap[location] || '';
  const ticketContent = (
    <Tabs
      onChange={key => changeActiveKey(key)}
      defaultActiveKey={defaultItemKey}
      activeKey={defaultItemKey}
      type={
        [ACCESS_PARTY.GLOAB_SELLING, ACCESS_PARTY.SELLWE_SERVICE_HOSTING].includes(accessPartyId) ? 'button' : 'line'
      }
      keepDOM={false}
    >
      {/* <context.Provider value={{defaultItemKey, changeDefaultItemKey }}> */}
      <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
        <context.Provider
          value={{
            defaultItemKey,
            defaultItemKeyOne,
            changeDefaultItemKey,
            loadingRuleList,
            onlineRuleList,
            changeOnlineRuleList,
            changePreReleaseRulesList,
            skillGroups,
            bizTypeList,
            loadedRuleList,
            getPreReleaseRuleList,
            getOnlineRuleList,
            setInputValue,
            botList,
            fieldList,
            ticketSkillGroups,
            changeRuleName,
            changeSkillGroupList,
            changeStatusList,
            ruleName,
            skillGroupList,
            statusList
          }}
        >
          <TicketOnlineRules />
        </context.Provider>
      </TabPane>
      <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
        <context.Provider
          value={{
            newRuleId,
            defaultItemKeyOne,
            defaultItemKey,
            PreReleaseRulesList,
            onlineRuleList,
            changeDefaultItemKey,
            changePreReleaseRulesList,
            changeOnlineRuleList,
            skillGroups,
            bizTypeList,
            getPreReleaseRuleList,
            getOnlineRuleList,
            setInputValue,
            botList,
            fieldList,
            ticketSkillGroups,
            changeRuleName,
            changeSkillGroupList,
            changeStatusList,
            ruleName,
            skillGroupList,
            statusList,
            existRulesIfNotFilter,
          }}
        >
          <TicketPreReleaseRules />
        </context.Provider>
      </TabPane>
    </Tabs>
  );
  const afterSalesContent = (
    <Tabs
      onChange={key => changeActiveKey(key)}
      defaultActiveKey={defaultItemKey}
      activeKey={defaultItemKey}
      type="button"
      keepDOM={false}
    >
      <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
        <context.Provider
          value={{
            defaultItemKey,
            defaultItemKeyOne,
            changeDefaultItemKey,
            loadingRuleList,
            onlineRuleList,
            changeOnlineRuleList,
            changePreReleaseRulesList,
            skillGroups,
            bizTypeList,
            loadedRuleList,
            getPreReleaseRuleList,
            getOnlineRuleList,
            setInputValue,
            botList,
            fieldList,
            ticketSkillGroups,
            changeRuleName,
            changeSkillGroupList,
            changeStatusList,
            ruleName,
            skillGroupList,
            statusList,
            existRulesIfNotFilter,
          }}
        >
          {/* <ServiceOnlineRules /> */}
          {/* {platform == platformMap.bot_routingV2? (<BotOnlineRules></BotOnlineRules>):
            platform == platformMap.offline_routingV2? (<OfflineOnlineRules></OfflineOnlineRules>):
            platform == platformMap.quality_check_routingV2? (<QualityCheckOnlineRules></QualityCheckOnlineRules>):
            platform == platformMap.service_routingV2? (<ServiceOnlineRules></ServiceOnlineRules>):
            platform == platformMap.ticket_routingV2? (<TicketOnlineRules></TicketOnlineRules>): null
          } */}
          <TicketOnlineRules />
        </context.Provider>
      </TabPane>
      <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
        <context.Provider
          value={{
            newRuleId,
            defaultItemKey,
            defaultItemKeyOne,
            PreReleaseRulesList,
            onlineRuleList,
            changeDefaultItemKey,
            changePreReleaseRulesList,
            changeOnlineRuleList,
            skillGroups,
            bizTypeList,
            getPreReleaseRuleList,
            getOnlineRuleList,
            setInputValue,
            botList,
            fieldList,
            ticketSkillGroups,
            changeRuleName,
            changeSkillGroupList,
            changeStatusList,
            ruleName,
            skillGroupList,
            statusList,
            existRulesIfNotFilter,
          }}
        >
          {/* {platform == platformMap.bot_routingV2? (<BotPreReleaseRules></BotPreReleaseRules>):
        platform == platformMap.offline_routingV2? (<OfflinePreReleaseRules></OfflinePreReleaseRules>):
        platform == platformMap.quality_check_routingV2? (<QualityCheckPreReleaseRules></QualityCheckPreReleaseRules>):
        platform == platformMap.service_routingV2? (<ServicePreReleaseRules></ServicePreReleaseRules>):
        platform == platformMap.ticket_routingV2? (<TicketPreReleaseRules></TicketPreReleaseRules>): null
        } */}
          <TicketPreReleaseRules />
        </context.Provider>
      </TabPane>
      {/* </context.Provider> */}
      {/* < TabPane tab = {I18n.t ('historical_version ', {}, ' Historical Version') } itemKey = "historyRule" >
       < HistoryRule/>
     </TabPane > */}
    </Tabs>
  );
  const content = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        <Tabs
          onChange={key => changeActiveKey(key)}
          defaultActiveKey={defaultItemKey}
          activeKey={defaultItemKey}
          type="line"
          keepDOM={false}
        >
          {/* <context.Provider value={{defaultItemKey, changeDefaultItemKey }}> */}
          <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
            <context.Provider
              value={{
                defaultItemKey,
                changeDefaultItemKey,
                loadingRuleList,
                onlineRuleList,
                changeOnlineRuleList,
                changePreReleaseRulesList,
                skillGroups,
                bizTypeList,
                loadedRuleList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                botList,
                fieldList,
                ticketSkillGroups,
              }}
            >
              {/* <ServiceOnlineRules /> */}
              {/* {platform == platformMap.bot_routingV2? (<BotOnlineRules></BotOnlineRules>):
                  platform == platformMap.offline_routingV2? (<OfflineOnlineRules></OfflineOnlineRules>):
                  platform == platformMap.quality_check_routingV2? (<QualityCheckOnlineRules></QualityCheckOnlineRules>):
                  platform == platformMap.service_routingV2? (<ServiceOnlineRules></ServiceOnlineRules>):
                  platform == platformMap.ticket_routingV2? (<TicketOnlineRules></TicketOnlineRules>): null
                } */}
              <TicketOnlineRules />
            </context.Provider>
          </TabPane>
          <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
            <context.Provider
              value={{
                newRuleId,
                defaultItemKey,
                PreReleaseRulesList,
                onlineRuleList,
                changeDefaultItemKey,
                changePreReleaseRulesList,
                changeOnlineRuleList,
                skillGroups,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                botList,
                fieldList,
                ticketSkillGroups,
              }}
            >
              {/* {platform == platformMap.bot_routingV2? (<BotPreReleaseRules></BotPreReleaseRules>):
              platform == platformMap.offline_routingV2? (<OfflinePreReleaseRules></OfflinePreReleaseRules>):
              platform == platformMap.quality_check_routingV2? (<QualityCheckPreReleaseRules></QualityCheckPreReleaseRules>):
              platform == platformMap.service_routingV2? (<ServicePreReleaseRules></ServicePreReleaseRules>):
              platform == platformMap.ticket_routingV2? (<TicketPreReleaseRules></TicketPreReleaseRules>): null
              } */}
              <TicketPreReleaseRules />
            </context.Provider>
          </TabPane>
          {/* </context.Provider> */}
          {/* < TabPane tab = {I18n.t ('historical_version ', {}, ' Historical Version') } itemKey = "historyRule" >
             < HistoryRule/>
           </TabPane > */}
        </Tabs>
      </div>
    </Spin>
  );
  const containComponent = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        {![ACCESS_PARTY.GLOAB_SELLING, ACCESS_PARTY.SELLWE_SERVICE_HOSTING].includes(accessPartyId) && (
          <>{ticketContent}</>
        )}
        {[ACCESS_PARTY.GLOAB_SELLING, ACCESS_PARTY.SELLWE_SERVICE_HOSTING].includes(accessPartyId) && (
          <Tabs
            onChange={key => changeActiveKeyOne(key)}
            defaultActiveKey={defaultItemKeyOne}
            activeKey={defaultItemKeyOne}
            type="line"
            keepDOM={false}
          >
            <TabPane tab={I18n.t('ticket_routing', {}, '工单路由')} itemKey="ticketRoute">
              {ticketContent}
            </TabPane>
            <TabPane tab={I18n.t('task_order_routing', {}, '任务单路由')} itemKey="afterSalesRoute">
              {afterSalesContent}
            </TabPane>
          </Tabs>
        )}
      </div>
    </Spin>
  );
  return containComponent;
};
export default RouterPage;
