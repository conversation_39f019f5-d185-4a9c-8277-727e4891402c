import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Input, Icon, Button, Toast, Modal } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { KefuPageEmptyContent } from '@ies/kefu-components';
import { useHistory } from 'react-router-dom';
import styles from './index.scss';
import RuleList from '@components/ForecastRule';
import { context } from '../index';
import { routeType } from '@/const/enums';
import { UserContext } from '@/context/user';
import { useNewTicketRuleBaseParam } from '@hooks/useTicketRuleBaseParam';
import * as t from '@http_idl/demo';
import { ACCESS_PARTY, ROUTE_PATH } from '@/common/constants/property';
import { isTTP } from '@/common/utils/env';
import { useDoorAuthority } from '@/hooks';
import { havePermission } from '@/common/utils/hasPermission';
import { joinClassArr } from '@/common/utils/css';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import RouteFilter from '@/components/RouteFilter';
function Index(props) {
  const [timer, changeTimer] = useState(null);
  const user = useContext(UserContext);
  const history = useHistory();
  const {
    changeDefaultItemKey,
    defaultItemKeyOne,
    PreReleaseRulesList,
    onlineRuleList,
    newRuleId,
    changePreReleaseRulesList,
    skillGroups,
    bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    botList,
    fieldList,
    ticketSkillGroups,
    changeSkillGroupList,
    changeRuleName,
    changeStatusList,
    statusList,
    skillGroupList,
    ruleName,
    existRulesIfNotFilter
  } = useContext(context);
  const [searchData, changeListData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const { EventKey } = useNewTicketRuleBaseParam(defaultItemKeyOne);
  const { hasEditAuth } = useDoorAuthority();
  const addNewRouteRule = () => {
    if (fieldList.length > 0 && skillGroups.length > 0) {
      history.push({
        pathname: '/ticket_create_rule',
        state: {
          plaformTag: routeType.ticket_routing,
          viewType: 'create',
          Priority: 1,
          EventKey,
          ruleLength: 0,
          skillGroups,
          fieldLists: fieldList,
          bizTypeList,
          ruleName,
          statusList,
          skillGroupList
        },
      });
    } else {
      Toast.warning(
        I18n.t('_please_check_the_skill_group_and_routing_condition_values_', {}, '「请检查技能组与路由条件值」')
      );
    }
  };
  useEffect(() => {
    if (inputValue) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(inputValue));
      changeListData(safeJSONParse(JSON.stringify(newListData)));
    } else {
      changeListData(safeJSONParse(JSON.stringify(PreReleaseRulesList)));
    }
  }, [PreReleaseRulesList]);
  const findData = val => {
    if (val) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(PreReleaseRulesList);
    }
  };
  const onRelease = async () => {
    const RuleIds = PreReleaseRulesList.map(item => item.Id);
    const res = await t.demoClient.PublishRuleGroup({
      RuleGroupId: PreReleaseRulesList[0].RuleGroupId,
      RuleIds,
      eventKey: EventKey,
    });
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    changeDefaultItemKey('onlineRule');
    // 创建一个新的状态对象，更新其中的特定属性
    const newState = { ...history.location.state, jumpPublish: false };

    // 使用 history.replace() 更新状态，但不刷新页面
    history.replace(ROUTE_PATH.TICKET, newState);
    await getPreReleaseRuleList();
    await getOnlineRuleList();

    Toast.success(I18n.t('rule_group_released_successfully', {}, '规则组发布成功'));
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        setInputValue(val);
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };
  const onCancle = async () => {
    try {
      setConfirmLoading(true);
      const res = await t.demoClient.UpdateRuleStatus({
        Ids: PreReleaseRulesList?.map(val => val?.Id),
        RuleStatus: 3,
        Version: 'v1',
        Draft: true,
        ruleGroupId: PreReleaseRulesList[0]?.RuleGroupId,
        operateGroupAllRules: 1,
      });
      if (res.code !== 0) {
        Toast.error(res.message);
        return;
      }
      setModalVisible(false);
      setModalVisible(false);
      changeDefaultItemKey('onlineRule');
      // 创建一个新的状态对象，更新其中的特定属性
      const newState = { ...history.location.state, jumpPublish: false };
  
      // 使用 history.replace() 更新状态，但不刷新页面
      history.replace(ROUTE_PATH.TICKET, newState);
      await getPreReleaseRuleList();
      await getOnlineRuleList();
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'onCancle' });
    }
  };
  return (
    <div className={styles.allContainer}>
      <RouteFilter
        skillGroupList={skillGroupList}
        ruleName={ruleName}
        skillGroups={ticketSkillGroups}
        statusList={statusList}
        changeStatusList={changeStatusList}
        changeRuleName={changeRuleName}
        changeSkillGroupList={changeSkillGroupList}
      />

      {searchData.length ? (
        <>
          <div
            className={joinClassArr([
              styles.routerList,
              [ACCESS_PARTY.GLOAB_SELLING, ACCESS_PARTY.SELLWE_SERVICE_HOSTING].includes(user.accessPartyId) &&
                styles.routerListAop,
            ])}
          >
            <RuleList
              newRuleId={newRuleId}
              ruleList={searchData}
              bizTypeList={bizTypeList}
              botList={botList}
              skillGroups={skillGroups}
              defaultItemKeyOne={defaultItemKeyOne}
              changePreReleaseRulesList={changePreReleaseRulesList}
              ruleType={routeType.ticket_routing}
              fieldList={fieldList}
              getPreReleaseRuleList={getPreReleaseRuleList}
              inputValue={inputValue}
              ticketAllSkillGroups={ticketSkillGroups}
              ruleName={ruleName}
              statusList={statusList}
              skillGroupList={skillGroupList}
            />
          </div>
          <div className={styles.foot}>
            <div className={styles.footBtn}>
              <Button
                onClick={() => setModalVisible(true)}
                disabled={isTTP() || havePermission()}
                className={styles.btnCancle}
              >
                {I18n.t('cancel', {}, '取消')}
              </Button>
              <Modal
                title={I18n.t('route_Starling_12', {}, '请问您是否确认要取消本次修改？')}
                visible={modalVisible}
                onOk={onCancle}
                cancelText={I18n.t('cancel', {}, '取消')}
                okText={I18n.t('ok', {}, '确定')}
                onCancel={() => setModalVisible(false)}
                okType="primary"
                confirmLoading={confirmLoading}
                icon={<Icon style={{ color: 'var(--color-warning)' }} type="alert_triangle" size="extra-large" />}
              >
                {I18n.t('route_Starling_13', {}, '一旦取消，数据将无法恢复，请谨慎操作')}
              </Modal>
              <Button
                onClick={() => {
                  onRelease();
                }}
                theme="solid"
                type="primary"
                disabled={isTTP() || havePermission() || !hasEditAuth}
              >
                {I18n.t('release_online', {}, '发布上线')}
              </Button>
            </div>
          </div>
        </>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_pre_release_routing_rules', {}, '暂无预发布路由规则')}
        >
          {onlineRuleList.length || existRulesIfNotFilter ? null : (
            <Button
              disabled={isTTP() || havePermission() || !hasEditAuth}
              icon="plus"
              theme="solid"
              type="primary"
              style={{ width: '100%' }}
              onClick={addNewRouteRule}
            >
              {I18n.t('new_routing_rule', {}, '新建路由规则')}
            </Button>
          )}
        </KefuPageEmptyContent>
      )}
    </div>
  );
}

export default Index;
