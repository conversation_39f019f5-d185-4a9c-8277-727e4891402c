import { I18n } from '@ies/starling_intl';
import React, { useState } from 'react';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane, Spin } from '@ies/semi-ui-react';
import * as styles from './index.scss';
import ServiceOnlineRules from '../newServiceRouting/onlineRules';
import ServicePreReleaseRules from '../newServiceRouting/preReleaseRules';
import BotOnlineRules from '../newBotRouting/onlineRules';
import BotPreReleaseRules from '../newBotRouting/preReleaseRules';
import OfflineOnlineRules from '../newOfflineRouting/onlineRules';
import OfflinePreReleaseRules from '../newOfflineRouting/preReleaseRules';
import QualityCheckOnlineRules from '../newQualityCheckRouting/onlineRules';
import QualityCheckPreReleaseRules from '../newQualityCheckRouting/preReleaseRules';
import TicketOnlineRules from '../newTicketRouting/onlineRules';
import TicketPreReleaseRules from '../newTicketRouting/preReleaseRules';
// import HistoryRule from '@/pages/historyRule';
import { useHistory } from 'react-router-dom';
export const context = React.createContext(null);
// This is route mapping
const platformMap = {
  service_routingV2: 'service_create_rule',
  bot_routingV2: 'bot_create_rule',
  offline_routingV2: 'offline_create_rule',
  quality_check_routingV2: 'qualitycheck_create_rule',
  ticket_routingV2: 'ticket_create_rule',
};

const RouterPage: React.FC = () => {
  const [isLoading, setLoading] = useState(false);
  // const [onlineRuleList, changeOnlineRuleList] = useState(ruleList);
  // const [PreReleaseRulesList, changePreReleaseRulesList] = useState(ruleList);
  const [defaultItemKey, changeDefaultItemKey] = useState('onlineRule');
  const changeActiveKey = key => {
    changeDefaultItemKey(key);
  };
  const history = useHistory();
  const location = history.location.pathname.slice(1);
  const platform = platformMap[location] || '';
  const content = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        <Tabs
          onChange={key => changeActiveKey(key)}
          defaultActiveKey={defaultItemKey}
          activeKey={defaultItemKey}
          type="line"
        >
          {/* <context.Provider value={{defaultItemKey, changeDefaultItemKey }}> */}
          <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
            <context.Provider
              value={{
                defaultItemKey,
                changeDefaultItemKey,
                // onlineRuleList,
                // changeOnlineRuleList,
                // changePreReleaseRulesList,
              }}
            >
              {/* <ServiceOnlineRules /> */}
              {platform === platformMap.bot_routingV2 ? (
                <BotOnlineRules />
              ) : platform === platformMap.offline_routingV2 ? (
                <OfflineOnlineRules />
              ) : platform === platformMap.quality_check_routingV2 ? (
                <QualityCheckOnlineRules />
              ) : platform === platformMap.service_routingV2 ? (
                <ServiceOnlineRules />
              ) : platform === platformMap.ticket_routingV2 ? (
                <TicketOnlineRules />
              ) : null}
            </context.Provider>
          </TabPane>
          <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
            <context.Provider
              value={{
                defaultItemKey,
                // PreReleaseRulesList,
                // changeDefaultItemKey,
                // changePreReleaseRulesList,
                // changeOnlineRuleList,
              }}
            >
              {platform === platformMap.bot_routingV2 ? (
                <BotPreReleaseRules />
              ) : platform === platformMap.offline_routingV2 ? (
                <OfflinePreReleaseRules />
              ) : platform === platformMap.quality_check_routingV2 ? (
                <QualityCheckPreReleaseRules />
              ) : platform === platformMap.service_routingV2 ? (
                <ServicePreReleaseRules />
              ) : platform === platformMap.ticket_routingV2 ? (
                <TicketPreReleaseRules />
              ) : null}
            </context.Provider>
          </TabPane>
          {/* </context.Provider> */}
          {/* < TabPane tab = {I18n.t ('historical_version ', {}, ' Historical Version') } itemKey = "historyRule" >
             < HistoryRule/>
           </TabPane > */}
        </Tabs>
      </div>
    </Spin>
  );
  return content;
};
export default RouterPage;
