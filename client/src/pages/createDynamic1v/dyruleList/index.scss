.ruleListBox {
  display: flex;
  flex-direction: column;
  width: fit-content;
  .ruleContainer {
    display: flex;
  }
  .ruleDivider {
    width: 100%;
    margin: 1px 0;
    border-bottom: 1px solid #ebecec;
  }
  .ruleForm {
    display: flex;
    flex-direction: column;
  }
  .ruleList {
    display: flex;
    flex-direction: row;

    :global {

      .united_route_manage-form-field {
        padding-top: 4px;
        padding-bottom: 4px;
      }
    }
  }
  .ruleText {
    font-weight: 600;
    font-size: 14px;
    line-height: 38px;
    color:#222727;
    min-width: fit-content;
  }

  .workStatus {
    display: flex;
    flex-direction: row;

    :global {

      .united_route_manage-form-field {
        padding-top: 4px;
        padding-bottom: 4px;
      }
    }
  }
  .ruleMinusBtn {
    display: flex;
    align-items: center;
  }
}

.addRuleListBox {
  display: flex;
  padding: 4px 0 12px;

  .addRuleListBtn {
    display: flex;
    align-items: center;
    // color: #0077fa;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
  }
}

.errSelect {

  :global {

    .united_route_manage-form-field-error-message {
      padding-left: 8px;
    }
  }
}
