import React, { useState, useEffect } from 'react';
import { Form, Button, TagInput, Divider } from '@ies/semi-ui-react';
import { getRequiredRule } from '@common/rules';
import { getRuleName, handleOperatorChange, handleFieldNameChange, useDyRuleList, getSuffix } from './use';
import { formatTreeData } from '@/common/utils/treeData';
import styles from './index.scss';
import * as t from '@http_idl/demo';
import { I18n } from '@ies/starling_intl';
import { safeJSONParse } from '@/common/utils';

type DyruleList = {
  values: any;
  ruleListName: string;
  dyFieldList: Array<any>;
  formApi: any;
  workStatus: Array<t.WorkStatusConfig>;
  workStatusIndex: string;
};

interface triggerRenderProps {
  value: Array<any>; // All currently selected options
  inputValue: string; // The input value of the current input box
  onChange: (inputValue: string) => void; // The function used to update the value of the input box, you should call this function when the value of the Input component customized in triggerRender is updated, which is used to synchronize the state to the Select internal
  onClear: () => void; // Functions for clearing values
  disabled: boolean; // Whether to disable Select
  placeholder: string; // Selected placeholders
  // ComponentProps://All users pass props to Select
}

const DyRuleList: React.FC<DyruleList> = props => {
  const { values, ruleListName, dyFieldList, formApi, workStatus, workStatusIndex } = props;
  const [fieldList, setFieldList] = useState([]);

  useEffect(() => {
    if (dyFieldList?.length === 0) {
      return;
    }
    const arr = ['$skillGroupId', '$agentStatus', '$ruleTag', '$agentWorkInSkillGroupDays'];
    const temp = dyFieldList?.filter(el => arr.findIndex(val => val === el.FieldName) === -1);
    setFieldList(temp);
  }, [dyFieldList]);
  const { deleteItem, addRuleItem } = useDyRuleList(formApi, ruleListName, fieldList);

  // Return operator control
  const triggerRender = (props: triggerRenderProps): React.ReactElement => {
    const { value } = props;
    return (
      <TagInput
        value={(value || []).map(item => item.label)}
        // inputValue={inputValue}
        maxTagCount={2}
        maxLength={100}
        placeholder={I18n.t('select_condition_field', {}, '选择条件字段')}
        onInputChange={(v, e): void => {
          console.log(v);
        }}
        showRestTagsPopover={true}
        restTagsPopoverProps={{ position: 'top', style: { width: 460, overflow: 'hidden auto' } }}
        onChange={v => {
          const valueArr = [];
          for (let i = 0; i < v.length; i++) {
            const formValue = (value || []).filter(item => item.label === v[i])[0]?.value;
            valueArr.push(formValue);
          }
          formApi.setValue(`${ruleListName}${workStatusIndex}.FieldValue`, valueArr);
        }}
      />
    );
  };
  const renderSelectOperator = (filter, index) => {
    let OperatorList = [];
    const field = (fieldList || []).find(o => o?.FieldName === filter.FieldName);
    if (filter && field) {
      OperatorList = field?.OperatorList || [];
    }

    const OperatorSelect = (
      <div className={styles.errSelect}>
        <Form.Select
          filter
          noLabel
          field={`${ruleListName}[${index}].OperatorId`}
          style={{ width: 214, marginRight: 8 }}
          placeholder={I18n.t('correspondence', {}, '对应关系')}
          onChange={e => handleOperatorChange(index, formApi, ruleListName)}
          rules={[getRequiredRule(I18n.t('please_select_a_correspondence', {}, '请选择对应关系'))]}
        >
          {(OperatorList || []).map(val => {
            const content = (
              <Form.Select.Option value={val} key={val}>
                {getRuleName(val)}
              </Form.Select.Option>
            );
            return content;
          })}
        </Form.Select>
      </div>
    );
    return OperatorSelect;
  };
  // Return Conditional Value Control
  const renderSelectValue = (filter, index: number) => {
    // return;

    if (fieldList.length === 0) {
      return;
    }
    // return;
    const field = (fieldList || []).find(o => o?.FieldName === filter.FieldName);
    const fieldValue = field?.OperatorFieldvalues[filter.OperatorId];
    let EntityValueComponents = ( // Need to delete
      <Form.InputNumber
        // hideButtons={true}
        style={{ width: 374 }}
        field={`${ruleListName}[${index}].FieldValue`}
        noLabel={true}
        suffix={getSuffix(filter, fieldList)}
        precision={0}
        min={0}
        rules={[getRequiredRule(I18n.t('please_enter', {}, '请输入'))]}
      />
    );

    if (!field || !fieldValue) {
      return EntityValueComponents;
    }
    const { FieldValueType, FieldValueList } = fieldValue;
    console.log('=FieldValueType==', FieldValueType, FieldValueList);
    switch (FieldValueType) {
      case 1:
        EntityValueComponents = (
          <Form.Select style={{ width: 311 }} field={`${ruleListName}[${index}].FieldValue`} noLabel={true} filter>
            {(FieldValueList || []).map(o => (
              <Form.Select.Option key={o.value} value={o.value}>
                {o.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
        );
        break;
      case 2:
        // break;
        EntityValueComponents = (
          <Form.Select
            multiple
            style={{ width: 311 }}
            field={`${ruleListName}[${index}].FieldValue`}
            noLabel={true}
            maxTagCount={2}
            filter
            rules={[getRequiredRule(I18n.t('please_select', {}, '请选择'))]}
            optionList={FieldValueList || []}
          >
            {/* {(FieldValueList || []).map(o => (
              <Form.Select.Option key={o.value} value={o.value}>
                {o.name}
              </Form.Select.Option>
            ))} */}
          </Form.Select>
        );
        break;
      case 7:
        console.log('formatTreeData------', formatTreeData(safeJSONParse(JSON.stringify(FieldValueList))));
        EntityValueComponents = (
          <Form.TreeSelect
            multiple
            noLabel
            leafOnly
            showClear
            filterTreeNode
            showFilteredOnly
            maxTagCount={2}
            // onChange={(e): void => (changeFun(parentIndex, index, 'rhs', e))}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            style={{ width: 311 }}
            // fieldClassName={styles.ruleListRhs}
            field={`${ruleListName}[${index}].FieldValue`}
            treeData={formatTreeData(safeJSONParse(JSON.stringify(FieldValueList)))}
          />
        );
        break;
      case 10: // integer
        EntityValueComponents = (
          <Form.InputNumber
            // onChange={(e): void => (changeFun(parentIndex, index, 'rhs', e))}
            // fieldClassName={styles.ruleListRhs}
            // hideButtons={true}
            style={{ width: 311 }}
            field={`${ruleListName}[${index}].FieldValue`}
            noLabel={true}
            min={0}
            suffix={getSuffix(filter, fieldList)}
            precision={0}
            rules={[getRequiredRule(I18n.t('please_enter', {}, '请输入'))]}
          />
        );
        break;
      case 8:
        EntityValueComponents = (
          <Form.TimePicker
            noLabel
            // onChange={(e): void => (changeFun(parentIndex, index, 'rhs', e))}
            hideDisabledOptions
            secondStep={30}
            scrollItemProps={{
              cycled: false,
            }}
            disabledSeconds={() => [30]}
            // fieldClassName={styles.ruleListRhs}
            field={`${ruleListName}[${index}].FieldValue`}
          />
        );
        break;
      default:
        EntityValueComponents = (
          <Form.InputNumber
            // hideButtons={true}
            style={{ width: 311 }}
            field={`${ruleListName}[${index}].FieldValue`}
            noLabel={true}
            // suffix={getSuffix(filter)}
            // precision={getPrecision(filter)}
            // max={getValueMax(filter)}
            min={0}
            rules={[getRequiredRule(I18n.t('please_enter', {}, '请输入'))]}
          />
        );
    }

    return EntityValueComponents;
  };
  const content = (
    <div className={styles.ruleListBox}>
      {(values[ruleListName] || []).map((el, index) => {
        const content = (
          <>
            <div key={el.key} className={styles.ruleContainer}>
              <div className={styles.ruleForm}>
                <div className={styles.ruleList}>
                  <Form.Select
                    noLabel
                    field={`${ruleListName}[${index}].type`}
                    style={{ width: 122, marginRight: 12 }}
                    placeholder={I18n.t('max/min', {}, 'max/min')}
                    rules={[getRequiredRule(I18n.t('please_select', {}, '请选择'))]}
                  >
                    <Form.Select.Option value="max" key="max">max</Form.Select.Option>
                    <Form.Select.Option value="min" key="min">min</Form.Select.Option>
                  </Form.Select>
                  <span className={styles.ruleText}>{I18n.t('queueSize', {}, 'queue size in')}</span>
                  <Form.InputNumber
                    // onChange={(e): void => (changeFun(parentIndex, index, 'rhs', e))}
                    // fieldClassName={styles.ruleListRhs}
                    // hideButtons={true}
                    style={{ width: 175, marginLeft: 12 }}
                    field={`${ruleListName}[${index}].minutes`}
                    noLabel={true}
                    min={0}
                    suffix={'minute(s)'}
                    precision={0}
                    rules={[getRequiredRule(I18n.t('please_enter', {}, '请输入'))]}
                    validate={(val, values) => {
                      if (!val && +val !== 0) {
                        return I18n.t('please_enter', {}, '请输入');
                      }
                      const seconds = values?.[ruleListName]?.[index].seconds;
                      if (Number(val) > 5) {
                        return 'Cannot set a time more than 5 minutes';
                      }
                      if (Number(val) === 5 && seconds && Number(seconds) > 0) {
                        return 'Cannot set a time more than 5 minutes';
                      }
                    }}
                  />
                  <Form.InputNumber
                    // onChange={(e): void => (changeFun(parentIndex, index, 'rhs', e))}
                    // fieldClassName={styles.ruleListRhs}
                    // hideButtons={true}
                    style={{ width: 175, marginLeft: 12 }}
                    field={`${ruleListName}[${index}].seconds`}
                    noLabel={true}
                    min={0}
                    suffix={'second(s)'}
                    precision={0}
                    rules={[getRequiredRule(I18n.t('please_enter', {}, '请输入'))]}
                    validate={(val, values) => {
                      if (!val && +val !== 0) {
                        return I18n.t('please_enter', {}, '请输入');
                      }
                      const minutes = values?.[ruleListName]?.[index].minutes;
                      const less30s = !val || Number(val) < 30;
                      if (!minutes && less30s) {
                        return 'Cannot set a time less than 30 seconds';
                      }
                      if (Number(val) > 59) {
                        return 'Cannot set a time more than 59 seconds';
                      }
                    }}
                  />
                </div>
                <div className={styles.ruleList}>
                  {renderSelectOperator(el, index)}
                  {renderSelectValue(el, index)}
                </div>
              </div>
              <div className={styles.ruleMinusBtn}>
                <Button
                  icon="minus_circle"
                  type="tertiary"
                  theme="borderless"
                  onClick={() => deleteItem(el.key)}
                  disabled={values[ruleListName].length === 1}
                  // className={styles.ruleMinusCircle}
                  style={{ marginLeft: 8, marginTop: 4 }}
                />
              </div>
            </div>
            {
              index < values[ruleListName].length - 1
                ? <div className={styles.ruleDivider} style={{ marginTop: 12, marginBottom: 12 }} />
                : null
            }
          </>
        );
        return content;
      })}
      {/* WorkStatus 选择 */}
      {(workStatus || []).length === 0 ? null : (
        <div className={styles.workStatus}>
          <Form.Select field={`${ruleListName}${workStatusIndex}.FieldName`} noLabel style={{ width: 214 }} disabled>
            {dyFieldList
              .filter(item => item.FieldName === '$agentStatus')
              .map(el => {
                const content = (
                  <Form.Select.Option value={el.FieldName} key={el.FieldName}>
                    {el.FieldDisplayName}
                  </Form.Select.Option>
                );
                return content;
              })}
          </Form.Select>
          <div className={styles.errSelect}>
            <Form.Select
              noLabel
              field={`${ruleListName}${workStatusIndex}.OperatorId`}
              style={{ width: 128, marginRight: 8, marginLeft: 8 }}
              placeholder={I18n.t('correspondence', {}, '对应关系')}
              rules={[getRequiredRule(I18n.t('please_select_a_correspondence', {}, '请选择对应关系'))]}
            >
              {(dyFieldList.filter(item => item.FieldName === '$agentStatus')[0]?.OperatorList || []).map(el => {
                const content = (
                  <Form.Select.Option value={el} key={el}>
                    {getRuleName(el)}
                  </Form.Select.Option>
                );
                return content;
              })}
            </Form.Select>
          </div>
          <Form.Select
            noLabel
            field={`${ruleListName}${workStatusIndex}.FieldValue`}
            style={{ width: 246 }}
            multiple
            triggerRender={triggerRender}
            rules={[getRequiredRule(I18n.t('please_select', {}, '请选择'))]}
          >
            {(workStatus || []).map(item => {
              const content = (
                <Form.Select.Option value={item.WorkStatus} key={item.WorkStatus}>
                  {item.WorkStatusDesc}
                </Form.Select.Option>
              );
              return content;
            })}
          </Form.Select>
        </div>
      )}
      <div className={styles.addRuleListBox}>
        <Button className={styles.addRuleListBtn} icon="plus_circle" theme="borderless" onClick={addRuleItem}>
          {I18n.t('add_rule_condition', {}, '添加规则条件')}
        </Button>
      </div>
    </div>
  );
  return content;
};
export default DyRuleList;
