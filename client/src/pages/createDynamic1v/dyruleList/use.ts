import { safeJSONParse } from '@/common/utils';
import { ruleOpcheckArr } from '@/pages/createRule/createRuleType';
// Get the Chinese name corresponding to the operator
export const getRuleName = opKey => {
  for (let i = 0; i < ruleOpcheckArr.length; i++) {
    if (ruleOpcheckArr[i].serveOp === opKey) {
      return ruleOpcheckArr[i].clientName();
    }
  }
};

// Get suffix
const getInputConfig = (filter, fieldList) => {
  const inputSuffix = fieldList.filter(item => item.FieldName === filter.FieldName) || [];
  if (inputSuffix.length === 0) {
    return;
  }
  if (!filter?.OperatorId) {
    return;
  }
  const suffixValue = inputSuffix[0]?.OperatorFieldvalues[filter?.OperatorId]?.FieldValues;
  if (suffixValue === '') {
    return;
  }
  const operator = safeJSONParse(suffixValue);
  if (operator?.data.length === 0) {
    return;
  }
  const str = operator?.data[0]?.suffix;
  return str;
};

// Text box suffix
export const getSuffix = (filter, fieldList) => {
  const str = getInputConfig(filter, fieldList);
  return str;
};

export function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min)) + min; // Not including maximum value, including minimum value
}

export const handleOperatorChange = (index, formApi, ruleListName) => {
  if (!formApi) {
    return;
  }
  const list = formApi.getValue(ruleListName).slice();
  list[index] = {
    ...list[index],
    FieldValue: 0,
    key: getRandomInt(1, 10000),
  };
  formApi.setValue(ruleListName, list);
};

export const handleFieldNameChange = (index, formApi, ruleListName) => {
  if (!formApi) {
    return;
  }
  const list = formApi.getValue(ruleListName).slice();
  list[index] = {
    ...list[index],
    OperatorId: '',
    FieldValue: 0,
    key: getRandomInt(1, 10000),
  };
  formApi.setValue(ruleListName, list);
};

export const useDyRuleList = (formApi, ruleListName, fieldList) => {
  // Delete delete condition
  const deleteItem = (key: number) => {
    if (!formApi) {
      return;
    }
    let list = (formApi.getValue(ruleListName) || []).slice();
    list = list.filter(o => key !== o?.key);
    formApi.setValue(ruleListName, list);
  };

  const addRuleItem = () => {
    if (!formApi) {
      return;
    }
    const list = (formApi.getValue(ruleListName) || []).slice();
    list.push({
      FieldName: '',
      OperatorId: '',
      FieldValue: null,
      key: getRandomInt(1, 10000),
    });
    formApi.setValue(ruleListName, list);
  };

  return {
    deleteItem,
    addRuleItem,
  };
};
