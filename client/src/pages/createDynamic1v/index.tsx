import React, { useEffect, useState, useRef, useCallback, useMemo, useContext } from 'react';
import { useHistory } from 'react-router-dom';
import { Modal, Icon, Form, Button, Tooltip, Spin, TagInput, Toast } from '@ies/semi-ui-react';
import CreateRuleHeader from '@/components/createRuleHeader';
import * as styles from './index.scss';
import { useCreateDynamic, triggerRenderProps } from './use';
import { getRequiredRule } from '@common/rules';
import DyRuleList from './dyruleList';
import { UserContext } from '@context/user';
import { I18n } from '@ies/starling_intl';
import { safeJSONParse } from '@/common/utils';

export type createDynamicRuleProps = {
  tabKey: string; // Tab Bar Label
  viewType: string; // Distinguish between new and edited
  ruleDetail?: any;
  dyFieldList: Array<any>;
  skillGroup: Array<any>;
  allSkillGroups: Array<any>;
  beConfigList?: Array<any>;
  AccessPartyId: string;
};

let ruleFormApi = null;
const Index: React.FC = () => {
  const path = 'create_dynamic_rule';
  const history = useHistory();
  const tempRouterParams: createDynamicRuleProps = {
    tabKey: '',
    viewType: '',
    ruleDetail: {},
    dyFieldList: [],
    skillGroup: [],
    allSkillGroups: [],
    beConfigList: [],
    AccessPartyId: '',
  };
  const user = useContext(UserContext);

  const routerParams: createDynamicRuleProps = Object.assign({}, tempRouterParams, history.location.state);
  const { dyFieldList, skillGroup, ruleDetail = {} } = routerParams;
  const [skillGroups, setSkillGroups] = useState(skillGroup);
  const [channelType, setChannelType] = useState(null);
  const [isCustomConfig, setIsCustomConfig] = useState(false);
  const topTitle = I18n.t('number_of_passengers_configuration', {}, '对客数配置');

  const {
    loading,
    setRuleFormRef,
    initFormDetail,
    deleteLayerItem,
    addLayerItem,
    submitInfo,
    changeLayered,
    changeDynamicConfig,
    isCanSubmit,
    getWorkStatusConfigs,
    workStatus,
    changeWorkStatus,
    changeEvConfig,
  } = useCreateDynamic(routerParams);
  // Text box validation
  const getOncallId = () => ({
    validator(rule, value, cb) {
      const ruleStr = new RegExp('^[0-9]*$');
      const flag = ruleStr.test(value);
      if (!value || !value.trim()) {
        cb(I18n.t('please_enter_larkid', {}, '请输入larkId'));
      } else if (!flag) {
        cb(I18n.t('route_Starling_42', {}, '请输入数字'));
      } else {
        cb();
      }
    },
  });

  useEffect(() => {
    if (routerParams.AccessPartyId !== user?.accessPartyId) {
      history.replace({
        pathname: '/flexible_art',
        state: {
          tabKey: 'dynamic',
        },
      });
    }
  }, []);

  // Abandon editing and return to the work area diversion page
  const handleGoBack = async () => {
    let confirmed = true;
    let title: string;
    const displayName = I18n.t('number_of_passengers_configuration', {}, '对客数配置');
    switch (routerParams.tabKey) {
      case 'create':
        title = `${I18n.t(
          'give_up_creating_a_new__{placeholder1}_?',
          { placeholder1: displayName || I18n.t('unnamed_routing_rules', {}, '未命名路由规则') },
          '放弃新建「{placeholder1}」？'
        )}`;
        break;
      case 'edit':
        title = `${I18n.t(
          'abandon_editing_of__{placeholder1}_?',
          { placeholder1: displayName || I18n.t('unnamed_routing_rules', {}, '未命名路由规则') },
          '放弃「{placeholder1}」的编辑？'
        )}`;
        break;
      default:
        title = I18n.t('abandon_the_current_operation?', {}, '放弃当前操作？');
        break;
    }
    confirmed = await new Promise(resolve => {
      Modal.confirm({
        icon: <Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />,
        title,
        okButtonProps: {
          type: 'danger',
        },
        content: I18n.t(
          'once_abandoned__the_data_will_not_be_recovered__please_operate_with_caution',
          {},
          '一旦放弃，数据将无法恢复，请谨慎操作'
        ),
        onCancel() {
          resolve(false);
        },
        onOk() {
          resolve(true);
        },
      });
    });
    if (confirmed) {
      history.replace({
        pathname: '/flexible_art',
        state: {
          tabKey: 'dynamic',
        },
      });
    }
  };
  const configLable = () => {
    const content = (
      <div className={styles.toolTips}>
        <span>{I18n.t('whether_to_enable_seat_to_guest_configuration', {}, '是否启用坐席对客数配置')}</span>
        <Tooltip
          style={{ width: 220, marginLeft: 4 }}
          position="right"
          content={I18n.t(
            'skill_group_day_dimension_automatic_update_rules_can_be_configured_by_business_t',
            {},
            '可依业务类型配置技能组天维度自动更新规则'
          )}
        >
          <Icon type="help_circle" style={{ color: '#888D92', marginLeft: 4 }} />
        </Tooltip>
      </div>
    );
    return content;
  };

  const customLable = () => {
    const content = (
      <div className={styles.toolTips}>
        <span>{I18n.t('control_item_configuration', {}, '调控项配置')}</span>
        <Tooltip
          style={{ width: 220, marginLeft: 4 }}
          position="right"
          content={I18n.t(
            'im_telephone__and_quality_inspection_skill_groups_support_the_configuration_of__',
            {},
            'IM 、电话、质检技能组支持“人员对客数”配置，新工单、电商工单技能组支持“接单上限系数”或“人员对客数”配置，接单上限系数取值0-1'
          )}
        >
          <Icon type="help_circle" style={{ color: '#888D92', marginLeft: 4 }} />
        </Tooltip>
      </div>
    );
    return content;
  };

  const dyConfigLable = () => {
    const content = (
      <div className={styles.toolTips}>
        <span>{I18n.t('whether_to_enable_dynamic_configuration', {}, '是否启用动态配置')}</span>
        <Tooltip
          style={{ width: 220, marginLeft: 4 }}
          position="right"
          content={I18n.t(
            'the_number_of_seats_can_be_automatically_adjusted_through_threshold_setting',
            {},
            '可通过阈值设定，自动调控坐席对客数'
          )}
        >
          <Icon type="help_circle" style={{ color: '#888D92', marginLeft: 4 }} />
        </Tooltip>
      </div>
    );
    return content;
  };

  const changeSkillGroup = value => {
    const temp = safeJSONParse(JSON.stringify(skillGroup));
    let channelTypeTemp = null;

    if ((value || []).length > 0) {
      temp.map(item => {
        if (item.value !== value[0][0]) {
          item.disabled = true;
        }
        return item;
      });
      channelTypeTemp = value[0][0];
    }
    setChannelType(channelTypeTemp);
    setSkillGroups(temp);
  };

  /**
   * Reset control item configuration when changing skill set
   * @param value
   */
  const reSetIsCustom = value => {
    console.log('===value==', !ruleFormApi);
    if (!ruleFormApi) {
      return;
    }
    ruleFormApi.setValue('isCustom', true);
  };
  useEffect(() => {
    changeSkillGroup(initFormDetail.SkillGroupId);
    console.log('=init==', initFormDetail);
  }, [initFormDetail]);
  useEffect(() => {
    if (channelType) {
      getWorkStatusConfigs(channelType);
    } else {
      changeWorkStatus();
      setIsCustomConfig(false);
    }
    const wordTypeArr = [3, 1, 8];
    if (wordTypeArr.includes(channelType)) {
      setIsCustomConfig(true);
    }
    if (!ruleFormApi) {
      return;
    }
    // debugger;
    // if (Object.keys(ruleDetail).length === 0) {
    //   const list = (ruleFormApi.getValue('layeredConfigCondition') || []).slice();
    //   list.map(item => {
    //     item.WordType = null;
    //     return item;
    //   });
    //   ruleFormApi.setValue('layeredConfigCondition', list);
    // }
  }, [channelType]);
  const { RadioGroup, Radio, InputNumber, Select, Cascader, Input } = Form;
  const content = (
    <Spin spinning={loading}>
      <div className={styles.bigBox}>
        <div className={styles.createRuleContent}>
          <CreateRuleHeader platform={routerParams.tabKey} name={topTitle} handleGoBack={handleGoBack} />
          <div className={styles.formContent}>
            <Form
              // onValueChange={handleFormValueChange}
              getFormApi={formApi => {
                ruleFormApi = formApi;
                setRuleFormRef(formApi);
              }}
              initValues={initFormDetail}
              onChange={e => console.log(e.values)}
            >
              {({ values, formApi }) => (
                <>
                  <Cascader
                    field="SkillGroupId"
                    style={{ width: 360 }}
                    // treeData={skillGroups.filter(item => item.value === 1)}
                    treeData={skillGroups}
                    placeholder={I18n.t('select_skill_group', {}, '选择技能组')}
                    label={I18n.t('configure_skill_groups', {}, '配置技能组')}
                    multiple
                    leafOnly
                    max={10}
                    showClear
                    onExceed={v => {
                      Toast.error(
                        I18n.t(
                          'the_number_of_skill_sets_configured_cannot_exceed_10',
                          {},
                          '配置技能组个数不能超过10个'
                        )
                      );
                    }}
                    showRestTagsPopover={true}
                    restTagsPopoverProps={{
                      position: 'top',
                      style: { width: 460, maxHeight: 244, overflow: 'hidden auto' },
                    }}
                    maxTagCount={2}
                    rules={[getRequiredRule(I18n.t('please_configure_skill_group', {}, '请配置技能组'))]}
                    onChange={e => {
                      console.log('==e===', e);
                      changeSkillGroup(e);
                      reSetIsCustom(e);
                    }}
                    disabled={Object.keys(routerParams.ruleDetail).length !== 0}
                  />

                  {/* < RadioGroup
                    field = "isEnableEVConfig"
                    label = {configLable () }
                    rules = {[{type: 'bool e a n ' }]}
                     o n Cha n g e = {e = > changeLayered (e.target) }
                   >
                     < Radio value = {true} > Yes </Radio >
                     < Radio value = {false} > No </Radio >
                   </RadioGroup > */}
                  {/* {! values.isEnableEVConfig? Null: (
                       < >
                         < RadioGroup
                        field = "isCustom"
                        label = {custom Lable () }
                         ru les = {[{type: 'boolean'}, RequigetredRule ('please select co n figura tio n ') ]}
                           Cha n ge ={() => cha n ge Ev Co n fig () }
                         >
                           < Radio value = {true} > Number Of passengers per person </Radio >
                           < Radio value = {false} disabled = {isCustomConfig} >
                           Upper limit coefficient for receiving orders
                           </Radio >
                         </RadioGroup >
                         < InputNumber
                        field = "defalutEV"
                        label = {values.isCustom? 'Default number of people to customers': 'Default order upper limit coefficient'}
                        placeholder = {values.isCustom? 'Enter number of people to customers': 'Enter order upper limit coefficient'}
                        hideButtons
                        min = {0}
                        max = {values.isCustom? Infinity: 1}
                        precision = {values.isCustom? 0:2}
                        style = {{width: 200}}
                        rules = {[getRequiredRule ('Please enter the default number of guests ') ]}
                      />
                         < div className = {styles.evConfig} >
                           < div className = {styles.ev
                           < div className = {styles.evConfigTopTitle} > Hierarchical configuration of the number of guests </div >
                           < div className = {styles.evConfigTopSelect} >
                             < Select
                               field = "configCondition"
                              noLabel
                              style = {{width: 176, marginRight: 8}}
                              placeholder = "Select Configuration Conditions"
                             >
                               {dyFieldList
                                .filter (item = > item. FieldName === '$ agentWorkInSkillGroupDays')
                                .map (el => {
                                  co nst content = (
                                     < Select. Option value = {el. FieldName} key = {el. FieldName} >
                                       {el. FieldDisplayName}
                                     </Select
                                   </Select >
                                   < Select field = "configConditionOperate" noLabel style = {{width: 128}} >
                                 < Select. Option value = {1} key = {'1'} >
                            Belongs to
                             </Select. Option >
                               </Select >
                                 </div >
                               </div >
                             < div className = {styles.evConfigBottom} >
                           { (values. layeredConfigCondition || []). map ((item, index ) => {
                        co nst content = (
                         < div key = {item.key} className = {styles.evConfigBottomCondition} >
                           < InputNumber
                            field = {' layeredConfigCondition [${index}] .start '}
                              noLabel
                                Buthidetons
                                  style = {{width: 140}}
                                  suffix = "day"
                                  min = {0}
                                  precision = {0}
                                  rules = {[getRequiredRule (' Please enter rule conditions') ]}
                                  />
                                   < span style = {{marginLeft: 8 , marginRight: 8, marginTop: 10 }}>~</ span >
                                   < InputNumber
                                field = {'layeredConfigCondition [${index}] .end'}
                                noLabel
                                hideButtons
                                  style = {{width: 140}}
                                  suffix = "day"
                                  min = {0}
                                  precision = {0}
                                  rules = {[getRequiredRule ('Please enter rule conditions') ]}
                                  />
                                   < div
                                  style = {{marginLeft: 8, marginRight: 8, marginTop: 10}}
                                className = {styles.midtext}
                                 >
                                   {values.isCustom? 'When the number of people to customers is equal to': ', the upper limit coefficient for receiving orders is equal to'}
                                   </div >
                                 < InputNumber
                                  field = {'layeredConfigCondition [${index}] .evNumber'}
                                noLabel
                                hideButtons
                                  style = {{width: values.isCustom? 215:205}
                                  min = {0}
                                  max = { 0:2}
                                  rules = {[getRequiredRule ('Please enter rule conditions') ]}
                                  />
                                   < Button
                                  icon = "minus_circle"
                                  type = "tertiary"
                                theme = "borderless"
                                style = {{marginTop: 4}}
                                  disabled = {values.layeredConfigCondition === 1}
                                  on Click ={() => delete Layer Item (item.key) }
                                  />
                                   </div >
                                  );
                                  return content;
                                }) }
                               < div className = {styles.addRuleListBox} >
                             < Button
                            className = {styles.addRuleListBtn}
                          icon = "plus_circle"
                          theme = "borderless"
                            on ={() => Click add Layer Item () }
                               >
                              Add Configuration
                               </Button >
                               </div >
                             </div >
                               </div >
                             </div >
                           </div >
                         </>
                       </>
                    ) }
                  )} */}

                  <RadioGroup
                    field="isEnableDYConfig"
                    label={dyConfigLable()}
                    rules={[{ type: 'boolean' }]}
                    onChange={e => {
                      changeDynamicConfig(e.target.value);
                      if (e.target.value) {
                        getWorkStatusConfigs(channelType);
                      }
                    }}
                  >
                    <Radio value={true}>{I18n.t('yes', {}, '是')}</Radio>
                    <Radio value={false}>{I18n.t('no', {}, '否')}</Radio>
                  </RadioGroup>
                  {!values.isEnableDYConfig ? null : (
                    <div className={styles.dynamicRule}>
                      <div style={{ marginBottom: 12, marginTop: 12 }}>
                        <div className={styles.evConfigTopTitle}>
                          {I18n.t(
                            'configure_the_rules_for_increasing_the_number_of_customers',
                            {},
                            '配置对客数上调规则'
                          )}
                        </div>
                        <div className={styles.upRuleList}>
                          <DyRuleList
                            values={values}
                            ruleListName={'upDyConfigList'}
                            dyFieldList={dyFieldList}
                            formApi={formApi}
                            workStatus={workStatus}
                            workStatusIndex={'Up'}
                          />
                          <div className={styles.removeBottom}>
                            <InputNumber
                              field="upEvNum"
                              label={I18n.t('increase_in_the_number_of_passengers', {}, '对客数上调')}
                              suffix={I18n.t('people', {}, '人')}
                              precision={0}
                              min={0}
                              style={{ width: 204 }}
                            />
                          </div>
                        </div>
                      </div>
                      <div style={{ marginBottom: 12, marginTop: 12 }}>
                        <div className={styles.evConfigTopTitle}>
                          {I18n.t('configure_the_number_of_passengers_reduction_rules', {}, '配置对客数下调规则')}
                        </div>
                        <div className={styles.upRuleList}>
                          <DyRuleList
                            values={values}
                            ruleListName={'downDyConfigList'}
                            dyFieldList={dyFieldList}
                            formApi={formApi}
                            workStatus={workStatus}
                            workStatusIndex={'Down'}
                          />
                          <div className={styles.removeBottom}>
                            <InputNumber
                              field="downEvNum"
                              label={I18n.t('decrease_in_the_number_of_passengers', {}, '对客数下调')}
                              suffix={I18n.t('people', {}, '人')}
                              precision={0}
                              min={0}
                              style={{ width: 204 }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <RadioGroup
                    field="isOncallId"
                    label={I18n.t('whether_to_add_an_alarm_group', {}, '是否添加报警群')}
                    rules={[{ type: 'boolean' }]}
                  >
                    <Radio value={true}>{I18n.t('yes', {}, '是')}</Radio>
                    <Radio value={false}>{I18n.t('no', {}, '否')}</Radio>
                  </RadioGroup>
                  {!values.isOncallId ? null : (
                    <div className={styles.removeBottom}>
                      <Input
                        field="oncallId"
                        label={I18n.t('alarm_group_id', {}, '报警群 ID')}
                        placeholder={I18n.t('enter_alarm_group_id', {}, '输入报警群 ID')}
                        style={{ width: 200 }}
                        rules={[getOncallId()]}
                      />
                      <div className={styles.larkText}>
                        {I18n.t('click', {}, '点击')}
                        <strong
                          className={styles.larkBt}
                          onClick={() => window.open('https://open.feishu.cn/tool/token')}
                        >
                          {I18n.t('query_group_id', {}, '查询群 ID')}
                        </strong>
                        {I18n.t(
                          'find_input__feishu_group_message_notification_reassign_information',
                          {},
                          '查找输入，飞书群消息通知改派信息'
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}
            </Form>
          </div>
        </div>
        {!isCanSubmit ? null : (
          <div className={styles.createRuleBottom}>
            <Button type="tertiary" onClick={handleGoBack}>
              {I18n.t('cancel', {}, '取消')}
            </Button>
            <Button style={{ marginLeft: 8 }} type="secondary" theme="solid" onClick={submitInfo}>
              {routerParams.viewType === 'edit' ? I18n.t('update', {}, '更新') : I18n.t('new', {}, '新建')}
            </Button>
          </div>
        )}
      </div>
    </Spin>
  );
  return content;
};

export default Index;
