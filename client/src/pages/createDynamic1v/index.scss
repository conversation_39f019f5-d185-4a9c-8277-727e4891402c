.create-rule-content {
  padding: 24px 0 0 25px;
  // height: calc(100vh);  
  // overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 760px;
  margin: 0 auto;
  margin-bottom: 150px;

  .form-content {
    flex: 1;
    // overflow: auto;

    
  }
}

.toolTips {
  display: flex;
  align-items: center;
}

.evConfig {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
  margin-bottom: 12px;

  &-top {
    display: flex;
    flex-direction: column;

    &-title {
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 4px;
      // margin-top: 12px;
    }

    &-select {
      display: flex;
      flex-direction: row;

      > div {
        padding: 4px 0 8px !important;
      }
    }
  }

  &-bottom {
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(var(--grey-1), 1);
    border-radius: 6px;
    padding: 12px 16px;

    &-condition {
      display: flex;
      flex-direction: row;
      align-items: flex-start;

      :global {

        .united_route_manage-form-field {
          padding-top: 4px;
          padding-bottom: 4px;
        }
      }

      .midtext {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #1c1f23;
        display: flex;
        flex-direction: row;
      }
    }
  }
}

.dynamicRule {
  display: flex;
  flex-direction: column;
}

.upRuleList {
  border: 1px solid rgba(var(--grey-1), 1);
  border-radius: 6px;
  padding: 12px 16px;
}

.create-rule-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 56px;
  border-top: 2px solid rgba(28, 31, 35, .08);
  display: flex;
  // justify-content: flex-end;
  align-items: center;
  padding-left: 60%;
  background-color: #fff;
}

.bigBox {
  overflow: hidden auto;
}


.addRuleListBox {
  display: flex;
  padding: 4px 0 0;

  .addRuleListBtn {
    display: flex;
    align-items: center;
    // color: #0077fa;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
  }
}

.larkText {

  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: rgba(28, 31, 35, .6);

  .larkBt {

    color: rgba(11, 135, 125, 1);
    cursor: pointer;
    margin: 0 4px;
  }
}

.removeBottom {

  :global {

    .united_route_manage-form-field {
      padding: 12px 0 4px;
    }
  }
}

