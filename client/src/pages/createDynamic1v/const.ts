import { I18n } from '@ies/starling_intl';

export const RULE_TAG = '$ruleTag';
export const SKILLGROUP = '$skillGroupId';
export const textList = [
  {
    label: I18n.t('number_of_staff_to_guests', {}, '人员对客数'),
    value: 1,
    disabled: false,
    otherKey: 1,
  },
  {
    label: I18n.t('order_upper_limit_coefficient', {}, '接单上限系数'),
    value: 0,
    disabled: false,
    otherKey: 0,
  },
];
export const serveValue = {
  DisplayName: I18n.t('rule_name', {}, '规则名称'),
  Priority: 1, // Need to check
  Expression: {
    OpGroup: 'AND',
    ConditionGroups: [
      {
        OpGroup: 'AND',
        Conditions: [
          {
            OpCheck: 'LIST_IN',
            Lhs: {
              VarExpr: '$skillGroupId',
            },
            Rhs: {
              ConstantList: [],
            },
          },
          {
            OpCheck: '==',
            Lhs: {
              VarExpr: '$ruleTag',
            },
            Rhs: {
              Constant: '',
            },
          },
        ],
      },
    ],
  },
  ActionInfo: {
    ReturnValue: {
      Constant: '',
    },
  },
  CreatorAgentId: '',
};
