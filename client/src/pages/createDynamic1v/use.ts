import { useEffect, useState } from 'react';
import { serveValue, RULE_TAG, SKILLGROUP } from './const';
import * as t from '@http_idl/demo';
import { useUser } from '@hooks/useUser';
import { Toast } from '@ies/semi-ui-react';
import { getRuleName } from './dyruleList/use';
import { useHistory } from 'react-router-dom';
import { DYNAMIC_EVENT_ID, DYNAMIC_EVENT_KEY } from '@constants/property';
import { I18n } from '@ies/starling_intl';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';

export interface triggerRenderProps {
  value: Array<any>; // All currently selected options
  inputValue: string; // The input value of the current input box
  onChange: (inputValue: string) => void; // The function used to update the value of the input box, you should call this function when the value of the Input component customized in triggerRender is updated, which is used to synchronize the state to the Select internal
  onClear: () => void; // Functions for clearing values
  disabled: boolean; // Whether to disable Select
  placeholder: string; // Selected placeholders
}

let ruleCreateFormApi = null;

export function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min)) + min; // Not including maximum value, including minimum value
}

export const useCreateDynamic = props => {
  const {
    viewType,
    ruleDetail,
    dyFieldList,
    allSkillGroups, //  Unprocessed full skill set
    beConfigList,
  } = props;
  const [loading, setLoading] = useState(false);
  const [isCanSubmit, setIsCanSubmit] = useState(true);
  const [isLayered, setIsLayered] = useState(true);
  const [isDynamic, setIsDynamic] = useState(true);
  const [workStatus, setWorkStatus] = useState([]);
  const history = useHistory();

  const user = useUser();
  // Form initial value
  const getInitFormValue = () => {
    let initValue = {
      SkillGroupId: [],
      isEnableEVConfig: true,
      isEnableDYConfig: true,
      isCustom: true,
      defalutEV: 0,
      configCondition: '$agentWorkInSkillGroupDays', // Seat order days
      configConditionOperate: 1, // Seat order days operator
      layeredConfigCondition: [
        {
          start: 0,
          end: 0,
          evNumber: 0,
          key: getRandomInt(1, 100000),
        },
      ],
      upEvNum: 0,
      downEvNum: 0,
      upDyConfigList: [
        {
          FieldName: 'query_queue_size.queue_size',
          OperatorId: '',
          FieldValue: 0,
          key: getRandomInt(1, 100000),
        },
      ],
      downDyConfigList: [
        {
          FieldName: 'query_queue_size.queue_size',
          OperatorId: '',
          FieldValue: 0,
          key: getRandomInt(1, 100000),
        },
      ],
      isOncallId: true,
      oncallId: null,
    };
    if (Object.keys(ruleDetail).length !== 0) {
      initValue = Object.assign(initValue, ruleDetail);
      if (initValue.layeredConfigCondition?.length === 0) {
        initValue.isEnableEVConfig = false;
      }
      if (initValue.upDyConfigList?.length === 0 && initValue.downDyConfigList?.length === 0) {
        initValue.isEnableDYConfig = false;
      }
      if (!initValue.oncallId) {
        initValue.isOncallId = false;
      }
      const temp = allSkillGroups?.filter(v => v?.ID === ruleDetail?.SkillGroupId.value);
      initValue.SkillGroupId = [[temp?.[0]?.ChannelType, temp?.[0]?.ID]];
    }
    return initValue;
  };
  const [initFormDetail, setInitFormDetail] = useState(() => getInitFormValue());

  // Set the ref of the form
  const setRuleFormRef = formApi => {
    ruleCreateFormApi = formApi;
  };

  // Delete hierarchical configuration for number of guests
  const deleteLayerItem = key => {
    if (!ruleCreateFormApi) {
      return;
    }
    let list = (ruleCreateFormApi.getValue('layeredConfigCondition') || []).slice();
    list = list.filter(item => item.key !== key);
    ruleCreateFormApi.setValue('layeredConfigCondition', list);
  };

  // Add hierarchical coordination for the number of passengers

  const addLayerItem = () => {
    if (!ruleCreateFormApi) {
      return;
    }
    const list = (ruleCreateFormApi.getValue('layeredConfigCondition') || []).slice();
    const obj = {
      start: list[list.length - 1].end,
      end: 0,
      evNumber: 0,
      key: getRandomInt(1, 100000),
    };
    list.push(obj);
    ruleCreateFormApi.setValue('layeredConfigCondition', list);
  };

  const getLayeredValue = value => {
    const arr = [];
    const cycleLength = value.layeredConfigCondition?.length;
    const layeredConfigCondition = safeJSONParse(JSON.stringify(value.layeredConfigCondition));
    for (let i = 0; i < cycleLength; i++) {
      const obj = safeJSONParse(JSON.stringify(serveValue));
      const itemStart = {
        OpCheck: i === 0 ? '>=' : '>',
        Lhs: {
          VarExpr: value.configCondition,
        },
        Rhs: {
          Constant: String(layeredConfigCondition[i]?.start),
        },
      };
      const itemEnd = {
        OpCheck: '<=',
        Lhs: {
          VarExpr: value.configCondition,
        },
        Rhs: {
          Constant: String(layeredConfigCondition[i]?.end),
        },
      };
      obj.Expression.ConditionGroups[0]?.Conditions.push(itemStart);
      obj.Expression.ConditionGroups[0]?.Conditions.push(itemEnd);
      obj.Expression.ConditionGroups[0].Conditions.map(item => {
        if (item.Lhs.VarExpr === RULE_TAG) {
          item.Rhs.Constant = '"layered"';
        }
        return item;
      });
      obj.ActionInfo.ReturnValue.Constant = {
        maxTaskNum: value.isCustom ? layeredConfigCondition[i]?.evNumber : null,
        coefficient: value.isCustom ? null : layeredConfigCondition[i]?.evNumber,
      };
      // `"${layeredConfigCondition[i]?.evNumber}"`;
      obj.CreatorAgentId = user.agent.ID;
      arr.push(obj);
    }
    // Default number of guests
    const defaultObj = safeJSONParse(JSON.stringify(serveValue));

    defaultObj.Expression.ConditionGroups[0].Conditions.map(item => {
      if (item.Lhs.VarExpr === RULE_TAG) {
        item.Rhs.Constant = '"layered"';
      }
      return item;
    });
    defaultObj.ActionInfo.ReturnValue.Constant = {
      maxTaskNum: value.isCustom ? value.defalutEV : null,
      coefficient: value.isCustom ? null : value.defalutEV,
    };
    defaultObj.CreatorAgentId = user.agent.ID;
    arr.push(defaultObj);
    return arr;
  };

  // Get alarm information
  const getWarningText = value => {
    const list = safeJSONParse(JSON.stringify(value));
    let warninText = '';
    list.forEach((item, index) => {
      const filedInfo = dyFieldList.filter(val => val.FieldName === item.FieldName)?.[0];
      let fieldDisplayName = filedInfo?.FieldDisplayName;
      if (filedInfo.FieldName === 'query_queue_size.queue_size') {
        fieldDisplayName = `${item?.type === 'max' ? 'Maximum' : 'Minimum'} queue size in ${item?.minutes} minutes ${
          item?.seconds
        } seconds`;
      }
      const operatorName = getRuleName(item?.OperatorId);
      warninText += `${index === 0 ? '【' : I18n.t('and', {}, '且')}${fieldDisplayName}${operatorName}${
        item?.FieldValue
      }${index === (list || []).length - 1 ? '】' : ','}`;
    });
    return warninText;
  };
  const getAgentStatus = data => {
    const agentStatus = {
      contains: data.OperatorId === 'LIST_RETAIN' ? data.FieldValue : [],
      notContains: data.OperatorId === 'NOT LIST_RETAIN' ? data.FieldValue : [],
    };
    return agentStatus;
  };
  const getDynamicValue = value => {
    let dynamicTemp = [];
    const upDynamic = safeJSONParse(JSON.stringify(value.upDyConfigList));
    const downDynamic = safeJSONParse(JSON.stringify(value.downDyConfigList));
    const upObj = safeJSONParse(JSON.stringify(serveValue));
    const downObj = safeJSONParse(JSON.stringify(serveValue));
    (upDynamic || []).forEach((item, index) => {
      const conditionItem = {
        OpCheck: item?.OperatorId,
        Lhs: {
          VarExpr: item?.FieldName,
        },
        Rhs: {
          Constant: String(item?.FieldValue),
        },
      };
      if (item.FieldName === 'query_queue_size.queue_size') {
        const { type, minutes, seconds } = item;
        conditionItem.Lhs = {
          FeatureExpr: {
            FeatureName: item.FieldName,
            ParamExprMap: {
              type: {
                Constant: JSON.stringify(type),
              },
              timeRange: {
                Constant: String(minutes * 60 + seconds),
              },
            }
          }
        };
      }
      upObj.Expression.ConditionGroups[0]?.Conditions.push(conditionItem);
      upObj.Expression.ConditionGroups[0].Conditions.map(item => {
        if (item.Lhs.VarExpr === RULE_TAG) {
          item.Rhs.Constant = '"up"';
        }
        return item;
      });
    });
    upObj.CreatorAgentId = user.agent.ID;
    upObj.ActionInfo.ReturnValue.Constant = {
      increaseNum: value.upEvNum,
      agentStatus: getAgentStatus(value.upDyConfigListUp),
      warningText: getWarningText(upDynamic),
      larkId: value.oncallId,
    };
    (downDynamic || []).forEach(item => {
      const conditionItem = {
        OpCheck: item?.OperatorId,
        Lhs: {
          VarExpr: item?.FieldName,
        },
        Rhs: {
          Constant: String(item?.FieldValue),
        },
      };
      if (item.FieldName === 'query_queue_size.queue_size') {
        const { type, minutes, seconds } = item;
        conditionItem.Lhs = {
          FeatureExpr: {
            FeatureName: item.FieldName,
            ParamExprMap: {
              type: {
                Constant: JSON.stringify(type),
              },
              timeRange: {
                Constant: String(minutes * 60 + seconds),
              },
            }
          }
        };
      }
      downObj.Expression.ConditionGroups[0]?.Conditions.push(conditionItem);
      downObj.Expression.ConditionGroups[0].Conditions.map(item => {
        if (item.Lhs.VarExpr === RULE_TAG) {
          item.Rhs.Constant = '"down"';
        }
        return item;
      });
    });
    downObj.CreatorAgentId = user.agent.ID;
    downObj.ActionInfo.ReturnValue.Constant = {
      increaseNum: value.downEvNum,
      agentStatus: getAgentStatus(value.downDyConfigListDown),
      warningText: getWarningText(downDynamic),
      larkId: value.oncallId,
    };
    dynamicTemp = [upObj, downObj];
    return dynamicTemp;
  };

  // Get Skill Group Name
  const getSkillName = data => {
    let text = '';
    text += allSkillGroups.filter(item => item.ID === data[0])[0]?.Name;
    return `【${text}】`;
  };
  // const getTexts = data => {
  //   const channel = allSkillGroups.filter(item => item.ID === data[0])[0]?.ChannelType;
  //   const skillFlag = [3, 1, 8].includes(channel);
  //   Const str = I18n.t ('number_of_seats_for_guests ', {}, ' number of seats per guest');
  //   // if (!skillFlag) {
  //   //   str = I18n.t(
  //   //     'simultaneous_maximum_order_volume/same_day_order_limit/hourly_order_limit',
  //   //     {},
  //   //'Simultaneous maximum order volume/daily order limit/hourly order limit'
  //   //   );
  //   // }
  //   return str;
  // };

  // Alarm information Add skill text
  const getSkillWarningText = data => {
    const temp = (data || []).slice();
    let skilltemp = [];
    temp[0].Expression.ConditionGroups[0].Conditions.forEach(item => {
      if (item.Lhs.VarExpr === SKILLGROUP) {
        skilltemp = item.Rhs.ConstantList;
      }
    });
    const warningText = getSkillName(skilltemp);
    // const str = getTexts(skilltemp);
    temp.map(el => {
      const isUp = el.Expression.ConditionGroups[0].Conditions[1].Rhs.Constant === '"up"';
      const isDown = el.Expression.ConditionGroups[0].Conditions[1].Rhs.Constant === '"down"';
      const constant = el.ActionInfo.ReturnValue.Constant.increaseNum;
      const text = el.ActionInfo.ReturnValue.Constant.warningText;
      if (isUp) {
        // Skill group [group name] + condition judgment [the number of queues in the past 3min is greater than 10], the number of customer service 1v in the skill group is increased by 1
        // el.ActionInfo.ReturnValue.Constant.warningText += `${I18n.t(
        //   '_skill_set__{str}_up_{constant}_of_{warningtext}',
        //   { warningText, str, constant },
        //   ', Skill set: {str} of {warningText} up {constant}'
        // )}`;
        el.ActionInfo.ReturnValue.Constant.warningText = I18n.t(
          'message_test_up_v2',
          {
            placeholder1: warningText,
            placeholder3: text,
            placeholder5: constant,
          },
          `技能组：${warningText}${text}，技能组内客服1v数上调${constant}`
        );
      }
      if (isDown) {
        // el.ActionInfo.ReturnValue.Constant.warningText += `${I18n.t(
        //   '_skill_set__{str}_of_{warningtext}_down_{constant}',
        //   { warningText, str, constant },
        //   ', Skill set: {str} of {warningText} down {constant}'
        // )}`;
        el.ActionInfo.ReturnValue.Constant.warningText = I18n.t(
          'message_test_down',
          {
            placeholder1: warningText,
            placeholder3: text,
            placeholder5: constant,
          },
          `技能组：${warningText}${text}，技能组内客服1v数下调${constant}`
        );
      }
      return el;
    });
    return temp;
  };

  const getServeValue = (value, Enable) => {
    let layeredValue = [];
    let dynamicValue = [];
    if (value.isEnableEVConfig) {
      layeredValue = getLayeredValue(value);
    }
    if (value.isEnableDYConfig) {
      dynamicValue = getDynamicValue(value);
    }
    const ruleListemp = [];
    (value.SkillGroupId || []).forEach(el => {
      // const ruleObj = safeJSONParse(JSON.stringify([...layeredValue, ...dynamicValue]));
      // Todo only needs to configure the list for the number of guests.
      const ruleObj = safeJSONParse(JSON.stringify([...dynamicValue]));
      const AntlrRule = {
        EventKey: DYNAMIC_EVENT_KEY,
        GroupDisplayName: `${I18n.t(
          'number_of_guests_rule___{placeholder1}',
          { placeholder1: getSkillName([el[1]]) },
          '对客数规则-{placeholder1}'
        )}`,
        CreatorAgentId: user.agent.ID,
        Product: DYNAMIC_EVENT_KEY,
        RuleList: [],
        Enable,
        RuleEnv: 1,
        AccessPartyId: user.accessPartyId,
        SkillGroupId: el[1],
      };
      (ruleObj || []).map(val => {
        val.Expression.ConditionGroups[0].Conditions[0].Rhs.ConstantList.push(el[1]);
        return val;
      });
      AntlrRule.RuleList = ruleObj.slice();
      ruleListemp.push(AntlrRule);
    });
    ruleListemp.map(val => {
      val.RuleList = getSkillWarningText(val.RuleList);
      val.RuleList.forEach((item, index) => {
        item.Priority = index + 1;

        item.ActionInfo.ReturnValue.Constant = `"${JSON.stringify(item.ActionInfo.ReturnValue.Constant)}"`;
      });
      return val;
    });
    return ruleListemp;
  };

  // Repeat the hierarchical configuration for the number of passengers
  const isRepeat = value => {
    value.sort((a, b) => a.start - b.start);
    const temp = [];
    value.forEach(item => {
      temp.push(item.start, item.end);
    });
    const isRepeatArr = [];
    for (let i = 0; i < temp.length; i++) {
      const flag = temp.indexOf(temp[i]) === temp.lastIndexOf(temp[i]);
      if (flag) {
        isRepeatArr.push(temp[i]);
      }
    }
    return isRepeatArr.length > 2;
  };
  // batch creation
  const BatchCreateRuleGroup = async params => {
    setLoading(true);
    try {
      // return;
      const res = await t.demoClient.BatchCreateRuleGroup(params);
      if (res.code === 0) {
        history.replace({
          pathname: '/flexible_art',
          state: {
            tabKey: 'dynamic',
          },
        });
        Toast.success(I18n.t('batch_creation_rule_successful', {}, '批量创建规则成功'));
      } else {
        Toast.error(I18n.t('failed_to_create_rules_in_bulk', {}, '批量创建规则失败'));
      }

      console.log(res, '批量创建规则');
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'BatchCreateRuleGroup' });
      Toast.error(I18n.t('failed_to_create_rules_in_bulk', {}, '批量创建规则失败'));
    } finally {
      setLoading(false);
    }
  };
  // Batch update
  const BatchUpdateRuleGroup = async params => {
    setLoading(true);
    try {
      const res = await t.demoClient.BatchUpdateRuleGroup(params);
      if (res.code === 0) {
        history.replace({
          pathname: '/flexible_art',
          state: {
            tabKey: 'dynamic',
          },
        });
        Toast.success(I18n.t('batch_update_rule_successful', {}, '批量更新规则成功'));
      } else {
        Toast.error(I18n.t('batch_update_rule_failed', {}, '批量更新规则失败'));
      }

      console.log(res, '批量创建规则');
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'BatchUpdateRuleGroup' });
      Toast.error(I18n.t('batch_update_rule_failed', {}, '批量更新规则失败'));
    } finally {
      setLoading(false);
    }
  };
  // submit
  const submitInfo = async () => {
    if (!ruleCreateFormApi) {
      return;
    }
    setLoading(true);
    try {
      const values = await ruleCreateFormApi.validate();
      const isRepeatFlag = isRepeat(values.layeredConfigCondition);
      if (isRepeatFlag) {
        Toast.error(
          I18n.t(
            'for_the_hierarchical_configuration_rule_of_the_number_of_passengers__there_is_an',
            {},
            '对客数分层配置规则条件时间存在交叉，请重新配置'
          )
        );
        return;
      }
      if (viewType === 'create') {
        const serveValue = getServeValue(values, true);
        const params = {
          RuleGroups: serveValue,
        };
        // return
        await BatchCreateRuleGroup(params);
        setLoading(false);
      } else if (viewType === 'edit') {
        console.log('==edit==');
        const serveValue = getServeValue(values, true);
        const params = {
          RuleGroups: serveValue,
        };
        params.RuleGroups.map(v => {
          const ids = beConfigList.filter(el => el.extraInfos.skill_group_id === v.SkillGroupId)[0];
          v.RuleGroupId = ids?.RuleGroupId;
          v.OriginId = ids?.OriginId;
          v.Version = 1;
          v.UpdaterAgentId = user.agent.ID;
        });
        // return
        await BatchUpdateRuleGroup(params);
        setLoading(false);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'submitInfo' });
    } finally {
      setLoading(false);
    }
  };

  const resetLayeredConfig = e => {
    if (!e) {
      return;
    }
    if (!ruleCreateFormApi) {
      return;
    }
    // debugger;
    const value = ruleCreateFormApi.getValues();
    value.isCustom = true;
    value.configCondition = '$agentWorkInSkillGroupDays'; // Seat order days
    value.configConditionOperate = 1; // Seat order days operator
    value.layeredConfigCondition = [
      {
        start: 0,
        end: 0,
        evNumber: 0,
        key: getRandomInt(1, 100000),
      },
    ];
    value.defalutEV = 0;
    ruleCreateFormApi.setValues(value, { isOverride: true });
  };

  // Layered configuration folding
  const changeLayered = e => {
    setIsLayered(e);
    resetLayeredConfig(e);
  };

  // Seat status
  const setWorkSatusValue = () => {
    if (!ruleCreateFormApi) {
      return;
    }
    const value = ruleCreateFormApi.getValues();
    if (Object.keys(ruleDetail).length !== 0) {
      return;
    }
    value.upDyConfigListUp = {
      FieldName: '$agentStatus',
      OperatorId: '',
      FieldValue: [],
    };
    value.downDyConfigListDown = {
      FieldName: '$agentStatus',
      OperatorId: '',
      FieldValue: [],
    };
    ruleCreateFormApi.setValues(value);
  };
  const getWorkStatusConfigs = async channelType => {
    const params = {
      TenantId: '1',
      ChannelType: channelType,
      AccessPartyId: user?.accessPartyId,
      SupportUnifiedWorkStatus: true,
    };
    try {
      const res = await t.demoClient.GetWorkStatusConfigs(params);
      const temp = (res?.WorkStatusConfigs || []).filter(item => item.Enabled) || [];
      setWorkStatus(temp);
      setWorkSatusValue();
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getWorkStatusConfigs' });
      Toast.error(I18n.t('failed_to_get_customer_service_status', {}, '获取客服状态失败'));
    }
  };

  // Dynamic configuration folding
  const changeDynamicConfig = e => {
    setIsDynamic(e);
  };

  // Clear the skill group and hide the agent status
  const changeWorkStatus = () => {
    setWorkStatus([]);
  };
  useEffect(() => {
    if (!isLayered && !isDynamic) {
      setIsCanSubmit(false);
    } else {
      setIsCanSubmit(true);
    }
  }, [isLayered, isDynamic]);

  const changeEvConfig = () => {
    if (!ruleCreateFormApi) {
      return;
    }

    const list = ruleCreateFormApi.getValue('layeredConfigCondition') || [];
    list?.map(el => {
      el.evNumber = null;
      return el;
    });

    ruleCreateFormApi.setValue('defalutEV', null);
    ruleCreateFormApi.setValue('layeredConfigCondition', list);
  };
  return {
    loading,
    setRuleFormRef,
    initFormDetail,
    deleteLayerItem,
    addLayerItem,
    submitInfo,
    changeLayered,
    changeDynamicConfig,
    isCanSubmit,
    getWorkStatusConfigs,
    workStatus,
    changeWorkStatus,
    changeEvConfig,
  };
};
