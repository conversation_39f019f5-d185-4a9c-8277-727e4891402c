import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Input, Icon, Button, Empty, Toast } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import styles from './index.scss';
import RuleList from '@/components/AccessRuleList';
import { context } from '../index';
import * as t from '@http_idl/demo';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import { ROUTE_TYPE_MAP } from '@/common/constants/property';

function Index() {
  const [timer, changeTimer] = useState(null);
  const {
    changeDefaultItemKey,
    onlineRuleList,
    getPreReleaseRuleList,
    fieldList,
    accessPartyStrategy,
    skillGroupStrategy,
  } = useContext(context);

  const [searchData, changeListData] = useState([]);
  const [loading, setLoading] = useState(false);
  const pathname = location.pathname.split('/');
  const typeStr = pathname[pathname.length - 1];

  const copyRouterRule = async () => {
    try {
      setLoading(true);
      const res = await t.demoClient.CopyRuleGroup({
        RuleGroupId: onlineRuleList[0].RuleGroupId,
      });
      if (res?.code === 200) {
        setLoading(false);
        Toast.success(I18n.t('rule_group_replication_successful', {}, 'Rule group replication successful'));
        await getPreReleaseRuleList();
        changeDefaultItemKey('readyToRelease');
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
    } finally {
      setLoading(false);
    }

  };

  useEffect(() => {
    changeListData(safeJSONParse(JSON.stringify(onlineRuleList)));
  }, [onlineRuleList]);

  const findData = val => {
    if (val) {
      const newListData = onlineRuleList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(onlineRuleList);
    }
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };

  return (
    <div className={styles.allContainer}>
      <div className={styles.topHead}>
        <Input
          placeholder={I18n.t('search_access_party_offload_rule', { }, 'search access party offload rule')}
          onChange={getInputValue}
          className={styles.controlInput}
          prefix={<Icon type="search" />}
          showClear
        />
        <Button
          disabled={!searchData.length || isTTP() || havePermission()}
          theme="solid"
          type="primary"
          icon="plus"
          loading={loading}
          onClick={copyRouterRule}
        >
          {I18n.t('copy_to_pre_release_rules', {}, 'Copy to pre-release rules')}
        </Button>
      </div>
      {searchData.length ? (
        <div className={styles.routerList}>
          <RuleList
            tabKey="onlineRule"
            ruleList={searchData}
            fieldList={fieldList}
            ruleType={ROUTE_TYPE_MAP[typeStr]}
            accessPartyStrategy={accessPartyStrategy}
            skillGroupStrategy={skillGroupStrategy}
          />
        </div>
      ) : (
        <Empty image={NoContent} description="" title={I18n.t('there_are_no_online_access_party_diversion_rules_yet_', { }, 'There are no online access party diversion rules yet.')} />
      )}
    </div>
  );
}
export default Index;
