import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect } from 'react';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane, Spin } from '@ies/semi-ui-react';
import * as styles from './index.scss';
import OnlineRules from './onlineRules';
import PreReleaseRules from './preReleaseRules';
import { useRoutingRule } from './use';
import { useHistory } from 'react-router-dom';

export const context = React.createContext(null);

const RouterPage: React.FC = () => {
  const [defaultItemKey, changeDefaultItemKey] = useState('onlineRule');
  const {
    ruleList,
    preRuleList,
    loadingRuleList,
    bizTypeList,
    skillGroupStrategy,
    accessPartyStrategy,
    getPreReleaseRuleList,
    getOnlineRuleList,
    setInputValue,
    fieldList,
  } = useRoutingRule(defaultItemKey);

  const [isLoading, setLoading] = useState(false);
  const [onlineRuleList, changeOnlineRuleList] = useState(ruleList);
  const [preReleaseRulesList, changePreReleaseRulesList] = useState(preRuleList);
  
  const changeActiveKey = key => {
    changeDefaultItemKey(key);
  };

  const history = useHistory();
  const { jumpPublish, newRuleId } = Object.assign({ jumpPublish: false, newRuleId: '' }, history.location.state || {});

  const jumpToPpublish = () => {
    if (jumpPublish) {
      changeDefaultItemKey('readyToRelease');
    }
  };
  useEffect(() => {
    jumpToPpublish();
  }, []);

  useEffect(() => {
    changePreReleaseRulesList(preRuleList);
  }, [preRuleList]);

  useEffect(() => {
    changeOnlineRuleList(ruleList);
  }, [ruleList]);

  useEffect(() => {
    setLoading(loadingRuleList);
  }, [loadingRuleList]);

  const content = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        <Tabs
          onChange={key => changeActiveKey(key)}
          defaultActiveKey={defaultItemKey}
          activeKey={defaultItemKey}
          type="line"
          keepDOM={false}
        >
          <TabPane tab={I18n.t('online_rules', {}, 'Online rules')} itemKey="onlineRule">
            <context.Provider
              value={{
                changeDefaultItemKey,
                onlineRuleList,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                fieldList,
                skillGroupStrategy,
                accessPartyStrategy
              }}
            >
              <OnlineRules />
            </context.Provider>
          </TabPane>
          <TabPane tab={I18n.t('pre_release_version', {}, 'Pre-release version')} itemKey="readyToRelease">
            <context.Provider
              value={{
                newRuleId,
                preReleaseRulesList,
                onlineRuleList,
                changeDefaultItemKey,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                fieldList,
                skillGroupStrategy,
                accessPartyStrategy
              }}
            >
              <PreReleaseRules />
            </context.Provider>
          </TabPane>
        </Tabs>
      </div>
    </Spin>
  );
  return content;
};
export default RouterPage;
