import { ROUTE_EVENT_ID_MAP, ROUTE_EVENT_KEY_MAP } from '@constants/property';
import { useBizTypeList, useFieldList, useStrategyList } from '@hooks/index';
import { useRuleList } from '@hooks/useAccessRuleList';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
const filterRuleList = list =>
  (list || [])
    .map(o => {
      try {
        let newStr = '';
        let val = {} as any;
        newStr = o?.ActionInfo?.ReturnValue?.Constant?.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
        val = safeJSONParse(unescape(newStr));
        o.accessPartyId = val?.accessPartyId;
        o.skillGroupRouteStrategyList = val?.skillGroupRouteStrategyList;
        o.changeAccessPartyStrategy = val?.changeAccessPartyStrategy;
        o.UpdatedAt = `${o?.UpdatedAt?.substring(0, o?.UpdatedAt?.indexOf?.('T'))} ${o?.UpdatedAt?.substring(
          o?.UpdatedAt?.indexOf?.('T') + 1,
          o?.UpdatedAt?.length
        )}`;
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'filterRuleList' });
        o.accessPartyId = '';
        o.skillGroupRouteStrategyList = []; // 1: support overflow; 0: not supported
        o.changeAccessPartyStrategy = {
          condition: [],
          newAccessPartyId: ''
        };
        console.error(error);
      }

      return o;
    })
    ?.sort((a, b) => a.Priority - b.Priority);

export const useRoutingRule = (defaultItemKey: string) => {
  const { bizTypeList, loaded: bizTypeListLoaded } = useBizTypeList();
  const pathname = location.pathname.split('/');
  const typeStr = pathname[pathname.length - 1];

  const { fieldList } = useFieldList({
    when: bizTypeListLoaded,
    eventId: ROUTE_EVENT_ID_MAP[typeStr],
    appids: bizTypeList.map(v => v.ID.toString()),
    noAccessParty: true,
  });

  const {
    loading: loadingRuleList,
    loaded: loadedRuleList,
    setInputValue,
    inputValue,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
  } = useRuleList({
    eventKey: ROUTE_EVENT_KEY_MAP[typeStr],
    filterRuleList,
    tabKey: defaultItemKey,
  });

  const { skillGroupStrategy, accessPartyStrategy } = useStrategyList();

  return {
    loadingRuleList,
    loadedRuleList,
    bizTypeList,
    ruleList,
    preRuleList,
    skillGroupStrategy,
    accessPartyStrategy,
    getOnlineRuleList,
    getPreReleaseRuleList,
    inputValue,
    setInputValue,
    fieldList,
  };
};
