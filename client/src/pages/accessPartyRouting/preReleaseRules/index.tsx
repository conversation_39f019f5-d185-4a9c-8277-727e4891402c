import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Input, Icon, Button, Modal, Toast } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { KefuPageEmptyContent } from '@ies/kefu-components';
import { useHistory } from 'react-router-dom';
import RuleList from '@/components/AccessRuleList';
import * as t from '@http_idl/demo';
import { ROUTE_EVENT_KEY_MAP, ROUTE_TYPE_MAP } from '@constants/property';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { safeJSONParse } from '@/common/utils';
import { useAccessDoorAuthority } from '@/hooks';
import { errorReporting } from '@/common/utils/errorReporting';
import { context } from '../index';
import styles from './index.scss';

function Index(props) {
  const [timer, changeTimer] = useState(null);
  const history = useHistory();
  const {
    changeDefaultItemKey,
    preReleaseRulesList,
    onlineRuleList,
    newRuleId,
    bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    fieldList,
    skillGroupStrategy,
    accessPartyStrategy,
  } = useContext(context);

  const { hasEditAuth } = useAccessDoorAuthority();
  const pathname = location.pathname.split('/');
  const typeStr = pathname[pathname.length - 1];

  // New route
  const addNewRouteRule = () => {
    if (fieldList.length > 0) {
      history.push({
        pathname: `/${ROUTE_TYPE_MAP[typeStr]}`,
        state: {
          plaformTag: ROUTE_TYPE_MAP[typeStr],
          viewType: 'create',
          Priority: 1,
          EventKey: ROUTE_EVENT_KEY_MAP[typeStr],
          ruleLength: 0,
          fieldLists: fieldList,
          bizTypeList,
          skillGroupStrategy,
          accessPartyStrategy,
        },
      });
    } else {
      Toast.warning(
        I18n.t('please_check_the_access_party_offload_condition_value', { }, 'Please check the access party offload condition value')
      );
    }
  };

  const [searchData, changeListData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Get search data
  const findData = val => {
    if (val) {
      const newListData = preReleaseRulesList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(preReleaseRulesList);
    }
  };

  useEffect(() => {
    if (inputValue) {
      const newListData = preReleaseRulesList.filter(item => item.DisplayName.includes(inputValue));
      changeListData(safeJSONParse(JSON.stringify(newListData)));
    } else {
      changeListData(safeJSONParse(JSON.stringify(preReleaseRulesList)));
    }
  }, [preReleaseRulesList]);

  const onRelease = async () => {
    const RuleIds = preReleaseRulesList.map(item => item.Id);
    const res = await t.demoClient.PublishRuleGroup({
      RuleGroupId: preReleaseRulesList[0].RuleGroupId,
      RuleIds,
    });
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    changeDefaultItemKey('onlineRule');
    // 创建一个新的状态对象，更新其中的特定属性
    const newState = { ...history.location.state, jumpPublish: false };

    // 使用 history.replace() 更新状态，但不刷新页面
    history.replace(typeStr, newState);

    await getPreReleaseRuleList();
    await getOnlineRuleList();
    Toast.success(I18n.t('rule_group_released_successfully', {}, 'Rule group released successfully'));
  };

  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        setInputValue(val);
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };

  const onCancle = async () => {
    try {
      setConfirmLoading(true);
      const res = await t.demoClient.UpdateRuleStatus({
        Ids: preReleaseRulesList?.map(val => val?.Id),
        RuleStatus: 3,
        Version: 'v1',
        Draft: true,
      });
      if (res.code !== 0) {
        Toast.error(res.message);
        return;
      }
      setModalVisible(false);
      changeDefaultItemKey('onlineRule');
      // 创建一个新的状态对象，更新其中的特定属性
      const newState = { ...history.location.state, jumpPublish: false };

      // 使用 history.replace() 更新状态，但不刷新页面
      history.replace(typeStr, newState);

      await getPreReleaseRuleList();
      await getOnlineRuleList();
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'onCancle' });
    }
  };

  return (
    <div className={styles.allContainer}>
      <Input
        placeholder={I18n.t('search_access_party_offload_rule', { }, 'search access party offload rule')}
        onChange={getInputValue}
        style={{ width: 268 }}
        prefix={<Icon type="search" />}
        showClear
      />
      {searchData.length ? (
        <>
          <div className={styles.routerList}>
            <RuleList
              tabKey="preReleaseRules"
              bizTypeList={bizTypeList}
              newRuleId={newRuleId}
              getPreReleaseRuleList={getPreReleaseRuleList}
              ruleList={searchData}
              ruleType={ROUTE_TYPE_MAP[typeStr]}
              fieldList={fieldList}
              skillGroupStrategy={skillGroupStrategy}
              accessPartyStrategy={accessPartyStrategy}
            />
          </div>
          <div className={styles.foot}>
            <div className={styles.footBtn}>
              <Button
                onClick={() => setModalVisible(true)}
                disabled={isTTP() || havePermission()}
                className={styles.btnCancle}
              >
                {I18n.t('cancel', {}, 'Cancel')}
              </Button>
              <Modal
                title={I18n.t('route_Starling_12', {}, 'Are you sure you want to cancel this modification?')}
                visible={modalVisible}
                onOk={onCancle}
                cancelText={I18n.t('cancel', {}, 'Cancel')}
                okText={I18n.t('ok', {}, 'OK')}
                onCancel={() => setModalVisible(false)}
                okType="danger"
                confirmLoading={confirmLoading}
                icon={<Icon style={{ color: 'var(--color-danger)' }} type="alert_circle" size="extra-large" />}
              >
                {I18n.t('route_Starling_13', {}, 'Be cautious! The data will not be recovered once abandoned.')}
              </Modal>
              <Button
                onClick={() => {
                  onRelease();
                }}
                disabled={!hasEditAuth}
                theme="solid"
                type="primary"
              >
                {I18n.t('release_online', {}, '发布上线')}
              </Button>
            </div>
          </div>
        </>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_pre_release_access_party_offloading_rules', { }, 'No pre-release access party offloading rules')}
        >
          {onlineRuleList.length ? null : (
            <Button
              icon="plus"
              theme="solid"
              type="primary"
              style={{ width: '100%' }}
              disabled={!hasEditAuth}
              onClick={addNewRouteRule}
            >
              {I18n.t('new_access_party_offload_rule', { }, '建接入方分流规则')}
            </Button>
          )}
        </KefuPageEmptyContent>
      )}
    </div>
    // </Spin>
  );
}

export default Index;
