import { I18n } from '@ies/starling_intl';
import { useState } from 'react';

export const HistoryRuleHooks = () => {
  const moakData = [
    {
      UpdatedAt: new Date().getTime(),
      DisplayName: I18n.t('test_version_name', {}, '测试版本名称'),
      CreatorAgentId: I18n.t('creator', {}, '创建人'),
      UpdaterAgentId: I18n.t('update_person', {}, '更新人'),
      version: '1.0.2',
      status: '',
    },
  ];
  const [tableData, setTableData] = useState(moakData);
  return {
    tableData,
    setTableData,
  };
};
