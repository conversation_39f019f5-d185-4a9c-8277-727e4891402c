import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import * as styles from './index.scss';
import { Input, Icon, Avatar, Button, Popover, Dropdown, Table } from '@ies/semi-ui-react';
import formatDate from '@/common/utils/date';
import { userHeadProps, TableHadleProps } from './historyRuleType';
import { HistoryRuleHooks } from './hooks';

// User image
const UserHeadDom: React.FC<userHeadProps> = ({ userName = '', avatarUrl = '' }) => {
  const NameAvatar = <Avatar size="small">{userName.length !== 0 ? userName.slice(0, 1) : '-'}</Avatar>;
  const ImageAvatar = <Avatar size="small" url={avatarUrl} />;
  return avatarUrl.length !== 0 ? ImageAvatar : NameAvatar;
};
// Action items of the form
const TableHandleDom: React.FC<TableHadleProps> = () => {
  const TableHandleContent = (
    <>
      <Dropdown.Menu>
        <Dropdown.Item className={styles.popoverContnetLi}>{I18n.t('view', {}, '查看')}</Dropdown.Item>
        <Dropdown.Item className={styles.popoverContnetLi}>
          {I18n.t('add_to_pre_release_version', {}, '添加到预发布版本')}
        </Dropdown.Item>
        <Dropdown.Item className={styles.popoverContnetLi}>
          {I18n.t('replace_to_pre_release_version', {}, '替换到预发布版本')}
        </Dropdown.Item>
        <Dropdown.Item className={styles.popoverContnetLi}>
          {I18n.t('add_to_pre_release_version', {}, '添加到预发布版本')}
        </Dropdown.Item>
        <Dropdown.Divider />
        <Dropdown.Item type="danger" active className={styles.popoverContnetLi}>
          {I18n.t('delete', {}, '删除')}
        </Dropdown.Item>
      </Dropdown.Menu>
    </>
  );
  return TableHandleContent;
};
// Form header
const columns = [
  {
    title: I18n.t('time_2', {}, '时间'),
    dataIndex: 'UpdatedAt',
    render: (text, record, index) => {
      const time = formatDate(text, I18n.t('yyyy_mm_month_dd_day', {}, 'YYYY年mm月dd日'));
      return <p>{time}</p>;
    },
  },
  {
    title: I18n.t('version_name', {}, '版本名称'),
    dataIndex: 'DisplayName',
  },
  {
    title: I18n.t('creator', {}, '创建人'),
    dataIndex: 'CreatorAgentId',
    render: (text, record) => <UserHeadDom userName={record.username} avatarUrl={record.avatarUrl} />,
  },
  {
    title: I18n.t('update_person', {}, '更新人'),
    dataIndex: 'UpdaterAgentId',
    render: (text, record) => <UserHeadDom userName={record.username} avatarUrl={record.avatarUrl} />,
  },
  {
    title: I18n.t('version_number', {}, '版本号'),
    dataIndex: 'version',
  },
  {
    title: I18n.t('status', {}, '状态'),
    dataIndex: 'status',
    render: (text, record) => {
      const content = (
        <Popover trigger="click" content={<TableHandleDom />}>
          <Button icon={<Icon type="more" />} />
        </Popover>
      );
      return content;
    },
  },
];

const pathname = location.pathname.split('/');
const typeStr = pathname[pathname.length - 1];

const Index: React.FC = () => {
  const { tableData } = HistoryRuleHooks();
  const content = (
    <div className={styles.historyRuleContent}>
      <Input
        placeholder={`${I18n.t(
          'search_{placeholder1}_rule',
          {
            placeholder1:
              typeStr === 'accessparty_split_flow' ? I18n.t('shunt', {}, '分流') : I18n.t('routing', {}, '路由'),
          },
          '搜索{placeholder1}规则'
        )}`}
        prefix={<Icon type="search" />}
        showClear
        className={styles.historyRuleSearchinput}
      />
      <Table columns={columns} dataSource={tableData} />
    </div>
  );
  return content;
};
export default Index;
