import { I18n } from '@ies/starling_intl';
import React, { useEffect, useContext } from 'react';
import styles from './index.scss';
import { Layout, Typography, Tabs, TabPane, Icon } from '@ies/semi-ui-react';
import Filters from './components/Filters';
import OperationButton from './components/OperationButton';
import BindingsTable from './components/BindingsTable';
import { UserContext } from '@context/user';
import { useStore } from '@/stores';
import AddBindingConfig from './components/AddBindingConfig';
import BatchOperationModal from './components/BatchOperationModal';
import { observer } from 'mobx-react-lite';

const { Title } = Typography;

const geoList = [{
  name: 'ROW',
  key: 'ROW',
  info: 'geo_info_row',
}, {
  name: 'GCP',
  key: 'GCP',
  info: 'geo_info_gcp',
}, {
  name: 'TTP',
  key: 'TTP',
  info: 'geo_info_ttp',
}];

const BindingsConfiguration: React.FC = observer(() => {
  const global = useStore('global');
  const tableStore = useStore('tableStore');
  const { bindingConfigVisible, batchOperationModalVisible, setGeo, geo } = tableStore;
  const user = useContext(UserContext);
  useEffect(() => {
    global.fetchGetAllSkillGroups({ accessPartyId: user.accessPartyId || '2' });
  }, []);
  return (
    <Layout className={styles.layout}>
      <Title heading={4}>{I18n.t('binding_relationship_configuration', {}, '绑定关系配置')}</Title>
      <Tabs type="line" onChange={setGeo} activeKey={geo}>
        {
          geoList.map(({ name, key, info }) => (
            <TabPane tab={name} itemKey={key} key={key}>
              <div className={styles.tabDesp}>
                <div className={styles.infoIcon}><Icon type="info_circle" /></div>
                <div className={styles.infoText}>{I18n.t(info)}</div>
              </div>
            </TabPane>
          ))
        }
      </Tabs>
      <div className={styles.operatingContainer}>
        <Filters />
        <OperationButton />
      </div>
      <BindingsTable />
      {bindingConfigVisible && <AddBindingConfig />}
      {batchOperationModalVisible && <BatchOperationModal />}
    </Layout>
  );
});
export default BindingsConfiguration;
