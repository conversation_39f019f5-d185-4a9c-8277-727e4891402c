import { I18n } from '@ies/starling_intl';
import React, { useEffect, useRef, useState } from 'react';
import { SideSheet, Form, withField, Button, Toast } from '@ies/semi-ui-react';
import { formApi as FormApi } from '@ies/semi-ui-react/form';
import { observer } from 'mobx-react-lite';
import UserSelect from '../../../../components/UserSelect';
import { demoClient } from '@http_idl/demo';
import styles from './index.scss';
import { useStore } from '@/stores';
import { errorReporting } from '@/common/utils/errorReporting';
const FromUserSelect = withField(UserSelect);

const AddBindingConfig: React.FC = () => {
  const formApiRef = useRef<FormApi>();
  const tableStore = useStore('tableStore');
  const globalStore = useStore('global');
  const [loading, setLoading] = useState<boolean>(false);
  const { countryList, skillGroupMap } = globalStore;
  const { bindingConfigVisible, setBindingConfigVisible, isEdit, columnsRowInfo, fetchSearchSamBind, geo } = tableStore;
  const [initValue, setInitValue] = useState({});
  const isCreate = useRef(0);
  const sideSheetTitle = isEdit ? I18n.t('edit_binding', {}, '编辑绑定') : I18n.t('new_binding', {}, '新增绑定');
  const onSubmit = async (): Promise<void> => {
    setLoading(true);
    isCreate.current = 0;
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const formValue: any = await formApiRef?.current?.validate(['sellerCountryCode']);
      if (isEdit) {
        const res = await demoClient.UpdateSamBind({ id: columnsRowInfo?.id, geo, isDel: 0, ...formValue });
        if (res?.BaseResp?.StatusCode !== 0) {
          Toast.error(res?.BaseResp?.StatusMessage || I18n.t('edit_binding_failed', {}, '编辑绑定失败'));
        } else {
          Toast.success(I18n.t('edit_binding_successful', {}, '编辑绑定成功'));
          fetchSearchSamBind({
            pageNum: '1',
            pageSize: '20',
          });
        }
        setBindingConfigVisible(false);
        setLoading(false);
      } else {
        const res = await demoClient.CreateSamBind({ ...formValue, geo });
        if (res?.BaseResp?.StatusCode !== 0) {
          Toast.error(res?.BaseResp?.StatusMessage || I18n.t('failed_to_create_binding', {}, '创建绑定失败'));
        } else {
          Toast.success(I18n.t('binding_created_successfully', {}, '创建绑定成功'));
          fetchSearchSamBind();
        }
        setBindingConfigVisible(false);
        setLoading(false);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'onSubmit' });
      Toast.error(
        `${I18n.t(
          '{placeholder0}_binding_failed',
          { placeholder0: isEdit ? I18n.t('edit', {}, '编辑') : I18n.t('route_Starling_48', {}, '创建') },
          '{placeholder0} 绑定失败 '
        )}`
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isEdit) {
      setInitValue({
        sellerId: columnsRowInfo?.sellerId,
        emailDomain: columnsRowInfo?.emailDomain,
        ticketGroupId: columnsRowInfo?.ticketGroupId,
        imGroupId: columnsRowInfo?.imGroupId,
        ticketAgentId: columnsRowInfo?.ticketAgentId,
        imAgentId: columnsRowInfo?.imAgentId,
        sellerCountryCode: columnsRowInfo?.sellerCountry,
        note: columnsRowInfo?.note,
      });
    }
  }, [formApiRef]);

  const validate = async (value): Promise<string> => {
    const onlyNumbers = /^\d+$/;
    let sleep;
    if (onlyNumbers.test(value) && value && !isEdit && !isCreate.current) {
      const res = await demoClient.GetSellerInfo({ sellerId: value });
      sleep = (ms: number): any =>
        new Promise(resolve =>
          setTimeout(() => {
            if (res?.BaseResp?.StatusCode === 0) {
              isCreate.current = 1;
              formApiRef?.current?.setValue('sellerCountryCode', res?.sellerCountryCode);
              resolve(res?.bindStatus);
            }
          }, ms)
        );
    } else {
      sleep = () => new Promise(resolve => setTimeout(resolve, 5));
    }
    return sleep(1000).then(status => {
      if (!value) {
        return I18n.t('this_field_is_required_2', {}, '该字段必填');
      }
      if (!onlyNumbers.test(value)) {
        return I18n.t('route_Starling_42', {}, '请输入数字');
      }
      if (status === 0) {
        return I18n.t('this_seller_is_bound_and_cannot_be_bound_repeatedly_', {}, '此卖家已绑定，不能重复绑定');
      }
    });
  };

  return (
    <SideSheet
      title={sideSheetTitle}
      visible={bindingConfigVisible}
      onCancel={(): void => {
        setBindingConfigVisible(false);
      }}
    >
      <Form
        onSubmit={onSubmit}
        initValues={isEdit ? initValue : {}}
        getFormApi={(formApi: FormApi): void => {
          formApiRef.current = formApi;
        }}
      >
        {formState => (
          <div className={styles.sideSheetContent}>
            <Form.Input
              label={I18n.t('merchant_id', {}, '商家ID')}
              style={{ width: '100%' }}
              field="sellerId"
              disabled={isEdit}
              validate={validate}
              onChange={(): void => {
                isCreate.current = 0;
              }}
            />
            <Form.Select
              style={{ width: '100%' }}
              field="sellerCountryCode"
              label={I18n.t('merchant_country/region', {}, '商家国家/地区')}
              disabled={true}
              optionList={countryList || []}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <Form.Input
              label={{
                text: (
                  <div style={{ display: 'flex' }}>
                    <span>{I18n.t('merchant_email_domain_name', { }, '商家邮箱域名')}</span>
                    <span className={styles.optionalLabel}>{I18n.t('optional', {}, '选填')}</span>
                  </div>
                ),
                required: false,
              }}
              style={{ width: '100%' }}
              field="emailDomain"
            />
            <Form.Select
              style={{ width: '100%' }}
              label={I18n.t('bind_im_skill_set', {}, '绑定IM技能组')}
              field="imGroupId"
              optionList={skillGroupMap.im || []}
              onChange={(): void => {
                formApiRef.current?.setValue('imAgentId', '');
              }}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <FromUserSelect
              showClear={true}
              onClear={() => formApiRef.current?.setValue('imAgentId', '')}
              style={{ width: '100%' }}
              field="imAgentId"
              label={{
                text: (
                  <div style={{ display: 'flex' }}>
                    <span>{I18n.t('bind_im_customer_service', {}, '绑定IM客服')}</span>
                    <span className={styles.optionalLabel}>{I18n.t('optional', {}, '选填')}</span>
                  </div>
                ),
                required: false,
              }}
              agentName={isEdit && formState?.values?.imAgentId ? columnsRowInfo.imAgentName : ''}
              skillGroupId={formState?.values?.imGroupId || ''}
            />
            <Form.Select
              label={I18n.t('bind_ticket_skill_set', {}, '绑定工单技能组')}
              style={{ width: '100%' }}
              field="ticketGroupId"
              optionList={skillGroupMap.ticket || []}
              onChange={(): void => {
                formApiRef?.current?.setValue('ticketAgentId', '');
              }}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <FromUserSelect
              showClear={true}
              onClear={() => formApiRef.current?.setValue('ticketAgentId', '')}
              label={{
                text: (
                  <div style={{ display: 'flex' }}>
                    <span>{I18n.t('bind_work_order_customer_service', {}, '绑定工单客服')}</span>
                    <span className={styles.optionalLabel}>{I18n.t('optional', {}, '选填')}</span>
                  </div>
                ),
                required: false,
              }}
              style={{ width: '100%' }}
              field="ticketAgentId"
              skillGroupId={formState?.values?.ticketGroupId || ''}
              agentName={isEdit && formState?.values?.ticketAgentId ? columnsRowInfo.ticketAgentName : ''}
            />
            <Form.TextArea
              label={{
                text: (
                  <div style={{ display: 'flex' }}>
                    <span>{I18n.t('remarks', {}, '备注')}</span>
                    <span className={styles.optionalLabel}>{I18n.t('optional', {}, '选填')}</span>
                  </div>
                ),
                required: false,
              }}
              style={{ width: '100%' }}
              field="note"
              maxCount={1000}
              rules={[
                {
                  validator: (rule, value) => {
                    if (value) {
                      return value?.length <= 1000;
                    } else {
                      return true;
                    }
                  },
                  message: I18n.t(
                    'the_word_count_exceeds_the_upper_limit__please_adjust_it_',
                    {},
                    '字数超过上限，请调整'
                  ),
                },
              ]}
            />
            <div className={styles.footer}>
              <div className={styles.bottomActions}>
                <Button
                  type="tertiary"
                  onClick={(): void => {
                    setBindingConfigVisible(false);
                  }}
                >
                  {I18n.t('cancel', {}, '取消')}
                </Button>
                <Button
                  style={{
                    marginLeft: '10px',
                  }}
                  loading={loading}
                  type="primary"
                  theme="solid"
                  htmlType="submit"
                >
                  {isEdit ? I18n.t('update', {}, '更新') : I18n.t('route_Starling_48', {}, '创建')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </Form>
    </SideSheet>
  );
};
export default observer(AddBindingConfig);
