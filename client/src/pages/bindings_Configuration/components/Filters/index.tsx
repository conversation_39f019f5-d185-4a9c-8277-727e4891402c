import { I18n } from '@ies/starling_intl';
import React, { useEffect } from 'react';
import KefuFilters, { StaticSelectFilter, Search, PeopleFilter, DateRangeFilter } from '@ies/kefu-filters';
import { useStore } from '@/stores';
import { demoClient } from '@http_idl/demo';
import { ChannelType } from '@ies/ais/idl/http_idl/base';
import { observer } from 'mobx-react-lite';

const Filters: React.FC = observer(() => {
  const global = useStore('global');
  const tableStore = useStore('tableStore');
  const { fetchCountryRegion, countryList } = global;
  const { setFilterData, setPagination, pagination } = tableStore;
  useEffect(() => {
    fetchCountryRegion();
  }, []);
  const searchAgent = async (val: string, type: ChannelType | any) => {
    const res = await demoClient.GetAgentsByCondition({
      ChannelType: type ? type : undefined,
      Keyword: val,
    });
    if (res.BaseResp.StatusCode) {
      return [];
    }
    const list = res.Agents.map(item => ({
      value: item.ID,
      label: item.UserName,
      username: item.UserName,
      email: item.Email,
      avatar: item.UserName.slice(0, 1),
    }));
    return list;
  };
  return (
    <KefuFilters
      onValuesChange={(val): void => {
        setFilterData({
          ...val,
          createTimeStart: val.createTime?.length ? String(new Date(val.createTime[0]).getTime()) : undefined,
          createTimeEnd: val.createTime?.length ? String(new Date(val.createTime[1]).getTime()) : undefined,
          updateTimeStart: val.updateTime?.length ? String(new Date(val.updateTime[0]).getTime()) : undefined,
          updateTimeEnd: val.updateTime?.length ? String(new Date(val.updateTime[1]).getTime()) : undefined,
        });
        setPagination({ pageNum: '1', pageSize: pagination.pageSize });
      }}
    >
      <Search
        field="sellerId"
        inputProps={{ placeholder: I18n.t('enter_the_merchant_id_to_search', {}, '输入商家ID搜索') }}
      />
      <Search
        field="emailDomain"
        inputProps={{ placeholder: I18n.t('enter_the_merchant_email_domain_name_search', { }, '输入商家邮箱域名搜索') }}
      />
      <StaticSelectFilter
        hideFilter
        field="sellerCountryCode"
        label={I18n.t('merchant_country/region', {}, '商家国家/地区')}
        optionList={countryList}
      />
      <StaticSelectFilter
        field="imGroupId"
        label={I18n.t('im_skill_set', {}, 'IM技能组')}
        optionList={global?.skillGroupMap?.im || []}
      />
      <PeopleFilter
        field="imAgentId"
        label={I18n.t('im_staff', {}, 'IM人员')}
        onSearch={(val: string) => searchAgent(val, ChannelType.IM)}
      />
      <StaticSelectFilter
        field="ticketGroupId"
        label={I18n.t('work_order_skill_group', {}, '工单技能组')}
        optionList={global?.skillGroupMap?.ticket || []}
      />
      <PeopleFilter
        field="ticketAgentId"
        label={I18n.t('ticket_staff', {}, '工单人员')}
        onSearch={(val: string) => searchAgent(val, ChannelType.TICKET)}
      />
      <PeopleFilter
        field="operateAgentId"
        label={I18n.t('update_person', {}, '更新人')}
        onSearch={(val: string) => searchAgent(val, '')}
      />
      <DateRangeFilter field="updateTime" type="dateTimeRange" label={I18n.t('update_time', {}, '更新时间')} />
      <DateRangeFilter field="createTime" type="dateTimeRange" label={I18n.t('creation_time', {}, '创建时间')} />
    </KefuFilters>
  );
});
export default Filters;
