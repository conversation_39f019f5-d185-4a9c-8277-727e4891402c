import { I18n } from '@ies/starling_intl';
import React, { useState, useContext } from 'react';
import { Button, Toast } from '@ies/semi-ui-react';
import { observer } from 'mobx-react-lite';
import { ModalTitle } from '@/common/constants/property';
import { useStore } from '@/stores';
import styles from './index.scss';
import { demoClient } from '@http_idl/demo';
import { errorReporting } from '@/common/utils/errorReporting';
const OperationButton: React.FC = () => {
  const tableStore = useStore('tableStore');
  const [exportLoading, setExportLoading] = useState(false);
  const {
    setBindingConfigVisible,
    setIsEdit,
    setBatchOperationModalVisible,
    setbBtchOperationModalTitle,
    filterData,
    geo,
  } = tableStore;
  const batchExport = async (): Promise<void> => {
    setExportLoading(true);
    try {
      const res = await demoClient.BatchExportSamBind({
        ...filterData,
        geo,
        createTime: undefined,
        updateTime: undefined,
      });
      if (res.BaseResp.StatusCode !== 0) {
        Toast.error(res.BaseResp.StatusMessage || I18n.t('export_failed', {}, '导出失败'));
      } else {
        Toast.success(I18n.t('export_successful', {}, '导出成功'));
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'batchExport' });
      Toast.error(I18n.t('export_failed', {}, '导出失败'));
    } finally {
      setExportLoading(false);
    }
  };
  return (
    <div className={styles.operationButtonContainer}>
      <div>
        <Button icon="export" loading={exportLoading} theme="borderless" type="primary" onClick={batchExport}>
          {I18n.t('export', {}, '导出')}
        </Button>
        <Button
          theme="solid"
          type="primary"
          style={{ marginLeft: '10px' }}
          onClick={() => {
            setIsEdit(false);
            setBindingConfigVisible(true);
          }}
        >
          {I18n.t('new_binding', {}, '新增绑定')}
        </Button>
      </div>
      <div className={styles.operationButtonContainer} style={{ marginTop: '5px' }}>
        <Button
          theme="borderless"
          type="primary"
          onClick={(): void => {
            setbBtchOperationModalTitle(ModalTitle.BULK_DELETE);
            setBatchOperationModalVisible(true);
          }}
        >
          {I18n.t('bulk_delete_binding', {}, '批量删除绑定')}
        </Button>
        <Button
          theme="borderless"
          type="primary"
          onClick={(): void => {
            setbBtchOperationModalTitle(ModalTitle.BULK_IMPORT);
            setBatchOperationModalVisible(true);
          }}
        >
          {I18n.t('bulk_import_binding', {}, '批量导入绑定')}
        </Button>
      </div>
    </div>
  );
};
export default observer(OperationButton);
