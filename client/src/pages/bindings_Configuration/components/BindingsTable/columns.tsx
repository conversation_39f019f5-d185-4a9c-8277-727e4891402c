import { I18n } from '@ies/starling_intl';
import React from 'react';
import { Dropdown, Icon, Modal, Avatar, Typography } from '@ies/semi-ui-react';
import formatI18n from '@/common/utils/datei18n';
import styles from './index.scss';
const { Text, Paragraph } = Typography;
function columns(
  setIsEdit: (val: boolean) => void,
  setColumnsRecord: (obj: Record<string, any>) => void,
  handleDelete: (val: string) => void,
  setBindingConfigVisible: (val: boolean) => void,
  countryList: any[]
): any[] {
  return [
    {
      title: I18n.t('merchant_id', {}, '商家ID'),
      fixed: 'left',
      width: 280,
      dataIndex: 'sellerId',
      render: (_, record) => (
        <div style={{ display: 'flex' }}>
          <Avatar size="default" src={record?.sellerImg} />
          <div style={{ marginLeft: '5px' }}>
            <Text
              style={{ width: '180px' }}
              ellipsis={{
                showTooltip: {
                  opts: { content: record?.sellerName, style: { wordBreak: 'break-all' } },
                },
              }}
            >
              {record?.sellerName || '-'}
            </Text>
            <div className={styles.tableFontLabel}>ID：{record?.sellerId || '-'}</div>
          </div>
        </div>
      ),
    },
    {
      title: I18n.t('merchant_country/region', {}, '商家国家/地区'),
      width: 200,
      dataIndex: 'sellerCountry',
      render: (text): JSX.Element => {
        const country = countryList.find(item => item.value === text)?.label || '-';
        return <Text>{country || '-'}</Text>;
      },
    },
    {
      title: I18n.t('merchant_email_domain_name', {}, '商家邮箱域名'),
      width: 240,
      dataIndex: 'emailDomain',
      render: (text): JSX.Element => (
        <>
          <Text
            style={{ width: '200px' }}
            ellipsis={{
              showTooltip: {
                opts: { content: text, style: { width: 200, wordBreak: 'break-all' } },
              },
            }}
          >
            {text || '-'}
          </Text>
        </>
      ),
    },
    {
      title: I18n.t('im_binding_skill_set', {}, 'IM绑定技能组'),
      width: 200,
      dataIndex: 'imGroupName',
      render: (text): JSX.Element => (
        <>
          <Text
            style={{ width: '180px' }}
            ellipsis={{
              showTooltip: {
                opts: { content: text, style: { width: 200, wordBreak: 'break-all' } },
              },
            }}
          >
            {text || '-'}
          </Text>
        </>
      ),
    },
    {
      title: I18n.t('im_binding_customer_service', {}, 'IM绑定客服'),
      width: 200,
      dataIndex: 'imAgentName',
      render: (_, record): JSX.Element => (
        <>
          <Text
            style={{ width: '180px' }}
            ellipsis={{
              showTooltip: {
                opts: { content: record?.imAgentName, style: { width: 200, wordBreak: 'break-all' } },
              },
            }}
          >
            {record?.imAgentName || '-'}
          </Text>
          <div className={styles.tableFontLabel}>{record?.imAgentEmail || '-'}</div>
        </>
      ),
    },
    {
      title: I18n.t('ticket_binding_skill_set', {}, '工单绑定技能组'),
      width: 200,
      dataIndex: 'ticketGroupName',
      render: (text): JSX.Element => (
        <>
          <Text
            style={{ width: '180px' }}
            ellipsis={{
              showTooltip: {
                opts: { content: text, style: { width: 200, wordBreak: 'break-all' } },
              },
            }}
          >
            {text || '-'}
          </Text>
        </>
      ),
    },
    {
      title: I18n.t('work_order_binding_customer_service', {}, '工单绑定客服'),
      width: 220,
      dataIndex: 'ticketAgentName',
      render: (_, record): JSX.Element => (
        <>
          <div className={styles.tableFontValue}>{record?.ticketAgentName || '-'}</div>
          <div className={styles.tableFontLabel}>{record?.ticketAgentEmail || '-'}</div>
        </>
      ),
    },
    {
      title: I18n.t('remarks', {}, '备注'),
      width: 200,
      dataIndex: 'note',
      render: (text: string): JSX.Element => (
        <>
          <Paragraph
            ellipsis={{
              rows: 2,
              showTooltip: {
                type: 'popover',
                opts: {
                  content: text,
                  style: { width: 300, height: 200, wordBreak: 'break-all', overflowY: 'scroll' },
                },
              },
            }}
          >
            {text}
          </Paragraph>
        </>
      ),
    },
    {
      title: I18n.t('operator_2', {}, '操作人'),
      width: 200,
      dataIndex: 'operateName',
      render: (_, record): JSX.Element => (
        <div>
          <Avatar size="extra-small" src={record?.operateImg} />
          <span style={{ marginLeft: '5px' }}>{record?.operateName || '-'}</span>
        </div>
      ),
    },
    {
      title: I18n.t('update_time', {}, '更新时间'),
      width: 200,
      dataIndex: 'updateTime',
      render: (time: string) => formatI18n(time, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: I18n.t('creation_time', {}, '创建时间'),
      width: 200,
      dataIndex: 'createTime',
      render: (time: string) => formatI18n(time, 'YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: I18n.t('operation', {}, '操作'),
      fixed: 'right',
      width: 100,
      render: (_, record): React.ReactNode => (
        <Dropdown
          autoAdjustOverflow
          clickToHide
          position="bottomRight"
          render={(
            <Dropdown.Menu style={{ width: '150px' }}>
              <Dropdown.Item
                onClick={() => {
                  setIsEdit(true);
                  setBindingConfigVisible(true);
                  setColumnsRecord(record);
                }}
              >
                {I18n.t('edit', {}, '编辑')}
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item
                type="danger"
                onClick={() => {
                  Modal.error({
                    title: I18n.t('make_sure_to_delete_this_seller_binding_', {}, '确定删除这个卖家绑定'),
                    okText: I18n.t('delete', {}, '删除'),
                    cancelText: I18n.t('cancel', {}, '取消'),
                    onOk: () => handleDelete(record?.sellerId || ''),
                  });
                }}
              >
                {I18n.t('delete', {}, '删除')}
              </Dropdown.Item>
            </Dropdown.Menu>
          )}
        >
          <Icon type="more" />
        </Dropdown>
      ),
    },
  ];
}
export default columns;
