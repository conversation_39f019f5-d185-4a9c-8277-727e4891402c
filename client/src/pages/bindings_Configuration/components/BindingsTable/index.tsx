import { I18n } from '@ies/starling_intl';
import React, { useEffect } from 'react';
import { Table, Toast } from '@ies/semi-ui-react';
import columns from './columns';
import { useStore } from '@/stores';
import { demoClient } from '@http_idl/demo';
import BulkOperation from '../BulkOperation';
import { observer } from 'mobx-react-lite';
const BindingsTable: React.FC = () => {
  const tableStore = useStore('tableStore');
  const globalStore = useStore('global');
  const { countryList } = globalStore;
  const {
    samBindList,
    filterData,
    tableLoading,
    pagination,
    total,
    fetchSearchSamBind,
    setIsEdit,
    setColumnsRecord,
    selectedRowKeys,
    setSelectedItems,
    setSelectedRowKeys,
    setPagination,
    setBindingConfigVisible,
    geo,
  } = tableStore;
  useEffect(() => {
    fetchSearchSamBind({
      ...filterData,
      pageNum: '1',
      pageSize: pagination?.pageSize,
      createTime: undefined,
      updateTime: undefined,
    });
  }, [filterData]);
  const handleDelete = async (sellerId: string): Promise<void> => {
    const res = await demoClient.BatchDelSamBind({
      sellerIdList: [sellerId],
      geo,
    });
    if (res?.BaseResp?.StatusCode !== 0) {
      Toast.error(res?.BaseResp?.StatusMessage || I18n.t('failed_to_delete_binding', {}, '删除绑定失败'));
    } else {
      fetchSearchSamBind();
      Toast.success(I18n.t('deleted_binding_successfully', {}, '删除绑定成功'));
    }
  };
  const rowSelection = {
    fixed: true,
    selectedRowKeys,
    onChange: (selectedRowKey, selectedRows): void => {
      setSelectedItems(selectedRows);
      setSelectedRowKeys(selectedRowKey);
    },
  };
  const handleChange = (currentPage: number, pageSize: number): void => {
    setPagination({
      pageNum: String(currentPage),
      pageSize: String(pageSize),
    });
    fetchSearchSamBind({
      ...filterData,
      pageNum: String(currentPage),
      pageSize: String(pageSize),
      createTime: undefined,
      updateTime: undefined,
    });
  };
  return (
    <>
      <Table
        rowSelection={rowSelection}
        rowKey="sellerId"
        columns={columns(setIsEdit, setColumnsRecord, handleDelete, setBindingConfigVisible, countryList)}
        loading={tableLoading}
        scroll={{ y: 'calc(100vh - 300px)' }}
        pagination={{
          showSizeChanger: true,
          pageSizeOpts: [10, 20, 50, 100, 200],
          currentPage: Number(pagination?.pageNum),
          pageSize: Number(pagination?.pageSize),
          onChange: handleChange,
          total: Number(total),
        }}
        dataSource={samBindList}
      />
      <BulkOperation />
    </>
  );
};

export default observer(BindingsTable);
