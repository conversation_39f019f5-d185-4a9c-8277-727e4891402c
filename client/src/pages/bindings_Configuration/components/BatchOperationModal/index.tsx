import { I18n } from '@ies/starling_intl';
import React, { useRef, useState } from 'react';
import { Banner, Modal, Form, Toast, Button } from '@ies/semi-ui-react';
import { observer } from 'mobx-react-lite';
import { ModalTitle, permDescripLarkLink, tempLarkLink, deleteTempLarkLink } from '@/common/constants/property';
import { demoClient } from '@http_idl/demo';
import { useStore } from '@/stores';
import { formApi as FormApi } from '@ies/semi-ui-react/form';
import { errorReporting } from '@/common/utils/errorReporting';

const BatchOperationModal: React.FC = () => {
  const tableStore = useStore('tableStore');
  const [loading, setLoading] = useState(false);

  const {
    batchOperationModalVisible,
    setBatchOperationModalVisible,
    batchOperationModalTitle,
    fetchSearchSamBind,
    geo,
  } = tableStore;
  const formApiRef = useRef<FormApi>();
  const handleOk = async (): Promise<void> => {
    await formApiRef?.current?.validate();
    try {
      setLoading(true);
      const larkLink = await formApiRef?.current?.getValue('larkLink');
      if (ModalTitle.BULK_DELETE === batchOperationModalTitle) {
        const res = await demoClient.BatchDelSamBindByExcel({
          excelUrl: larkLink,
          geo,
        });
        if (res.BaseResp.StatusCode !== 0) {
          Toast.error(res?.BaseResp?.StatusMessage || I18n.t('file_import_failed', {}, '文件导入失败'));
          setLoading(false);
        } else {
          fetchSearchSamBind();
          Toast.success(
            I18n.t(
              'the_file_was_uploaded_successfully__please_check_the_feishu_message_',
              {},
              '文件上传成功，请查看飞书消息'
            )
          );
        }
        setBatchOperationModalVisible(false);
        setLoading(false);
      }
      if (ModalTitle.BULK_IMPORT === batchOperationModalTitle) {
        const res = await demoClient.BatchCreateSamBind({
          excelUrl: larkLink,
          geo,
        });
        if (res.BaseResp.StatusCode !== 0) {
          Toast.error(res?.BaseResp?.StatusMessage || I18n.t('file_import_failed', {}, '文件导入失败'));
        } else {
          fetchSearchSamBind();
          Toast.success(
            I18n.t(
              'the_file_was_uploaded_successfully__please_check_the_feishu_message_',
              {},
              '文件上传成功，请查看飞书消息'
            )
          );
        }
        setBatchOperationModalVisible(false);
        setLoading(false);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'BatchTransferSamBind' });
      Toast.error(I18n.t('file_import_failed', {}, '文件导入失败'));
    } finally {
      setBatchOperationModalVisible(false);
      setLoading(false);
    }
  };
  return (
    <Modal
      title={batchOperationModalTitle ? I18n.t('bulk_import', {}, '批量导入') : I18n.t('batch_delete', {}, '批量删除')}
      visible={batchOperationModalVisible}
      style={{ width: 554 }}
      footer={(
        <>
          <Button
            onClick={(): void => {
              setBatchOperationModalVisible(false);
            }}
          >
            {I18n.t('cancel', {}, '取消')}
          </Button>
          <Button loading={loading} theme="solid" type="primary" onClick={handleOk}>
            {batchOperationModalTitle ? I18n.t('import', {}, '导入') : I18n.t('delete', {}, '删除')}
          </Button>
        </>
      )}
      onCancel={(): void => {
        setBatchOperationModalVisible(false);
      }}
    >
      <div>
        <Banner
          style={{ width: 506, height: 84 }}
          fullMode={false}
          type="info"
          bordered
          closeIcon={null}
          description={(
            <div style={{ fontWeight: 700 }}>
              <div>{I18n.t('feishu_document_permission_authorization_prompt_area', {}, '飞书文档权限授权提示区')}</div>
              <a
                style={{ color: 'var(--color-primary)', fontWeight: 600 }}
                href={permDescripLarkLink}
                target="_blank"
                rel="noreferrer"
              >
                {I18n.t('permission_enablement_instructions', {}, '权限启用说明')}
              </a>
            </div>
          )}
        />
        <Form getFormApi={(formApi: FormApi): FormApi => (formApiRef.current = formApi)}>
          <Form.Input
            field="larkLink"
            label={I18n.t('feishu_link', {}, '飞书链接')}
            style={{ width: '100%' }}
            rules={[
              {
                required: true,
                message: I18n.t('please_fill_in_the_feishu_documentation_link', {}, '请填写飞书文档链接'),
              },
            ]}
          />
          <span style={{ fontSize: '14px', color: 'var(--color-text-2)' }}>
            {I18n.t('haven_t_created_it_yet?', {}, '还没有创建？')}
            <a
              style={{ color: '#0B877D', fontWeight: 700, cursor: 'pointer', marginLeft: '5px' }}
              href={batchOperationModalTitle ? tempLarkLink : deleteTempLarkLink}
              target="_blank"
              rel="noreferrer"
            >
              {I18n.t('template_prompt', {}, '模版提示')}
            </a>
          </span>
        </Form>
      </div>
    </Modal>
  );
};
export default observer(BatchOperationModal);
