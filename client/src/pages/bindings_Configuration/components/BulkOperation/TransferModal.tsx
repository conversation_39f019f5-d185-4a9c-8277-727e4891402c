import { I18n } from '@ies/starling_intl';
import React, { useRef, useState } from 'react';
import { Form, Toast, withField, Button } from '@ies/semi-ui-react';
import Modal from '@ies/semi-ui-react/modal/Modal';
import UserSelect from '@/components/UserSelect';
import { observer } from 'mobx-react-lite';
import { demoClient } from '@http_idl/demo';
import { formApi as FormApi } from '@ies/semi-ui-react/form';
import { useStore } from '@/stores';
import { errorReporting } from '@/common/utils/errorReporting';
const FromUserSelect = withField(UserSelect);

const TransferModal: React.FC = () => {
  const globalStore = useStore('global');
  const tableStore = useStore('tableStore');
  const [loading, setLoading] = useState(false);
  const { skillGroupMap } = globalStore;
  const {
    setTransferModalVisible,
    transferModalVisible,
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedItems,
    fetchSearchSamBind,
  } = tableStore;
  const formApiRef = useRef<FormApi>();
  const handleOk = async (): Promise<void> => {
    const formValue: any = await formApiRef.current?.validate();
    setLoading(true);
    try {
      const res = await demoClient.BatchTransferSamBind({
        sellerIdList: selectedRowKeys,
        ...formValue,
      });
      if (res?.BaseResp?.StatusCode !== 0) {
        res?.BaseResp?.StatusMessage &&
          Toast.error(res?.BaseResp?.StatusMessage || I18n.t('batch_transfer_failed', {}, '批量转交失败'));
      } else {
        setSelectedRowKeys([]);
        setSelectedItems([]);
        Toast.success(I18n.t('batch_transfer_successful', {}, '批量转交成功'));
        fetchSearchSamBind();
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'BatchTransferSamBind' });
      Toast.error(I18n.t('batch_transfer_failed', {}, '批量转交失败'));
    } finally {
      setLoading(false);
      setTransferModalVisible(false);
    }
  };
  return (
    <Modal
      title={I18n.t('bulk_transfer', {}, '批量转交')}
      onOk={handleOk}
      visible={transferModalVisible}
      footer={(
        <>
          <Button
            onClick={(): void => {
              setTransferModalVisible(false);
            }}
          >
            {I18n.t('cancel', {}, '取消')}
          </Button>
          <Button loading={loading} theme="solid" type="primary" onClick={handleOk}>
            {I18n.t('transfer', {}, '转交')}
          </Button>
        </>
      )}
      onCancel={(): void => {
        setTransferModalVisible(false);
      }}
    >
      <Form getFormApi={(formApi: FormApi): FormApi => (formApiRef.current = formApi)}>
        {(formState): JSX.Element => (
          <>
            <Form.Input
              style={{ width: '100%' }}
              label={I18n.t('merchant_email_domain_name', { }, '商家邮箱域名')}
              field="emailDomain"
            />
            <Form.Select
              style={{ width: '100%' }}
              label={I18n.t('bind_im_skill_set', {}, '绑定IM技能组')}
              field="imGroupId"
              optionList={skillGroupMap?.im || []}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <FromUserSelect
              style={{ width: '100%' }}
              field="imAgentId"
              label={I18n.t('bind_im_customer_service', {}, '绑定IM客服')}
              skillGroupId={formState?.values?.imGroupId || ''}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <Form.Select
              style={{ width: '100%' }}
              label={I18n.t('bind_ticket_skill_set', {}, '绑定工单技能组')}
              field="ticketGroupId"
              optionList={skillGroupMap?.ticket || []}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
            <FromUserSelect
              style={{ width: '100%' }}
              field="ticketAgentId"
              label={I18n.t('bind_work_order_customer_service', {}, '绑定工单客服')}
              skillGroupId={formState?.values?.ticketGroupId || ''}
              rules={[
                {
                  required: true,
                  message: I18n.t('this_field_is_required_2', {}, '该字段必填'),
                },
              ]}
            />
          </>
        )}
      </Form>
    </Modal>
  );
};
export default observer(TransferModal);
