import { I18n } from '@ies/starling_intl';
import React from 'react';
import KefuBulkAction from '@ies/kefu-bulk-action';
import { observer } from 'mobx-react-lite';
import { Modal, Toast } from '@ies/semi-ui-react';
import { languageMap } from '@/common/constants/property';
import { useStore } from '@/stores';
import TransferModal from './TransferModal';
import { demoClient } from '@http_idl/demo';

const BulkOperation: React.FC = () => {
  const tableStore = useStore('tableStore');
  const globalStore = useStore('global');
  const {
    samBindList,
    selectedItems,
    selectedRowKeys,
    setSelectedItems,
    setSelectedRowKeys,
    setTransferModalVisible,
    fetchSearchSamBind,
    geo,
  } = tableStore;
  const { unitedLang } = globalStore;
  const handleOk = async (): Promise<void> => {
    const res = await demoClient.BatchDelSamBind({
      sellerIdList: selectedRowKeys,
      geo,
    });
    if (res?.BaseResp?.StatusCode !== 0) {
      Toast.error(res?.BaseResp?.StatusMessage || I18n.t('bulk_delete_failed', {}, '批量删除失败'));
    } else {
      Toast.success(I18n.t('deleted_successfully', {}, '删除成功'));
      setSelectedRowKeys([]);
      setSelectedItems([]);
      fetchSearchSamBind();
    }
  };
  const actionItems = [
    {
      label: I18n.t('transfer', {}, '转交'),
      onClick: (): void => {
        setTransferModalVisible(true);
      },
    },
    {
      label: I18n.t('delete', {}, '删除'),
      onClick: (): void => {
        Modal.error({
          title: `${I18n.t(
            'make_sure_to_delete_{placeholder1}_seller_bindings',
            { placeholder1: selectedRowKeys?.length },
            '确定删除{placeholder1}个卖家绑定'
          )}`,
          onOk: handleOk,
          cancelText: I18n.t('cancel', {}, '取消'),
          okText: I18n.t('delete', {}, '删除'),
        });
      },
    },
  ];
  return (
    <>
      <KefuBulkAction
        total={samBindList?.length || 0}
        selectedItems={selectedItems}
        actionItems={actionItems}
        lang={languageMap[unitedLang]}
        onCancelAll={(): void => {
          setSelectedRowKeys([]);
        }}
      />
      <TransferModal />
    </>
  );
};

export default observer(BulkOperation);
