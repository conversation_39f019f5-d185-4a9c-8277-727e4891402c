import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import { Input, Button, Icon, Toast, Modal } from '@ies/semi-ui-react';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import styles from './index.scss';
import RuleList from '@components/ForecastRule';
import { KefuPageEmptyContent } from '@ies/kefu-components';
import { context } from '../index';
import { havePermission } from '@/common/utils/hasPermission';
import { isTTP } from '@/common/utils/env';
import { routeType } from '@/const/enums';
import { useHistory } from 'react-router-dom';
import { UserContext } from '@/context/user';
import * as t from '@http_idl/demo';
import { BOT_ROUTE, ROUTE_PATH } from '@constants/property';
import { safeJSONParse } from '@/common/utils';
import { useDoorAuthority } from '@/hooks';
import { errorReporting } from '@/common/utils/errorReporting';

function Index(props) {
  const [timer, changeTimer] = useState(null);
  // const { skillGroups, fieldList, botList } = useRoutingRule();
  const user = useContext(UserContext);
  const history = useHistory();
  const {
    changeDefaultItemKey,
    PreReleaseRulesList,
    onlineRuleList,
    newRuleId,
    changePreReleaseRulesList,
    skillGroups,
    bizTypeList,
    getPreReleaseRuleList,
    getOnlineRuleList,
    botList,
    fieldList,
  } = useContext(context);
  const [searchData, changeListData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const { hasEditAuth } = useDoorAuthority();

  // New route
  const addNewRouteRule = () => {
    if (fieldList.length > 0 && skillGroups.length > 0) {
      history.push({
        pathname: '/bot_create_rule',
        state: {
          plaformTag: routeType.bot_routing,
          viewType: 'create',
          Priority: 1,
          EventKey: BOT_ROUTE,
          ruleLength: 0,
          skillGroups,
          fieldLists: fieldList,
          bizTypeList,
        },
      });
    } else {
      Toast.warning(
        I18n.t('_please_check_the_skill_group_and_routing_condition_values_', {}, '「请检查技能组与路由条件值」')
      );
    }
  };

  // Get search data
  const findData = val => {
    if (val) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(val));
      changeListData(newListData);
    } else {
      changeListData(PreReleaseRulesList);
    }
  };
  useEffect(() => {
    // changeListData(safeJSONParse(JSON.stringify(PreReleaseRulesList)));
    if (inputValue) {
      const newListData = PreReleaseRulesList.filter(item => item.DisplayName.includes(inputValue));
      changeListData(safeJSONParse(JSON.stringify(newListData)));
    } else {
      changeListData(safeJSONParse(JSON.stringify(PreReleaseRulesList)));
    }
  }, [PreReleaseRulesList]);
  const onRelease = async () => {
    const RuleIds = PreReleaseRulesList.map(item => item.Id);
    const res = await t.demoClient.PublishRuleGroup({
      RuleGroupId: PreReleaseRulesList[0].RuleGroupId,
      RuleIds,
    });
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    changeDefaultItemKey('onlineRule');
    // 创建一个新的状态对象，更新其中的特定属性
    const newState = { ...history.location.state, jumpPublish: false };

    // 使用 history.replace() 更新状态，但不刷新页面
    history.replace(ROUTE_PATH.IM_BOT, newState);
    await getPreReleaseRuleList();
    await getOnlineRuleList();
    Toast.success(I18n.t('rule_group_released_successfully', {}, '规则组发布成功'));
  };
  const getInputValue = val => {
    if (timer) {
      clearTimeout(timer);
    }
    changeTimer(
      setTimeout(() => {
        setInputValue(val);
        findData(val);
        changeTimer(null);
      }, 500)
    );
  };

  const onCancle = async () => {
    try {
      setConfirmLoading(true);
      const res = await t.demoClient.UpdateRuleStatus({
        Ids: PreReleaseRulesList?.map(val => val?.Id),
        RuleStatus: 3,
        Version: 'v1',
        Draft: true,
      });
      if (res.code !== 0) {
        Toast.error(res.message);
        return;
      }
      setModalVisible(false);
      changeDefaultItemKey('onlineRule');
      // 创建一个新的状态对象，更新其中的特定属性
      const newState = { ...history.location.state, jumpPublish: false };

      // 使用 history.replace() 更新状态，但不刷新页面
      history.replace(ROUTE_PATH.IM_BOT, newState);
      await getPreReleaseRuleList();
      await getOnlineRuleList();
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'onCancle' });
    }
  };
  return (
    <div className={styles.allContainer}>
      <Input
        placeholder={I18n.t('search_routing_rules', {}, '搜索路由规则')}
        onChange={getInputValue}
        style={{ width: 268 }}
        prefix={<Icon type="search" />}
        showClear
      />
      {searchData.length ? (
        <>
          <div className={styles.routerList}>
            <RuleList
              bizTypeList={bizTypeList}
              newRuleId={newRuleId}
              getPreReleaseRuleList={getPreReleaseRuleList}
              ruleList={searchData}
              skillGroups={skillGroups}
              changePreReleaseRulesList={changePreReleaseRulesList}
              botList={botList}
              ruleType={routeType.bot_routing}
              fieldList={fieldList}
            />
          </div>
          <div className={styles.foot}>
            <div className={styles.footBtn}>
              <Button onClick={() => setModalVisible(true)} className={styles.btnCancle}>
                {I18n.t('cancel', {}, '取消')}
              </Button>
              <Modal
                title={I18n.t('route_Starling_12', {}, '请问您是否确认要取消本次修改？')}
                visible={modalVisible}
                onOk={onCancle}
                cancelText={I18n.t('cancel', {}, '取消')}
                okText={I18n.t('ok', {}, '确定')}
                onCancel={() => setModalVisible(false)}
                okType="danger"
                confirmLoading={confirmLoading}
                icon={<Icon style={{ color: 'var(--color-danger)' }} type="alert_circle" size="extra-large" />}
              >
                {I18n.t('route_Starling_13', {}, '一旦取消，数据将无法恢复，请谨慎操作')}
              </Modal>
              <Button
                onClick={() => {
                  onRelease();
                }}
                disabled={isTTP() || havePermission() || !hasEditAuth}
                theme="solid"
                type="primary"
              >
                {I18n.t('release_online', {}, '发布上线')}
              </Button>
            </div>
          </div>
        </>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_pre_release_routing_rules', {}, '暂无预发布路由规则')}
        >
          {onlineRuleList.length ? null : (
            <Button
              icon="plus"
              theme="solid"
              type="primary"
              disabled={isTTP() || havePermission() || !hasEditAuth}
              style={{ width: '100%' }}
              onClick={addNewRouteRule}
            >
              {I18n.t('new_routing_rule', {}, '新建路由规则')}
            </Button>
          )}
        </KefuPageEmptyContent>
      )}
    </div>
  );
}

export default Index;
