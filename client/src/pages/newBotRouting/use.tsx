import { ChannelType } from '@http_idl/demo';
import { BOT_ROUTING_EVENT_ID, BOT_ROUTE } from '@constants/property';

import { useSkillGroups, useBizTypeList, useBotList, useFieldList } from '@hooks/index';
import { useRuleList } from '@hooks/newUseRuleList';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';

const filterRuleList = list =>
  (list || [])
    .map(o => {
      try {
        const newStr = o.ActionInfo.ReturnValue?.Constant.substring(1, o.ActionInfo.ReturnValue.Constant.length - 1);
        const val = safeJSONParse(unescape(newStr));
        // eslint-disable-next-line @ies/eden/max-calls-in-template
        o.UpdatedAt = `${o.UpdatedAt.substring(0, o.UpdatedAt.indexOf('T'))} ${o.UpdatedAt.substring(
          o.UpdatedAt.indexOf('T') + 1,
          o.UpdatedAt.length
        )}`;
        o.SkillGroupId = val?.skill_group?.id;
        o.SupportOverflow = val?.support_overflow;
        o.SkillGroupOverflow = (val?.skill_group_overflow || []).map(o => o.id);
        o.IsOpen = val?.is_open;
        o.BotId = val?.bot_id?.id;
        o.needRiskIdentification = val?.needRiskIdentification;
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'getWorkStatusConfigs' });

        o.SkillGroupId = '';
        o.SupportOverflow = 0; // 1: support overflow; 0: not supported
        o.SkillGroupOverflow = [];
        o.IsOpen = 0; // 1: supported; 0: not supported
        o.BotId = '';
        o.needRiskIdentification = 0;
        console.error(error);
      }
      return o;
    })
    ?.sort((a, b) => a.Priority - b.Priority);

export const useRoutingRule = (defaultItemKey: string) => {
  const { bizTypeList, loaded: bizTypeListLoaded } = useBizTypeList();

  const { skillGroups } = useSkillGroups({
    channelType: ChannelType.IM,
    routerKey: defaultItemKey,
  });

  // const { cardList } = useCardList({
  //   when: bizTypeListLoaded,
  //   appids: bizTypeList.map(v => v.ID.toString()),
  // });

  const { fieldList } = useFieldList({
    eventId: BOT_ROUTING_EVENT_ID,
    appids: bizTypeList.map(v => v.ID.toString()),
    when: bizTypeListLoaded,
  });

  const { botList } = useBotList();

  const {
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    inputValue,
    setInputValue,
    filterSearch,
    loading,
    loaded,
  } = useRuleList({
    filterRuleList,
    eventKey: BOT_ROUTE,
    TabKey: defaultItemKey,
  });
  // eventId: BOT_ROUTING_EVENT_ID,
  // sourceId: BOT_ROUTING_SOURCE_ID,
  // appId: BOT_ROUTING_APP_ID,
  // StopStatus: RuleStopStatus.USING,
  return {
    loadingRuleList: loading,
    loadedRuleList: loaded,
    bizTypeList,
    skillGroups,
    // cardList,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    inputValue,
    setInputValue,
    fieldList,
    filterSearch,
    botList,
  };
};
