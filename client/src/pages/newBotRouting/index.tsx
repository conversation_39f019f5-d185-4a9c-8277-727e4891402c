import { I18n } from '@ies/starling_intl';
import React, { useContext, useState, useEffect } from 'react';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane, Spin } from '@ies/semi-ui-react';
import * as styles from './index.scss';
import BotOnlineRules from './onlineRules/index';
import BotPreReleaseRules from './preReleaseRules/index';
import { useRoutingRule } from './use';
import { UserContext } from '@context/user';
// import HistoryRule from '@/pages/historyRule';
import { useHistory } from 'react-router-dom';
export const context = React.createContext(null);
// This is route mapping
const platformMap = {
  service_routingV2: 'service_create_rule',
  bot_routingV2: 'bot_create_rule',
  offline_routingV2: 'offline_create_rule',
  quality_check_routingV2: 'qualitycheck_create_rule',
  ticket_routingV2: 'ticket_create_rule',
};

const RouterPage: React.FC = () => {
  const [defaultItemKey, changeDefaultItemKey] = useState('onlineRule');
  const {
    ruleList,
    preRuleList,
    loadingRuleList,
    // loadedRuleList,
    bizTypeList,
    skillGroups,
    getPreReleaseRuleList,
    getOnlineRuleList,
    // inputValue,
    setInputValue,
    fieldList,
    // filterSearch,
    botList,
  } = useRoutingRule(defaultItemKey);
  const [isLoading, setLoading] = useState(false);
  const [onlineRuleList, changeOnlineRuleList] = useState(ruleList);
  const [PreReleaseRulesList, changePreReleaseRulesList] = useState(preRuleList);
  const changeActiveKey = key => {
    changeDefaultItemKey(key);
  };
  const history = useHistory();
  const { jumpPublish, newRuleId } = Object.assign({ jumpPublish: false, newRuleId: '' }, history.location.state || {});
  const jumpToPpublish = () => {
    if (jumpPublish) {
      changeDefaultItemKey('readyToRelease');
    }
  };
  useEffect(() => {
    jumpToPpublish();
  }, []);
  useEffect(() => {
    changePreReleaseRulesList(preRuleList);
  }, [preRuleList]);
  useEffect(() => {
    changeOnlineRuleList(ruleList);
  }, [ruleList]);
  useEffect(() => {
    setLoading(loadingRuleList);
  }, [loadingRuleList]);
  const user = useContext(UserContext);
  // const history = useHistory();
  const location = history.location.pathname.slice(1);
  const platform = platformMap[location] || '';
  const content = (
    <Spin spinning={isLoading}>
      <div className={styles.ruleConfigContent}>
        <RouterPageHeader />
        <Tabs
          onChange={key => changeActiveKey(key)}
          defaultActiveKey={defaultItemKey}
          activeKey={defaultItemKey}
          type="line"
          keepDOM={false}
        >
          {/* <context.Provider value={{defaultItemKey, changeDefaultItemKey }}> */}
          <TabPane tab={I18n.t('online_rules', {}, '线上规则')} itemKey="onlineRule">
            <context.Provider
              value={{
                defaultItemKey,
                changeDefaultItemKey,
                onlineRuleList,
                changeOnlineRuleList,
                changePreReleaseRulesList,
                skillGroups,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                botList,
                fieldList,
              }}
            >
              <BotOnlineRules />
            </context.Provider>
          </TabPane>
          <TabPane tab={I18n.t('pre_release_version', {}, '预发布版本')} itemKey="readyToRelease">
            <context.Provider
              value={{
                newRuleId,
                defaultItemKey,
                PreReleaseRulesList,
                onlineRuleList,
                changeDefaultItemKey,
                changePreReleaseRulesList,
                changeOnlineRuleList,
                skillGroups,
                bizTypeList,
                getPreReleaseRuleList,
                getOnlineRuleList,
                setInputValue,
                botList,
                fieldList,
              }}
            >
              <BotPreReleaseRules />
            </context.Provider>
          </TabPane>
        </Tabs>
      </div>
    </Spin>
  );
  return content;
};
export default RouterPage;
