import React, { useContext } from 'react';
import I18n from '@ies/starling_intl';
import { useHistory } from 'react-router-dom';
import { createRuleProps } from '../../createRuleType';
import { tempRouterParams } from '../constants';
import { formApi as FormApi } from '@ies/semi-ui-react/form';
import { REDIRECT_URL_MAP } from '../constants/property';
import { Icon, Modal } from '@ies/semi-ui-react';
import { UserContext } from '@/context/user';

export const useHandleGoBack = ({
  ruleFormApi,
}: {
  ruleFormApi: FormApi;
}): {
    handleGoBack: () => Promise<void>;
  } => {
  const Browerhistory = useHistory();
  const plaformTag = Browerhistory?.location?.pathname?.slice?.(1);
  const routerParams: createRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});
  const typeName = I18n.t('routing', {}, '路由');
  const displayName = ruleFormApi?.getValue('DisplayName');
  const user = useContext(UserContext);

  const handleGoBack = async () => {
    let confirmed = true;
    let title: string;
    if (routerParams?.viewType === 'view') {
      const url = REDIRECT_URL_MAP[plaformTag];
      const { EventKey, ruleName, skillGroupList, statusList } = routerParams;
      Browerhistory.replace({
        pathname: url,
        state: {
          jumpPublish: true,
          eventKey: EventKey,
          ruleName,
          skillGroupList,
          statusList,
          accessPartyId: user?.accessPartyId
        },
      });
      return;
    }
    switch (routerParams?.viewType) {
      case 'create':
        const titleNameCreate =
          displayName || `${I18n.t('unnamed_{typename}_rule', { typeName }, '未命名{typeName}规则')}`;
        title = `${I18n.t('abandon_new__{titlenamecreate}_', { titleNameCreate }, '放弃新建「{titleNameCreate}」')}`;
        break;
      case 'edit':
        const titleNameEdit =
          displayName || `${I18n.t('unnamed_{typename}_rule', { typeName }, '未命名{typeName}规则')}`;
        title = `${I18n.t('abandon_editing_of__{titlenameedit}_', { titleNameEdit }, '放弃「{titleNameEdit}」的编辑')}`;
        break;
      default:
        title = I18n.t('abandon_the_current_operation?', {}, '放弃当前操作？');
        break;
    }
    confirmed = await new Promise(resolve => {
      Modal.confirm({
        icon: <Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />,
        title,
        okButtonProps: {
          type: 'danger',
        },
        cancelText: I18n.t('cancel', {}, '取消'),
        okText: I18n.t('ok', {}, '确定'),
        content: I18n.t(
          'once_abandoned__the_data_will_not_be_recovered__please_operate_with_caution',
          {},
          '一旦放弃，数据将无法恢复，请谨慎操作'
        ),
        onCancel() {
          resolve(false);
        },
        onOk() {
          resolve(true);
        },
      });
    });

    if (confirmed) {
      const url = REDIRECT_URL_MAP[plaformTag];
      const { EventKey, ruleName, skillGroupList, statusList } = routerParams;
      Browerhistory.replace({
        pathname: url,
        state: {
          jumpPublish: true,
          eventKey: EventKey,
          ruleName,
          skillGroupList,
          statusList,
          accessPartyId: user?.accessPartyId
        },
      });

      // Need to jump
    }
  };
  return {
    handleGoBack,
  };
};

export default useHandleGoBack;
