import { useContext } from 'react';
import { getServerRuleData } from '../../hooks';
import { UserContext } from '@/context/user';
import { useHistory } from 'react-router';
import { I18n } from '@ies/starling_intl';
import { Toast } from '@ies/semi-ui-react';
import { demoClient, CreateRuleV2Request, UpdateRuleRequest } from '@http_idl/demo';
import { useActionInfo } from './useActionInfo';
import { SubmitProps, CreateRuleProps, tempRouterParams } from '../constants';
import { routeType } from '@/const/enums';

import { ErrorCodeNoPermission } from '@/const';
import { REDIRECT_URL_MAP } from '../constants/property';
export const useSubmit = ({
  ruleLists,
  fieldList,
  slaMetaSimple,
}: SubmitProps): {
    handleSubmit: (val: any) => void;
  } => {
  const user = useContext(UserContext);
  const { getActionInfo } = useActionInfo();
  const Browerhistory = useHistory();
  const locationType = Browerhistory?.location?.pathname?.slice?.(1);
  const plaformTag = locationType;
  const routerParams: CreateRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});

  const createEndRule = async (values, addAccessPartyIdRule, ruleGroupId, routeStateId) => {
    const sendRule = {
      OpGroup: 'AND',
      ConditionGroups: [],
    };
    const req = {
      Version: 'v1',
      RuleEnv: values.RuleEnv,
      Enable: true,
      EventKey: routerParams.EventKey,
      DisplayName: I18n.t('bottom_rules ', {}, ' rule'),
      Description: '',
      AccessPartyId: user?.accessPartyId,
      Priority: 9999,
      RuleGroupId: ruleGroupId.toString(),
      Expression: sendRule,
      ActionInfo: getActionInfo({
        values,
        isLast: true,
        firstCreateEndRule: true,
        slaMetaSimple,
      }),
    };
    const res = await demoClient.createRule(req);
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    Toast.success(
      I18n.t(
        'create_successfully__return_to_the_rules_page_after_2s ',
        {},
        ' created successfully, return to the rules page after 2s'
      )
    );
    const url = REDIRECT_URL_MAP[plaformTag];
    const { EventKey, ruleName, skillGroupList, statusList } = routerParams;
    Browerhistory.replace({
      pathname: url,
      state: {
        jumpPublish: true,
        newRuleId: routeStateId,
        eventKey: EventKey,
        ruleName,
        skillGroupList,
        statusList,
        accessPartyId: user?.accessPartyId
      },
    });
  };
  const updateRule = async (values, ruleInfo, isLast, skillArr) => {
    const updateReq: UpdateRuleRequest = {
      Id: routerParams.ruleId,
      Version: 'v1',
      DisplayName: values.DisplayName,
      Expression: ruleInfo,
      ActionInfo: getActionInfo({
        values,
        isLast,
        firstCreateEndRule: false,
        slaMetaSimple,
      }),
      PermCode: '',
      ExtraInfo: (plaformTag === routeType.ticket_routing || plaformTag === routeType.service_routing) ? {
        skillGroupList: JSON.stringify(skillArr),
      } : {}

    };

    const res = await demoClient.updateRuleById(updateReq);
    if (res.code !== 0) {
      if (res.code === ErrorCodeNoPermission) {
        Toast.error(I18n.t('no_editing_permission ', {}, ' no edit permission'));
      } else {
        Toast.error(res.message);
      }
      return;
    }
    Toast.success(
      I18n.t(
        'update_successful__return_to_the_rules_page_after_2s ',
        {},
        ' update successfully, return to the rules page after 2s'
      )
    );
    const url = REDIRECT_URL_MAP[plaformTag];
    const { EventKey, ruleName, skillGroupList, statusList } = routerParams;

    Browerhistory.replace({
      pathname: url,
      state: {
        newRuleId: res.Rule.Id,
        jumpPublish: true,
        eventKey: EventKey,
        ruleName,
        skillGroupList,
        statusList,
        accessPartyId: user?.accessPartyId
      },
    });
  };
  const handleSubmit = async values => {
    const addAccessPartyIdRule = {
      OpGroup: 'AND',
      Conditions: [
        {
          Lhs: {
            VarExpr: '$access_party_id',
          },
          OpCheck: '==',
          Rhs: {
            Constant: user?.accessPartyId,
          },
        },
      ],
    };
    const rulesInfo = getServerRuleData(ruleLists, fieldList);
    let sendRule: any = rulesInfo;
    if (plaformTag === routeType.ticket_routing) {
      const addRouteType = {
        OpGroup: 'AND',
        ConditionGroups: [
          {
            OpGroup: 'AND',
            Conditions: values.RouteType ?
              [
                {
                  Lhs: {
                    VarExpr: '$upgrade_time',
                  },
                  OpCheck: 'LIST_IN',
                  Rhs: {
                    ConstantList: values.RouteType,
                  },
                },
              ] :
              [],
          },
        ],
      };
      addRouteType.ConditionGroups.push(rulesInfo);
      sendRule = addRouteType;
    }

    let skillArr = [];
    if (values.isShunt && !values.isAutoShunt) {
      skillArr = values?.skillList?.map(val => val.value);
    } else if (values.isShunt && values.isAutoShunt) {
      skillArr = values.autoShuntSkillList;
    } else {
      skillArr = [values.SkillGroupId];
    }

    if (values.SupportOverflow) {
      values?.skillGroupOverflowList?.forEach(val => {
        if (val?.id) {
          skillArr = [...skillArr, ...val.id];
        }
      });
    }

    const saveData: CreateRuleV2Request = {
      Version: 'v1',
      RuleEnv: values.RuleEnv,
      Enable: values.Enable === 1,
      EventKey: routerParams.EventKey,
      DisplayName: values.DisplayName,
      Description: '',
      AccessPartyId: user?.accessPartyId,
      Priority: routerParams.Priority || 0,
      RuleGroupId: routerParams.RuleGroupId === -1 ? undefined : routerParams.RuleGroupId.toString(),
      Expression: sendRule,
      ActionInfo: getActionInfo({
        values,
        isLast: routerParams?.isLast,
        firstCreateEndRule: false,
        slaMetaSimple,
      }),
      ExtraInfo: (plaformTag === routeType.ticket_routing || plaformTag === routeType.service_routing) ? {
        skillGroupList: JSON.stringify(skillArr),
      } : {}
    };
    if (routerParams.viewType === 'edit') {
      updateRule(values, sendRule, routerParams.isLast, skillArr);
      return;
    }
    const res = await demoClient.createRule(saveData);
    if (res.code !== 0) {
      Toast.error(res.message);
      return;
    }
    if (routerParams.ruleLength === 0) {
      createEndRule(values, addAccessPartyIdRule, res.Rule.RuleGroupId, res.Rule.Id);
      return;
    }
    Toast.success(
      I18n.t(
        'create_successfully__return_to_the_rules_page_after_2s ',
        {},
        ' created successfully, return to the rules page after 2s'
      )
    );
    const url = REDIRECT_URL_MAP[plaformTag];
    const { EventKey, ruleName, skillGroupList, statusList } = routerParams;

    Browerhistory.replace({
      pathname: url,
      state: {
        jumpPublish: true,
        newRuleId: res.Rule.Id,
        eventKey: EventKey,
        ruleName,
        skillGroupList,
        statusList,
        accessPartyId: user?.accessPartyId
      },
    });
  };
  return {
    handleSubmit,
  };
};

export default useSubmit;
