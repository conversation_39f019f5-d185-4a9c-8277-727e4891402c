import React from 'react';
import { I18n } from '@ies/starling_intl';
import { Typography, Modal, Toast } from '@ies/semi-ui-react';
import { OFFLINE_ROUTE } from '@/common/constants/property';
import { CreateRuleProps, tempRouterParams } from '../constants';
import { useHistory } from 'react-router';
import useSubmit from './useSubmit';
import { demoClient } from '@http_idl/demo';
export const useCheckSkillgroupAutoAssign = ({
  ruleLists,
  fieldList,
  slaMetaSimple,
  skillGroupAll,
}: {
  ruleLists: any;
  fieldList: any;
  slaMetaSimple: any;
  skillGroupAll: any;
}): { checkSkillgroupAutoAssign: (val: any) => void } => {
  const Browerhistory = useHistory();
  const { handleSubmit } = useSubmit({ ruleLists, fieldList, slaMetaSimple });
  const routerParams: CreateRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});
  const checkSkillgroupAutoAssign = async (values: any) => {
    const { Text } = Typography;
    const isTTP = [];
    let skillArr = [];
    if (values.isShunt && !values.isAutoShunt) {
      skillArr = values?.skillList?.map(val => val.value);
    } else if (values.isShunt && values.isAutoShunt) {
      skillArr = values.autoShuntSkillList;
    } else {
      skillArr = [values.SkillGroupId];
    }
    if (values.SupportOverflow) {
      values?.skillGroupOverflowList?.forEach(val => {
        skillArr = [...skillArr, ...val?.id];
      });
    }
    Object.keys(values).map(key => {
      if (values[key] === 'source_idc.is_ttp') {
        const [parentIndex, myIndex] = key.split('_').slice(2);
        isTTP.push(values[`item_rhs_${parentIndex}_${myIndex}`]);
      }
    });
    const isRowSkill = skillGroupAll?.filter(item => skillArr?.includes(item?.value) && item?.IdcType === 1);
    const isTTPSkill = skillGroupAll?.filter(item => skillArr?.includes(item?.value) && item?.IdcType === 2);
    const res = await demoClient.checkSkillGroupAutoAssign({
      skillGroupIdList: [...new Set(skillArr)].filter(item => Boolean(item)),
    });
    const linkUrl = `${location.origin}/united/staff_management/groups/${
      routerParams.EventKey === OFFLINE_ROUTE ? 'feedback' : 'ticket'
    }`;
    const { checkSkillGroupAutoAssign } = res;
    const autoAssignList = [];
    const unautoAssignAgents = [];
    const skillGroupAgentsCount = [];
    if (res.code !== 0 || !checkSkillGroupAutoAssign) {
      Toast.error(`${I18n.t('routing_rule_validation_failed', {}, '路由规则校验失败')}`);
      return;
    }
    res.checkSkillGroupAutoAssign.map(item => {
      const skillGroupName = skillGroupAll.find(skill => skill.ID === item.skillGroupId)?.Name;
      if (!item.autoAssign) {
        autoAssignList.push({ ...item, skillGroupName });
      }
      if (!Number(item.skillGroupAgentsCount)) {
        skillGroupAgentsCount.push({ ...item, skillGroupName });
      }
      if (item.unautoAssignAgents.length) {
        const agentNameList = item.unautoAssignAgents.map(val => val.UserName);
        unautoAssignAgents.push({ ...item, skillGroupName, agentNameList });
      }
    });
    if (autoAssignList.length) {
      // Non-automatic grouping
      const modal = Modal.error();
      modal.update({
        title: `${I18n.t('skill_sets_are_not_set_up_for_automatic_order_splitting', {}, '技能组未设置自动分单！')}`,
        width: 500,
        content: (
          <>
            <span>{I18n.t('route_Starling_4', {}, '当前设置的技能组')}</span>
            {autoAssignList?.map(item => (
              <Text
                style={{ margin: '0 8px' }}
                key={item.skillGroupId}
                onClick={() => modal.destroy()}
                link={{ href: `${linkUrl}?skillGroupId=${item.skillGroupId}`, target: '_blank' }}
              >
                {item.skillGroupName}
              </Text>
            ))}
            <span>{I18n.t('route_Starling_7', {}, '未设置自动分单，请点击技能组名称跳转去设置')}</span>
          </>
        ),
        okText: `${I18n.t('go_to_settings', {}, '去设置')}`,
        cancelText: `${I18n.t('cancel', {}, '取消')}`,
        onOk: () =>
          window.open(
            autoAssignList.length > 1 ? linkUrl : `${linkUrl}?skillGroupId=${autoAssignList[0].skillGroupId}`
          ),
      });
      return;
    }
    if (isTTP.includes('1') && isRowSkill.length) {
      const modal = Modal.error();
      modal.update({
        title: `${I18n.t('route_Starling_33', {}, '规则技能组确认')}`,
        width: 588,
        content: (
          <>
            <span>{I18n.t('route_Starling_34', {}, '当前是ttp路由规则，检测到技能组')}</span>
            {isRowSkill?.map(item => (
              <Text style={{ margin: '0 8px' }} key={item.value} link={true}>
                {item.label}
              </Text>
            ))}
            <span>{I18n.t('route_Starling_35', {}, '并非对应区域技能组，是否需要修改规则')}</span>
          </>
        ),
        okText: `${I18n.t('route_Starling_36', {}, '修改')}`,
        cancelText: `${I18n.t('still_save', {}, '仍保存')}`,
        onOk: () => modal.destroy(),
        cancelButtonProps: {
          onClick: () => {
            modal.destroy();
            handleSubmit(values);
          },
        },
      });
      return;
    }
    if (isTTP.includes('0') && isTTPSkill.length) {
      const modal = Modal.error();
      modal.update({
        title: `${I18n.t('route_Starling_33', {}, '规则技能组确认')}`,
        width: 588,
        content: (
          <>
            <span>{I18n.t('route_Starling_37', {}, '当前是row路由规则，检测到技能组')}</span>
            {isTTPSkill?.map(item => (
              <Text style={{ margin: '0 8px' }} key={item.value} link={true}>
                {item.label}
              </Text>
            ))}
            <span>{I18n.t('route_Starling_35', {}, '并非对应区域技能组，是否需要修改规则')}</span>
          </>
        ),
        okText: `${I18n.t('route_Starling_36', {}, '修改')}`,
        cancelText: `${I18n.t('still_save', {}, '仍保存')}`,
        onOk: () => modal.destroy(),
        cancelButtonProps: {
          onClick: () => {
            modal.destroy();
            handleSubmit(values);
          },
        },
      });
      return;
    }
    if (unautoAssignAgents.length) {
      const modal = Modal.error();
      modal.update({
        title: `${I18n.t('skills_group_personnel_take_orders_parameter_is_0_hint', {}, '技能组人员接单参数为0提示')}`,
        width: 588,
        content: (
          <>
            <span>{I18n.t('route_Starling_4', {}, '当前设置的技能组')}</span>
            {unautoAssignAgents?.map(item => (
              <span key={item.skillGroupId}>
                <Text
                  onClick={() => modal.destroy()}
                  link={{ href: `${linkUrl}/member/${item.skillGroupId}`, target: '_blank' }}
                >
                  {`${item.skillGroupName}:`}
                </Text>
                <span style={{ margin: '0 8px' }}>{item.agentNameList.join('、')}</span>
              </span>
            ))}
            <span>{I18n.t('route_Starling_6', {}, '人员接单参数为0，请点击技能组名称跳转设置人员接单数')}</span>
          </>
        ),
        okText: `${I18n.t('go_to_settings', {}, '去设置')}`,
        cancelText: `${I18n.t('still_save', {}, '仍保存')}`,
        onOk: () =>
          window.open(
            unautoAssignAgents.length > 1 ? linkUrl : `${linkUrl}/member/${unautoAssignAgents[0].skillGroupId}`
          ),
        cancelButtonProps: {
          onClick: () => {
            modal.destroy();
            handleSubmit(values);
          },
        },
      });
      return;
    }
    if (skillGroupAgentsCount.length) {
      const modal = Modal.error();
      modal.update({
        title: `${I18n.t('no_seats_in_the_skill_group', {}, '技能组内无坐席')}`,
        width: 588,
        content: (
          <>
            <span>{I18n.t('route_Starling_4', {}, '当前设置的技能组')}</span>
            {skillGroupAgentsCount?.map(item => (
              <Text
                key={item.skillGroupId}
                onClick={() => modal.destroy()}
                link={{ href: `${linkUrl}/member/${item.skillGroupId}`, target: '_blank' }}
              >
                {item.skillGroupName}
              </Text>
            ))}
            <span>{I18n.t('route_Starling_5', {}, '无坐席，点击技能组名称跳转设置')}</span>
          </>
        ),
        okText: `${I18n.t('to_increase', {}, '去增加')}`,
        cancelText: `${I18n.t('still_save', {}, '仍保存')}`,
        onOk: () =>
          window.open(
            skillGroupAgentsCount.length > 1 ? linkUrl : `${linkUrl}/member/${skillGroupAgentsCount[0].skillGroupId}`
          ),
        cancelButtonProps: {
          onClick: () => {
            modal.destroy();
            handleSubmit(values);
          },
        },
      });
      return;
    }
    handleSubmit(values);
  };
  return {
    checkSkillgroupAutoAssign,
  };
};

export default useCheckSkillgroupAutoAssign;
