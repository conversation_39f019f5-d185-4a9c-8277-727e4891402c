import { ActionInfoProps, ReclaimExtra } from '../constants';
import { LOCATION_MAP } from '../constants/property';
import { errorReporting } from '@/common/utils/errorReporting';
import { safeJSONParse } from '@/common/utils';
import { useHistory } from 'react-router';
import { routeType } from '@/const/enums';
import { AimExpr } from '@http_idl/demo';

export const useActionInfo = (): { getActionInfo: (val: ActionInfoProps) => AimExpr } => {
  const Browerhistory = useHistory();
  const locationType = Browerhistory?.location?.pathname?.slice?.(1);
  const getSkillGroupOverflowList = list => {
    if (!list || !list?.length) {
      return [];
    }
    const arr = [];
    list?.forEach(val => {
      (val?.id || [])?.map(item => {
        arr?.push({ id: item, accessPartyId: val?.accessPartyId });
      });
    });
    return arr;
  };

  const getActionInfo = ({ values, isLast, firstCreateEndRule, slaMetaSimple }: ActionInfoProps): any => {
    const getSelectedSlaMetaSimple = (id?: string) => slaMetaSimple.find(item => item.Id === id);
    function getSlaMetaSimpleExtra(id?: string): ReclaimExtra {
      const item = getSelectedSlaMetaSimple(id);
      try {
        return safeJSONParse(item?.Extra || '');
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'getSlaMetaSimpleExtra' });
        return {
          reclaim_type: 'empty',
          reclaim_default_value: 0,
        };
      }
    }
    const showAimByLocation = LOCATION_MAP?.[locationType];
    if (!Object.keys(values || {})?.length) {
      return;
    }
    let action: any = '';
    if (isLast) {
      if (showAimByLocation === 'bot_routing') {
        action = {
          is_open: 1,
          bot_id: {
            id: values.BotId,
          },
          needRiskIdentification: values.needRiskIdentification,
        };
      } else if (showAimByLocation === 'service_routing') {
        action = {
          is_open: 1,
          bot_id: {
            id: values.BotId,
          },
          skill_group: {
            id: firstCreateEndRule ? '' : values.SkillGroupId,
          },
          needRiskIdentification: values.needRiskIdentification,
        };
      } else {
        action = {
          skill_group: {
            // Skills group
            id: firstCreateEndRule ? '' : values.SkillGroupId,
          },
          is_open: 1,
        };
      }
    } else {
      const reclaimExtra = getSlaMetaSimpleExtra(values?.ReclaimCondition);
      let minutes: number;
      switch (reclaimExtra?.reclaim_type) {
        case 'time':
          minutes = (values.ReclaimHours || 0) * 60 + (values.ReclaimMinutes || 0);
          break;
        case 'empty':
        default:
          minutes = reclaimExtra?.reclaim_default_value;
          break;
      }

      const reclaimGroupIds = values.SkillGroupReclaim ? [values.SkillGroupReclaim] : [];
      const targetSlaMeta = slaMetaSimple.find(item => item.Id === values.ReclaimCondition);
      action = {
        is_open: values.IsOpen !== undefined ? values.IsOpen : 1,
        bot_id: {
          id: values.BotId,
        },
        needRiskIdentification: values.needRiskIdentification,
        skill_group: {
          id: values.SkillGroupId,
        },
        isShunt: values.isShunt,
        isAutoShunt: values.isAutoShunt,
        autoShuntSkillList: values.autoShuntSkillList || [],
        support_overflow: values.SupportOverflow,
        overflow_threshold: values.queueOverflowCount || 1,
        skill_group_overflow: getSkillGroupOverflowList(values?.skillGroupOverflowList),
        skillGroupOverflowList: values?.skillGroupOverflowList,
        skill_group_reclaim: (reclaimGroupIds || [])?.map(id => ({ id })),
        support_reclaim: values.SupportReclaim, // Whether to salvage
        reclaim_event_id: 26,
        route_type: values.RouteType,
        skillList: values.skillList,
        reclaim_config_name: targetSlaMeta?.Name || '',
        reclaim_config: [
          {
            // Recycling configuration
            aimMetaId: values.ReclaimCondition, // Fixed value used to identify work orders that have not yet been assigned
            reclaimType: reclaimExtra?.reclaim_type,
            aimTime: 0, // Fixed value
            aimTimeUnit: 1, // Time type 1 minute
            aimSteps: [
              {
                actionTimeType: 0, // Fixed value
                actionTime: minutes, // 480 minutes
                actionTimeUnit: 1, // Time type 1 minute
                actions: [
                  {
                    actionMetaId: 16, // Action ID, fixed
                    actualParams: {
                      target: reclaimGroupIds.map(id => `##${id}##`), // Target skill group
                      title: '',
                    },
                  },
                ],
              },
            ],
          },
        ],
      };
    }
    // 只有工单路由在溢出和手动分流的时候会给后端传diversionCal函数
    // todo 后期跟乃斌商量下是否直接使用ReturnValue 值返回个json 给后端传递
    if (showAimByLocation === 'ticket_routing') {
      if (values.SupportOverflow) {
        const ConstantList = ['"route_type"'];
        const ExprList = [
          {
            VarExpr: '$upgrade_time',
          },
        ] as any;
        // eslint-disable-next-line max-depth
        if (values?.isShunt && !values?.isAutoShunt) {
          ConstantList.push('"skill_group.id"');
          ExprList.push({
            FuncExpr: {
              FuncName: 'diversionCal',
              ParamExprMap: {
                calValue: { VarExpr: '$Id' },
                percentList: { Constant: `\"${JSON.stringify(values.skillList)}\"` },
              },
            },
          });
        }
        return {
          ReturnValue: {
            FuncExpr: {
              FuncName: 'padding',
              ParamExprMap: {
                paddingNames: { ConstantList },
                paddingValues: {
                  ExprList,
                },
                originData: {
                  Constant: `\"${JSON.stringify(action)}\"`,
                },
              },
            },
          },
        };
      }
    }
    if (values.isShunt && !values?.isAutoShunt) {
      return {
        ReturnValue: {
          FuncExpr: {
            FuncName: 'diversion',
            ParamExprMap: {
              calValue: {
                VarExpr: locationType === routeType.service_routing ? '$task_id' : '$Id',
              },
              percentList: {
                Constant: `\"${JSON.stringify(values.skillList)}\"`,
              },
              valueName: {
                Constant: '"skill_group.id"',
              },
              originData: {
                Constant: `\"${JSON.stringify(action)}\"`,
              },
            },
          },
        },
      };
    } else {
      return {
        ReturnValue: {
          Constant: `\"${JSON.stringify(action)}\"`,
        },
      };
    }
  };

  return {
    getActionInfo,
  };
};
