import { SLAAimMetaSimple, SkillGroup } from '@http_idl/demo';

export const tempRouterParams: CreateRuleProps = {
  Priority: 0,
  RuleGroupId: -1,
  ruleId: '',
  plaformTag: '',
  Enable: false,
  viewType: 'create',
  isLast: false,
  EventKey: '',
  ruleLength: 0,
};

export interface ShuntRuleConfigProps {
  sKipGroupLists: SelectOption[];
  viewType?: string;
}

export interface SelectOption {
  label: string;
  value: string;
  otherKey: string;
  [propName: string]: any;
}
export interface TicketRuleConfigProps {
  sKipGroupLists: SelectOption[];
  overFlowLists: SelectOption[];
  slaMetaSimple: SelectOption[];
  showSupportReclaim: number;
  getSlaMetaSimpleExtra: (id?: string) => ReclaimExtra;
  isLast: boolean;
  showOverflow: number;
  viewType?: string;
  skillGroupAll?: Array<SkillGroup>;
  accessPartyList?: AccessParty[];
}

export interface ReclaimExtra {
  reclaim_type: 'empty' | 'time';
  reclaim_default_value: number;
}
export interface IMUserRouterConfigProps {
  sKipGroupLists: SelectOption[];
  overFlowLists: SelectOption[];
  showOverflow: number;
  isLast: boolean;
  viewType?: string;
  skillGroupAll?: Array<SkillGroup>;
  accessPartyList?: AccessParty[];
}
export interface AccessParty {
  label: string;
  value: string;
}
export interface IMAIRouterConfigProps {
  robotLists: SelectOption[];
  showSelectBot: number;
  showNeedRisk: number;
  isLast: boolean;
  viewType?: string;
}

export type CreateRuleHeaderProps = {
  name: string;
  platform: string;
  handleGoBack: () => void;
};
export type CreateRuleProps = {
  Priority: number; // Rule priority
  RuleGroupId?: number; // Rule group ID
  ruleId?: string; // Rule ID,
  plaformTag: string; // Platform identity
  Enable: boolean;
  viewType: string; // Edit or New
  isLast: boolean;
  EventKey: string;
  ruleLength: number;
  ruleName?: string;
  statusList: number[];
  skillGroupList?: number[];
};

export interface ActionInfoProps {
  values: any;
  isLast: boolean;
  firstCreateEndRule: boolean;
  slaMetaSimple: SLAAimMetaSimple[];
}

export interface SubmitProps {
  ruleLists: any;
  fieldList: any;
  slaMetaSimple: SLAAimMetaSimple[];
}
