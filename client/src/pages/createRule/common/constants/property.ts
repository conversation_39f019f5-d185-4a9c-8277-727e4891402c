import { I18n } from '@ies/starling_intl';
import { routeType } from '@/const/enums';

export const TIPS_NAME_MAP = {
  [routeType.service_routing]: {
    title: (): string => I18n.t('return_to_the_list_of_im_manual_routing_rules', {}, '返回IM人工路由规则列表'),
  },
  [routeType.ticket_routing]: {
    title: (): string => I18n.t('return_to_the_list_of_work_order_routing_rules', {}, '返回工单路由规则列表'),
  },
  [routeType.bot_routing]: {
    title: (): string => I18n.t('return_to_the_list_of_im_intelligent_routing_rules', {}, '返回IM智能路由规则列表'),
  },
};
export const TRIGER_DESC_MAP = {
  [routeType.service_routing]: {
    title: (): string =>
      I18n.t('when_no_im_human_customer_service_matching_rules_are_obtained', {}, '当未获取任何 IM 人工客服匹配规则时'),
  },
  [routeType.ticket_routing]: {
    title: (): string => I18n.t('when_no_work_order_matching_rules_are_obtained', {}, '当未获取任何工单匹配规则时'),
  },
  [routeType.bot_routing]: {
    title: (): string =>
      I18n.t('when_no_im_smart_customer_service_matching_rules_are_obtained', {}, '当未获取任何 IM 智能客服匹配规则时'),
  },
};

export const LOCATION_MAP = {
  service_create_rule: 'service_routing',
  bot_create_rule: 'bot_routing',
  ticket_create_rule: 'ticket_routing',
};

export const REDIRECT_URL_MAP = {
  [routeType.bot_routing]: '/bot_routing_v2',
  [routeType.service_routing]: '/service_routing_v2',
  [routeType.ticket_routing]: '/ticket_routing_v2',
};

export const defaultAccessPartyList = [
  {
    value: '2',
    label: 'TikTok Shop Seller Service',
  },
  {
    value: '3',
    label: 'TikTok Shop Buyer Service',
  },
  {
    value: '9',
    label: 'TikTok Shop Creator Service',
  },
  {
    value: '35',
    label: 'Project S',
  },
  {
    value: '46',
    label: 'Seller Service Hosting',
  },
];
