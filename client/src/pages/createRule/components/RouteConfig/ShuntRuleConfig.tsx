import { I18n } from '@ies/starling_intl';
import React from 'react';
import { Form, ArrayField, Button, useFormApi, Tooltip, Icon } from '@ies/semi-ui-react';
import { getRequiredRule } from '@/common/rules';
import { ShuntRuleConfigProps } from '../../common/constants';
import styles from './index.scss';
const ShuntRuleConfig: React.FC<ShuntRuleConfigProps> = ({ sKipGroupLists, viewType }) => {
  const ruleFormApi = useFormApi();
  const isShunt = ruleFormApi?.getValue('isShunt');
  const isAutoShunt = ruleFormApi?.getValue('isAutoShunt');
  const content2 = (
    <Form.Select
      noLabel={true}
      className={styles.createRuleFormMinwidth}
      optionList={sKipGroupLists}
      style={{ display: isShunt ? 'none' : '' }}
      filter={true}
      field="SkillGroupId"
      rules={
        isShunt ?
          [{ required: false }] :
          [getRequiredRule(I18n.t('please_select_assign_skill_group', {}, '请选择分配技能组'))]
      }
    />
  );
  const content1 = (
    <div style={{ display: isShunt && !isAutoShunt ? '' : 'none' }}>
      <ArrayField field="skillList">
        {({ add, arrayFields, addWithInitValue }) => (
          <React.Fragment>
            {arrayFields.map(({ field, key, remove }, i) => (
              <div key={key} style={{ width: '100%', display: 'flex' }}>
                <Form.InputNumber
                  field={`${field}[percent]`}
                  noLabel={true}
                  min={0}
                  max={100}
                  suffix={'%'}
                  style={{ width: 100 }}
                  formatter={value => `${value}`.replace(/\D/g, '')}
                />
                <div
                  style={{
                    lineHeight: '56px',
                    fontSize: '14px',
                    color: 'var(--color-text-0)',
                    height: '20px',
                    margin: '0 8px',
                  }}
                >
                  {I18n.t('route_Starling_26', {}, '进线流量，分流至')}
                </div>
                <div style={{ flexGrow: 1 }}>
                  <Form.Select
                    field={`${field}[value]`}
                    noLabel={true}
                    optionList={sKipGroupLists}
                    style={{ width: '100%' }}
                    filter={true}
                    rules={
                      isShunt && !isAutoShunt ?
                        [getRequiredRule(I18n.t('please_select_assign_skill_group', {}, '请选择分配技能组'))] :
                        [{ required: false }]
                    }
                  />
                </div>
                <Button
                  disabled={arrayFields.length < 3 || viewType === 'view'}
                  theme="borderless"
                  type="tertiary"
                  icon="minus_circle"
                  onClick={remove}
                  style={{ margin: 12 }}
                />
              </div>
            ))}
            <Button disabled={viewType === 'view'} onClick={add} icon="plus_circle" theme="borderless">
              {I18n.t('route_Starling_14', {}, '新增分配技能组')}
            </Button>
          </React.Fragment>
        )}
      </ArrayField>
    </div>
  );
  return (
    <>
      <Form.RadioGroup
        field="isShunt"
        label={I18n.t('route_ Starling_12', {}, '是否启用分流')}
        onChange={e => {
          if (e?.target?.value) {
            ruleFormApi?.setValue('isAutoShunt', 0);
          }
        }}
      >
        <Form.Radio value={1}>{I18n.t('yes', {}, '是')}</Form.Radio>
        <Form.Radio value={0}>{I18n.t('no', {}, '否')}</Form.Radio>
      </Form.RadioGroup>
      {Boolean(isShunt) && (
        <Form.RadioGroup field="isAutoShunt" label={I18n.t('shunt_mode', {}, '分流方式')}>
          <Form.Radio value={0}>{I18n.t('manual', {}, '手动')}</Form.Radio>
          <Form.Radio value={1}>
            {I18n.t('automatic', {}, '自动')}
            <Tooltip
              position="topLeft"
              content={(
                <span>
                  {I18n.t(
                    'automatic_triage_supports_triage_based_on_the_idle_level_and_overload_of_the_ski',
                    {},
                    '自动分流支持根据技能组的空闲程度和过载程度进行分流。'
                  )}
                </span>
              )}
            >
              <Icon className={styles.radioIcon} type="help_circle" />
            </Tooltip>
          </Form.Radio>
        </Form.RadioGroup>
      )}
      <label className={styles.labelText}>
        <div>{I18n.t('assign_skill_groups', {}, '分配技能组')}</div>
      </label>
      {Boolean(isAutoShunt) && (
        <Form.Select
          className={styles.createRuleFormMinwidth}
          noLabel
          optionList={sKipGroupLists}
          filter
          multiple
          field="autoShuntSkillList"
          rules={
            isShunt && isAutoShunt ?
              [
                  { required: true, message: I18n.t('this_field_is_required', {}, '该字段为必填项') },
                  {
                    validator: (rule, value) => {
                      if (value.length <= 1) {
                        return false;
                      } else {
                        return true;
                      }
                    },
                    message: I18n.t('select_at_least_two_skill_sets', {}, '最少选择两个技能组'),
                  },
                ] :
              [{ required: false }]
          }
        />
      )}
      {content1}
      {content2}
    </>
  );
};
export default ShuntRuleConfig;
