import React from 'react';
import { I18n } from '@ies/starling_intl';
import styles from './index.scss';
import { Tooltip, Icon } from '@ies/semi-ui-react';
const RiskIdentificationLabel: React.FC = () => (
  <div style={{ display: 'flex', alignItems: 'center' }}>
    <span>{I18n.t('whether_risk_identification_is_required', {}, '是否需要风险识别')}</span>
    <Tooltip
      position="right"
      content={I18n.t(
        'after_selecting_the_need__if_the_information_hits_the_major_customer_complaint_m_8ab1920e9dda50e0acd8f5726e2f80ed',
        {},
        '选择需要后，若信息命中重大客诉模型，则直接流转重大客服队列，但需要在“人工路由规则”里设置。'
      )}
    >
      <Icon className={styles.radioIcon} type="help_circle" style={{ marginLeft: 8 }} />
    </Tooltip>
  </div>
);

export default RiskIdentificationLabel;
