import React from 'react';
import { I18n } from '@ies/starling_intl';
import { IMAIRouterConfigProps } from '../../common/constants';
import RiskIdentificationLabel from './RiskIdentificationLabel';
import { getRequiredRule } from '@/common/rules';
import { Form } from '@ies/semi-ui-react';

const IMAIRouterConfig: React.FC<IMAIRouterConfigProps> = ({ showSelectBot, showNeedRisk, robotLists, isLast }) => {
  const content = (
    <>
      {!isLast ? (
        <Form.RadioGroup
          field="IsOpen"
          label={I18n.t('whether_to_access_the_intelligent_customer_service_robot', {}, '是否接入智能客服机器人')}
          extraText={(
            <span
              style={{
                fontSize: 14,
                color: 'var(--color-text-2)',
              }}
            >
              {I18n.t(
                'if_you_do_not_access__then_skip_the_intelligent_customer_service_under_the_trigg_e809e0b670dd0baa5cadbca20ecdfb19',
                {},
                '若不接入，那么在该触发条件下跳过智能客服，直接进入人工客服'
              )}
            </span>
          )}
          rules={[
            getRequiredRule(
              I18n.t(
                'please_select_whether_to_access_the_intelligent_customer_service_robot',
                {},
                '请选择是否接入智能客服机器人'
              )
            ),
          ]}
        >
          <Form.Radio value={1}>{I18n.t('yes', {}, '是')}</Form.Radio>
          <Form.Radio value={0}>{I18n.t('no', {}, '否')}</Form.Radio>
        </Form.RadioGroup>
      ) : null}
      <span />
      {showSelectBot ? (
        <Form.Select
          style={{ width: 450 }}
          field="BotId"
          optionList={robotLists}
          label={I18n.t('flow_to_robot', {}, '流转至机器人')}
          placeholder={I18n.t('select_flow_robot', {}, '选择流转机器人')}
          rules={[getRequiredRule(I18n.t('please_select_the_flow_robot', {}, '请选择流转机器人'))]}
        />
      ) : null}
      {showNeedRisk ? (
        <Form.RadioGroup
          field="needRiskIdentification"
          label={<RiskIdentificationLabel />}
          rules={[
            getRequiredRule(
              I18n.t('please_select_whether_risk_identification_is_required', {}, '请选择是否需要风险识别')
            ),
          ]}
        >
          <Form.Radio value={1}>{I18n.t('yes', {}, '是')}</Form.Radio>
          <Form.Radio value={0}>{I18n.t('no', {}, '否')}</Form.Radio>
        </Form.RadioGroup>
      ) : null}
    </>
  );
  return content;
};

export default IMAIRouterConfig;
