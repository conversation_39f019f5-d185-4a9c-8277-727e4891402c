import React from 'react';
import TicketRuleConfig from './TicketRuleConfig';
import IMUserRouterConfig from './IMUserRouterConfig';
import IMAIRouterConfig from './IMAIRouterConfig';
import { routeType } from '@/const/enums';
const RouteConfig = props => {
  const { type } = props;
  const ComponentMap = {
    [routeType.ticket_routing]: <TicketRuleConfig {...props} />,
    [routeType.service_routing]: <IMUserRouterConfig {...props} />,
    [routeType.bot_routing]: <IMAIRouterConfig {...props} />,
  };
  return ComponentMap[type];
};

export default RouteConfig;
