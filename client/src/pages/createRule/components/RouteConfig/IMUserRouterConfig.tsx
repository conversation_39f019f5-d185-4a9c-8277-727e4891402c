import React from 'react';
import { I18n } from '@ies/starling_intl';
import { IMUserRouterConfigProps } from '../../common/constants';
import ShuntRuleConfig from './ShuntRuleConfig';
import TipedLabel from '../OperationLog/TipedLabel';
import { getRequiredRule, countIntegerNumberRule } from '@/common/rules';
import { Form, ArrayField, Button, useFormApi } from '@ies/semi-ui-react';
const IMUserRouterConfig: React.FC<IMUserRouterConfigProps> = ({
  sKipGroupLists,
  overFlowLists,
  showOverflow,
  isLast,
  viewType,
  skillGroupAll,
  accessPartyList,
}) => {
  const ruleFormApi = useFormApi();
  const skillGroupOverflowList = ruleFormApi?.getValue('skillGroupOverflowList');
  const getAccessSkilllist = React.useCallback(
    (accessPartyId, alloptions) => {
      // console.log(accessPartyId, alloptions, 'jin1234');
      if (!accessPartyId || !alloptions) {
        return [];
      }
      const options = [];
      alloptions?.forEach(item => {
        if (item.AccessPartyId.includes(accessPartyId)) {
          options.push({
            value: item.ID,
            label: item.Name,
          });
        }
      });
      return options;
    },
    [JSON.stringify(skillGroupOverflowList)]
  );

  return (
    <>
      <ShuntRuleConfig sKipGroupLists={sKipGroupLists} viewType={viewType} />
      {!isLast ? (
        <>
          <Form.RadioGroup
            label={(
              <TipedLabel
                label={I18n.t('whether_overflow_is_supported', {}, '是否支持溢出')}
                question={I18n.t('what_is_overflow?', {}, '什么是溢出？')}
                answer={I18n.t(
                  'route_Starling_50',
                  {},
                  '当设置溢出后，组内未分配工单达到以下设置状态时，会启动溢出支援逻辑，将工单给支援组有空闲的人员来处理'
                )}
              />
            )}
            field="SupportOverflow"
            rules={[getRequiredRule(I18n.t('please_select_whether_overflow_is_supported', {}, '请选择是否支持溢出'))]}
          >
            <Form.Radio value={1}>{I18n.t('yes', {}, '是')}</Form.Radio>
            <Form.Radio value={0}>{I18n.t('no', {}, '否')}</Form.Radio>
          </Form.RadioGroup>
          {showOverflow ? (
            <>
              <Form.InputNumber
                style={{
                  width: 286,
                }}
                field="queueOverflowCount"
                label={I18n.t('aksda7kasd', {}, '队内当前排队数超过以下数值时开始溢出')}
                min={1}
                max={9999}
                rules={showOverflow ? [countIntegerNumberRule] : [{ required: false }]}
                formatter={value => `${value}`.replace(/\D/g, '')}
              />
              <label className="united_route_manage-form-field-label united_route_manage-form-field-label-left">
                <div className="united_route_manage-form-field-label-text">
                  {I18n.t('route_Starling_39', {}, '溢出技能组')}
                </div>
              </label>
              <ArrayField field="skillGroupOverflowList">
                {({ add, arrayFields, addWithInitValue }) => (
                  <React.Fragment>
                    {arrayFields.map(({ field, key, remove }, i) => (
                      <div key={key} style={{ width: '100%', display: 'flex' }}>
                        <div style={{ width: 300 }}>
                          <Form.Select
                            placeholder={I18n.t('route_Starling_44', {}, '请选择接入方')}
                            field={`${field}[accessPartyId]`}
                            noLabel={true}
                            optionList={accessPartyList}
                            style={{ width: '100%' }}
                            filter={true}
                            rules={
                              showOverflow ?
                                [getRequiredRule(I18n.t('route_Starling_44', {}, '请选择接入方'))] :
                                [{ required: false }]
                            }
                            onChange={value => {
                              ruleFormApi?.setValue(`${field}[id]`, []);
                            }}
                          />
                        </div>
                        <div
                          style={{
                            lineHeight: '56px',
                            fontSize: '14px',
                            color: 'var(--color-text-0)',
                            height: '20px',
                            margin: '0 8px',
                          }}
                        >
                          {I18n.t('route_Starling_41', {}, '中')}
                        </div>
                        <div style={{ width: 300 }}>
                          <Form.Select
                            placeholder={I18n.t('route_Starling_45', {}, '请选择承载技能组')}
                            field={`${field}[id]`}
                            noLabel={true}
                            optionList={getAccessSkilllist(skillGroupOverflowList?.[i]?.accessPartyId, skillGroupAll)}
                            style={{ width: '100%' }}
                            filter={true}
                            rules={
                              showOverflow ?
                                [getRequiredRule(I18n.t('route_Starling_45', {}, '请选择承载技能组'))] :
                                [{ required: false }]
                            }
                            multiple
                          />
                        </div>
                        <Button
                          disabled={arrayFields?.length === 1 || viewType === 'view'}
                          theme="borderless"
                          type="tertiary"
                          icon="minus_circle"
                          onClick={remove}
                          style={{ margin: 12 }}
                        />
                      </div>
                    ))}
                    <Button disabled={viewType === 'view'} onClick={add} icon="plus_circle" theme="borderless">
                      {I18n.t('route_Starling_40', {}, '添加溢出技能组')}
                    </Button>
                  </React.Fragment>
                )}
              </ArrayField>
            </>
          ) : null}
        </>
      ) : null}
    </>
  );
};

export default IMUserRouterConfig;
