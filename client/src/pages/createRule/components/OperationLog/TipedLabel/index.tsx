import React from 'react';
import { Icon, Tooltip } from '@ies/semi-ui-react';
import * as styles from './index.scss';

interface TipedLabelProps {
  label?: string;
  question?: string;
  answer: string;
}
const TipedLabel: React.FC<TipedLabelProps> = ({ label, question, answer }) => (
  // const { label, question, answer } = props;
  <div className={styles.radioBox}>
    {label}
    <Tooltip
      position="bottomLeft"
      showArrow={false}
      style={{
        width: '292px',
        maxWidth: 'initial',
        backgroundColor: '#fff',
        padding: '16px 24px 16px 12px',
        borderColor: 'rgba(28, 31, 35, 0.08)',
        borderWidth: 1,
        borderStyle: 'solid',
      }}
      content={(
        <div className={styles.tooltipBox}>
          <div className={styles.tooltipBoxRight}>
            <div className={styles.tooltipBoxTitle}>{question}</div>
            <div className={styles.tooltipBoxLabel}>{answer}</div>
          </div>
        </div>
      )}
    >
      <Icon className={styles.radioIcon} type="help_circle" />
    </Tooltip>
  </div>
);

export default TipedLabel;
