import React, { useState, useEffect, useRef } from 'react';
import { Timeline, Spin, Typography, Empty } from '@ies/semi-ui-react';
import InfiniteScroll from 'react-infinite-scroller';
import * as styles from './index.scss';
import { demoClient } from '@http_idl/demo';
import { ROUTE_EVENT_ID, LOG_RULE_STATUS_MAP, routeTypeMap } from '@common/constants/property';
import { useUser } from '@hooks/useUser';
import { I18n } from '@ies/starling_intl';
export default function ActionsLog(props): React.ReactElement {
  const { ruleId, viewType, eventKey } = props;
  const [loading, setLoading] = useState(false);
  const [actionLog, setActionLog] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const hasMoreRef = useRef(false);
  const { Paragraph } = Typography;
  const user = useUser();
  const [page, setPage] = useState(1);
  const getActionLog = async id => {
    if (!hasMoreRef.current) {
      hasMoreRef.current = true;
      setLoading(true);
      const params = {
        eventId: ROUTE_EVENT_ID[eventKey] || '',
        accessPartyId: user?.accessPartyId || '',
        ruleId: id,
        page,
        pageSize: 10,
      };
      const res = await demoClient.GetRuleOperationLogs(params);
      setActionLog([...actionLog, ...res.RuleOperationLogList]);
      setTotalCount(Number(res.totalCount));
      setLoading(false);
      hasMoreRef.current = false;
    }
  };
  useEffect(() => {
    if (['edit', 'view'].includes(viewType)) {
      getActionLog(ruleId);
    }
  }, [page]);

  const TimelineItemContent = item => (
    <>
      {item?.logList?.map((log, idx) => (
        <div key={idx}>
          {log?.name ? <Paragraph className={styles.actionsLogText}>{log?.name}</Paragraph> : <></>}
          {log?.status ? (
            <Paragraph className={styles.actionsLogText}>{LOG_RULE_STATUS_MAP[log?.status]?.() || log?.status}</Paragraph>
          ) : (
            <></>
          )}
          {log?.priority ? <Paragraph className={styles.actionsLogText}>{log?.priority}</Paragraph> : <></>}
          {log?.groupsRelationChange ? (
            <>
              <Paragraph className={styles.actionsLogText}>
                {I18n.t('route_Starling_25', {}, '规则组间的关系改变')}
              </Paragraph>
              <Paragraph
                ellipsis={{
                  rows: 2,
                  expandable: true,
                  collapsible: true,
                  collapseText: I18n.t('route_Starling_22', {}, '折叠'),
                }}
                className={styles.actionsLogText}
                style={{ width: 668 }}
              >
                {log?.groupsRelationChange}
              </Paragraph>
            </>
          ) : (
            <></>
          )}
          {log?.retutnValue ? (
            <>
              <Paragraph className={styles.actionsLogText}>
                {log?.retutnValue?.beforeValue.length === 1 && log?.retutnValue?.afterValue.length === 1 ?
                  I18n.t('route_Starling_28', {}, '规则技能组变更') :
                  I18n.t('route_Starling_20', {}, '')}
              </Paragraph>
              <div style={{ display: 'flex' }}>
                <span>
                  {log?.retutnValue?.beforeValue?.map((val, index) => (
                    <Paragraph key={index} className={styles.actionsLogText}>
                      {`【 ${val.skillGroupName} 】${val?.percent ? `${val?.percent}%` : ''}`}
                    </Paragraph>
                  ))}
                </span>
                <Paragraph className={styles.actionsLogText}>to</Paragraph>
                <span>
                  {log?.retutnValue?.afterValue?.map((val, index) => (
                    <Paragraph className={styles.actionsLogText} key={index}>
                      {`【 ${val.skillGroupName} 】${val?.percent ? `${val?.percent}%` : ''}`}
                    </Paragraph>
                  ))}
                </span>
              </div>
            </>
          ) : (
            <></>
          )}
          {log?.ValueChange.length ? (
            <>
              <Paragraph className={styles.actionsLogText}>
                {I18n.t('route_Starling_21', {}, '规则条件值改变')}
              </Paragraph>
              <div>
                {log.ValueChange.map((val, index) => (
                  <Paragraph
                    key={index}
                    ellipsis={{
                      rows: 2,
                      expandable: true,
                      collapsible: true,
                      collapseText: I18n.t('route_Starling_22', {}, '折叠'),
                    }}
                    className={styles.actionsLogText}
                    style={{ width: 668 }}
                  >
                    {`【 ${I18n.t('route_Starling_10', {}, '规则组')}${val.groupIndex} 】【 ${
                      val.conditionName
                    } 】${I18n.t(val.updateValueType, {}, val.updateValueType)} ${I18n.t(
                      val.options,
                      {},
                      val.options
                    )}`}
                  </Paragraph>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {log?.ConditionsChange.length ? (
            <>
              <Paragraph className={styles.actionsLogText}>{I18n.t('route_Starling_23', {}, '规则条件改变')}</Paragraph>
              <div>
                {log.ConditionsChange.map((val, index) => (
                  <Paragraph
                    key={index}
                    ellipsis={{
                      rows: 2,
                      expandable: true,
                      collapsible: true,
                      collapseText: I18n.t('route_Starling_22', {}, '折叠'),
                      expandText: I18n.t('route_Starling_27', {}, '更多'),
                    }}
                    style={{ width: 668 }}
                    className={styles.actionsLogText}
                  >
                    {`${I18n.t(val.updateConditionsType, {}, val.updateConditionsType)}【 ${I18n.t(
                      'route_Starling_10',
                      {},
                      '规则组'
                    )}${val.groupIndex} 】【 ${val.conditionName} 】${I18n.t(
                      val.updateValueType,
                      {},
                      val.updateValueType
                    )} ${val.options}`}
                  </Paragraph>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
          {log?.ConditionRelationChange.length ? (
            <>
              <Paragraph className={styles.actionsLogText}>
                {I18n.t('route_Starling_24', {}, '规则间的条件关系改变')}
              </Paragraph>
              <div>
                {log.ConditionRelationChange.map((val, index) => (
                  <Paragraph
                    key={index}
                    ellipsis={{
                      rows: 2,
                      expandable: true,
                      collapsible: true,
                      collapseText: I18n.t('route_Starling_22', {}, '折叠'),
                    }}
                    style={{ width: 668 }}
                    className={styles.actionsLogText}
                  >
                    {`【 ${I18n.t('route_Starling_10', {}, '规则组')}${val.groupIndex} 】${val.conditionName}`}
                  </Paragraph>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}

          {log?.theTiming ? (
            <>
              <Paragraph className={styles.actionsLogText}>{I18n.t('route_Starling_28', {}, '路由时机变更')}</Paragraph>
              <div style={{ display: 'flex' }}>
                <span>{`【 ${log?.theTiming?.beforeValue?.map(v => routeTypeMap[v]?.())?.join('、')}】`}</span>
                <Paragraph className={styles.actionsLogText}>change to</Paragraph>
                <span>{`【 ${log?.theTiming?.afterValue?.map(v => routeTypeMap[v]?.())?.join('、')}】`}</span>
              </div>
            </>
          ) : (
            <></>
          )}

          {log?.theOverflow ? (
            <>
              <Paragraph className={styles.actionsLogText}>{I18n.t('溢出设置变更', {}, '溢出设置变更')}</Paragraph>
              <div style={{ display: 'flex' }}>
                <span>
                  {`${I18n.t(
                    '[load_factor__{placeholder1}__overflow_skill_set__{placeholder3}]',
                    {
                      placeholder1: log?.theOverflow?.beforeValue?.factor || '',
                      placeholder3:
                        log?.theOverflow?.beforeValue?.skill_group_overflow?.map(v => v.skillGroupName)?.join('、') ||
                        '',
                    },
                    '【 负载因子:{placeholder1}，溢出技能组:{placeholder3}】'
                  )}`}
                </span>
                <Paragraph className={styles.actionsLogText}>change to</Paragraph>
                <span>
                  {`${I18n.t(
                    '[load_factor__{placeholder1}__overflow_skill_set__{placeholder3}]',
                    {
                      placeholder1: log?.theOverflow?.afterValue?.factor || '',
                      placeholder3:
                        log?.theOverflow?.afterValue?.skill_group_overflow?.map(v => v.skillGroupName)?.join('、') ||
                        '',
                    },
                    '【 负载因子:{placeholder1}，溢出技能组:{placeholder3}】'
                  )}`}
                </span>
              </div>
            </>
          ) : (
            <></>
          )}
          {log?.automaticShunt && (
            <>
              <Paragraph className={styles.actionsLogText}>{I18n.t('shunt_rule_change', {}, '分流规则变更')}</Paragraph>
              <div style={{ display: 'flex' }}>
                <span>
                  {I18n.t(
                    'automatic_diversion_{placeholder1}',
                    {
                      placeholder1:
                        log?.automaticShunt?.beforeValue?.map(v => `【${v?.skillGroupName}】`)?.join('、') || '',
                    },
                    '自动分流{placeholder1}'
                  )}
                </span>
                <Paragraph className={styles.actionsLogText}> to </Paragraph>
                <span>
                  <span>
                    {I18n.t(
                      'manual_shunt_{placeholder1}',
                      {
                        placeholder1:
                          log?.automaticShunt?.afterValue
                            ?.map(v => `【${v?.skillGroupName}】${v?.percent ? `${v?.percent}%` : ''}`)
                            ?.join('、') || '',
                      },
                      '手动分流{placeholder1}'
                    )}
                  </span>
                </span>
              </div>
            </>
          )}
          {log?.manualShunt && (
            <>
              <Paragraph className={styles.actionsLogText}>{I18n.t('shunt_rule_change', {}, '分流规则变更')}</Paragraph>
              <div style={{ display: 'flex' }}>
                <span>
                  {I18n.t(
                    'manual_shunt_{placeholder1}',
                    {
                      placeholder1:
                        log?.manualShunt?.beforeValue
                          ?.map(v => `【${v?.skillGroupName}】${v?.percent ? `${v?.percent}%` : ''}`)
                          ?.join('、') || '',
                    },
                    '手动分流{placeholder1}'
                  )}
                </span>
                <Paragraph className={styles.actionsLogText}> to </Paragraph>
                <span>
                  {I18n.t(
                    'automatic_diversion_{placeholder1}',
                    {
                      placeholder1:
                        log?.manualShunt?.afterValue?.map(v => `【${v?.skillGroupName}】`)?.join('、') || '',
                    },
                    '自动分流{placeholder1}'
                  )}
                </span>

                <span />
              </div>
            </>
          )}
        </div>
      ))}
    </>
  );
  const onLoadMore = (): void => {
    if (totalCount === actionLog.length || Number(totalCount) === 0) {
      setHasMore(false);
      return;
    } else {
      setPage(page + 1);
    }
  };
  return (
    <Spin spinning={loading}>
      <div className={styles.timeLineBox}>
        <InfiniteScroll pageStart={0} initialLoad={false} loadMore={onLoadMore} hasMore={hasMore} useWindow={false}>
          {actionLog?.length ? (
            <Timeline style={{ padding: '0px' }}>
              {actionLog.map((item, idx) => (
                <Timeline.Item key={idx} extra={TimelineItemContent(item)} color={'var(--color-fill-1)'}>
                  <span className={styles.actionsLogHeader}>{item.agentName}</span>
                  <span className={styles.actionsLogHeader}>{item.title}</span>
                  <span className={styles.actionsLogText}>{item.updateTime}</span>
                </Timeline.Item>
              ))}
            </Timeline>
          ) : (
            <Empty title={I18n.t('route_Starling_8', {}, '暂无操作日志')} />
          )}
        </InfiniteScroll>
      </div>
    </Spin>
  );
}
