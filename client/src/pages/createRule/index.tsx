import { I18n } from '@ies/starling_intl';
import React, { useContext } from 'react';
import * as styles from './index.scss';
import { useHistory } from 'react-router-dom';
import { CreateRuleHooks } from './hooks';
import { UserContext } from '@/context/user';
import { demoClient } from '@http_idl/demo';
import RuleLists from '@/components/ruleLists';
import ActionsLog from './components/OperationLog';
import { Icon, Form, Button, Toast } from '@ies/semi-ui-react';
import { routeType, ChannelTypeMAP } from '@/const/enums';
import {
  TRIGER_DESC_MAP,
  TIPS_NAME_MAP,
  LOCATION_MAP,
  defaultAccessPartyList,
  ACCESS_PARTY_LIST_MAP
} from './common/constants/property';
import { tempRouterParams, ReclaimExtra } from './common/constants';
import { createRuleProps } from './createRuleType';
import { getRequiredRule } from '@common/rules';
import CreateRuleHeader from '@/components/createRuleHeader';
import { AOP_TICKET_ROUTE, routeTypeList } from '@common/constants/property';
import { safeJSONParse } from '@/common/utils';
import RouteConfig from './components/RouteConfig';

import { useSubmit, useCheckSkillgroupAutoAssign, useHandleGoBack } from './common/hooks';
import { errorReporting } from '@/common/utils/errorReporting';
import AIS from '@ies/ais';

let ruleFormApi = null;

const Index: React.FC = () => {
  const Browerhistory = useHistory();
  const { handleGoBack } = useHandleGoBack({ ruleFormApi });
  const user = useContext(UserContext);
  const routerParams: createRuleProps = Object.assign({}, tempRouterParams, Browerhistory?.location?.state || {});
  const locationType = Browerhistory?.location?.pathname?.slice?.(1);
  const plaformTag = locationType;
  const [skillGroupAll, setSkillGroupAll] = React.useState([]);
  const [accessPartyList, setAccessPartyList] = React.useState([]);
  const typeName = I18n.t('routing', {}, '路由');
  const {
    skillGroupsLists,
    slaMetaSimple,
    slaMetaSimpleLists,
    botListData,
    initFormDetail,
    ruleLists,
    fieldList,
    ruleListIsError,
    addRuleGroup,
    changeConditionsJoinWay,
    setRuleFormRef,
    deleteRuleItem,
    handleFormValueChange,
    handleOpRadioChange,
    checkRuelAllWrite,
    showContent,
    changeFormValuesByRuleList,
    deleteGroup,
  } = CreateRuleHooks(routerParams.ruleId, plaformTag, routerParams.isLast, routerParams);

  const { handleSubmit } = useSubmit({ ruleLists, fieldList, slaMetaSimple });
  const { checkSkillgroupAutoAssign } = useCheckSkillgroupAutoAssign({
    ruleLists,
    fieldList,
    slaMetaSimple,
    skillGroupAll,
  });
  const getSelectedSlaMetaSimple = (id?: string) => slaMetaSimple.find(item => item.Id === id);
  function getSlaMetaSimpleExtra(id?: string): ReclaimExtra {
    const item = getSelectedSlaMetaSimple(id);
    try {
      return safeJSONParse(item?.Extra || '');
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getSlaMetaSimpleExtra' });
      return {
        reclaim_type: 'empty',
        reclaim_default_value: 0,
      };
    }
  }
  const showAimByLocation = LOCATION_MAP[locationType];

  const submitInfo = async () => {
    if (!ruleFormApi) {
      return;
    }
    if (!checkRuelAllWrite(routerParams.isLast)) {
      return;
    }
    const values = await ruleFormApi.validate();
    if (values.isShunt && !values?.isAutoShunt) {
      let count = 0;
      if (!values.skillList.length) {
        return;
      }
      values?.skillList?.map(val => {
        count += val?.percent;
      });
      if (count !== 100) {
        Toast.error(I18n.t('route_Starling_3', {}, '流量分布总和非100%，请检查并修改'));
        return;
      }
    }
    if (showAimByLocation === 'bot_routing') {
      handleSubmit(values);
    } else {
      checkSkillgroupAutoAssign(values);
    }
  };
  // Pull the access party
  React.useEffect(() => {
    if ([routeType.ticket_routing, routeType.service_routing]?.includes(plaformTag as routeType)) {
      AIS.fe
        .getJsonTCC({
          serviceName: 'ies.fe.route_manage_i18n',
          key: 'overflow_skill_group_access_party_config',
        })
        ?.then(res => {
          setAccessPartyList(res?.data?.[user?.accessPartyId] || defaultAccessPartyList);
        })
        ?.catch(error => {
          errorReporting({ error, type: 'promise_name', name: 'GetSkillGroupsByAccessParties' });
        });
      demoClient
        ?.GetSkillGroupsByAccessParties({
          TenantId: '1',
          ChannelType: routerParams?.EventKey === AOP_TICKET_ROUTE ? 12 : ChannelTypeMAP[plaformTag],
          AccessPartyIds: ['2', '3', '9', '35', '40', '46', '51', '52'],
        })
        ?.then(res => {
          setSkillGroupAll(res.SkillGroups);
        })
        ?.catch(error => {
          errorReporting({ error, type: 'promise_name', name: 'GetSkillGroupsByAccessParties' });
        });
    }
  }, []);
  const content = (
    <div className={styles.createRuleContent}>
      <CreateRuleHeader platform={plaformTag} name={TIPS_NAME_MAP[plaformTag]?.title()} handleGoBack={handleGoBack} />
      <div className={styles.formContent}>
        <Form
          disabled={routerParams?.viewType === 'view'}
          onValueChange={handleFormValueChange}
          getFormApi={formApi => {
            ruleFormApi = formApi;
            setRuleFormRef(formApi);
          }}
          initValues={{
            ...initFormDetail,
            AccesspartyId: initFormDetail?.AccesspartyId ? String(initFormDetail?.AccesspartyId) : '',
          }}
        >
          {({ values, formApi }) => (
            <>
              <Form.Input
                className={styles.createRuleFormMinwidth}
                label={`${I18n.t('{typename}_rule_name', { typeName }, '{typeName}规则名称')}`}
                field="DisplayName"
                rules={[
                  getRequiredRule(
                    `${I18n.t('please_fill_in_{typename}_rule_name', { typeName }, '请填写{typeName}规则名称')}`
                  ),
                ]}
              />
              {plaformTag === routeType.ticket_routing && (
                <Form.Select
                  field="RouteType"
                  label={I18n.t('routing_timing', {}, '路由时机')}
                  placeholder={I18n.t('please_select_the_routing_timing', {}, '请选择路由时机')}
                  style={{ width: '450px' }}
                  rules={
                    routerParams.isLast ?
                      [{ required: false }] :
                      [getRequiredRule(I18n.t('please_select_the_routing_timing', {}, '请选择路由时机'))]
                  }
                  multiple
                >
                  {routeTypeList.map(option => (
                    <Form.Select.Option key={option.value} value={option.value}>
                      {option.name()}
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              )}
              {routerParams.isLast ? (
                <>
                  <Form.Slot label={{ text: I18n.t('trigger_condition', {}, '触发条件') }}>
                    <div className={styles.lastTip}>
                      {TRIGER_DESC_MAP[plaformTag]?.title?.() || I18n.t('unknown_rule_type', {}, '未知规则类型')}
                    </div>
                  </Form.Slot>
                </>
              ) : (
                <>
                  <Form.RadioGroup
                    field="OperateType"
                    label={I18n.t('trigger_condition', {}, '触发条件')}
                    onChange={handleOpRadioChange}
                    type="card"
                    style={{ marginBottom: '10px' }}
                  >
                    <Form.Radio
                      value={1}
                      style={values.OperateType === 0 ? { border: '0.5px solid rgba(34, 39, 39, 0.35)' } : null}
                    >
                      {I18n.t('meet_all_of_the_following_conditions', {}, '满足下列所有条件')}
                    </Form.Radio>
                    <Form.Radio
                      value={0}
                      style={values.OperateType === 1 ? { border: '0.5px solid rgba(34, 39, 39, 0.35)' } : null}
                    >
                      {I18n.t('meet_any_of_the_following_conditions', {}, '满足下列任一条件')}
                    </Form.Radio>
                  </Form.RadioGroup>
                  {ruleLists.map((item, index) => {
                    const content = (
                      <React.Fragment key={index}>
                        <RuleLists
                          changeFormValuesByRuleList={changeFormValuesByRuleList}
                          changeJoinWay={changeConditionsJoinWay}
                          values={values}
                          formApi={formApi}
                          groupInfo={item}
                          parentIndex={index}
                          deleteItem={deleteRuleItem}
                          fieldList={fieldList}
                          deleteGroup={deleteGroup}
                          viewType={routerParams.viewType}
                          disabledGroup={ruleLists?.length === 1}
                        />
                      </React.Fragment>
                    );
                    return content;
                  })}
                  {ruleListIsError ? (
                    <div className={styles.errorTipBox}>
                      <Icon type="clear" style={{ marginRight: '4px' }} />
                      {`${I18n.t('please_fill_in_the_{typename}_rule', { typeName }, '请填写{typeName}规则')}`}
                    </div>
                  ) : null}
                  <div className={styles.addRuleListBox}>
                    <Button
                      className={styles.addRuleListBtn}
                      icon="plus_circle"
                      theme="borderless"
                      onClick={addRuleGroup}
                      disabled={routerParams.viewType === 'view'}
                      style={{ margin: '0 auto' }}
                    >
                      {I18n.t('route_Starling_1', {}, '添加规则组')}
                    </Button>
                  </div>
                </>
              )}
              <RouteConfig
                type={plaformTag}
                isLast={routerParams.isLast}
                sKipGroupLists={skillGroupsLists}
                overFlowLists={skillGroupsLists}
                showOverflow={values.SupportOverflow}
                viewType={routerParams.viewType}
                skillGroupAll={skillGroupAll}
                showSelectBot={values.IsOpen}
                showNeedRisk={values.BotId ? values.BotId.length : 0}
                robotLists={botListData}
                SkillGroupReclaimLists={skillGroupsLists}
                slaMetaSimple={slaMetaSimpleLists}
                showSupportReclaim={values.SupportReclaim}
                getSlaMetaSimpleExtra={getSlaMetaSimpleExtra}
                ReclaimCondition={values.ReclaimCondition}
                accessPartyList={accessPartyList}
              />
              {['create', 'clone'].includes(routerParams.viewType) && (
                <>
                  <Form.RadioGroup
                    field="Enable"
                    label={I18n.t('effective_status', {}, '生效状态')}
                    onChange={handleOpRadioChange}
                  >
                    <Form.Radio value={0}>{I18n.t('disable', {}, '禁用')}</Form.Radio>
                    <Form.Radio value={1}>{I18n.t('enable', {}, '启用')}</Form.Radio>
                  </Form.RadioGroup>
                  <Form.RadioGroup
                    field="RuleEnv"
                    label={I18n.t('production_environment', {}, '生产环境')}
                    onChange={handleOpRadioChange}
                  >
                    <Form.Radio value={1}>{I18n.t('online', {}, '线上')}</Form.Radio>
                  </Form.RadioGroup>
                </>
              )}
            </>
          )}
        </Form>
        <>
          <div className={styles.actionLogTitle}>{I18n.t('route_Starling_2', {}, '操作日志')}</div>
          <ActionsLog ruleId={routerParams.ruleId} viewType={routerParams.viewType} eventKey={routerParams.EventKey} />
        </>
      </div>
      <div className={styles.createRuleBottom} style={{ display: routerParams.viewType === 'view' ? 'none' : 'flex' }}>
        <Button type="tertiary" onClick={handleGoBack}>
          {I18n.t('cancel', {}, '取消')}
        </Button>
        <Button style={{ marginLeft: 8 }} type="secondary" theme="solid" onClick={submitInfo}>
          {routerParams.viewType === 'edit' ? I18n.t('update', {}, '更新') : I18n.t('new', {}, '新建')}
        </Button>
      </div>
    </div>
  );
  return <>{showContent ? content : null}</>;
  // return <FormRuleCom />;
};
export default Index;
