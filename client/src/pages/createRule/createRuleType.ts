import { I18n } from '@ies/starling_intl';

export interface routerParamsType {
  Priority: number;
  ruleId: string;
}

export interface ruleFormInit {
  DisplayName: string;
  OperateType: number;
  opGroup: string;
  SkillGroupId?: string;
  Enable: number;
  RuleEnv: number;
  Expression: {
    opGroup: string;
    conditionGroups: Array<ruleGroup>;
  };
  // IM manual routing rules
  SupportOverflow?: number;
  queueOverflowCount?: number;
  SkillGroupOverflow?: Array<string>;
  // IM Smart Routing Rules
  IsOpen?: number;
  BotId?: string;
  isAutoShunt?: number;
  needRiskIdentification?: number;
  // Work order routing rules
  SupportReclaim?: number; // Recycling
  ReclaimCondition?: string; // Trigger condition
  ReclaimHours?: number;
  ReclaimMinutes?: number;
  SkillGroupReclaim?: string;
  AccesspartyId?: number;
  skillList?: any;
  isShunt?: number;
  skillGroupOverflowList?: any;
}
export type CreateRuleHeaderProps = {
  name: string;
  platform: string;
  handleGoBack: () => void;
};
export type createRuleProps = {
  Priority: number; // Rule priority
  RuleGroupId?: number; // Rule group ID
  ruleId?: string; // Rule ID,
  plaformTag: string; // Platform identity
  Enable: boolean;
  viewType: string; // Edit or New
  isLast: boolean;
  EventKey: string;
  ruleLength: number;
  ruleName?: string;
  statusList?: number[];
  skillGroupList?: number[];
};

export type ruleItem = {
  Lhs: any;
  OpCheck: string;
  Rhs: any;
  [propName: string]: any;
};
export interface ruleGroup {
  OpGroup: string;
  Conditions: Array<ruleItem>;
}
export interface ruleGroupClient {
  opGroup: string;
  conditions: Array<ruleItem>;
}
export const ruleOpcheckArr = [
  { serveOp: '==', clientOp: 1, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 2, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: 'CONTAINS', clientOp: 3, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: '>', clientOp: 4, opType: 'math', clientName: () => I18n.t('greater_than', {}, '大于') },
  { serveOp: '<', clientOp: 5, opType: 'math', clientName: () => I18n.t('less_than', {}, '小于') },
  { serveOp: '>=', clientOp: 6, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, '大于等于') },
  { serveOp: '<=', clientOp: 7, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, '小于等于') },
  { serveOp: 'START_WITH', clientOp: 8, opType: 'fun', clientName: () => I18n.t('started_with', {}, '开始于') },
  { serveOp: 'END_WITH', clientOp: 9, opType: 'fun', clientName: () => I18n.t('ended_in', {}, '结束于') },
  { serveOp: 'IS_NULL', clientOp: 10, opType: 'fun', clientName: () => I18n.t('empty', {}, '为空') },
  { serveOp: 'IS_NOT_NULL', clientOp: 11, opType: 'fun', clientName: () => I18n.t('not_empty', {}, '不为空') },
  { serveOp: 'LIST_IN', clientOp: 12, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: 'STRING_CONTAINS', clientOp: 13, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: '==', clientOp: 14, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 15, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: '==', clientOp: 16, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 17, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: '>', clientOp: 18, opType: 'math', clientName: () => I18n.t('greater_than', {}, '大于') },
  { serveOp: '<', clientOp: 19, opType: 'math', clientName: () => I18n.t('less_than', {}, '小于') },
  { serveOp: '>=', clientOp: 20, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, '大于等于') },
  { serveOp: '<=', clientOp: 21, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, '小于等于') },
  { serveOp: 'LIST_NOT_IN', clientOp: 22, opType: 'fun', clientName: () => I18n.t('not_included', {}, '不包含') },
  { serveOp: 'LIST_EQUAL', clientOp: 30, opType: 'fun', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: 'LIST_NOT_EQUAL', clientOp: 31, opType: 'fun', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  {
    serveOp: 'STRING_NOT_CONTAINS',
    clientOp: 32,
    opType: 'fun',
    clientName: () => I18n.t('not_included', {}, '不包含'),
  },
  { serveOp: 'LIST_RETAIN', clientOp: 33, opType: 'fun', clientName: () => I18n.t('intersect', {}, '有交集') },
  { serveOp: 'NOT LIST_IN', clientOp: 34, opType: 'fun', clientName: () => I18n.t('not_included', {}, '不包含') },
  { serveOp: 'BETWEEN_ALL_CLOSE', clientOp: 35, opType: 'fun', clientName: () => I18n.t('belong_to', {}, '属于') },
  {
    serveOp: 'NOT BETWEEN_ALL_OPEN',
    clientOp: 36,
    opType: 'fun',
    clientName: () => I18n.t('do_not_belong', {}, '不属于'),
  },
  {
    serveOp: 'NOT LIST_RETAIN',
    clientOp: 37,
    opType: 'fun',
    clientName: () => I18n.t('no_intersection', {}, '无交集'),
  },
];
