.AdjustPriorityButtonIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-right: 8px;
  transition: background-color .1s ease-in-out;
  padding: 8px;
  box-sizing: content-box;
  border-radius: 6px;
  // color: rgba(11, 135, 125, 1) !important;
  

  &-disabled {
    color: var(--color-disabled-text);
    cursor: not-allowed;
  }

  &:hover {
    background-color: var(--color-fill-0);
  }
}
