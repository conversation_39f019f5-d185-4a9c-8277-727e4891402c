import React from 'react';
import { Tooltip, Icon } from '@ies/semi-ui-react';
import * as styles from './index.scss';
interface AdjustPriorityButtonProps {
  icon: string;
  tooltip: string;
  disabled?: boolean;
  onClick: () => void;
}
export const AdjustPriorityButton: React.FC<AdjustPriorityButtonProps> = props => {
  const { icon, tooltip, disabled, onClick } = props;

  const className = React.useMemo(() => {
    const classList = [styles.AdjustPriorityButtonIcon];

    if (disabled) {
      classList.push(styles.AdjustPriorityButtonIconDisabled);
    }
    return classList.join(' ');
  }, [disabled]);
  return (
    <Tooltip disabled={disabled} content={tooltip}>
      <Icon
        className={className}
        style={{ width: 8, height: 8 }}
        type={icon}
        onClick={() => {
          !disabled && onClick();
        }}
      />
    </Tooltip>
  );
};