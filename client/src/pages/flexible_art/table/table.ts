import * as t from '@http_idl/demo';
import { CardReOrderType } from '@/const/enums';

export type TableProps = {
  loading: boolean;
  reOrdering: boolean;
  tableData: any[];
  tabKey: string;
  fieldList: any[];
  ruleGroup: string;
  changeRuleStatus: (type: any, ruleData: any) => Promise<void>;
  onReOrder: (ruleData: t.ClientRule, type: CardReOrderType) => Promise<void>;
  changeLoading: (status: boolean) => void;
  filter: FlexibleFilter;
  setFilter: (value: FlexibleFilter) => void;
  flexibleSkillGroup: any[];
};

type FlexibleFilter = {
  serachName: string;
  channel: string[];
  status: string[];
  skillGroups: string[];
};