.pagination {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.tableList {
  margin: 16px 24px 16px 0;

  .tableTop {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 16px 0 6px;
  
    .filter {
      display: flex;
      flex-direction: row;
    }
  }
}

.adjustButtom {
  display: flex;
  flex-direction: row;

  :global {

    .united_route_manage-icons {
      color: rgba(11, 135, 125, 1) !important;
    }
    
    .kefu-dropdown {

      .united_route_manage-button.united_route_manage-button-with-icon-only {
        width: 24px;
        height: 24px;

        .united_route_manage-icons-default {

          color: #0b877d;
    
        }
      }

    }
    
    
  }
}

.conditionBox {
  display: flex;
  flex-direction: row;

  .condition {
    white-space: nowrap;
  }
}

.hiddenBox {
  display: none;
}

.tips {
  height: 28px;
  line-height: 28px;
  font-size: 14px;
  color: rgba(var(--grey-9), .6);
}
