import { I18n } from '@ies/starling_intl';
import React, { useEffect, useState, useContext } from 'react';
import { Table, Tag, Pagination, Input, Icon, Button, Spin } from '@ies/semi-ui-react';
import TableHandleDom from '../tableHandleDom';
import * as styles from './index.scss';
import { useHistory } from 'react-router-dom';
import { AdjustPriorityButton } from '../adjustPriorityButton/index';
import UpIcon from '@/images/route_up_arrow.svg';
import DownIcon from '@/images/route_down_arrow.svg';
import TopIcon from '@/images/route_top_arrow.svg';
import BottomIcon from '@/images/route_bottom_arrow.svg';

import { TableProps } from './table';
import { CardReOrderType } from '@/const/enums';
import { ConditionFilterGroup, MultiSelect } from '@ies/kefu-filter-group';
import { subAndDeMap, degreeMap } from './const';
import { ruleOpcheckArr } from '@/pages/createRule/createRuleType';
import { mul } from '../hooks';
import { UserContext } from '@context/user';
import { isEmpty } from 'lodash';
import { safeJSONParse } from '@/common/utils';

enum Relation {
  'AND' = I18n.t('and', {}, '且'),
  'OR' = I18n.t('or', {}, '或'),
}

const Index: React.FC<TableProps> = TableProps => {
  const {
    loading,
    reOrdering,
    tableData,
    tabKey,
    fieldList,
    ruleGroup,
    changeRuleStatus,
    changeLoading,
    onReOrder,
    filter,
    setFilter,
    flexibleSkillGroup,
  } = TableProps;
  const user = useContext(UserContext);
  const [currentData, setCurrentData] = useState([]);
  const [skillGroup, setSkillGroup] = useState([]);

  const history = useHistory();
  const getRuleName = opKey => {
    for (let i = 0; i < ruleOpcheckArr?.length; i++) {
      if (ruleOpcheckArr[i].serveOp === opKey) {
        return ruleOpcheckArr[i].clientName();
      }
    }
  };
  const columns = [
    {
      title: I18n.t('configuration_name', {}, '配置名称'),
      dataIndex: 'DisplayName',
      width: 160,
    },
    {
      title: I18n.t('skill_group', {}, '技能组'),
      dataIndex: 'skillGruop',
      render: (text, record) => {
        const content = <div>{record?.skillInfo?.name}</div>;
        return content;
      },
    },
    {
      title: I18n.t('threshold_condition', {}, '阈值条件'),
      dataIndex: 'threshold',
      width: 260,
      render: (text, record) => {
        const content = (
          <div>
            {record?.desCondtions.str.map((item, index) => {
              const content = (
                <div key={index} className={styles.conditionBox}>
                  <div className={index === 0 ? styles.hiddenBox : null}>{record?.desCondtions?.relation}</div>
                  {item?.name} {item?.opcheck} {item?.value}
                  {item?.unit}
                </div>
              );
              return content;
            })}
          </div>
        );
        return content;
      },
    },
    {
      title: I18n.t('supply_and_demand_status', {}, '供需状态'),
      dataIndex: 'SupportAndDemandStatus',
      render: (text, record) => {
        const content = <div>{record?.statusInfo?.SupportAndDemandStatus?.name}</div>;
        return content;
      },
    },
    {
      title: I18n.t('unbalanced_state', {}, '失衡状态'),
      dataIndex: 'DegreeOfImbalance',
      render: (text, record) => {
        const content = <div>{record?.statusInfo?.DegreeOfImbalance?.name}</div>;
        return content;
      },
    },
    {
      title: I18n.t('configuration_person', {}, '配置人'),
      dataIndex: 'UpdaterAgentName',
    },
    {
      title: I18n.t('status', {}, '状态'),
      dataIndex: 'Status',
      render: (text, record) => {
        const status = record.Status ? I18n.t('enable', {}, '启用') : I18n.t('disable', {}, '禁用');
        const content = (
          <Tag color={record.Status ? 'teal' : 'red'}>
            <strong>{status}</strong>
          </Tag>
        );
        return content;
      },
    },
    {
      title: I18n.t('operation', {}, '操作'),
      dataIndex: 'adjust',
      render: (text, record) => {
        const content = (
          <div className={styles.adjustButtom}>
            <AdjustPriorityButton
              disabled={false}
              icon={TopIcon.id}
              tooltip={I18n.t('top', {}, '置顶')}
              onClick={() => {
                onReOrder(record, CardReOrderType.Top);
              }}
            />
            <AdjustPriorityButton
              disabled={false}
              icon={UpIcon.id}
              tooltip={I18n.t('move_one_up', {}, '向上挪动一位')}
              onClick={() => {
                onReOrder(record, CardReOrderType.Up);
              }}
            />
            <AdjustPriorityButton
              disabled={false}
              icon={DownIcon.id}
              tooltip={I18n.t('move_one_down', {}, '向下挪动一位')}
              onClick={() => {
                onReOrder(record, CardReOrderType.Down);
              }}
            />
            <AdjustPriorityButton
              disabled={false}
              icon={BottomIcon.id}
              tooltip={I18n.t('bottom', {}, '置底')}
              onClick={() => {
                onReOrder(record, CardReOrderType.Bottom);
              }}
            />
            <TableHandleDom
              tabKey={tabKey}
              ruleDetail={record}
              fieldList={fieldList}
              ruleList={tableData}
              ruleGroup={ruleGroup}
              flexibleSkillGroup={flexibleSkillGroup}
              flexiblechannelType={filter.channel[0]}
              changeRuleStatus={changeRuleStatus}
            />
          </div>
        );
        return content;
      },
    },
  ];

  // New rule
  const toCreateRule = () => {
    if (isEmpty(fieldList)) {
      return;
    }
    history.push({
      pathname: '/create_flexible_rule',
      state: {
        tabKey,
        viewType: 'create',
        fieldList,
        ruleList: tableData,
        ruleGroup,
        AccessPartyId: user?.accessPartyId,
        flexibleSkillGroup,
        flexiblechannelType: filter.channel[0],
      },
    });
  };

  const getskillGroupList = () => {
    const temp = flexibleSkillGroup?.map(item => {
      const obj = {
        label: item?.name,
        value: item?.id,
      };
      return obj;
    });
    setSkillGroup(temp);
  };

  const getSkillInfo = data => {
    if (isEmpty(filter.channel)) {
      return;
    }
    // debugger;
    let constant = data?.Expression?.ConditionGroups[0]?.Conditions?.[0]?.Rhs?.Constant;

    if (filter.channel[0] === 'HOTLINE') {
      constant = constant.slice(1, constant.length - 1);
    }
    const skill = flexibleSkillGroup.filter(item => item.id === constant);
    if (skill.length === 0) {
      return {
        name: '',
        value: '',
        channelType: '',
      };
    }
    const skillInfo = skill[0];
    return skillInfo;
  };

  const getStatusInfo = data => {
    const obj = data?.ActionInfo?.ReturnValue?.Constant;
    const statusObj = safeJSONParse(obj.slice(1, obj.length - 1));
    const statusInfo = {
      SupportAndDemandStatus: {
        name: subAndDeMap[Number(statusObj?.SupportAndDemandStatus)],
        value: statusObj?.SupportAndDemandStatus,
      },
      DegreeOfImbalance: {
        name: degreeMap[Number(statusObj?.DegreeOfImbalance)],
        value: statusObj?.DegreeOfImbalance,
      },
    };
    return statusInfo;
  };

  const getDesCondtion = data => {
    // debugger;
    const str = [];
    const list = data?.Expression?.ConditionGroups?.[1]?.ConditionGroups?.[0]?.Conditions || [];
    const relation = Relation[data?.Expression?.ConditionGroups[1]?.OpGroup];
    list.forEach(item => {
      // debugger;
      const condition = (fieldList || []).filter(el => el.FieldName === item?.Lhs?.VarExpr) || [];
      const opcheck = getRuleName(item.OpCheck);
      let unit = condition[0]?.OperatorFieldvalues[item.OpCheck]?.FieldValues || '';
      if (unit !== '') {
        unit = safeJSONParse(unit)?.data[0]?.suffix || '';
      }
      let conditionValue = item?.Rhs?.Constant;
      if (unit === I18n.t('natural_number', {}, '自然数')) {
        unit = '';
      }
      if (unit === '%') {
        conditionValue = String(mul(conditionValue, 100));
      }
      const obj = {
        name: condition[0]?.FieldDisplayName,
        value: conditionValue || '',
        opcheck,
        unit,
      };
      str.push(obj);
    });
    const temp = {
      str,
      relation,
    };
    return temp;
  };
  const changeruleList = () => {
    if (fieldList.length === 0 || tableData.length === 0) {
      return;
    }
    changeLoading(true);
    const list = tableData.map(item => {
      item.skillInfo = getSkillInfo(item);
      item.statusInfo = getStatusInfo(item);
      item.desCondtions = getDesCondtion(item);
      return item;
    });
    changeLoading(false);
    return list;
  };

  /**
   * Query list data according to conditions
   * @param list list data
   */
  const getFilterData = list => {
    if (!Array.isArray(list) && !isEmpty(list)) {
      return [];
    }
    const temp = (list || []).filter(el => {
      let nameFlag = true;
      let statusFlag = true;
      let skillFlag = true;
      // let IMFlag = true;
      if (filter.serachName) {
        nameFlag = (el.DisplayName || '').indexOf(filter.serachName) !== -1;
      }
      if (filter.status?.[0]) {
        statusFlag = el.Status === Number(filter.status?.[0]);
      }
      // debugger;
      if (filter.skillGroups?.[0]) {
        skillFlag = el.skillInfo.id === filter.skillGroups?.[0];
      }

      return nameFlag && statusFlag && skillFlag;
    });
    return temp;
  };

  useEffect(() => {
    if (tableData.length === 0) {
      setCurrentData([]);
      // changeLoading(false);
      return;
    }
    let list = [];
    // changeLoading(true);
    const listCopy = changeruleList();
    list = getFilterData(listCopy);
    setCurrentData(list);
    // changeLoading(false);
  }, [filter.serachName, filter.skillGroups, filter.status, tableData, fieldList, flexibleSkillGroup]);

  useEffect(() => {
    if (isEmpty(flexibleSkillGroup)) {
      return;
    }
    getskillGroupList();
  }, [flexibleSkillGroup]);

  const content = (
    <div className={styles.tableList}>
      <div className={styles.tableTop}>
        <div className={styles.filter}>
          <Input
            showClear
            onClear={() =>
              setFilter({
                ...filter,
                serachName: '',
              })
            }
            prefix={<Icon type="search" />}
            style={{ width: 286, marginRight: 10 }}
            placeholder={I18n.t('search', {}, '搜索')}
            onEnterPress={e => {
              setFilter({
                ...filter,
                serachName: e.currentTarget.value,
              });
            }}
          />

          <ConditionFilterGroup
            onChange={e => {
              setFilter({
                ...filter,
                status: e.status || [],
                skillGroups: e.skillGroups || [],
                channel: e.channel || [],
              });
            }}
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            onRef={() => {}}
            isShowExtraOptions={false}
          >
            <MultiSelect
              field="channel"
              options={[
                { label: 'IM', value: 'IM' },
                { label: I18n.t('hotline', {}, '热线'), value: 'HOTLINE' },
              ]}
              title={I18n.t('channel', {}, '渠道')}
              isSingle
              defaultValue={filter.channel}
            />
            <MultiSelect
              field="status"
              options={[
                { label: I18n.t('enable', {}, '启用'), value: '1' },
                { label: I18n.t('disable', {}, '禁用'), value: '0' },
              ]}
              title={I18n.t('status', {}, '状态')}
              isSingle
              defaultValue={filter.status}
            />
            <MultiSelect
              field="skillGroups"
              options={skillGroup}
              title={I18n.t('skill_group', {}, '技能组')}
              isSingle
              defaultValue={filter.skillGroups}
            />
          </ConditionFilterGroup>
        </div>
        <Button theme="solid" type="primary" icon="plus_circle" onClick={toCreateRule}>
          {I18n.t('new_elastic_rule', {}, '新建弹性规则')}
        </Button>
      </div>
      <div className={styles.tips}>
        {I18n.t(
          'elastic_to_manual_rule_judgment_in_sequence_from_top_to_bottom',
          {},
          '从上而下的顺序依次进行弹性转人工规则判断'
        )}
      </div>
      <Table loading={loading || reOrdering} columns={columns} dataSource={currentData} pagination={false} />
    </div>
  );
  return content;
};

export default Index;
