import { safeJSONParse } from '@/common/utils';
import { ruleOpcheckArr } from '@/pages/createRule/createRuleType';
import { deepCopy } from '@common/utils/commonHook';
// Get the Chinese name corresponding to the operator
export const getRuleName = opKey => {
  for (let i = 0; i < ruleOpcheckArr.length; i++) {
    if (ruleOpcheckArr[i].serveOp === opKey) {
      return ruleOpcheckArr[i].clientName();
    }
  }
};

export function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min)) + min; // Not including maximum value, including minimum value
}

const getAgentInfo = data => {
  if (!data) {
    return [];
  }
  const agentStatus = safeJSONParse(data.slice(1, data.length - 1));
  const agent = agentStatus.agentStatus;
  const obj = {
    FieldName: '$agentStatus',
    OperatorId: (agent.contains || []).length !== 0 ? 'LIST_RETAIN' : 'NOT LIST_RETAIN',
    FieldValue: (agent.contains || []).length !== 0 ? agent.contains : agent.notContains,
  };
  const arr = [agentStatus, obj];
  return arr;
};

const getlist = (val, key) => {
  const list =
    val.RuleList.filter(
      el =>
        el.Expression.ConditionGroups[0].Conditions.filter(val => val.Lhs?.VarExpr === '$ruleTag')[0]?.Rhs.Constant ===
        key
    ) || [];
  return list;
};

const getConfigInfo = (condition, info) => {
  const { FeatureExpr = {} } = condition.Lhs;
  const fieldName = FeatureExpr?.FeatureName;
  const type = safeJSONParse(FeatureExpr?.ParamExprMap?.type?.Constant);
  const totalSeconds = FeatureExpr?.ParamExprMap?.timeRange?.Constant;
  const seconds = totalSeconds % 60;
  const minutes = Math.floor(totalSeconds / 60);
  return fieldName === 'query_queue_size.queue_size' ? {
    ...info,
    FieldName: fieldName,
    // maximum queue size in 3 minutes
    FieldNameCn: `${type === 'max' ? 'Maximum' : 'Minimum'} queue size in ${minutes} minutes ${seconds} seconds`,
    type,
    minutes,
    seconds,
  } : info;
};

export const changeRuleList = (dynamicRuleList, allSkillGroups, dyFieldList) => {
  const rulelistcopy = dynamicRuleList.slice();
  if (rulelistcopy?.length === 0) {
    return [];
  }
  const ruleList = [];
  const obj = {
    SkillGroupId: {
      name: '',
      value: '',
    },
    layeredConfigCondition: [],
    isEnableEVConfig: true,
    isEnableDYConfig: true,
    upDyConfigList: [],
    downDyConfigList: [],
    upEvNum: null,
    downEvNum: null,
    oncallId: null,
    upDyConfigListUp: {
      FieldName: '',
      OperatorId: '',
      FieldValue: [],
    },
    downDyConfigListDown: {
      FieldName: '',
      OperatorId: '',
      FieldValue: [],
    },
    defalutEV: null,
    isOncallId: true,
    Status: 1,
  };
  rulelistcopy.forEach(val => {
    const ruleItem = Object.assign({}, safeJSONParse(JSON.stringify(obj)), val);

    ruleItem.SkillGroupId = {
      name: allSkillGroups.filter(v => v.ID === ruleItem.ExtraInfo.skill_group_id)[0]?.Name || '',
      value: ruleItem.ExtraInfo.skill_group_id,
    };

    if (!val?.ExtraInfo?.skill_group_id) {
      const skillTemp =
        val.RuleList.filter(el =>
          el.Expression.ConditionGroups[0].Conditions.filter(val => val.Lhs?.VarExpr === '$skillGroupId')
        ) || [];
      let id = [];
      skillTemp[0].Expression.ConditionGroups[0].Conditions.forEach(item => {
        if (item.Lhs?.VarExpr === '$skillGroupId') {
          id = item.Rhs.ConstantList;
        }
      });
      ruleItem.SkillGroupId = {
        name: allSkillGroups.filter(v => v.ID === id[0])[0]?.Name || '',
        value: id[0],
      };
    }

    const layeredlist = getlist(val, '"layered"');
    const uplist = getlist(val, '"up"');
    const downlist = getlist(val, '"down"');
    (layeredlist || []).forEach(el => {
      const temp = el.Expression.ConditionGroups[0].Conditions.filter(
        el => el.Lhs?.VarExpr === '$agentWorkInSkillGroupDays'
      );
      let str = el.ActionInfo.ReturnValue.Constant;
      str = safeJSONParse(str.slice(1, str.length - 1));
      if (temp.length === 0) {
        ruleItem.defalutEV = Number(str?.maxTaskNum || str?.coefficient || 0);
        ruleItem.isCustom = str?.maxTaskNum !== null;
      } else {
        const obj = {
          start: Number(temp.filter(val => val?.OpCheck === '>' || val?.OpCheck === '>=')[0].Rhs.Constant),
          end: Number(temp.filter(val => val?.OpCheck === '<=')[0].Rhs.Constant),
          evNumber: Number(str?.maxTaskNum || str.coefficient || 0),
          key: getRandomInt(1, 100000),
          WordType: str?.maxTaskNum !== null,
        };
        ruleItem.layeredConfigCondition.push(obj);
      }
    });
    if (layeredlist.length === 0) {
      ruleItem.isEnableEVConfig = false;
      ruleItem.defalutEV = 0;
      ruleItem.layeredConfigCondition.push({
        start: 0,
        end: 0,
        evNumber: 0,
        key: getRandomInt(1, 100000),
      });
    }
    (uplist[0]?.Expression.ConditionGroups[0].Conditions || []).slice(2).forEach(item => {
      const obj = {
        FieldName: item.Lhs.FeatureExpr?.FeatureName,
        OperatorId: item.OpCheck,
        FieldValue: Number(item.Rhs.Constant),
        key: getRandomInt(1, 100000),
        FieldNameCn: dyFieldList.filter(v => v.FieldName === item.Lhs.VarExpr)?.[0]?.FieldDisplayName,
        OperatorIdCn: getRuleName(item.OpCheck),
        label: 'up',
      };
      ruleItem.upDyConfigList.push(getConfigInfo(item, obj));
    });

    const agentStatusUp = getAgentInfo(uplist[0]?.ActionInfo.ReturnValue.Constant);

    ruleItem.upDyConfigListUp = agentStatusUp[1] || [];
    ruleItem.upEvNum = Number(agentStatusUp[0]?.increaseNum);
    (downlist[0]?.Expression.ConditionGroups[0].Conditions || []).slice(2).forEach(item => {
      const obj = {
        FieldName: item.Lhs.VarExpr,
        OperatorId: item.OpCheck,
        FieldValue: Number(item.Rhs.Constant),
        key: getRandomInt(1, 100000),
        FieldNameCn: dyFieldList.filter(v => v.FieldName === item.Lhs.VarExpr)?.[0]?.FieldDisplayName,
        OperatorIdCn: getRuleName(item.OpCheck),
        label: 'down',
      };
      ruleItem.downDyConfigList.push(getConfigInfo(item, obj));
    });
    const agentStatusDown = getAgentInfo(downlist[0]?.ActionInfo.ReturnValue.Constant);
    ruleItem.downDyConfigListDown = agentStatusDown[1] || [];
    ruleItem.downEvNum = Number(agentStatusDown[0]?.increaseNum);

    const agentTempObj = {
      FieldName: '$agentStatus',
      OperatorId: '',
      FieldValue: [],
    };
    const configTempObj = {
      FieldName: '',
      OperatorId: '',
      FieldValue: 0,
      key: getRandomInt(1, 100000),
    };
    if (uplist.length === 0) {
      ruleItem.upDyConfigListUp = deepCopy(agentTempObj);
      ruleItem.upEvNu = 0;
      ruleItem.downDyConfigListDown = deepCopy(agentTempObj);
      ruleItem.downEvNum = 0;
      ruleItem.isEnableDYConfig = false;
      ruleItem.upDyConfigList.push(configTempObj);
      ruleItem.downDyConfigList.push(configTempObj);
    }
    ruleItem.oncallId = agentStatusDown[0]?.larkId || '';
    ruleItem.isOncallId = Boolean(agentStatusDown[0]?.larkId);
    ruleItem.Status = val.RuleGroupStatus;
    ruleList.push(ruleItem);
  });
  return ruleList;
};
