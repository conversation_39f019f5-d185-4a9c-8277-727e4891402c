.dynamicContent {
  margin: 16px 24px 16px 0;

  .filterGroup {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .buttonGroup {
      display: flex;
      flex-direction: row;
    }
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  margin: 28px 0;
}

.adjustButtom {

  :global {
    
    .kefu-dropdown {

      .united_route_manage-button.united_route_manage-button-with-icon-only {
        width: 24px;
        height: 24px;

        .united_route_manage-icons-default {

          color: #0b877d;
    
        }
      }

    }
    
    
  }
}
