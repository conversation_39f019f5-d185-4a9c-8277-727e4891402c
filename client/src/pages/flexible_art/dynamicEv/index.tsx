import React, { useState, useEffect, useContext } from 'react';
import { Spin, Table, Button, Tag, Pagination, Tooltip } from '@ies/semi-ui-react';
import { ConditionFilterGroup, MultiSelect } from '@ies/kefu-filter-group';
import styles from './index.scss';
import { useHistory } from 'react-router-dom';
import TableHandleDom from '../tableHandleDom';
import { DynamicEvProps } from './dynamicEv';
import { changeRuleList } from './use';
import { deepCopy, deepCopyv2 } from '@/common/utils/commonHook';
import { UserContext } from '@context/user';
import { I18n } from '@ies/starling_intl';

const DynamicEv: React.FC<DynamicEvProps> = props => {
  const {
    loading,
    tabKey,
    limit,
    currentPage,
    onPaginationChange,
    filter,
    changeFilterValue,
    filterSkillGroups,
    editSkillGroups,
    createSkillGroups,
    dyFieldList,
    allSkillGroups,
    dyTotal,
    dynamicRuleList,
    UpdateRuleGroupStatus,
    beConfigList,
  } = props;
  const user = useContext(UserContext);
  const [skillGroup, setSkillGroup] = useState([]);
  const [currentData, setCurrentData] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);
  const history = useHistory();

  useEffect(() => {
    if (allSkillGroups.length === 0 || dyFieldList.length === 0) {
      return;
    }
    setLocalLoading(true);
    const rulelist = changeRuleList(dynamicRuleList, allSkillGroups, dyFieldList);
    setCurrentData(rulelist);
    setLocalLoading(false);
  }, [dynamicRuleList, allSkillGroups, dyFieldList]);
  useEffect(() => {
    setSkillGroup(filterSkillGroups);
  }, [filterSkillGroups]);

  // Jump to edit page
  const toCreateRule = (key: string) => {
    if ((dyFieldList || []).length === 0) {
      return;
    }
    const state = {
      viewType: 'create',
      tabKey,
      skillGroup: [],
      dyFieldList,
      allSkillGroups,
      beConfigList: [],
      AccessPartyId: user?.accessPartyId,
    };
    console.log('==key===', key);
    // Need to carry different skill sets
    if (key === 'batchUpdate') {
      state.skillGroup = editSkillGroups.slice();
      state.beConfigList = beConfigList.slice();
      state.viewType = 'edit';
    } else if (key === 'create') {
      state.skillGroup = createSkillGroups.slice();
    }
    // Judgment with a pre-skill set
    history.push({
      pathname: '/create_dynamic_rule',
      state,
    });
  };
  const getDisplayRule = data => {
    let list = [];
    let layeredList = [];
    if (data.isEnableEVConfig) {
      layeredList = deepCopyv2(data.layeredConfigCondition);
    }
    if (data.isEnableDYConfig) {
      list = list.concat(data.upDyConfigList, data.downDyConfigList);
    }
    if (list.length > 2) {
      list.length = 2;
    }

    if (layeredList.length > 2) {
      layeredList.length = 2;
    }
    let content = null;
    if (list.length !== 0) {
      content = (
        <div>
          {list.map(item => {
            const val = (
              <div key={item.key}>
                {I18n.t(
                  'when_{placeholder0}_{placeholder1}_{placeholder2}__automatically',
                  {
                    placeholder0: item.FieldNameCn,
                    placeholder1: item.OperatorIdCn,
                    placeholder2: item.FieldValue
                  },
                  '{placeholder0}{placeholder1}{placeholder2}时， 自动'
                )}
                {item.label === 'up' ? I18n.t('add', {}, '加') : I18n.t('minus', {}, '减')}
                {item.label === 'up' ? data.upEvNum : data.downEvNum}
              </div>
            );
            return val;
          })}
        </div>
      );
    } else {
      content = (
        <div>
          {layeredList.map(item => {
            const val = (
              <div key={item.key}>
                {I18n.t(
                  'the_number_of_seat_order_days_belongs_to_{placeholder1}_days~_{placeholder3}_day',
                  { placeholder1: item.start, placeholder3: item.end },
                  '坐席接单天数属于{placeholder1}天 ~ {placeholder3}天,'
                )}
                {item.WordType ?
                  I18n.t('number_of_staff_to_guests', {}, '人员对客数') :
                  I18n.t('order_upper_limit_coefficient', {}, '接单上限系数')}
                {I18n.t('equal_to_{placeholder1}', { placeholder1: item.evNumber }, '等于{placeholder1}')}
              </div>
            );
            return val;
          })}
        </div>
      );
    }
    return content;
  };

  const getTooltipContent = data => {
    let list = [];
    let layeredList = [];
    if (data.isEnableEVConfig) {
      layeredList = data.layeredConfigCondition;
    }
    if (data.isEnableDYConfig) {
      list = list.concat(data.upDyConfigList, data.downDyConfigList);
    }
    const content = (
      <div>
        {(list || []).map(item => {
          const val = (
            <div key={item.key}>
              {I18n.t(
                'when_{placeholder0}_{placeholder1}_{placeholder2}__automatically',
                { placeholder0: item.FieldNameCn, placeholder1: item.OperatorIdCn, placeholder2: item.FieldValue },
                '{placeholder0}{placeholder1}{placeholder2}时， 自动'
              )}
              {item.label === 'up' ? I18n.t('add', {}, '加') : I18n.t('minus', {}, '减')}
              {item.label === 'up' ? data.upEvNum : data.downEvNum}
            </div>
          );
          return val;
        })}
        {(layeredList || []).map(item => {
          const val = (
            <div key={item.key}>
              {I18n.t(
                'the_number_of_seat_order_days_belongs_to_{placeholder1}_days~_{placeholder3}_day',
                { placeholder1: item.start, placeholder3: item.end },
                '坐席接单天数属于{placeholder1}天 ~ {placeholder3}天,'
              )}
              {item.WordType ?
                I18n.t('number_of_staff_to_guests', {}, '人员对客数') :
                I18n.t('order_upper_limit_coefficient', {}, '接单上限系数')}
              {I18n.t('equal_to_{placeholder1}', { placeholder1: item.evNumber }, '等于{placeholder1}')}
            </div>
          );
          return val;
        })}
      </div>
    );
    return content;
  };

  const columns = [
    {
      title: I18n.t('skill_group', {}, '技能组'),
      dataIndex: 'skillGruop',
      minWidth: 200,
      render: (text, record) => {
        const content = <div>{record.SkillGroupId.name}</div>;
        return content;
      },
    },
    {
      title: I18n.t('regulation_rules', {}, '调控规则'),
      dataIndex: 'ruleList',
      minWidth: 380,
      render: (text, record) => {
        const content = (
          <Tooltip style={{ minWidth: 330 }} content={getTooltipContent(record)}>
            <div>{getDisplayRule(record)}</div>
          </Tooltip>
        );
        return content;
      },
    },
    {
      title: I18n.t('status', {}, '状态'),
      dataIndex: 'Status',
      minWidth: 100,
      render: (text, record) => {
        const status = record.RuleGroupStatus ? I18n.t('enable', {}, '启用') : I18n.t('disable', {}, '禁用');
        const content = (
          <Tag color={record.RuleGroupStatus ? 'teal' : 'red'}>
            <strong>{status}</strong>
          </Tag>
        );
        return content;
      },
    },
    {
      title: I18n.t('update_person', {}, '更新人'),
      dataIndex: 'updaterName',
      minWidth: 100,
    },
    {
      title: I18n.t('update_time', {}, '更新时间'),
      dataIndex: 'UpdaterTime',
      minWidth: 200,
    },
    {
      dataIndex: 'operator',
      minWidth: 112,
      render: (text, record) => {
        const content = (
          <div className={styles.adjustButtom}>
            <TableHandleDom
              tabKey={tabKey}
              ruleDetail={record}
              dyFieldList={dyFieldList}
              skillGroup={editSkillGroups}
              ruleList={currentData}
              UpdateRuleGroupStatus={UpdateRuleGroupStatus}
              allSkillGroups={allSkillGroups}
              beConfigList={beConfigList}
            />
          </div>
        );
        return content;
      },
    },
  ];

  const content = (
    <Spin spinning={loading || localLoading}>
      <div className={styles.dynamicContent}>
        <div className={styles.filterGroup}>
          <div>
            <ConditionFilterGroup
              onChange={e => changeFilterValue(e)}
              // eslint-disable-next-line @typescript-eslint/no-empty-function
              onRef={() => {}}
              isShowExtraOptions={false}
            >
              <MultiSelect
                field="status"
                options={[
                  { label: I18n.t('enable', {}, '启用'), value: '1' },
                  { label: I18n.t('disable', {}, '禁用'), value: '0' },
                ]}
                title={I18n.t('status', {}, '状态')}
                isSingle
                defaultValue={filter.status}
              />
              <MultiSelect
                field="skillGroups"
                options={skillGroup}
                title={I18n.t('skill_group', {}, '技能组')}
                isSingle
                defaultValue={filter.skillGroups}
              />
            </ConditionFilterGroup>
          </div>
          <div className={styles.buttonGroup}>
            <Button
              theme="solid"
              type="primary"
              icon="setting"
              onClick={() => toCreateRule('batchUpdate')}
              style={{ marginRight: 8 }}
            >
              {I18n.t('batch_control', {}, '批量调控')}
            </Button>
            <Button theme="solid" type="primary" icon="plus_circle" onClick={() => toCreateRule('create')}>
              {I18n.t('new_rule', {}, '新建规则')}
            </Button>
          </div>
        </div>
        <Table columns={columns} dataSource={currentData} pagination={false} />
        <div className={styles.pagination}>
          <Pagination
            key={limit}
            total={dyTotal}
            showSizeChanger
            pageSize={limit}
            pageSizeOpts={[10, 50, 100, 200]}
            currentPage={currentPage}
            onChange={onPaginationChange}
            style={{ padding: '5px 16px' }}
          />
        </div>
      </div>
    </Spin>
  );
  return content;
};

export default DynamicEv;
