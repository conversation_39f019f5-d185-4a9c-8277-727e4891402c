
export type DynamicEvProps = {
  loading: boolean;
  tabKey: string;
  limit: number;
  currentPage: number;
  onPaginationChange: (currentPage: number, pageSize: number) => void;
  filter: filter;
  changeFilterValue: (e: filter) => void;
  filterSkillGroups: Array<SkillGroupItem>;
  editSkillGroups: Array<SkillGroupItem>;
  createSkillGroups: Array<SkillGroupItem>;
  dyFieldList: Array<any>;
  allSkillGroups: Array<any>;
  dyTotal: number;
  dynamicRuleList: Array<any>;
  UpdateRuleGroupStatus: (type: any, ruleData: any) => Promise<void>;
  beConfigList: Array<any>;
};

export type filter = {
  status: Array<string>;
  skillGroups: Array<string>;
};

export type SkillGroupItem = {
  label: string;
  value: string;
  children?: Array<SkillGroupItem>;
};

