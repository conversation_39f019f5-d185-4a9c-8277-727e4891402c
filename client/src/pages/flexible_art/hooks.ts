import { GetRuleListV2Request, demoClient, ClientRule } from '@http_idl/demo';
import {
  FLEXIBLE_ART_EVENT_ID,
  FLEXIBLE_ART_ROUTE,
  ApiAuthFailedMsg,
  DYNAMIC_EVENT_ID,
  DYNAMIC_EVENT_KEY,
  FLEXIBLE_HOT_LINE_ROUTE,
  FLEXIBLE_HOT_LINE_EVENT_ID,
  FLEXIBLE_CONFIG_KEY
} from '@constants/property';
import { useBizTypeList, useNewFieldList } from '@hooks/index';
import { useEffect, useState, useContext, useRef } from 'react';
import { UserContext } from '@context/user';
import { Toast } from '@ies/semi-ui-react';
import * as t from '@http_idl/demo';
import { getAccessPartyName } from '@common/utils/hook';
import { OperationType, CardReOrderType } from '@/const/enums';
// import { reportCustomFmp } from '@hooks/sendReport';
import { isEmpty } from 'lodash';
import { useHistory } from 'react-router-dom';
import { I18n } from '@ies/starling_intl';
import { errorReporting } from '@/common/utils/errorReporting';

enum ChannelType {
  'IM' = 1,
  '新工单' = 2,
  '电话' = 3,
  '质检' = 8,
  '电商工单' = 5,
}

enum FlexibleFilterChanel {
  IM = 'IM',
  HOTLINE = 'HOTLINE',
}
export const useRoutingRule = () => {
  const history = useHistory();
  const user = useContext(UserContext);
  const { bizTypeList, loaded: bizTypeListLoaded } = useBizTypeList();
  const [loading, setLoading] = useState(false);
  const [ruleGroup, setRuleGroup] = useState('');
  const [ruleList, setRuleList] = useState([]);

  const getflexibleFilter = () => {
    const { flexibelChannel } = Object.assign({ flexibelChannel: 'IM' }, history.location.state || {});
    const temp = {
      serachName: '',
      channel: [flexibelChannel],
      status: [],
      skillGroups: [],
    };
    return temp;
  };

  const [flexibleFilter, setflexibleFilter] = useState(() => getflexibleFilter());
  const [eventId, setEventId] = useState('');
  const { fieldList = [] } = useNewFieldList({
    // Get options
    eventId,
    appids: bizTypeList.map(v => v?.ID?.toString()),
    when: bizTypeListLoaded,
  });
  const [flexibleSkillGroup, setFlexibleSkillGroup] = useState([]);
  /**
   * Acquire skill sets
   * @returns
   */
  // const getFlexibleSkillGroup = async () => {
  //   if (isEmpty(flexibleFilter.channel)) {
  //     return;
  //   }
  //   const params = {
  //     TenantId: '1',
  //     ChannelTypes: [filterChannelType[flexibleFilter.channel?.[0]]],
  //     AccessPartyIds: [user?.accessPartyId],
  //     ConfigKeys: [FLEXIBLE_CONFIG_KEY],
  //   };

  //   const [err, res] = await to(demoClient.GetConfigInfos(params));
  //   if (err) {
  //     Toast.error(`${err}`);
  //     return;
  //   }
  //   let flexibleSkillGroup = res.ConfigInfos.filter(el => el.ConfigKey === FLEXIBLE_CONFIG_KEY);
  //   if (!isEmpty(flexibleSkillGroup)) {
  //     flexibleSkillGroup = flexibleSkillGroup.map(el => JSON.parse(el?.Data));
  //   }
  //   setFlexibleSkillGroup(flattenDeep(flexibleSkillGroup));
  // };

  const changeEventId = () => {
    if (isEmpty(flexibleFilter.channel)) {
      return;
    }
    const eventIdTemp =
      flexibleFilter.channel?.[0] !== FlexibleFilterChanel.IM ? FLEXIBLE_HOT_LINE_EVENT_ID : FLEXIBLE_ART_EVENT_ID;
    setEventId(eventIdTemp);
  };

  useEffect(() => {
    // getFlexibleSkillGroup();
    changeEventId();
    setflexibleFilter({
      ...flexibleFilter,
      skillGroups: [],
    });
    // setRuleList([]);
    if (isEmpty(flexibleFilter.channel)) {
      setflexibleFilter({
        ...flexibleFilter,
        channel: ['IM'],
      });
    }
    // You need to clear the skill set when switching.
    setFlexibleSkillGroup([]);
  }, [flexibleFilter.channel]);
  /**
   * Get the search criteria for elastic to manual
   * @param value
   */
  const getChangeFlexibleFilter = value => {
    // debugger
    setflexibleFilter(value);
  };

  // dynamic
  const { fieldList: dyFieldList = [] } = useNewFieldList({
    // Get options
    eventId: DYNAMIC_EVENT_ID,
    appids: bizTypeList.map(v => v?.ID?.toString()),
    when: bizTypeListLoaded,
  });

  const [dynamicRuleList, setDynamicRuleList] = useState([]); // List page data
  const [dynamicRuleGroup, setDynamicRuleGroup] = useState('');
  const [filterSkillGroups, setFilterSkillGroups] = useState([]);
  const [editSkillGroups, setEditSkillGroups] = useState([]);
  const [createSkillGroups, setCreateSkillGroups] = useState([]);
  const [allSkillGroups, setAllSkillGroups] = useState([]);
  const [beConfigList, setBeConfigList] = useState([]); // Rules skill set configured
  // Dynamic 1v
  const [limit, setLimit] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState({ status: [], skillGroups: [] });
  const [dyTotal, setDyTotal] = useState(0);

  const getlistRef = useRef(0);
  /**
   * Get Elastic List Data
   */
  const getRuleList = async (): Promise<void> => {
    // Get rules
    setLoading(true);
    if (isEmpty(flexibleFilter.channel)) {
      return;
    }
    const req: GetRuleListV2Request = {
      EventKey: flexibleFilter.channel?.[0] !== FlexibleFilterChanel.IM ? FLEXIBLE_HOT_LINE_ROUTE : FLEXIBLE_ART_ROUTE,
      AccessPartyId: user?.accessPartyId || '',
      IsShift: true,
    };

    const res = await demoClient.getNewRuleList(req);
    if (res.code === 200) {
      const list: Array<ClientRule> = (res.data || []).sort((a, b) => a.Priority - b.Priority);
      const tempRuleGroup = list?.[0]?.RuleGroupId || '';
      setRuleGroup(tempRuleGroup);
      getlistRef.current += 1;
      setRuleList(list);
    } else {
      Toast.error(
        I18n.t('flexible_to_manual_list_acquisition_failed__please_refresh', {}, '弹性转人工列表获取失败, 请刷新')
      );
    }
    setLoading(false);
  };

  // Get a list of guest configuration rules
  const getEvRuleList = async (): Promise<void> => {
    setLoading(true);
    const req: t.GetRuleGroupListByEventKeyRequest = {
      EventKey: DYNAMIC_EVENT_KEY,
      AccessPartId: user?.accessPartyId || '',
      Page: currentPage,
      PageSize: limit,
      RuleGroupStatus: filter.status?.map(el => Number(el)),
      SkillGroupId: filter.skillGroups[0],
    };
    try {
      const res = await demoClient.GetRuleGroupListByEventKey(req);
      if (res.code === 0) {
        setDyTotal(res.Count);
        setDynamicRuleList(res.RuleGroupList);
      }
      setLoading(false);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getEvRuleList' });
      Toast.error(I18n.t('failed_to_get_the_number_of_guests_list__please_refresh', {}, '对客数列表获取失败, 请刷新'));
    } finally {
      setLoading(false);
    }
  };

  // Get configured rules skill set information
  const beConfigSkillList = async () => {
    const params = {
      AccessPartId: user?.accessPartyId || '',
      EventKey: DYNAMIC_EVENT_KEY,
      ExtraKeys: ['skill_group_id'],
      Scenes: 'rule_group',
    };
    try {
      const res = await t.demoClient.GetExtraInfoV2(params);
      if (res.code !== 0) {
        Toast.error(
          I18n.t(
            'failed_to_get_list_of_configured_rule_skill_groups__please_refresh',
            {},
            '获取已配置规则技能组列表失败，请刷新'
          )
        );
      }
      setBeConfigList(res.RuleGroupRelations);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'beConfigSkillList' });
      Toast.error(
        I18n.t(
          'failed_to_get_list_of_configured_rule_skill_groups__please_refresh',
          {},
          '获取已配置规则技能组列表失败，请刷新'
        )
      );
    }
  };

  // Processing skill set
  const changeskillGroup = data => {
    const temp = [];
    (data || []).forEach(el => {
      const isNewType = temp.filter(val => val.value === el.ChannelType).length !== 0;
      // Todo Dynamic 1v only requires im channel
      if (!isNewType && el.ChannelType === ChannelType.IM) {
        const channelObj = {
          label: ChannelType[el.ChannelType],
          value: el.ChannelType,
          children: [],
        };
        temp.push(channelObj);
      }
      const skillObj = {
        label: el.Name,
        value: el.ID,
      };
      temp.map(item => {
        if (item.value === el.ChannelType) {
          item.children.push(skillObj);
        }
        return item;
      });
    });
    return temp;
  };

  // Get the number of guests skillset
  const getskillGroup = async () => {
    setLoading(true);
    const filterArr = ['2', '3', '16', '17', '19', '20'];
    const accessPartyIdFlag = filterArr.includes(user.accessPartyId);
    try {
      const params = {
        TenantId: '1',
        PageNo: 1,
        PageSize: 2000,
        AccessPartyId: user.accessPartyId,
        OnlySimpleData: true,
        ChannelTypes: [1, 2, 3, 8], // IM ticket, phone number, quality inspection
      };
      if (accessPartyIdFlag) {
        params.ChannelTypes.push(5);
      }

      const res = await t.demoClient.GetSkillGroupsByType(params);
      if (res.code !== 0) {
        Toast.error(I18n.t('failed_to_acquire_skill_set', {}, '获取技能组失败'));
      }
      setAllSkillGroups(res.SkillGroups || []);
      // Todo IM Dynamic 1V only show im skill set
      const temp = res.SkillGroups.filter(itemSkillGroup => itemSkillGroup.ChannelType === ChannelType.IM).map(item => {
        const obj = {
          label: item.Name,
          value: item.ID,
        };
        return obj;
      });
      setFilterSkillGroups(temp);
      setLoading(false);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getskillGroup' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (getlistRef.current === 0) {
      return;
    }
    // Todo: reporting event tracking
    // reportCustomFmp();
  }, [ruleList]);

  useEffect(() => {
    if (user?.accessPartyId) {
      //   getRuleList();
      getskillGroup();
      beConfigSkillList();
    }
  }, [user?.accessPartyId, flexibleFilter.channel]);
  let add = 0;
  // Separate the skill sets required for batch control and new rules
  const getCategorySkill = () => {
    if (allSkillGroups.length === 0) {
      return;
    }
    add++;
    const _beConfigList = new Set(beConfigList.map(v => v.extraInfos.skill_group_id));
    const editSkillGroupsTemp = allSkillGroups.filter(item => _beConfigList.has(item.ID));
    setEditSkillGroups(changeskillGroup(editSkillGroupsTemp));
    const createSkillGroupsTemp = allSkillGroups.filter(item => !_beConfigList.has(item.ID));
    setCreateSkillGroups(changeskillGroup(createSkillGroupsTemp));
  };
  useEffect(() => {
    getCategorySkill();
    // Separate skill sets
  }, [beConfigList, allSkillGroups]);

  const changeLoading = status => {
    setLoading(status);
  };

  // Enable, disable, delete
  const changeRuleStatus = async (type, ruleData): Promise<void> => {
    const req: t.UpdateRuleStatusV2Request = {
      Ids: [ruleData.Id || ruleData.RuleGroupId],
      RuleStatus: -1,
      UpdaterAgentId: user?.agent.ID,
      Version: 'V2',
      AccessPartyId: getAccessPartyName(),
      DisplayName: ruleData.DisplayName,
      RouteTypeStr: I18n.t('flexible_to_manual', {}, '弹性转人工'),
    };
    const onFailure = typeText => {
      Toast.error(
        `${I18n.t(
          'the_{placeholder1}_rule_has_been_{typetext}',
          { placeholder1: ruleData?.displayName, typeText },
          '「{placeholder1}」规则已{typeText}'
        )}`
      );
      setLoading(false);
    };

    setLoading(true);
    let errText = '';
    if (type === OperationType.Enable) {
      req.RuleStatus = 1;
      errText = I18n.t('enable', {}, '启用');
    }
    if (type === OperationType.Disable) {
      req.RuleStatus = 0;
      errText = I18n.t('disable', {}, '禁用');
    }
    if (type === OperationType.Delete) {
      req.RuleStatus = 3;
      errText = I18n.t('delete', {}, '删除');
    }

    try {
      const res = await t.demoClient.UpdateRuleStatus(req);
      if (res.code === 200) {
        // getRuleList();
      } else {
        onFailure(errText);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getskillGroup' });
    } finally {
      setLoading(false);
    }
  };

  const [reOrdering, setReOrdering] = useState(false);
  //   async function onReOrder(ruleData: t.ClientRule, type: CardReOrderType) {
  //     const index = ruleList.findIndex(card => ruleData.Id === card.Id);

  //     if (index === 0 && (type === CardReOrderType.Top || type === CardReOrderType.Up)) {
  //       Toast.success ('The operation was successful, the configuration is already in the first place');
  //       return;
  //     }

  //     if (index === ruleList.length - 1 && (type === CardReOrderType.Down || type === CardReOrderType.Bottom)) {
  //       Toast.success ('The operation was successful, the configuration is at the end');
  //       return;
  //     }

  //     setReOrdering(true);

  //     const newCardList: t.ClientRule[] = ruleList.filter(card => card.Id !== ruleData.Id);
  //     switch (type) {
  //       case CardReOrderType.Top:
  //         newCardList.unshift(ruleData);
  //         break;
  //       case CardReOrderType.Bottom:
  //         newCardList.push(ruleData);
  //         break;
  //       case CardReOrderType.Up:
  //         newCardList.splice(index - 1, 0, ruleData);
  //         break;
  //       case CardReOrderType.Down:
  //         newCardList.splice(index + 1, 0, ruleData);
  //         break;
  //       default:
  //         break;
  //     }
  //     try {
  //       await saveAdjust(newCardList);
  //       Toast.success ('operation succeeded');
  //       getRuleList();
  //     } catch (error) {
  //       Toast.error (error.message | | 'Failed to adjust order');
  //     } finally {
  //       setReOrdering(false);
  //     }
  //   }

  // Pager
  const onPaginationChange = (currentPage: number, pageSize: number) => {
    setLimit(pageSize);
    setCurrentPage(currentPage);
  };

  // Number of passengers condition
  const changeFilterValue = e => {
    setFilter(e);
  };
  // Enable, disable, delete the number of guests
  const UpdateRuleGroupStatus = async (type, ruleData): Promise<void> => {
    const req: t.UpdateRuleGroupStatusRequest = {
      RuleGroupId: ruleData.RuleGroupId,
      RuleStatus: -1,
      UpdaterAgentId: user?.agent.ID,
    };
    setLoading(true);
    let errText = '';
    if (type === OperationType.Enable) {
      req.RuleStatus = 1;
      errText = I18n.t('enable', {}, '启用');
    }
    if (type === OperationType.Disable) {
      req.RuleStatus = 0;
      errText = I18n.t('disable', {}, '禁用');
    }
    if (type === OperationType.Delete) {
      req.RuleStatus = 3;
      errText = I18n.t('delete', {}, '删除');
    }
    const onFailure = typeText => {
      Toast.error(`${I18n.t('rules_have_been_{typetext}', { typeText }, '规则已{typeText}')}`);
      setLoading(false);
    };
    try {
      const res = await t.demoClient.UpdateRuleGroupStatus(req);
      if (res.code === 0) {
        getEvRuleList();
      } else {
        onFailure(errText);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'UpdateRuleGroupStatus' });
    } finally {
      setLoading(false);
    }
  };

  // Get the data of the list page of the customer configuration page
  useEffect(() => {
    if (user?.accessPartyId) {
      getEvRuleList();
    }
  }, [limit, currentPage, filter, user?.accessPartyId]);
  return {
    loading,
    changeLoading,
    bizTypeList,
    ruleList,
    // onReOrder,
    ruleGroup,
    getRuleList,
    reOrdering,
    fieldList,
    changeRuleStatus,
    flexibleFilter,
    getChangeFlexibleFilter,
    flexibleSkillGroup,

    limit,
    currentPage,
    onPaginationChange,
    filter,
    changeFilterValue,
    filterSkillGroups,
    editSkillGroups,
    createSkillGroups,
    dyFieldList,
    allSkillGroups,
    dyTotal,
    dynamicRuleList,
    beConfigList,
    UpdateRuleGroupStatus,
  };
};
// multiply
export const mul = (arg1, arg2) => {
  let digits = 0;
  const s1 = arg1.toString();
  const s2 = arg2.toString();
  try {
    digits += s1.split('.')[1].length;
  } catch (error) {
    errorReporting({ error, type: 'callback_name', name: 'mul' });
  }
  try {
    digits += s2.split('.')[1].length;
  } catch (error) {
    errorReporting({ error, type: 'callback_name', name: 'mul_s2' });
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, digits);
};
