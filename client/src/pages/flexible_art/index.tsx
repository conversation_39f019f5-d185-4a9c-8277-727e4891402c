import React, { useState, useEffect } from 'react';
import * as styles from './index.scss';
import RouterPageHeader from '../ruleConfigHead';
import { Tabs, TabPane } from '@ies/semi-ui-react';
import { useHistory } from 'react-router-dom';
// import Table from './table';
import { useRoutingRule } from './hooks';
import DynamicEv from './dynamicEv/index';
import { I18n } from '@ies/starling_intl';
const RouterPage: React.FC = () => {
  const [defaultItemKey, setDefaultItemKey] = useState('dynamic');
  // Toggle tab
  const changeActiveKey = key => {
    setDefaultItemKey(key);
  };

  const {
    loading,
    // ruleList, // List page data
    // fieldList, // Get edit page rule selection list
    // ruleGroup, // Edit page creation rules required
    // changeRuleStatus, // Operation column handler
    // reOrdering,
    // onReOrder,//Adjust rule priority
    // changeLoading,
    // flexibleFilter,
    // getChangeFlexibleFilter,
    // flexibleSkillGroup,

    limit,
    currentPage,
    onPaginationChange,
    filter,
    changeFilterValue,
    filterSkillGroups,
    editSkillGroups,
    createSkillGroups,
    dyFieldList,
    allSkillGroups,
    dyTotal,
    dynamicRuleList,
    beConfigList,
    UpdateRuleGroupStatus,
  } = useRoutingRule();
  const history = useHistory();
  const { tabKey } = Object.assign({ tabKey: '' }, history.location.state || {});
  const jumpBack = () => {
    if (tabKey) {
      setDefaultItemKey(tabKey);
    }
  };

  useEffect(() => {
    jumpBack();
  }, []);
  const content = (
    <div className={styles.ruleConfigContent}>
      <RouterPageHeader />
      <Tabs
        onChange={key => changeActiveKey(key)}
        defaultActiveKey={defaultItemKey}
        activeKey={defaultItemKey}
        keepDOM={false}
      >
        <TabPane tab={I18n.t('number_of_passengers_configuration', {}, '对客数配置')} itemKey="dynamic">
          <DynamicEv
            loading={loading}
            tabKey={defaultItemKey}
            limit={limit}
            dyTotal={dyTotal}
            currentPage={currentPage}
            onPaginationChange={onPaginationChange}
            filter={filter}
            changeFilterValue={changeFilterValue}
            filterSkillGroups={filterSkillGroups}
            editSkillGroups={editSkillGroups}
            createSkillGroups={createSkillGroups}
            dyFieldList={dyFieldList}
            allSkillGroups={allSkillGroups}
            dynamicRuleList={dynamicRuleList}
            UpdateRuleGroupStatus={UpdateRuleGroupStatus}
            beConfigList={beConfigList}
          />
        </TabPane>
      </Tabs>
    </div>
  );

  return content;
};

export default RouterPage;
