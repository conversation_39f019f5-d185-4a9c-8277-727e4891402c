export type TableHadleProps = {
  ruleDetail: any;
  tabKey: string;
  fieldList?: any[];
  ruleList?: any[];
  ruleGroup?: string;
  changeRuleStatus?: (type: any, ruleData: any) => Promise<void>;
  UpdateRuleGroupStatus?: (type: any, ruleData: any) => Promise<void>;
  skillGroup?: any[];
  allSkillGroups?: any[];
  dyFieldList?: any[];
  beConfigList?: any[];
  flexibleSkillGroup?: any[];
  flexiblechannelType?: string;
};
