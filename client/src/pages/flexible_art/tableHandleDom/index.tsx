import React, { useState, useEffect, useMemo, useContext } from 'react';
import { Icon, Modal } from '@ies/semi-ui-react';
import { useHistory } from 'react-router-dom';
import { TableHadleProps } from './tablehandleDom';
import { KefuDropdown } from '@ies/kefu-components';
import { UserContext } from '@context/user';
import { isEmpty } from 'lodash';
import { I18n } from '@ies/starling_intl';

const TableHandleDom: React.FC<TableHadleProps> = data => {
  const history = useHistory();
  const {
    ruleDetail,
    tabKey,
    fieldList,
    ruleList,
    ruleGroup,
    changeRuleStatus,
    skillGroup,
    allSkillGroups,
    dyFieldList,
    beConfigList,
    UpdateRuleGroupStatus,
    flexibleSkillGroup = [],
    flexiblechannelType = 'IM',
  } = data;
  const user = useContext(UserContext);
  const [visible, setVisible] = useState(false);
  const [titleText, setTitleText] = useState('');
  const [title, setTitle] = useState('');
  const [operatType, setOperatType] = useState('');
  const handleCancel = () => {
    setVisible(false);
  };

  const ruleAdit = () => {
    switch (tabKey) {
      case 'flexibleArt':
        if (isEmpty(fieldList) || isEmpty(flexibleSkillGroup)) {
          return;
        }
        // debugger;
        history.push({
          pathname: '/create_flexible_rule',
          state: {
            tabKey: 'flexibleArt',
            ruleDetail,
            viewType: 'edit', // Should also require an array of skill sets
            fieldList, // Conditional rule group
            ruleList, // rule group
            ruleGroup, // Required to create rules
            AccessPartyId: user?.accessPartyId,
            flexibleSkillGroup,
            flexiblechannelType,
          },
        });
        break;
      case 'dynamic':
        if (dyFieldList?.length === 0 || allSkillGroups?.length === 0) {
          return;
        }
        history.push({
          pathname: '/create_dynamic_rule',
          state: {
            tabKey: 'dynamic',
            ruleDetail,
            viewType: 'edit',
            dyFieldList, // Conditional rule group
            skillGroup,
            allSkillGroups,
            beConfigList,
            AccessPartyId: user?.accessPartyId,
          },
        });
        break;
      default:
        break;
    }
  };

  const ruleView = () => {
    if (isEmpty(fieldList) || isEmpty(flexibleSkillGroup)) {
      return;
    }
    // debugger;
    switch (tabKey) {
      case 'flexibleArt':
        history.push({
          pathname: '/create_flexible_rule',
          state: {
            tabKey: 'flexibleArt',
            ruleDetail,
            viewType: 'view',
            fieldList, // Conditional rule group
            ruleList, // rule group
            ruleGroup, // Required to create rules
            AccessPartyId: user?.accessPartyId,
            flexibleSkillGroup,
            flexiblechannelType,
          },
        });
        break;
      default:
        break;
    }
  };
  const openModal = (text: string) => {
    setVisible(true);
    let txt = I18n.t('flexible_to_manual', {}, '弹性转人工');
    if (tabKey === 'dynamic') {
      txt = I18n.t('number_of_passengers_configuration', {}, '对客数配置');
    }
    let titleText = '';
    let Title = '';
    let opk = '';
    switch (text) {
      case 'del':
        titleText = I18n.t(
          'once_deleted__the_data_will_not_be_recovered__please_operate_with_caution',
          {},
          '一旦删除，数据将无法恢复，请谨慎操作'
        );
        Title = I18n.t('delete', {}, '删除');
        opk = 'del';
        break;
      case 'enable':
        titleText = I18n.t(
          'when_enabled__the_rule_will_take_effect_in_two_minutes_',
          {},
          '启用后，该规则将在两分钟后生效.'
        );
        Title = `${I18n.t(
          'enable_{txt}__{placeholder3}_',
          { txt, placeholder3: ruleDetail.DisplayName || I18n.t('rules', {}, '规则') },
          '启用{txt}「{placeholder3}」'
        )}`;
        opk = 'enable';
        break;
      case 'disable':
        titleText = I18n.t(
          'when_disabled__the_rule_will_take_effect_after_two_minutes_',
          {},
          '禁用后，该规则将在两分钟后生效.'
        );
        Title = `${I18n.t(
          'disable_{txt}__{placeholder3}_',
          { txt, placeholder3: ruleDetail.DisplayName || I18n.t('rules', {}, '规则') },
          '禁用{txt}「{placeholder3}」'
        )}`;
        opk = 'disable';
        break;
      default:
        break;
    }
    setTitleText(titleText);
    setTitle(Title);
    setOperatType(opk);
    setVisible(true);
  };

  const handleOk = () => {
    if (tabKey === 'flexibleArt') {
      changeRuleStatus(operatType, ruleDetail);
    } else if (tabKey === 'dynamic') {
      UpdateRuleGroupStatus(operatType, ruleDetail);
    }
    setVisible(false);
  };
  const dropDown = [
    { text: I18n.t('enable', {}, '启用'), onClick: () => openModal('enable') },
    { text: I18n.t('disable', {}, '禁用'), onClick: () => openModal('disable') },
    { text: I18n.t('view', {}, '查看'), onClick: () => ruleView() },
    { text: I18n.t('edit', {}, '编辑'), onClick: () => ruleAdit() },
    { text: I18n.t('delete', {}, '删除'), type: 'danger', onClick: () => openModal('del') },
  ];
  const dropdownItemsFlexibel = useMemo(() => {
    if (tabKey !== 'flexibleArt') {
      return;
    }
    let temp = [];
    switch (ruleDetail.Status) {
      case 1:
        return dropDown.slice(1, 4);
      case 0:
        temp = dropDown.slice();
        temp.splice(1, 1);
        return temp;
      default:
        return [];
    }
  }, [ruleDetail, tabKey]);

  const dropdownItemsEv = useMemo(() => {
    if (tabKey !== 'dynamic') {
      return;
    }
    let temps = [];
    switch (ruleDetail.Status) {
      case 1:
        temps = dropDown.slice(1, 4);
        temps.splice(1, 1);
        return temps;
      case 0:
        temps = dropDown.slice();
        temps.splice(1, 2);
        return temps;
      default:
        return [];
    }
  }, [ruleDetail, tabKey]);
  // const dropdownItemsFlexibel = ruleDetail.Status ? [
  //   {Text: 'Disable', onClick : () => openMod al ('disable') },
  //   {Text: 'View', onClick : () => rule View () },
  //   {Text: 'Edit', onClick : () => rule Ad it () },
  // ] : [
  //     {Text: 'Enable', onClick : () => openMod al ('enable') },
  //     {Text: 'View', onClick : () => rule View () },
  //     {Text: 'Edit', onClick : () => rule Ad it () },
  //     {Text: 'Delete', type: 'danger', onClick : () => openMod al ('del') }
  //   ];

  // const dropdownItemsEv = ruleDetail.Status ? [
  //   {Text: 'Disable', onClick : () => openMod al ('disable') },
  //   {Text: 'Edit', onClick : () => rule Ad it () },
  // ] : [
  //     {Text: 'Enable', onClick : () => openMod al ('enable') },
  //     {Text: 'Edit', onClick : () => rule Ad it () },
  //     {Text: 'Delete', type: 'danger', onClick : () => openMod al ('del') }
  //   ];

  const [dropdownItems, setDropdownItems] = useState([]);
  useEffect(() => {
    let list = [];
    if (tabKey === 'flexibleArt') {
      list = dropdownItemsFlexibel;
    } else if (tabKey === 'dynamic') {
      list = dropdownItemsEv;
    }
    setDropdownItems(list);
  }, [tabKey, ruleDetail]);
  const content = (
    <>
      <KefuDropdown dropdownItems={dropdownItems} />
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        icon={<Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />}
      >
        <p>{titleText}</p>
      </Modal>
    </>
  );
  return content;
};

export default TableHandleDom;
