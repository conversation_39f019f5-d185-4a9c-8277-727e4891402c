import Starling from '@ies/starling_client';
import i18n from './i18n';
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { hot } from 'react-hot-loader/root';
import App from './app';
import './override/semi';
import { reportSlardar } from '@sdks/slardar';
import slardarPlugin from '@byted/garfish-plugin-slardar';
import slardarIns from './sdks/slardar';
import config from '@common/constants/config';
import { errorReporting } from './common/utils/errorReporting';

const HotApp = hot(App);

const locale = i18n.language;

const starling = new Starling({
  api_key: '7528ec60f66811eb91e659796e4d90c0',
  namespace: ['route_manage_i18n'],
  locale,
  mode: 'test',
  fallbackLang: ['en'],
  zoneHost: 'https://starling-oversea.byteoversea.com',
});
export function provider() {
  return {
    render({ dom, basename }: { dom: Element; basename: string }) {
      starling
        .load()
        .then(texts => {
          i18n.addResourceBundle(locale, 'translation', texts, true, true);
          ReactDOM.render(
            <HotApp basename={basename} />,
            dom ? dom.querySelector('#root') : document.querySelector('#root')
          );
        })
        .catch(error => {
          errorReporting({ error, type: 'pormise_name', name: 'starling_load' });
        });
      slardarPlugin({
        // Must be placed in the render function
        getSlardarInstance: () => slardarIns,
        config: config.slardar, // Configuration of Slardar init
      });
      slardarIns('start');
    },
    destroy({ dom }: { dom: Element }) {
      if (dom) {
        ReactDOM.unmountComponentAtNode(dom as Element);
      }
    },
  };
}

if (window.secsdk) {
  starling
    .load()
    .then(texts => {
      i18n.addResourceBundle(locale, 'translation', texts, true, true);
      reportSlardar('initSecsdk');
      // No cross-domain requests, just set the current host
      window.secsdk.csrf.setProtectedHost([window.location.hostname]);
    })
    .catch(error => {
      errorReporting({ error, type: 'pormise_name', name: 'initSecsdk' });
    });
}

if (!window.__PROWER_BY_GAR__) {
  starling
    .load()
    .then(texts => {
      i18n.addResourceBundle(locale, 'translation', texts, true, true);
      ReactDOM.render(<HotApp />, document.querySelector('#root'));
    })
    .catch(error => {
      errorReporting({ error, type: 'pormise_name', name: 'addResourceBundle' });
    });
}
