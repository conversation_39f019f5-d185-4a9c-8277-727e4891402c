/**
 * @file override semi
 * <AUTHOR>
 */

import { Toast } from '@ies/semi-ui-react';

/**
 * Toast repeated information converges into one, and the duration is superimposed
 */

type Method = 'info' | 'success' | 'warning' | 'error';

interface Message {
  message: string;
  duration: number;
  id: number;
  type: Method;
}

Toast.config({
  duration: 0,
});

let isScanning = false;

function scan(list: Message[]) {
  isScanning = true;
  for (let i = 0; i < list.length; i++) {
    const m = list[i];
    m.duration--;
    if (m.duration <= 0) {
      list.splice(i, 1);
      Toast.close(m.id);
      i--;
    }
  }

  if (list.length) {
    setTimeout(() => scan(list), 1000);
  } else {
    isScanning = false;
  }
}

const toastList: Message[] = [];

const methods: Method[] = ['info', 'success', 'warning', 'error'];

methods.forEach(method => {
  const originMethod = Toast[method].bind(Toast);
  Toast[`raw${ method}`] = originMethod;
  // Todo currently only supports string parameters
  Toast[method] = function (message: string) {
    const index = toastList.findIndex(v => v.type === method && v.message === message);
    if (index < 0) {
      const toastId = originMethod({
        content: message,
        onClose: () => {
          const idx = toastList.findIndex(to => to.id === toastId);
          if (idx >= 0) {
            toastList.splice(idx, 1);
          }
        },
      });
      toastList.push({
        id: toastId,
        message,
        duration: 3,
        type: method,
      });
      if (!isScanning) {
        scan(toastList);
      }
    } else {
      console.debug('捕捉到重复Toast: ', message);
      toastList.splice(index, 1, {
        ...toastList[index],
        duration: toastList[index].duration + 3,
      });
      if (!isScanning) {
        scan(toastList);
      }
    }
  };
});

console.debug('override toast');
