import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import styles from './index.scss';
import { Tooltip, Icon, Tag } from '@ies/semi-ui-react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DragableBodyRow } from '@components/drag';
import update from 'immutability-helper';
import * as config from './config';
import { KefuDropdown, KefuPageEmptyContent } from '@ies/kefu-components';
import { DropdownTextItemCfg } from '@ies/kefu-dropdown';

import BotIcon from '@common/images/bot.svg';
import PeopleIcon from '@common/images/people.svg';
import { getFieldByValue } from '@/common/utils/treeData';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { CollapseHorizontalText } from '@components/CollapseText';
import { OperationType, RuleType } from '@/const/enums';
import { AdminRule, AppQuestionCardThrift, BizType, FieldValueItem, SkillGroup, RuleStopStatus } from '@http_idl/demo';
import { numberToTime } from '@/common/utils/dateTimeField';
import { UserContext } from '@/context/user';
import { useIsEcomAccessParty } from '@hooks/useTicketRuleBaseParam';
import { safeJSONParse } from '@/common/utils';

interface RuleListProps {
  operateType: OperationType;
  bizTypeList: BizType[];
  skillGroups: SkillGroup[];
  cardList?: AppQuestionCardThrift[];
  ruleList: AdminRule[];
  fieldList: any[]; // @todo type
  operatorCardFn: (ruleData: any, type: OperationType) => void;
  botList?: FieldValueItem[];
  ruleType: RuleType;
}

const RuleList: React.FC<RuleListProps> = ({
  operateType,
  bizTypeList,
  skillGroups,
  cardList,
  ruleList,
  fieldList,
  operatorCardFn,
  botList,
  ruleType,
}) => {
  const isEcomAccessParty = useIsEcomAccessParty();
  const [ruleDataList, setRuleDataList] = useState([]);
  useEffect(() => {
    setRuleDataList(safeJSONParse(JSON.stringify(ruleList)));
  }, [JSON.stringify(ruleList)]);

  const user = useContext(UserContext);

  const getSkillGroupName = id => {
    const skillGroup = (skillGroups || []).find(o => o.ID === id);
    if (skillGroup) {
      return `${skillGroup.Name}`;
    }
    return id;
  };

  const getBotName = id => {
    const bot = (botList || []).find(o => o.value === id);
    if (bot) {
      return bot.name;
    }
    return id;
  };

  const isAdjust = operateType === OperationType.Adjust;
  const listMoveRow = function (dragIndex, hoverIndex) {
    const list = safeJSONParse(JSON.stringify(ruleDataList));
    const dragRow = list[dragIndex];
    const res = update(list, {
      $splice: [
        [dragIndex, 1],
        [hoverIndex, 0, dragRow],
      ],
    });
    operatorCardFn(res, OperationType.Adjust);
    setRuleDataList(res);
  };

  const getFieldDisplayName = (filterData = {} as any) => {
    const { FieldMapName, FieldValue, OperatorId } = filterData;

    const field = fieldList.find(o => o.FieldMapName === FieldMapName);
    if (field) {
      const { FieldDisplayName, Fieldvalues } = field;
      const { FieldValueType, FieldValueList = [] } = Fieldvalues[OperatorId] || {};
      let FieldValueName = '';

      switch (FieldValueType) {
        case 8: // Radio TimePicker
          FieldValueName = numberToTime(FieldValue);
          break;
        case 1: // Radio
          FieldValueName = FieldValueList.find(o => o.value === FieldValue)?.name || FieldValue;
          break;
        case 2: // Multiple choice
          FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
            .reduce((str, val) => {
              const v = FieldValueList.find(o => o.value === val)?.name;
              return `${str}、${v ? v : val}`;
            }, '')
            .slice(1);
          break;
        case 7: // Tree shape
          FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
            .reduce((str, val) => {
              const target = getFieldByValue(
                JSON.stringify([user?.accessPartyId, ruleType, FieldMapName, OperatorId]),
                FieldValueList,
                val
              );
              const v = target?.name;
              return `${str}、${v ? v : val}`;
            }, '')
            .slice(1);
          break;
        case 3: // Mixing (multi-select + input box)
        case 4: // Time control
        case 5: // Input box
        case 6: // Batch Input
        default:
          FieldValueName = FieldValue;
      }

      return {
        FieldDisplayName,
        FieldValueName: Array.isArray(FieldValueName) ? FieldValueName.join('、') : FieldValueName,
      };
    }

    return {
      FieldDisplayName: FieldMapName,
      FieldValueName: Array.isArray(FieldValue) ? FieldValue.join('、') : FieldValue,
    };
  };

  const renderFilterComponents = (filterData, index, Filter) => {
    if (!filterData) {
      return null;
    }

    const FilterUnit = Filter?.FilterUnitList || [];
    const OperateType = Filter?.OperateType;
    const { OperatorId } = filterData;

    const { FieldDisplayName, FieldValueName } = getFieldDisplayName(filterData);
    const isLastFilter = FilterUnit.length === index + 1;
    const contactText = isLastFilter ? null : (
      <span className={styles.ruleContentLabel}>{config.FilterOperateType[OperateType]}</span>
    );
    return (
      <>
        <span className={styles.ruleContentText}>{FieldDisplayName}</span>
        <span className={styles.ruleContentLabel}>{config.OperatorTypeMap[OperatorId]}</span>
        <span className={styles.ruleContentText}>{FieldValueName}</span>
        {contactText}
      </>
    );
  };

  const renderRuleContent = (ruleData, index) => {
    const isLast = ruleData.Priority === 9999; // Bottom rules

    let dropdownItems: DropdownTextItemCfg[] = [];
    const Operations = {
      Edit: { text: I18n.t('edit', {}, '编辑'), onClick: () => operatorCardFn(ruleData, OperationType.Edit) },
      Enable: { text: I18n.t('enable', {}, '启用'), onClick: () => operatorCardFn(ruleData, OperationType.Enable) },
      Disable: { text: I18n.t('disable', {}, '禁用'), onClick: () => operatorCardFn(ruleData, OperationType.Disable) },
      Delete: {
        text: I18n.t('delete', {}, '删除'),
        type: 'danger',
        onClick: () => operatorCardFn(ruleData, OperationType.Delete),
      },
    } as const;

    const divider: DropdownTextItemCfg = { component: 'divider' };

    const enabled = ruleData.StopStatus === RuleStopStatus.USING;

    if (isLast) {
      dropdownItems = [Operations.Edit];
    } else {
      if (
        ruleType === RuleType.Ticket ||
        ruleType === RuleType.Offline ||
        ruleType === RuleType.Service ||
        ruleType === RuleType.QualityCheck
      ) {
        if (enabled) {
          dropdownItems = [Operations.Disable];
        } else {
          dropdownItems = [Operations.Edit, Operations.Enable, divider, Operations.Delete];
        }
      } else {
        dropdownItems = [Operations.Edit, divider, Operations.Delete];
      }
    }

    const status = enabled ? I18n.t('enable', {}, '启用') : I18n.t('disable', {}, '禁用');

    return (
      <div className={styles.ruleBox}>
        <div className={styles.ruleBoxHeader}>
          <div className={styles.headerTitle}>
            <div className={styles.headerLeftIconBox}>
              {isAdjust ? <Icon type="handle" className={styles.handleIcon} /> : <span>{`${index + 1}.`}</span>}
            </div>
            <div className={styles.headerNameBox}>
              <Tooltip className={styles.tooltip} content={ruleData.DisplayName}>
                <span className={styles.headerName}>{ruleData.DisplayName}</span>
              </Tooltip>
              {(ruleType === RuleType.Ticket ||
                ruleType === RuleType.Offline ||
                ruleType === RuleType.Service ||
                ruleType === RuleType.QualityCheck) && (
                <Tag className={styles.statusTag} color={enabled ? 'teal' : 'red'}>
                  {status}
                </Tag>
              )}
            </div>
          </div>
          <div className={styles.headerTipBox}>
            <span className={styles.headerTips}>
              {`${I18n.t(
                '{placeholder0}_updated_to_{placeholder2}',
                { placeholder0: ruleData.UpdaterAgentName || '', placeholder2: ruleData.UpdatedAt },
                '{placeholder0} 更新于 {placeholder2}'
              )}`}
            </span>
            {isAdjust ? null : <KefuDropdown dropdownItems={dropdownItems} />}
          </div>
        </div>
        <div>
          {/* filter */}
          <div className={styles.ruleFilterBox}>
            <div className={styles.ruleFilterDot} />
            <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
              <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
              {isLast ? (
                <span className={styles.ruleContentText}>{I18n.t('missed_the_above_rules', {}, '未命中以上规则')}</span>
              ) : (
                ruleData?.Filter?.FilterUnitList?.map((filterData, k) => (
                  <React.Fragment key={k}>{renderFilterComponents(filterData, k, ruleData?.Filter)}</React.Fragment>
                ))
              )}
            </CollapseHorizontalText>
          </div>

          {/* bot */}
          {ruleType === RuleType.Bot && ruleData.IsOpen === 1 && (
            <div className={styles.ruleFilterBox}>
              <div className={styles.ruleSkillDot} />
              <span className={styles.ruleContentLabel}>{I18n.t('provide', {}, '提供')}</span>
              <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
            </div>
          )}

          {/* Allocation */}
          <div className={styles.ruleSkillBox}>
            {ruleType === RuleType.Bot ? (
              ruleData.IsOpen === 1 ? (
                <>
                  <div className={styles.ruleSkillDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={BotIcon} />
                  <span className={styles.ruleContentText}>{getBotName(ruleData.BotId)}</span>
                </>
              ) : (
                <>
                  <div className={styles.ruleEmptyDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('not_available', {}, '不提供')}</span>
                  <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
                </>
              )
            ) : ruleType === RuleType.Service ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupId)}</span>
                  {Boolean(ruleData.SupportOverflow) && (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('and_when', {}, '且当')}</span>
                      <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupId)}</span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('the_number_of_people_in_line_is_greater_than', {}, '排队人数大于')}
                      </span>
                      <span className={styles.ruleContentText}>{ruleData.OverflowThreshold}</span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('when_people__overflow_to', {}, '人时 溢出至')}
                      </span>
                      <span className={styles.ruleContentText}>
                        {ruleData.SkillGroupOverflow.map(groupId => getSkillGroupName(groupId)).join('、')}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
            ) : ruleType === RuleType.Ticket ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  {isEcomAccessParty && (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('routing_timing_is', {}, '路由时机是')}</span>
                      <span className={styles.ruleContentText}>{ruleData.RouteTypeName}</span>
                      <span className={styles.ruleContentLabel}>{I18n.t('time', {}, '时')}</span>
                    </>
                  )}
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupId)}</span>
                </CollapseHorizontalText>
              </div>
            ) : ruleType === RuleType.Offline ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupId)}</span>
                </CollapseHorizontalText>
              </div>
            ) : ruleType === RuleType.QualityCheck ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupId)}</span>
                </CollapseHorizontalText>
              </div>
            ) : null}
          </div>

          {/* Overflow */}
          {[RuleType.Ticket].includes(ruleType) && Boolean(ruleData.SupportOverflow) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>
                    {RuleType.Service === ruleType ?
                      I18n.t('work_orders_to_be_assigned_within_the_group', {}, '组内待分配工单') :
                      null}
                  </span>
                  <span className={styles.ruleContentLabel}>{I18n.t('greater_than', {}, '大于')}</span>
                  <span className={styles.ruleContentText}>{ruleData.OverflowThreshold}</span>
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('overflow_to', {}, '溢出至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>
                      {ruleData.SkillGroupOverflow.map(groupId => getSkillGroupName(groupId)).join('、')}
                    </span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}

          {/* Fishing */}
          {ruleType === RuleType.Ticket && Boolean(ruleData.SupportReclaim) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>{ruleData.ReclaimConditionName}</span>
                  {ruleData.ReclaimType === 'time' && (
                    <>
                      {/* < span className={styles.ruleContentLabel} > greater than </span > */}
                      <span className={styles.ruleContentText}>
                        {`${I18n.t(
                          '{placeholder0}_hours_{placeholder2}_minutes',
                          { placeholder0: ruleData.ReclaimHours, placeholder2: ruleData.ReclaimMinutes },
                          '{placeholder0} 小时 {placeholder2} 分'
                        )}`}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('reclaimed_to', {}, '回捞至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>{getSkillGroupName(ruleData.SkillGroupReclaim)}</span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      {ruleDataList.length ? (
        <div className={styles.title}>
          <div className={styles.titlePoint} />
          <span style={{ color: 'var(--color-text-2)' }}>
            {I18n.t(
              'from_top_to_bottom_in_order_to_determine_the_routing_rules',
              {},
              '从上而下的顺序依次进行路由规则判断'
            )}
          </span>
        </div>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_routing_rules', {}, '暂无路由规则')}
          // eslint-disable-next-line react/no-children-prop
          children={null}
        />
      )}
      <DndProvider backend={HTML5Backend}>
        <div style={{ marginBottom: 40 }}>
          {ruleDataList.map((ruleData, index) => (
            <div key={ruleData.Id}>
              <div className={styles.intervalBox} />
              {isAdjust ? (
                <DragableBodyRow index={index} moveRow={listMoveRow}>
                  {renderRuleContent(ruleData, index)}
                </DragableBodyRow>
              ) : (
                renderRuleContent(ruleData, index)
              )}
            </div>
          ))}
        </div>
      </DndProvider>
    </div>
  );
};

export default RuleList;
