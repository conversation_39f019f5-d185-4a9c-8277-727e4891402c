$user-name-length: 164px;

.header-avatar-outer {
  margin-left: 15px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.header-avatar {
  width: 32px;
  height: 32px;
}

.profile {
  width: 240px;
}

.profile-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 16px 12px;
}

.info-outer {
  margin-left: 10px;
  overflow: hidden;
  white-space: nowrap;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-0);
  margin: 0;
  padding: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: $user-name-length;
}

.profile-email {
  font-size: 12px;
  color: var(--color-text-2);
  margin: 0;
  padding: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: $user-name-length;
}

.profile-opr {
  color: var(--color-text-0);
  position: relative;
}

.profile-icon {
  width: 16px !important;
  height: 16px !important;
  color: var(--color-text-2);
}

.profile-logout {
  display: flex;
  justify-content: center;
  color: unquote("rgb(var(--red-4))") !important;
  font-weight: bold;
}

.dropdown-item-selected {
  background-color: var(--color-fill-0);
}

.topnav-dropdown-item {
  min-width: 200px;
}

.fr {
  position: absolute;
  right: 12px;
  display: inline-flex;
  align-items: center;
  color: var(--color-text-2);
}

.dropdown {
  width: 200px;
}
