import * as React from 'react';
import { ReactNode } from 'react';

export interface Item {
    leftIcon?: string | ReactNode;
    text?: string | ReactNode;
    rightText?: string | ReactNode;
    onClick?: (...args: any[]) => any;
    onMouseEnter?: (...args: any[]) => any;
    onMouseLeave?: (...args: any[]) => any;
    disabled?: boolean;
    spacing?: number;
    position?: string;
    items?: Item[];
}

export interface UserInfo {
    avatar?: ReactNode | string;
    name?: ReactNode | string;
    email?: ReactNode | string;
}

export interface Footer {
    text?: ReactNode | string;
    onClick?: (...args: any[]) => any;
    onMouseEnter?: (...args: any[]) => any;
    onMouseLeave?: (...args: any[]) => any;
    disabled?: boolean;
}

export interface SemiReactProfileDropdownProps {
    children: any;
    showArrow?: boolean;
    position?: string;
    visible?: boolean;
    userInfo?: UserInfo;
    items?: Item[];
    footer?: Footer;
}

declare const SemiReactProfileDropdown: React.FC<SemiReactProfileDropdownProps>;

export default SemiReactProfileDropdown;
