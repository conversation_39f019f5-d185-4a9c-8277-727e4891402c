import { I18n } from '@ies/starling_intl';
import React, { useMemo, useCallback, isValidElement } from 'react';
import classnames from 'classnames';
import { get } from 'lodash';
import { Avatar, Dropdown, Icon, Popover } from '@ies/semi-ui-react';
import SiteAvatar from './images/site_avatar.svg';
import styles from './index.module.scss';
import PropTypes from 'prop-types';

// eslint-disable-next-line max-lines-per-function
function SemiReactProfileDropdown({
  children,
  showArrow,
  userInfo,
  items,
  footer,
  visible,
  trigger,
  position,
  clickToHide,
}) {
  const mergedClick = useCallback((e, item) => {
    if (e && !clickToHide) {
      if (typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }

      if (e.nativeEvent && typeof e.nativeEvent.stopImmediatePropagation === 'function') {
        e.nativeEvent.stopImmediatePropagation();
      }
    }

    const onClick = item && item.onClick;

    if (typeof onClick === 'function') {
      onClick(e, item);
    }
  }, []);

  const header = useMemo(
    () => (
      <div className={styles['profile-header']}>
        {isValidElement(userInfo.avatar) ? userInfo.avatar : <Avatar size={'small'}>{userInfo.avatar}</Avatar>}
        <div className={styles['info-outer']}>
          <div className={styles['profile-name']}>{userInfo.name}</div>
          <div className={styles['profile-email']}>{userInfo.email}</div>
        </div>
      </div>
    ),
    [userInfo]
  );

  const renderChildren = (items = [], level = 0) =>
    items.map((item = {}, index) => {
      const key = item.key !== null && item.key !== undefined ? item.key : `${level}-${index}`;
      const leftIcon =
        typeof item.leftIcon === 'string' && item.leftIcon ? (
          <Icon type={item.leftIcon} className={styles['profile-icon']} />
        ) : (
          item.leftIcon
        );
      const rightIcon =
        Array.isArray(item.items) && item.items.length ? (
          <Icon type={'chevron_right'} className={classnames(styles['profile-icon'])} />
        ) : null;
      const style = get(item, 'style', {});
      const itemElem = (
        <Dropdown.Item
          {...item}
          style={{ ...style, width: 200 }}
          key={key}
          onClick={e => mergedClick(e, item)}
          className={classnames(item.className, {
            [styles['dropdown-item-selected']]: item.active,
            [styles['topnav-dropdown-item']]: level > 0,
          })}
        >
          {leftIcon}
          {item.text}
          {item.rightText || item.rightIcon ? (
            <span className={styles.fr}>
              {item.rightText}
              {rightIcon}
            </span>
          ) : null}
        </Dropdown.Item>
      );

      if (Array.isArray(item.items) && item.items.length) {
        return (
          <Dropdown
            showTick
            clickToHide={clickToHide}
            key={key}
            position={item.position || 'leftTop'}
            spacing={typeof item.spacing === 'number' ? item.spacing : 2}
            render={<Dropdown.Menu>{renderChildren(item.items, level + 1)}</Dropdown.Menu>}
            contentClassName="dropdown"
            {...item}
          >
            {itemElem}
          </Dropdown>
        );
      }

      return itemElem;
    });

  return (
    <Popover
      position={position}
      trigger={trigger}
      visible={visible}
      showArrow={showArrow}
      style={{ padding: 0 }}
      clickToHide={clickToHide}
      content={(
        <div className={styles.profile}>
          {header}
          <Dropdown.Menu>
            <Dropdown.Divider />
            {renderChildren(items)}
            <Dropdown.Divider />
            <Dropdown.Item className={styles['profile-logout']} {...footer}>
              {footer.text}
            </Dropdown.Item>
          </Dropdown.Menu>
        </div>
      )}
    >
      {children}
    </Popover>
  );
}

SemiReactProfileDropdown.propTypes = {
  children: PropTypes.any,
  showArrow: PropTypes.bool,
  position: PropTypes.string,
  visible: PropTypes.bool,
  userInfo: PropTypes.shape({
    avatar: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
    name: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
    email: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  }),
  items: PropTypes.arrayOf(
    PropTypes.shape({
      leftIcon: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
      text: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
      rightText: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
      onClick: PropTypes.func,
      onMouseEnter: PropTypes.func,
      onMouseLeave: PropTypes.func,
      disabled: PropTypes.bool,
      spacing: PropTypes.number,
      position: PropTypes.string,
      items: PropTypes.arrayOf(PropTypes.any),
    })
  ),
  footer: PropTypes.shape({
    text: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
    onClick: PropTypes.func,
    onMouseEnter: PropTypes.func,
    onMouseLeave: PropTypes.func,
    disabled: PropTypes.bool,
  }),
};
SemiReactProfileDropdown.defaultProps = {
  userInfo: {},
  trigger: 'hover',
  position: 'bottomLeft',
  footer: { text: I18n.t('logout', {}, '登出') },
  items: [],
  showArrow: true,
  clickToHide: false,
};

export default SemiReactProfileDropdown;

export { SiteAvatar };
