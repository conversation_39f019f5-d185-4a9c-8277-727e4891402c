import React from 'react';
import { Icon } from '@ies/semi-ui-react';
import RueConfigHead from '@/pages/ruleConfigHead';
import * as styles from './index.scss';

interface CreateRuleHeaderProps {
  name: string;
  platform: string;
  handleGoBack: () => void;
}

const CreateRuleHeader: React.FC<CreateRuleHeaderProps> = ({ name = '', handleGoBack }) => {
  // Click on the header to return the routing message
  const content = (
    <>
      <div className={styles.createRuleTitle}>
        <Icon type="chevron_left" style={{ color: 'var(--color-text-2)' }} />
        <span onClick={handleGoBack} style={{ cursor: 'pointer' }}>{name}</span>
      </div>
      <RueConfigHead />
    </>
  );
  return content;
};
export default CreateRuleHeader;