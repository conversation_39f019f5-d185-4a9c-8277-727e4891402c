import { I18n } from '@ies/starling_intl';
import React, { useState, useEffect, useContext } from 'react';
import styles from './index.scss';
import * as config from './config';
import { Tooltip, Icon, Tag, Button, Modal, Toast } from '@ies/semi-ui-react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { CardOperation } from '@/components/cardOperation';
import { useHistory } from 'react-router-dom';
import UpIcon from '@/images/route_up_arrow.svg';
import DownIcon from '@/images/route_down_arrow.svg';
import TopIcon from '@/images/route_top_arrow.svg';
import BottomIcon from '@/images/route_bottom_arrow.svg';
import PeopleIcon from '@common/images/people.svg';
import BotIcon from '@common/images/bot.svg';
import { useIsEcomAccessParty, useNewTicketRuleBaseParam } from '@hooks/useTicketRuleBaseParam';
import { OperationType, routeType } from '@/const/enums';
import { KefuDropdown, KefuPageEmptyContent } from '@ies/kefu-components';
import { getFieldByValue } from '@/common/utils/treeData';
import { numberToTime } from '@/common/utils/dateTimeField';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { isJSON } from '@/common/utils/index';
import { CollapseHorizontalText } from '@components/NewCollapseText';
import { ErrorCodeNoPermission } from '../../const/index';
import format, { millisecondToTime } from '@common/utils/date';
import { formatNum } from '@/common/utils/inputFloat';
import { getDropdownList } from './getDropdown';
import {
  SERVICE_ROUTE,
  BOT_ROUTE,
  OFFLINE_ROUTE,
  QUALITY_CHECK_ROUTE,
  ACCESSPARTY_EVENT_KEY,
  routeTypeMap
} from '@constants/property';
import { FieldValueItem, SkillGroup } from '@http_idl/demo';
import * as t from '@http_idl/demo';
import { UserContext } from '@/context/user';
import { isTTP } from '@/common/utils/env';
import { havePermission } from '@/common/utils/hasPermission';
import { useDoorAuthority } from '@/hooks';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
// import { relative } from 'path';
interface RuleListProps {
  ruleList: any[];
  bizTypeList: any[];
  changePreReleaseRulesList: () => void;
  getPreReleaseRuleList: () => void;
  skillGroups: SkillGroup[];
  fieldList: any[];
  ruleType: routeType;
  botList?: FieldValueItem[];
  newRuleId: string;
  inputValue?: string;
  defaultItemKeyOne?: string;
  ticketAllSkillGroups?: SkillGroup[];
  ruleName?: string; 
  statusList?: number[];
  skillGroupList?: number[];
}
const CardReOrderType = {
  Top: 'top',
  Up: 'up',
  Down: 'down',
  Bottom: 'bottom',
};
const RuleList: React.FC<RuleListProps> = ({
  ruleList,
  newRuleId,
  bizTypeList,
  changePreReleaseRulesList,
  getPreReleaseRuleList,
  skillGroups,
  defaultItemKeyOne,
  fieldList,
  ruleType,
  botList,
  inputValue,
  ticketAllSkillGroups,
  ruleName,
  statusList,
  skillGroupList
}) => {
  const [ruleDataList, setRuleDataList] = useState([]);
  const user = useContext(UserContext);
  const [getChooseObj, changeChooseObj] = useState({ Id: '' });
  const isEcomAccessParty = useIsEcomAccessParty();
  const [modalVisible, changeVisible] = useState(false);
  const { EventKey } = useNewTicketRuleBaseParam(defaultItemKeyOne);
  const pathname = location.pathname.split('/');
  const typeStr = pathname[pathname.length - 1];
  const { hasEditAuth } = useDoorAuthority();
  // Adjust priority
  const onReOrder = (key, ruleDataChild) => {
    switch (key) {
      case 'top':
        let topRouterList = [];
        const routerList = [];
        if (ruleDataChild.Id === ruleDataList[0].Id) {
          Toast.error(I18n.t('already_top_rule', {}, '已经是顶部规则'));
          return false;
        }
        routerList.push(ruleDataChild);
        ruleDataList.forEach((item, index) => {
          if (item.Id !== ruleDataChild.Id && item.Priority !== 9999) {
            routerList.push(item);
          }
        });
        topRouterList = routerList.map((item, index) => ({
          Id: item.Id,
          Priority: index + 1,
        }));
        t.demoClient
          .UpdateRulePriority({
            Rules: topRouterList,
            Version: 'v1',
          })
          ?.then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('top_success', {}, '置顶成功'));
            getPreReleaseRuleList();
          })
          ?.catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_top' });
          });
        break;
      case 'up':
        let upRouterList = [];
        if (ruleDataChild.Id === ruleDataList[0].Id) {
          Toast.error(I18n.t('already_top_rule', {}, '已经是顶部规则'));
          return false;
        }
        ruleDataList.forEach((item, index) => {
          if (item.Id === ruleDataChild.Id) {
            const temp = ruleDataList[index - 1];
            ruleDataList[index - 1] = ruleDataChild;
            ruleDataList[index] = temp;
          }
        });
        upRouterList = ruleDataList.map((item, index) => ({
          Id: item.Id,
          Priority: item.Priority === 9999 ? 9999 : index + 1,
        }));
        upRouterList = upRouterList.filter(item => item.Priority !== 9999);
        t.demoClient
          ?.UpdateRulePriority({
            Rules: upRouterList,

            Version: 'v1',
          })
          ?.then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('move_up_successfully', {}, '上移成功'));
            getPreReleaseRuleList();
          })
          ?.catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_up' });
          });
        break;
      case 'down':
        let downRouterList = [];
        if (ruleDataChild.Id === ruleDataList[ruleDataList.length - 1].Id) {
          Toast.error(I18n.t('already_the_bottom_rule', {}, '已经是底部规则'));
          return false;
        }
        for (let i = 0; i < ruleDataList.length; i++) {
          if (ruleDataList[i].Id === ruleDataChild.Id) {
            const temp = ruleDataList[i + 1];
            ruleDataList[i + 1] = ruleDataChild;
            ruleDataList[i] = temp;
            downRouterList = ruleDataList.map((item, index) => ({
              Id: item.Id,
              Priority: item.Priority === 9999 ? 9999 : index + 1,
            }));
            downRouterList = downRouterList.filter(item => item.Priority !== 9999);
            t.demoClient
              ?.UpdateRulePriority({
                Rules: downRouterList,
                UpdaterAgentId: user.agent.ID,
                Version: 'v1',
              })
              ?.then(res => {
                if (res.code !== 0) {
                  Toast.error(res.message);
                  return;
                }
                Toast.success(I18n.t('move_down_successfully', {}, '下移成功'));
                getPreReleaseRuleList();
              })
              ?.catch(error => {
                errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_down' });
              });
            return false;
          }
        }
        break;
      default:
        let bottomRouterList = [];
        const bottomList = [];
        if (ruleDataChild.Id === ruleDataList[ruleDataList.length - 1].Id) {
          Toast.error(I18n.t('already_the_bottom_rule', {}, '已经是底部规则'));
          return false;
        }
        ruleDataList.forEach((item, index) => {
          if (item.Id !== ruleDataChild.Id && item.Priority !== 9999) {
            bottomList.push(item);
          }
        });
        bottomList.push(ruleDataChild);

        bottomRouterList = bottomList.map((item, index) => ({
          Id: item.Id,
          Priority: item.Priority === 9999 ? 9999 : index + 1,
        }));
        t.demoClient
          .UpdateRulePriority({
            Rules: bottomRouterList,

            Version: 'v1',
          })
          .then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('bottom_successful', {}, '置底成功'));
            getPreReleaseRuleList();
          })
          .catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_bottom' });
          });
    }
  };

  const history = useHistory();
  useEffect(() => {
    setRuleDataList(safeJSONParse(JSON.stringify(ruleList)));
  }, [JSON.stringify(ruleList)]);
  const createRouteRule = (plaformTag, vals, index, singleRuleData) => {
    const isLast = index + 1 === 9999;
    switch (plaformTag) {
      case routeType.bot_routing:
        if (fieldList.length > 0 && skillGroups.length > 0) {
          history.push({
            pathname: '/bot_create_rule',
            state: {
              plaformTag,
              viewType: 'create',
              Enable: false,
              EventKey: BOT_ROUTE,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              // ruleId: vals.Id,
              RuleGroupId: vals.RuleGroupId,
              ruleLength: ruleDataList.length || 0,
              isLast,
              Priority: singleRuleData.Priority + 1,
            },
          });
        }
        break;
      case routeType.offline_routing:
        if (fieldList.length > 0 && skillGroups.length > 0) {
          history.push({
            pathname: '/offline_create_rule',
            state: {
              plaformTag,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              viewType: 'create',
              Enable: false,
              ruleLength: ruleDataList.length || 0,
              EventKey: OFFLINE_ROUTE,
              // ruleId: vals.Id,
              RuleGroupId: vals.RuleGroupId,
              isLast,
              Priority: singleRuleData.Priority + 1,
            },
          });
        }
        break;
      case routeType.accessparty_routing:
        if (fieldList.length > 0) {
          history.push({
            pathname: `/${routeType.accessparty_routing}`,
            state: {
              plaformTag,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              viewType: 'create',
              Enable: false,
              // ruleId: vals.Id,
              EventKey: ACCESSPARTY_EVENT_KEY,
              RuleGroupId: vals.RuleGroupId,
              ruleLength: ruleDataList.length || 0,
              isLast,
              Priority: singleRuleData.Priority + 1,
            },
          });
        }
        break;
      case routeType.quality_check_routing:
        if (fieldList.length > 0 && skillGroups.length > 0) {
          history.push({
            pathname: '/qualitycheck_create_rule',
            state: {
              plaformTag,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              viewType: 'create',
              Enable: false,
              // ruleId: vals.Id,
              EventKey: QUALITY_CHECK_ROUTE,
              RuleGroupId: vals.RuleGroupId,
              ruleLength: ruleDataList.length || 0,
              isLast,
              Priority: singleRuleData.Priority + 1,
            },
          });
        }
        break;
      case routeType.ticket_routing:
        if (fieldList.length > 0 && skillGroups.length > 0) {
          history.push({
            pathname: '/ticket_create_rule',
            state: {
              plaformTag,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              viewType: 'create',
              Enable: false,
              // ruleId: vals.Id,
              EventKey,
              RuleGroupId: vals.RuleGroupId,
              ruleLength: ruleDataList.length,
              isLast,
              Priority: singleRuleData.Priority + 1,
              ruleName,
              statusList,
              skillGroupList
            },
          });
        }
        break;
      default:
        if (fieldList.length > 0 && skillGroups.length > 0) {
          history.push({
            pathname: '/service_create_rule',
            state: {
              plaformTag,
              fieldLists: fieldList,
              skillGroups,
              bizTypeList,
              viewType: 'create',
              Enable: false,
              // ruleId: vals.Id,
              EventKey: SERVICE_ROUTE,
              RuleGroupId: vals.RuleGroupId,
              ruleLength: ruleDataList.length || 0,
              isLast,
              Priority: singleRuleData.Priority + 1,
              ruleName,
              statusList,
              skillGroupList
            },
          });
        }
    }
  };
  // View, edit
  const clickDropItemEdit = (val, plaformTag, operation) => {
    const isLast = val.Priority === 9999;

    if (operation === OperationType.Edit) {
      switch (plaformTag) {
        case routeType.bot_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/bot_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'edit',
                Enable: val.Status,
                ruleId: val.Id,
                EventKey: BOT_ROUTE,
                RuleGroupId: val.RuleGroupId,
                ruleLength: ruleDataList.length || 0,
                isLast,
                Priority: val.Priority,
              },
            });
          }
          break;
        case routeType.ticket_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/ticket_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: config.compatibilityOldData(val, fieldList),
                bizTypeList,
                viewType: 'edit',
                Enable: val.Status,
                ruleId: val.Id,
                RuleGroupId: val.RuleGroupId,
                ruleLength: ruleDataList.length || 0,
                isLast,
                EventKey,
                Priority: val.Priority,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
          break;
        default:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/service_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'edit',
                Enable: val.Status,
                ruleId: val.Id,
                EventKey: SERVICE_ROUTE,
                ruleLength: ruleDataList.length || 0,
                RuleGroupId: val.RuleGroupId,
                isLast,
                Priority: val.Priority,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
      }
    } else {
      switch (plaformTag) {
        case routeType.bot_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/bot_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'view',
                Enable: val.Status,
                ruleId: val.Id,
                RuleGroupId: val.RuleGroupId,
                isLast,
                EventKey: BOT_ROUTE,
                Priority: val.Priority,
              },
            });
          }
          break;
        case routeType.ticket_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/ticket_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'view',
                Enable: val.Status,
                ruleId: val.Id,
                RuleGroupId: val.RuleGroupId,
                isLast,
                EventKey,
                Priority: val.Priority,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
          break;
        default:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/service_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'view',
                Enable: val.Status,
                ruleId: val.Id,
                RuleGroupId: val.RuleGroupId,
                isLast,
                EventKey: SERVICE_ROUTE,
                Priority: val.Priority,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
      }
    }
  };
  // Enable and disable rules
  const clickDropItem = async (operation, val) => {
    switch (operation) {
      case 'del':
        changeChooseObj(val);
        changeVisible(true);
        break;
      default:
        if (val.Status === 1) {
          const confirmed = await new Promise(resolve => {
            Modal.confirm({
              icon: <Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />,
              title: I18n.t('disable_rules', {}, '禁用规则'),
              content: `${I18n.t(
                'when_disabled__the_{placeholder1}_rule_will_not_take_effect',
                {
                  placeholder1:
                    ruleType === routeType.accessparty_routing ?
                      I18n.t('shunt', {}, '分流') :
                      I18n.t('routing', {}, '路由'),
                },
                '禁用后，该{placeholder1}规则将不会生效'
              )}`,
              okText: I18n.t('disable', {}, '禁用'),
              cancelText: I18n.t('cancel', {}, '取消'),
              okButtonProps: { type: 'danger' },
              onOk() {
                resolve(true);
              },
              onCancel() {
                resolve(false);
              },
            });
          });
          if (confirmed) {
            t.demoClient
              .UpdateRuleStatus({
                Ids: [val.Id],
                RuleStatus: 0,
                Version: 'v1',
              })
              .then(res => {
                if (res.code === 0) {
                  Toast.success(
                    `${I18n.t(
                      'the__{placeholder1}__rule_is_disabled',
                      { placeholder1: val.DisplayName },
                      '「{placeholder1}」规则已禁用'
                    )}`
                  );
                  getPreReleaseRuleList();
                } else {
                  if (res.message) {
                    Toast.error(res.message);
                    return;
                  } else {
                    Toast.error(
                      `${I18n.t(
                        '_{placeholder1}__rule_disabled_failed',
                        { placeholder1: val.DisplayName },
                        '「{placeholder1}」规则禁用失败'
                      )}`
                    );
                  }
                }
              })
              .catch(err => {
                Toast.error(
                  `${I18n.t(
                    '_{placeholder1}__rule_disabled_failed',
                    { placeholder1: val.DisplayName },
                    '「{placeholder1}」规则禁用失败'
                  )}`
                );
              });
          }
        } else {
          t.demoClient
            .UpdateRuleStatus({
              Ids: [val.Id],
              RuleStatus: 1,
              Version: 'v1',
            })
            .then(res => {
              if (res.code === 0) {
                Toast.success(
                  `${I18n.t(
                    'the__{placeholder1}__rule_is_enabled',
                    { placeholder1: val.DisplayName },
                    '「{placeholder1}」规则已启用'
                  )}`
                );
                getPreReleaseRuleList();
              } else {
                if (res.message) {
                  Toast.error(res.message);
                  return;
                } else {
                  Toast.error(
                    `${I18n.t(
                      '_{placeholder1}__rule_enabled_failed',
                      { placeholder1: val.DisplayName },
                      '「{placeholder1}」规则启用失败'
                    )}`
                  );
                }
              }
            })
            .catch(err => {
              Toast.error(
                `${I18n.t(
                  '_{placeholder1}__rule_enabled_failed',
                  { placeholder1: val.DisplayName },
                  '「{placeholder1}」规则启用失败'
                )}`
              );
            });
        }
    }
  };
  const clickClone = async (operation, val, plaformTag) => {
    const isLast = val.Priority === 9999;
    val.DisplayName = '';
    if (operation === OperationType.Clone) {
      switch (plaformTag) {
        case routeType.bot_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/bot_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'clone',
                Enable: val.Status,
                ruleId: val.Id,
                EventKey: BOT_ROUTE,
                RuleGroupId: val.RuleGroupId,
                ruleLength: ruleDataList.length || 0,
                isLast,
                Priority: val.Priority + 1,
              },
            });
          }
          break;
        case routeType.ticket_routing:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/ticket_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: config.compatibilityOldData(val, fieldList),
                bizTypeList,
                viewType: 'clone',
                Enable: val.Status,
                ruleId: val.Id,
                RuleGroupId: val.RuleGroupId,
                ruleLength: ruleDataList.length || 0,
                isLast,
                EventKey,
                Priority: val.Priority + 1,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
          break;
        default:
          if (fieldList.length > 0 && skillGroups.length > 0) {
            history.push({
              pathname: '/service_create_rule',
              state: {
                plaformTag,
                fieldLists: fieldList,
                skillGroups,
                ruleInfo: val,
                bizTypeList,
                viewType: 'clone',
                Enable: val.Status,
                ruleId: val.Id,
                EventKey: SERVICE_ROUTE,
                ruleLength: ruleDataList.length || 0,
                RuleGroupId: val.RuleGroupId,
                isLast,
                Priority: val.Priority,
                ruleName,
                statusList,
                skillGroupList
              },
            });
          }
      }
    }
  };
  const handleOk = async () => {
    // Request interface
    // delete
    const res = await t.demoClient.UpdateRuleStatus({
      Ids: [getChooseObj.Id],
      RuleStatus: 3,
      Version: 'v1',
      PermCode: typeStr === 'accessparty_split_flow' ? 'func.page.united_accessparty_split_flow_del' : '',
      Draft: true,
    });

    if (res.code !== 0) {
      if (res.code === ErrorCodeNoPermission) {
        Toast.error(I18n.t('no_delete_permission', {}, '没有删除权限'));
      } else {
        Toast.error(res.message);
      }
      return;
    }

    changeVisible(false);
    await getPreReleaseRuleList();
  };
  // Delete data
  const handleCancel = () => {
    changeVisible(false);
  };
  const ruleContent = (ruleDataChild, index) => {
    const isLast = ruleDataChild.Priority === 9999; // Is it a cover rule?
    const status = ruleDataChild.Status ? I18n.t('enable', {}, '启用') : I18n.t('disable', {}, '禁用');
    // Get Skill Group Name
    const getSkillGroupName = ({
      id,
      isShunt = null,
      skillList = null,
      supportOverflow = null,
      isAutoShunt = null,
      autoShuntSkillList = [],
    }) => {
      let ids = [];
      if (isShunt && !isAutoShunt) {
        ids = skillList;
      } else if (isShunt && isAutoShunt) {
        ids = autoShuntSkillList;
      } else {
        ids = Array.isArray(id) ? id : [id];
      }
      const skillGroup = (skillGroups || [])?.filter(o => {
        if (ids?.length) {
          return ids?.includes(o?.ID);
        } else {
          return '';
        }
      });
      if (supportOverflow) {
        const group = (ticketAllSkillGroups || [])?.filter(o => {
          if (ids?.length) {
            return ids?.includes(o?.ID);
          } else {
            return '';
          }
        });
        return group?.map(val => val?.Name)?.join('、');
      }
      if (skillGroup.length) {
        return skillGroup?.map(val => val?.Name)?.join('、');
      }
      return id;
    };
    const getBotName = id => {
      const bot = (botList || []).find(o => o.value === Number(id || '0'));
      if (bot) {
        return bot?.name;
      }
      return id;
    };

    // Convert the rule field to the specific rule name
    const getFieldDisplayName = (filterData = {} as any) => {
      const FieldName = filterData?.Lhs?.VarExpr || filterData?.Lhs?.FeatureExpr?.FeatureName;
      const OperatorId = filterData?.OpCheck;
      let FieldValue =
        filterData?.Rhs?.Constant ||
        filterData?.Rhs?.ConstantList ||
        filterData?.Rhs?.ExprList ||
        filterData?.Rhs?.VonstantList;
      if (FieldName === 'ticket_category.full_category_ids') {
        const categoryIds = [];
        FieldValue.forEach(item => {
          if (item.split(',').length > 2) {
            categoryIds.push(item);
          }
        });
        FieldValue = [...categoryIds];
      }
      if (Array.isArray(FieldValue)) {
        FieldValue = FieldValue.map(item => {
          const isFlag = isJSON(item);
          if (isFlag) {
            return safeJSONParse(item);
          } else {
            return item;
          }
        });
      } else {
        const isFlag = isJSON(FieldValue);
        if (isFlag) {
          FieldValue = safeJSONParse(FieldValue);
        }
        // FieldValue = safeJSONParse(FieldValue)
      }
      const field = fieldList.find(o => o.FieldName === FieldName);
      if (field) {
        const { FieldDisplayName, OperatorFieldvalues } = field;
        const { FieldValueType, FieldValueList = [] } = OperatorFieldvalues[OperatorId] || {};
        let FieldValueName = '';
        switch (FieldValueType) {
          case 8: // Time radio TimePicker
            FieldValueName = numberToTime(FieldValue);
            break;
          case 801: // Time radio TimePicker
            FieldValueName = millisecondToTime(FieldValue)?.join(' ~ ');
            break;
          case 17: // input float
            FieldValueName = formatNum(FieldValue);
            break;
          case 1: // Radio
            FieldValueName =
              FieldValueList.find(o => {
                if (typeof FieldValue === 'string') {
                  return o.value === FieldValue;
                } else {
                  return o.value === String(FieldValue);
                }
              })?.name || FieldValue;
            break;
          case 2: // Multiple choice
          case 701:
            FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
              .reduce((str, val) => {
                const v = FieldValueList.find(o => o.value === String(val))?.name;
                return `${str}、${v ? v : val}`;
              }, '')
              .slice(1);

            break;
          case 7: // Tree
            FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
              .reduce((str, val) => {
                const target = getFieldByValue(
                  JSON.stringify([user?.accessPartyId, ruleType, FieldName, OperatorId]),
                  FieldValueList,
                  val
                );
                const v = target?.name;
                return `${str}、${v ? v : val}`;
              }, '')
              .slice(1);
            break;
          case 3: // Mix (multiple choice + input box)
          case 4: // Time control
          case 14:
            FieldValueName = format(Number(FieldValue), 'YYYY-mm-dd HH: ii ss');
            break;

          case 5: // Input box
          case 6: // Batch Input
          default:
            FieldValueName = FieldValue;
        }

        return {
          FieldDisplayName,
          FieldValueName: Array.isArray(FieldValueName) ? FieldValueName.join('、') : FieldValueName,
        };
      }

      return {
        FieldDisplayName: FieldName,
        FieldValueName: Array.isArray(FieldValue) ? FieldValue.join('、') : FieldValue,
      };
    };

    const renderFilterComponents = (filterData, index, Filter) => {
      if (!filterData) {
        return null;
      }
      const FilterUnit = Filter?.Conditions || [];
      const OperateTypes = Filter?.OpGroup;
      const { FieldDisplayName, FieldValueName } = getFieldDisplayName(filterData);
      const isLastFilter = FilterUnit.length === index + 1;
      const contactText = isLastFilter ? null : (
        <span className={styles.ruleContentLabel}>{config.ConditionOperateType[OperateTypes]()}</span>
      );
      return (
        <>
          <span className={styles.ruleContentText}>{FieldDisplayName}</span>
          <span className={styles.ruleContentLabel}>
            {config.ruleOpcheckArr.filter(item => item.serveOp === filterData.OpCheck)[0]?.clientName()}
          </span>
          <span className={styles.ruleContentText}>{FieldValueName}</span>
          {contactText}
        </>
      );
    };

    // Specific rules of the rule list (xxx if xxx)
    const renderConditionGroupsComps = (filterDatas, k) => (
      <React.Fragment key={k}>
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
          <span className={styles.bracketsColor}>(</span>
        )}
        {filterDatas?.Conditions ?
          filterDatas?.Conditions?.map((filterData, h) => (
            <React.Fragment key={h}>{renderFilterComponents(filterData, h, filterDatas)}</React.Fragment>
            )) :
          filterDatas?.ConditionGroups?.map((newFilterDatas, n) => (
            <React.Fragment key={n}>
              {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                <span className={styles.bracketsColor}>(</span>
                )}
              {newFilterDatas?.Conditions?.map((newFilterData, m) => (
                <React.Fragment key={m}>{renderFilterComponents(newFilterData, m, newFilterDatas)}</React.Fragment>
                ))}
              {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                <span className={styles.bracketsColor}>)</span>
                )}
              {filterDatas?.ConditionGroups.length === n + 1 ? null : (
                <span className={styles.ruleContentLabel}>{config.ConditionOperateType[filterDatas?.OpGroup]()}</span>
                )}
            </React.Fragment>
            ))}
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
          <span className={styles.bracketsColor}>)</span>
        )}
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 ? null : (
          <span className={styles.ruleContentLabel}>
            {config.ConditionOperateType[ruleDataChild?.Expression?.OpGroup]()}
          </span>
        )}
      </React.Fragment>
    );
    return (
      <>
        {/* {it != 0 ? <div className={styles.empty} /> : null} */}
        <div className={styles.ruleBoxHeader}>
          <div className={styles.headerTitle}>
            <div className={styles.headerLeftIconBox}>
              <span>{`${ruleDataChild.ruleIndex + 1}.`}</span>
            </div>
            <div className={styles.headerNameBox}>
              <Tooltip className={styles.tooltip} content={ruleDataChild.DisplayName}>
                <span className={styles.headerName}>{ruleDataChild.DisplayName}</span>
              </Tooltip>
              {ruleDataChild.DraftEditType === 1 ? (
                <Tag className={styles.statusTag} color={'violet'}>
                  {config.DraftEditType[ruleDataChild.DraftEditType]()}
                </Tag>
              ) : ruleDataChild.DraftEditType === 2 ? (
                <Tag className={styles.statusTag} color={'violet'}>
                  {config.DraftEditType[ruleDataChild.DraftEditType]()}
                </Tag>
              ) : null}

              <Tag className={styles.statusTag} color={ruleDataChild.Status === 1 ? 'teal' : 'red'}>
                {status}
              </Tag>
            </div>
          </div>
          <div className={styles.cardOperations}>
            {ruleDataChild.Priority === 9999 || inputValue ? null : (
              <>
                <CardOperation
                  disabled={isTTP() || havePermission()}
                  icon={TopIcon.id}
                  tooltip={I18n.t('top', {}, '置顶')}
                  onClick={() => {
                    onReOrder(CardReOrderType.Top, ruleDataChild);
                  }}
                />
                <CardOperation
                  disabled={isTTP() || havePermission()}
                  icon={UpIcon.id}
                  tooltip={I18n.t('move_one_up', {}, '向上挪动一位')}
                  onClick={() => {
                    onReOrder(CardReOrderType.Up, ruleDataChild);
                  }}
                />
                <CardOperation
                  disabled={isTTP() || havePermission()}
                  icon={DownIcon.id}
                  tooltip={I18n.t('move_one_down', {}, '向下挪动一位')}
                  onClick={() => {
                    onReOrder(CardReOrderType.Down, ruleDataChild);
                  }}
                />
                <CardOperation
                  disabled={isTTP() || havePermission()}
                  icon={BottomIcon.id}
                  tooltip={I18n.t('bottom', {}, '置底')}
                  onClick={() => {
                    onReOrder(CardReOrderType.Bottom, ruleDataChild);
                  }}
                />
              </>
            )}
            {Boolean(
              getDropdownList({
                ruleDataChild,
                ruleType,
                clickDropItem,
                clickDropItemEdit,
                clickClone,
                accessPartyId: user?.accessPartyId,
              })?.length
            ) && (
              <KefuDropdown
                dropdownItems={getDropdownList({
                  ruleDataChild,
                  ruleType,
                  clickDropItem,
                  clickDropItemEdit,
                  clickClone,
                  accessPartyId: user?.accessPartyId,
                })}
              />
            )}
          </div>
        </div>
        <div className={styles.positionText}>
          <div className={styles.headerTipBox}>
            <span className={styles.headerTips}>
              {`${I18n.t(
                '{placeholder0}_updated_to_{placeholder2}',
                {
                  placeholder0: ruleDataChild.UpdaterAgentName,
                  placeholder2: ruleDataChild.UpdatedAt,
                },
                '{placeholder0} 更新于 {placeholder2}'
              )}`}
            </span>
          </div>
          <div className={styles.ruleFilterBox}>
            <div className={styles.ruleFilterDot} />
            <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
              <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>

              {isLast ? (
                <span className={styles.ruleContentText}>{I18n.t('missed_the_above_rules', {}, '未命中以上规则')}</span>
              ) : ruleDataChild?.Expression?.Conditions ? (
                ruleDataChild?.Expression?.Conditions?.map((filterData, k) => (
                  <React.Fragment key={k}>
                    {renderFilterComponents(filterData, k, ruleDataChild?.Expression)}
                  </React.Fragment>
                ))
              ) : (
                ruleDataChild?.Expression?.ConditionGroups?.map((filterDatas, k) =>
                  renderConditionGroupsComps(filterDatas, k)
                )
              )}
            </CollapseHorizontalText>
          </div>
          {/* bot */}
          {ruleType === routeType.bot_routing && ruleDataChild.IsOpen === 1 && (
            <div className={styles.ruleFilterBox}>
              <div className={styles.ruleSkillDot} />
              <span className={styles.ruleContentLabel}>{I18n.t('provide', {}, '提供')}</span>
              <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
            </div>
          )}
          {/* Distribution */}
          <div className={styles.ruleSkillBox}>
            {ruleType === routeType.bot_routing ? (
              ruleDataChild.IsOpen === 1 ? (
                <>
                  <div className={styles.ruleSkillDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={BotIcon} />
                  <span className={styles.ruleContentText}>{getBotName(ruleDataChild.BotId)}</span>
                </>
              ) : (
                <>
                  <div className={styles.ruleEmptyDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('not_available', {}, '不提供')}</span>
                  <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
                </>
              )
            ) : ruleType === routeType.service_routing ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>
                    {getSkillGroupName({
                      id: ruleDataChild.SkillGroupId,
                      isShunt: ruleDataChild.isShunt,
                      skillList: ruleDataChild?.skillList?.map(val => val.value),
                      isAutoShunt: ruleDataChild.isAutoShunt,
                      autoShuntSkillList: ruleDataChild.autoShuntSkillList,
                    })}
                  </span>
                  {Boolean(ruleDataChild.SupportOverflow) && (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('and_when', {}, '且当')}</span>
                      <span className={styles.ruleContentText}>
                        {getSkillGroupName({ id: ruleDataChild.SkillGroupId })}
                      </span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('the_number_of_people_in_line_is_greater_than', {}, '排队人数大于')}
                      </span>
                      <span className={styles.ruleContentText}>{ruleDataChild.OverflowThreshold}</span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('when_people__overflow_to', {}, '人时 溢出至')}
                      </span>
                      <span className={styles.ruleContentText}>
                        {ruleDataChild.SkillGroupOverflow?.map(groupId =>
                          getSkillGroupName({ id: groupId, supportOverflow: ruleDataChild.SupportOverflow })
                        ).join('、')}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
            ) : ruleType === routeType.ticket_routing ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  {ruleDataChild?.RouteType ? (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('routing_timing_is', {}, '路由时机是')}</span>
                      <span className={styles.ruleContentText}>
                        {ruleDataChild?.RouteType?.map(v => routeTypeMap[v]?.())?.join(',')}
                      </span>
                      <span className={styles.ruleContentLabel}>{I18n.t('time', {}, '时')}</span>
                    </>
                  ) : (
                    <></>
                  )}
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>
                    {getSkillGroupName({
                      id: ruleDataChild.SkillGroupId,
                      isShunt: ruleDataChild.isShunt,
                      skillList: ruleDataChild?.skillList?.map(val => val.value),
                      isAutoShunt: ruleDataChild.isAutoShunt,
                      autoShuntSkillList: ruleDataChild.autoShuntSkillList,
                    })}
                  </span>
                </CollapseHorizontalText>
              </div>
            ) : null}
          </div>

          {[routeType.ticket_routing].includes(ruleType) && Boolean(ruleDataChild.SupportOverflow) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>
                    {routeType.ticket_routing === ruleType ?
                      I18n.t('work_orders_to_be_assigned_within_the_group', {}, '组内待分配工单') :
                      null}
                  </span>
                  <span className={styles.ruleContentLabel}>{I18n.t('greater_than', {}, '大于')}</span>
                  <span className={styles.ruleContentText}>{ruleDataChild.queueOverflowCount}</span>
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('overflow_to', {}, '溢出至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>
                      {ruleDataChild.skillGroupOverflowList
                        ?.map(group =>
                          getSkillGroupName({ id: group.id, supportOverflow: ruleDataChild.SupportOverflow })
                        )
                        .join('、')}
                    </span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}

          {ruleType === routeType.ticket_routing && Boolean(ruleDataChild.SupportReclaim) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>{ruleDataChild.ReclaimConditionName}</span>
                  {ruleDataChild.ReclaimType === 'time' && (
                    <>
                      <span className={styles.ruleContentText}>
                        {`${I18n.t(
                          '{placeholder0}_hours_{placeholder2}_minutes',
                          {
                            placeholder0: ruleDataChild.ReclaimHours,
                            placeholder2: ruleDataChild.ReclaimMinutes,
                          },
                          '{placeholder0} 小时 {placeholder2} 分'
                        )}`}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('reclaimed_to', {}, '回捞至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>
                      {getSkillGroupName({ id: ruleDataChild.SkillGroupReclaim })}
                    </span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };
  const renderRuleContent = (ruleData, index) => {
    const highLight = ruleData.Id === newRuleId ? '1px solid #361C8A' : '';
    return (
      <div className={styles.ruleBox} style={{ border: highLight }}>
        {ruleContent(ruleData, index)}
      </div>
    );
  };

  return (
    <div>
      {ruleDataList.length ? (
        <div className={styles.title}>
          <div className={styles.titlePoint} />
          <span style={{ color: 'var(--color-text-2)' }}>
            {`${I18n.t(
              '{placeholder1}_rule_judgment_in_top_down_order',
              {
                placeholder1:
                  ruleType === routeType.accessparty_routing ?
                    I18n.t('shunt', {}, '分流') :
                    I18n.t('routing', {}, '路由'),
              },
              '从上而下的顺序依次进行{placeholder1}规则判断'
            )}`}
          </span>
        </div>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={`${I18n.t(
            'no_{placeholder1}_rule',
            {
              placeholder1:
                ruleType === routeType.accessparty_routing ?
                  I18n.t('shunt', {}, '分流') :
                  I18n.t('routing', {}, '路由'),
            },
            '暂无{placeholder1}规则'
          )}`}
          // eslint-disable-next-line react/no-children-prop
          children={null}
        />
      )}
      <DndProvider backend={HTML5Backend}>
        <div style={{ marginBottom: 40 }}>
          {ruleDataList.map((ruleData, index) => {
            const singleRuleData = index > 0 ? ruleDataList[index - 1] : { Priority: 0 };
            return (
              <div key={index}>
                {inputValue ? null : (
                  <Tooltip
                    content={`${I18n.t(
                      'no_{placeholder1}_rule',
                      {
                        placeholder1:
                          ruleType === routeType.accessparty_routing ?
                            I18n.t('shunt', {}, '分流') :
                            I18n.t('routing', {}, '路由'),
                      },
                      '暂无{placeholder1}规则'
                    )}`}
                  >
                    <Button
                      className={styles.ruleBtn}
                      disabled={isTTP() || havePermission() || !hasEditAuth}
                      onClick={() => {
                        createRouteRule(ruleType, ruleData, index, singleRuleData);
                      }}
                      icon="plus_circle"
                      style={{ width: 32 }}
                    />
                  </Tooltip>
                )}
                <div className={styles.intervalBox} />
                {renderRuleContent(ruleData, index)}
              </div>
            );
          })}
        </div>
      </DndProvider>
      <Modal
        title={I18n.t('delete', {}, '删除')}
        visible={modalVisible}
        onOk={handleOk}
        cancelText={I18n.t('cancel', {}, '取消')}
        okText={I18n.t('ok', {}, '确定')}
        onCancel={handleCancel}
        icon={<Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />}
      >
        <p>
          {I18n.t(
            'once_deleted__the_data_will_not_be_recovered__please_operate_with_caution',
            {},
            '一旦删除，数据将无法恢复，请谨慎操作'
          )}
        </p>
      </Modal>
    </div>
  );
};

export default RuleList;
