import { isTTP } from '@/common/utils/env';
import { I18n } from '@ies/starling_intl';
import { OperationType } from '@/const/enums';
import { shared } from '@ies/unified_communications_sdk';
import { DOOR_AUTHORITY_MAP } from '@/common/constants/property';
import { havePermission } from '@/common/utils/hasPermission';

export const getDropdownList = ({
  ruleDataChild,
  ruleType,
  clickDropItem,
  clickDropItemEdit,
  clickClone,
  accessPartyId,
}): any[] => {
  const pathname = location?.pathname?.split('/');
  const typeStr = pathname?.[pathname?.length - 1];
  const hasEditAuth = shared.judgeAccessPartyRights(
    `res.route_management/${DOOR_AUTHORITY_MAP[typeStr].EDIT}`,
    accessPartyId
  );
  const hasViewAuth = shared.judgeAccessPartyRights(
    `res.route_management/${DOOR_AUTHORITY_MAP[typeStr].VIEW}`,
    accessPartyId
  );
  let dropdownItems = [];
  if (!hasEditAuth && hasViewAuth) {
    dropdownItems = [
      {
        text: I18n.t('view', {}, '查看'),
        onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
      },
    ];
  }
  if (hasEditAuth && !hasViewAuth) {
    dropdownItems =
      ruleDataChild?.Priority !== 9999 ?
        [
          {
            text: ruleDataChild?.Status === 1 ? I18n.t('disable', {}, '禁用') : I18n.t('enable', {}, '启用'),
            onClick: () =>
              clickDropItem(
                  ruleDataChild?.Status === 1 ? OperationType.Disable : OperationType.Enable,
                  ruleDataChild
              ),
          },
          {
            text: I18n.t('edit', {}, '编辑'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
          },
          {
            text: I18n.t('copy', {}, '复制'),
            onClick: () => clickClone(OperationType.Clone, ruleDataChild, ruleType),
          },
          {
            text: I18n.t('delete', {}, '删除'),
            type: 'danger',
            onClick: () => clickDropItem(OperationType.Delete, ruleDataChild),
          },
        ] :
        [
          {
            text: I18n.t('edit', {}, '编辑'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
          },
        ];
  }
  if (hasEditAuth && hasViewAuth) {
    dropdownItems =
      ruleDataChild?.Priority !== 9999 ?
        [
          {
            text: ruleDataChild?.Status === 1 ? I18n.t('disable', {}, '禁用') : I18n.t('enable', {}, '启用'),
            onClick: () =>
              clickDropItem(
                  ruleDataChild?.Status === 1 ? OperationType.Disable : OperationType.Enable,
                  ruleDataChild
              ),
          },
          {
            text: I18n.t('view', {}, '查看'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
          },
          {
            text: I18n.t('edit', {}, '编辑'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
          },
          {
            text: I18n.t('copy', {}, '复制'),
            onClick: () => clickClone(OperationType.Clone, ruleDataChild, ruleType),
          },
          {
            text: I18n.t('delete', {}, '删除'),
            type: 'danger',
            onClick: () => clickDropItem(OperationType.Delete, ruleDataChild),
          },
        ] :
        [
          {
            text: I18n.t('view', {}, '查看'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
          },
          {
            text: I18n.t('edit', {}, '编辑'),
            onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
          },
        ];
  }

  if (isTTP() || havePermission()) {
    dropdownItems = [
      {
        text: I18n.t('view', {}, '查看'),
        onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
      },
    ];
  }
  if (!hasEditAuth && !hasViewAuth) {
    dropdownItems = [];
  }
  return dropdownItems;
};
