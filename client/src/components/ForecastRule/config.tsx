import { I18n } from '@ies/starling_intl';
export const ChannelTypeMap = {
  1: () => I18n.t('im_skill_group', {}, 'IM 技能组'), // IM
  2: () => I18n.t('work_order_skill_group', {}, '工单技能组'), // Work order
  3: () => I18n.t('telephone_skills_group', {}, '电话技能组'), // Phone
  4: () => I18n.t('other_skill_groups', {}, '其他技能组'), // Other
};

export const FilterOperateType = {
  0: () => I18n.t('or_2', {}, '或者'),
  1: () => I18n.t('and_2', {}, '并且'),
};
export const ConditionOperateType = {
  AND: () => I18n.t('and', {}, '且'),
  OR: () => I18n.t('or', {}, '或'),
  NOT: () => I18n.t('not', {}, '非'),
};

export const DraftEditType = {
  1: () => I18n.t('added_rules', {}, '新增规则'),
  2: () => I18n.t('editing_rules', {}, '编辑规则'),
  0: () => I18n.t('unmodified_rule', {}, '未修改规则'),
};
// export const OperatorTypeMap = {
//   NUMBER_EQUAL: 1,
//   NUMBER_NOT_EQUAL: 2,
//   CONTAINS: 3,
//   GREATER: 4,
//   LESS: 5,
//   GREATER_OR_EQUAL: 6,
//   LESS_OR_EQUAL: 7,
//   START_WITH: 8,
//   END_WITH: 9,
//   IS_NULL: 10,
//   IS_NOT_NULL: 11,
//   IN: 12,
//   STRING_CONTAINS: 13,
//   STRING_EQUAL: 14,
//   STRING_NOT_EQUAL: 15,
//   TIME_EQUAL: 16,
//   TIME_NOT_EQUAL: 17,
//   TIME_GREATER: 18,
//   TIME_LESS: 19,
//   TIME_GREATER_OR_EQUAL: 20,
//   TIME_LESS_OR_EQUAL: 21,
//   NOT_IN: 22,
//   LIST_EQUAL: 30,
//   LIST_NOT_EQUAL: 31,
//   STRING_NOT_CONTAINS: 32,
// }

export const OperatorTypeMap = {
  [1]: () => I18n.t('equals', {}, '等于'),
  [2]: () => I18n.t('not_equal_to', {}, '不等于'),
  [3]: () => I18n.t('include', {}, '包含'),
  [4]: () => I18n.t('greater_than', {}, '大于'),
  [5]: () => I18n.t('less_than', {}, '小于'),
  [6]: () => I18n.t('greater_than_or_equal_to', {}, '大于等于'),
  [7]: () => I18n.t('less_than_or_equal_to', {}, '小于等于'),
  [8]: () => I18n.t('started_with', {}, '开始于'),
  [9]: () => I18n.t('ended_in', {}, '结束于'),
  [10]: () => I18n.t('empty', {}, '为空'),
  [11]: () => I18n.t('not_empty', {}, '不为空'),
  [12]: () => I18n.t('include', {}, '包含'),
  [13]: () => I18n.t('include', {}, '包含'),
  [14]: () => I18n.t('equals', {}, '等于'),
  [15]: () => I18n.t('not_equal_to', {}, '不等于'),
  [16]: () => I18n.t('equals', {}, '等于'),
  [17]: () => I18n.t('not_equal_to', {}, '不等于'),
  [18]: () => I18n.t('greater_than', {}, '大于'),
  [19]: () => I18n.t('less_than', {}, '小于'),
  [20]: () => I18n.t('greater_than_or_equal_to', {}, '大于等于'),
  [21]: () => I18n.t('less_than_or_equal_to', {}, '小于等于'),
  [22]: () => I18n.t('not_included', {}, '不包含'),
  [30]: () => I18n.t('equals', {}, '等于'),
  [31]: () => I18n.t('not_equal_to', {}, '不等于'),
  [32]: () => I18n.t('not_included', {}, '不包含'),
  [33]: () => I18n.t('include', {}, '包含'),
  [34]: () => I18n.t('not_included', {}, '不包含'),
  [35]: () => I18n.t('belong_to', {}, '属于'),
  [36]: () => I18n.t('do_not_belong', {}, '不属于'),
};

export const ruleOpcheckArr = [
  { serveOp: '==', clientOp: 1, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 2, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: 'CONTAINS', clientOp: 3, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: '>', clientOp: 4, opType: 'math', clientName: () => I18n.t('greater_than', {}, '大于') },
  { serveOp: '<', clientOp: 5, opType: 'math', clientName: () => I18n.t('less_than', {}, '小于') },
  { serveOp: '>=', clientOp: 6, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, '大于等于') },
  { serveOp: '<=', clientOp: 7, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, '小于等于') },
  { serveOp: 'START_WITH', clientOp: 8, opType: 'fun', clientName: () => I18n.t('started_with', {}, '开始于') },
  { serveOp: 'END_WITH', clientOp: 9, opType: 'fun', clientName: () => I18n.t('ended_in', {}, '结束于') },
  { serveOp: 'IS_NULL', clientOp: 10, opType: 'fun', clientName: () => I18n.t('empty', {}, '为空') },
  { serveOp: 'IS_NOT_NULL', clientOp: 11, opType: 'fun', clientName: () => I18n.t('not_empty', {}, '不为空') },
  { serveOp: 'LIST_IN', clientOp: 12, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: 'STRING_CONTAINS', clientOp: 13, opType: 'fun', clientName: () => I18n.t('include', {}, '包含') },
  { serveOp: '==', clientOp: 14, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 15, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: '==', clientOp: 16, opType: 'math', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: '!=', clientOp: 17, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  { serveOp: '>', clientOp: 18, opType: 'math', clientName: () => I18n.t('greater_than', {}, '大于') },
  { serveOp: '<', clientOp: 19, opType: 'math', clientName: () => I18n.t('less_than', {}, '小于') },
  { serveOp: '>=', clientOp: 20, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, '大于等于') },
  { serveOp: '<=', clientOp: 21, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, '小于等于') },
  { serveOp: 'LIST_NOT_IN', clientOp: 22, opType: 'fun', clientName: () => I18n.t('not_included', {}, '不包含') },
  { serveOp: 'LIST_EQUAL', clientOp: 30, opType: 'fun', clientName: () => I18n.t('equals', {}, '等于') },
  { serveOp: 'LIST_NOT_EQUAL', clientOp: 31, opType: 'fun', clientName: () => I18n.t('not_equal_to', {}, '不等于') },
  {
    serveOp: 'STRING_NOT_CONTAINS',
    clientOp: 32,
    opType: 'fun',
    clientName: () => I18n.t('not_included', {}, '不包含'),
  },
  { serveOp: 'LIST_RETAIN', clientOp: 33, opType: 'fun', clientName: () => I18n.t('intersect', {}, '有交集') },
  { serveOp: 'NOT LIST_IN', clientOp: 34, opType: 'fun', clientName: () => I18n.t('not_included', {}, '不包含') },
  { serveOp: 'BETWEEN_ALL_CLOSE', clientOp: 35, opType: 'fun', clientName: () => I18n.t('belong_to', {}, '属于') },
  {
    serveOp: 'NOT BETWEEN_ALL_OPEN',
    clientOp: 36,
    opType: 'fun',
    clientName: () => I18n.t('do_not_belong', {}, '不属于'),
  },
  {
    serveOp: 'NOT LIST_RETAIN',
    clientOp: 37,
    opType: 'fun',
    clientName: () => I18n.t('no_intersection', {}, '无交集'),
  },
];

// Compatible with previous tag data
export const compatibilityOldData = (val, fieldList) => {
  let allTicketCategoryList = [];
  fieldList.map(item => {
    if (item.FieldMapName === 'ticket.category_id') {
      allTicketCategoryList = item.allTicketCategoryList;
    }
  });
  for (let i = 0; i < val?.Expression?.ConditionGroups?.length; i++) {
    const item = val?.Expression?.ConditionGroups[i];
    for (let j = 0; j < item?.Conditions?.length; j++) {
      const ele = item?.Conditions[j];
      const FeatureName = ele?.Lhs?.VarExpr || ele?.Lhs?.FeatureExpr?.FeatureName;
      if (FeatureName === '$CategoryId') {
        val.Expression.ConditionGroups[i].Conditions[j].Lhs.VarExpr = 'ticket_category.full_category_ids';
        ele?.Rhs?.ConstantList?.map((idx, index) => {
          if (idx.split(',').length === 1) {
            allTicketCategoryList.map(tel => {
              if (tel.includes(idx)) {
                val.Expression.ConditionGroups[i].Conditions[j].Rhs.ConstantList[index] = `'${tel.join()}'`;
              }
            });
          }
        });
      }
      if (FeatureName === 'ticket_category.full_category_ids') {
        ele.Rhs.ConstantList = ele?.Rhs?.ConstantList?.filter(idx => idx.split(',')?.length === 3);
      }
    }
  }
  return val;
};
