import { debounce } from 'lodash';
import React, { useEffect, useState } from 'react';
import { Avatar, Select } from '@ies/semi-ui-react';
import { demoClient, Agent } from '@http_idl/demo';

const { Option } = Select;

export const UserSelect: React.FC<any> = ({ onChange, value, skillGroupId, agentName, ...props }) => {
  const [agentsOption, setAgentsOption] = useState<Agent[]>([]);
  const [values, setValues] = useState(value);
  const [loading, setLoading] = useState(false);
  const fecthAgentsByCondition = async (val: string, skillGroupId: string, type: string): Promise<void> => {
    setLoading(true);
    const vals = type === 'init' ? agentName : type === 'search' ? val : undefined;
    const res = await demoClient.GetSkillGroupAgents({
      Keyword: vals,
      SkillGroupId: skillGroupId,
    });
    if (res.Agents?.length) {
      setLoading(false);
      setAgentsOption(res?.Agents || []);
    } else {
      setLoading(false);
      setAgentsOption([]);
    }
  };
  useEffect(() => {
    if (skillGroupId || value) {
      fecthAgentsByCondition(value, skillGroupId, 'init');
    }
  }, [skillGroupId]);
  useEffect(() => {
    setValues(value);
  }, [value]);

  const handleSearch = debounce(async (val: string, skillGroupId: string) => {
    setValues('');
    setAgentsOption([]);
    fecthAgentsByCondition(val, skillGroupId, 'search');
  }, 750);
  const renderCustomOption = (item: Agent): JSX.Element => {
    const optionStyle = {
      display: 'flex',
      paddingLeft: 24,
      paddingTop: 10,
      paddingBottom: 10,
    };
    return (
      <Option key={item.ID} value={item.ID} style={optionStyle} showTick={true} {...item}>
        <Avatar size="small">{item.UserName.slice(0, 1)}</Avatar>
        <div style={{ marginLeft: 8 }}>
          <div style={{ fontSize: 14 }}>{item.UserName}</div>
          <div style={{ color: 'var(--color-text-2)', fontSize: 12, lineHeight: '16px', fontWeight: 'normal' }}>
            {item.Email}
          </div>
        </div>
      </Option>
    );
  };

  const renderSelectedItem = (optionNode: any): JSX.Element => (
    <>
      {optionNode?.UserName && (
        <>
          <Avatar size="extra-extra-small">{optionNode?.UserName?.slice(0, 1)}</Avatar>
          {optionNode.UserName}({optionNode.Email})
        </>
      )}
    </>
  );
  const handleChange = (val: string): void => {
    setValues(val);
    onChange && onChange(val);
  };
  return (
    <Select
      {...props}
      value={values}
      filter
      remote
      loading={loading}
      onSearch={(val): Promise<void> => handleSearch(val, skillGroupId)}
      renderSelectedItem={renderSelectedItem}
      onChange={handleChange}
    >
      {agentsOption.map((item: Agent, index: number): JSX.Element => renderCustomOption(item))}
    </Select>
  );
};
export default UserSelect;
