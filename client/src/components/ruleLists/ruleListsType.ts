import { I18n } from '@ies/starling_intl';
import { ruleGroupClient } from '../../pages/createRule/createRuleType';
type fieldItem = {
  FieldDisplayName: string;
  FieldMapName: string;
  FieldId: string;
  OperatorIds: Array<number>;
  Fieldvalues: any;
  [propName: string]: any;
};
export interface ruleListsProps {
  groupInfo: ruleGroupClient;
  parentIndex: number;
  formApi: any;
  values: any;
  changeJoinWay: (parentIndex: number, myIndex: number, op: string) => void;
  deleteItem: (parentIndex: number, myIndex: number) => void;
  changeFormValuesByRuleList: () => void;
  fieldList: Array<fieldItem>;
  deleteGroup?: (groupIndex: number) => void;
  viewType?: string;
  disabledGroup?: boolean;
}

export const ChannelTypeMap = {
  1: I18n.t('im_skill_group', {}, 'IM 技能组'), // IM
  2: I18n.t('work_order_skill_group', {}, '工单技能组'), // Work order
  3: I18n.t('telephone_skills_group', {}, '电话技能组'), // Phone
  4: I18n.t('other_skill_groups', {}, '其他技能组'), // Other
};

export const OperatorTypeMap = {
  1: I18n.t('equals', {}, '等于'),
  2: I18n.t('not_equal_to', {}, '不等于'),
  3: I18n.t('include', {}, '包含'),
  4: I18n.t('greater_than', {}, '大于'),
  5: I18n.t('less_than', {}, '小于'),
  6: I18n.t('greater_than_or_equal_to', {}, '大于等于'),
  7: I18n.t('less_than_or_equal_to', {}, '小于等于'),
  8: I18n.t('started_with', {}, '开始于'),
  9: I18n.t('ended_in', {}, '结束于'),
  10: I18n.t('empty', {}, '为空'),
  11: I18n.t('not_empty', {}, '不为空'),
  12: I18n.t('include', {}, '包含'),
  13: I18n.t('include', {}, '包含'),
  14: I18n.t('equals', {}, '等于'),
  15: I18n.t('not_equal_to', {}, '不等于'),
  16: I18n.t('equals', {}, '等于'),
  17: I18n.t('not_equal_to', {}, '不等于'),
  18: I18n.t('greater_than', {}, '大于'),
  19: I18n.t('less_than', {}, '小于'),
  20: I18n.t('greater_than_or_equal_to', {}, '大于等于'),
  21: I18n.t('less_than_or_equal_to', {}, '小于等于'),
  22: I18n.t('not_included', {}, '不包含'),
  30: I18n.t('equals', {}, '等于'),
  31: I18n.t('not_equal_to', {}, '不等于'),
  32: I18n.t('not_included', {}, '不包含'),
  33: I18n.t('include', {}, '包含'),
  34: I18n.t('not_included', {}, '不包含'),
  35: I18n.t('belong_to', {}, '属于'),
  36: I18n.t('do_not_belong', {}, '不属于'),
};
