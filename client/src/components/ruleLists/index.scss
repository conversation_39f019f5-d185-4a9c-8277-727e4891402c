.rule-list-lhs {
  width: 200px;
}

.rule-list-rhs {
  width: 311px;
  margin-left: 8px !important;
}

.rule-list-op {
  width: 128px;
  margin-left: 8px !important;
}

.rule-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.ruleMinusCircle {
  margin-left: 8px;
}

.ruleListBox {
  border: 1px solid rgba(var(--grey-1), 1);
  border-radius: 3px;
  padding: 10px 20px 10px 10px;
  margin-bottom: 10px;
}

.rule-list-item-select {
  min-width: 112px;
  background-color: var(--color-fill-0);
  display: flex;
  align-items: center;
  padding-left: 12px;
  border-radius: 3px;
  color: var(--color-text-0);
  min-height: 32px;
  cursor: pointer;
  outline: none;
  border: 1px solid transparent;
  font-size: 14px;
  align-items: center;

  &:hover {
    border: 1px solid rgba(var(--teal-5), 1);
    background-color: var(--color-fill-0);
  }

  &:focus {
    border: 1px solid rgba(var(--teal-5), 1);
  }
}

.rule-list-item-select-tag {
  display: inline-block;
  flex-grow: 1;
}

.rule-list-item-select-tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex-grow: 1;
  gap: 2px;
  min-height: 24px;
}

.rule-list-item-select-tag-popover {
  width: 500px;
  height: 240px;
  overflow: auto;
}

.rule-list-header {
  width: 100%;
  display: flex;

  .rule-list-header-text {
    flex: 1;
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;
    color: #222727;
  }
  // .rule-list-header-btn{
  //   flex: 1;
  // }
}

.rule-list-item-tag {
  height: auto !important;
  min-height: 20px !important;
  max-width: 250px;
  overflow: hidden !important;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 0;

  .rule-list-item-tag-span {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
}
