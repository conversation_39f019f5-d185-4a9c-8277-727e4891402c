import { I18n } from "@ies/starling_intl";
import React, { useEffect } from "react";
import { ruleListsProps } from "./ruleListsType";
import { ruleOpcheckArr } from "@/pages/createRule/createRuleType";
import { countIntegerNumberRule, getRequiredRule } from "@common/rules";
import {
  Form,
  Button,
  LocaleProvider,
  Tag,
  Popover,
  Input,
  Icon,
  Tooltip,
} from "@ies/semi-ui-react";
import { formatTreeData } from "@/common/utils/treeData";
import * as styles from "./index.scss";
import * as dayjs from "dayjs";
import zh_CN from "@ies/semi-ui-react/locale/source/zh_CN";
import en_GB from "@ies/semi-ui-react/locale/source/en_GB";
import { safeJSONParse } from "@/common/utils";

const operatorMap = {
  0: "OR",
  1: "AND",
};
/* conditional group component */
const pathname = location.pathname.split("/");
const typeStr = pathname[pathname.length - 1];

const renderSelectValue = function (
  filterKey,
  index,
  fieldList,
  parentIndex,
  OperatorIdKey,
  formApi
) {
  if (!formApi) {
    return;
  }
  const filter = formApi.getValue(filterKey);
  const OperatorId = formApi.getValue(OperatorIdKey);
  const field = fieldList.find((o) => o?.FieldName === filter);

  if (!filter || !field) {
    return null;
  }
  const isEnLang = (): boolean => {
    const lang = window.localStorage.getItem("unitedLang");
    return lang === "en";
  };

  const fieldValue = field?.OperatorFieldvalues[OperatorId];
  if (!fieldValue) {
    return null;
  }

  const { FieldValueType } = fieldValue;
  let { FieldValueList } = fieldValue;
  if (field.FieldId === "362" && FieldValueList?.[0]) {
    FieldValueList = safeJSONParse(
      safeJSONParse(FieldValueList?.[0])?.ops_owner_name_list
    );
  }

  // debugger;
  let EntityValueComponents = null;
  const triggerRender = (props) => {
    const { value, field, componentProps, onChange, inputValue } = props;
    let showValue = [];
    let heidenValue = [];
    if (value.length > 2) {
      showValue = value.slice(0, 2);
      heidenValue = value.slice(2, value.length);
    } else {
      showValue = value;
    }
    const onClose = (tagValue) => {
      formApi.setValue(
        field,
        value?.filter((val) => val?.value !== tagValue)?.map((val) => val.value)
      );
    };
    const renderSelectTags = (tags, close) => (
      <div className={styles.ruleListItemSelectTagPopover}>
        <div style={{ height: "100%", width: "100%", padding: "8px" }}>
          {tags.map((tag) => (
            <Tag
              style={{ margin: "2px 4px" }}
              closable
              color="white"
              key={tag.value}
              onClose={() => close(tag.value)}
            >
              {tag.label}
            </Tag>
          ))}
        </div>
      </div>
    );
    return (
      <div className={styles.ruleListItemSelect}>
        <div className={styles.ruleListItemSelectTagWrapper}>
          {showValue?.map((item) => (
            <Tooltip key={item.value} content={item.label} position="top">
              <Tag
                style={{ margin: "1px 2px" }}
                closable
                color="white"
                onClose={() => onClose(item.value)}
                className={styles.ruleListItemTag}
              >
                {item.label}
              </Tag>
            </Tooltip>
          ))}
          {value.length > 2 && (
            <div style={{ margin: "0 8px", flexShrink: 0 }}>
              <Popover content={renderSelectTags(heidenValue, onClose)}>
                <span>{`+${value.length - 2}`}</span>
              </Popover>
            </div>
          )}
          {componentProps?.filter ? (
            <Input
              value={inputValue}
              onChange={onChange}
              style={{
                height: "100%",
                border: "none",
                backgroundColor: "transparent",
                minWidth: "60px",
                flex: "1 1 auto",
              }}
            />
          ) : null}
        </div>
        <div className="united_route_manage-select-arrow">
          <Icon type="chevron_down" />
        </div>
      </div>
    );
  };
  const treeTriggerRender = (props) => {
    const { value, field } = props;
    let showValue = [];
    let heidenValue = [];
    if (value.length > 2) {
      showValue = value.slice(0, 2);
      heidenValue = value.slice(2, value.length);
    } else {
      showValue = value;
    }
    const onClose = (tagValue) => {
      formApi.setValue(
        field,
        value?.filter((val) => val?.value !== tagValue)?.map((val) => val.value)
      );
    };
    const renderSelectTags = (tags, close) => (
      <div className={styles.ruleListItemSelectTagPopover}>
        <div style={{ height: "100%", width: "100%", padding: "8px" }}>
          {tags.map((tag) => (
            <Tag
              style={{ margin: "2px 4px" }}
              closable
              color="white"
              key={tag.value}
              onClose={() => close(tag.value)}
            >
              {tag.label}
            </Tag>
          ))}
        </div>
      </div>
    );
    return (
      <div className={styles.ruleListItemSelect}>
        <div className={styles.ruleListItemSelectTagWrapper}>
          {showValue?.map((item) => (
            <Tooltip key={item.value} content={item.label} position="top">
              <Tag
                style={{ margin: "1px 2px" }}
                closable
                color="white"
                onClose={() => onClose(item.value)}
                className={styles.ruleListItemTag}
              >
                {item.label}
              </Tag>
            </Tooltip>
          ))}
          {value.length > 2 && (
            <div style={{ margin: "0 8px", flexShrink: 0 }}>
              <Popover content={renderSelectTags(heidenValue, onClose)}>
                <span>{`+${value.length - 2}`}</span>
              </Popover>
            </div>
          )}
        </div>
        <div className="united_route_manage-select-arrow">
          <Icon type="chevron_down" />
        </div>
      </div>
    );
  };
  // console.log(FieldValueType);
  switch (FieldValueType) {
    case 1: // Radio
      EntityValueComponents = (
        <Form.Select
          fieldClassName={styles.ruleListRhs}
          style={{ width: 311 }}
          fieldStyle={{ padding: "4px 0px" }}
          field={`item_rhs_${parentIndex}_${index}`}
          noLabel={true}
          filter
        >
          {(FieldValueList || []).map((o, index) => (
            <Form.Select.Option key={index} value={o.value}>
              {o.name}
            </Form.Select.Option>
          ))}
        </Form.Select>
      );
      break;
    case 2: // Multiple choice
    case 701: // Multiple choice
      EntityValueComponents = (
        <Form.Select
          multiple
          fieldClassName={styles.ruleListRhs}
          fieldStyle={{ padding: "4px 0px" }}
          style={{ width: 311 }}
          field={`item_rhs_${parentIndex}_${index}`}
          noLabel={true}
          maxTagCount={2}
          showClear
          filter
          rules={[getRequiredRule(I18n.t("please_select", {}, "请选择"))]}
          triggerRender={(props) =>
            triggerRender({
              ...props,
              field: `item_rhs_${parentIndex}_${index}`,
            })
          }
        >
          {field.FieldId === "362"
            ? (FieldValueList || []).map((o, index) => (
                <Form.Select.Option key={index} value={o}>
                  {o}
                </Form.Select.Option>
              ))
            : (FieldValueList || []).map((o, index) => (
                <Form.Select.Option key={index} value={o.value}>
                  {o.name}
                </Form.Select.Option>
              ))}
        </Form.Select>
      );
      break;
    case 14:
    case 4:
      EntityValueComponents = (
        <LocaleProvider locale={isEnLang() ? en_GB : zh_CN}>
          <Form.DatePicker
            noLabel
            fieldStyle={{ padding: "4px 0px" }}
            field={`item_rhs_${parentIndex}_${index}`}
            type="dateTime"
            style={{
              width: "331px",
              marginLeft: "8px",
            }}
          />
        </LocaleProvider>
      );
      break;
    case 7: // Tree
      EntityValueComponents = (
        <Form.TreeSelect
          multiple
          noLabel
          leafOnly
          showClear
          filterTreeNode
          showFilteredOnly
          maxTagCount={2}
          dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
          style={{ width: 311 }}
          fieldStyle={{ padding: "4px 0px" }}
          fieldClassName={styles.ruleListRhs}
          field={`item_rhs_${parentIndex}_${index}`}
          treeData={formatTreeData(
            safeJSONParse(JSON.stringify(FieldValueList))
          )}
          triggerRender={(props) =>
            treeTriggerRender({
              ...props,
              field: `item_rhs_${parentIndex}_${index}`,
            })
          }
        />
      );
      break;
    case 10: // Integer type
      EntityValueComponents = (
        <Form.InputNumber
          fieldStyle={{ padding: "4px 0px" }}
          fieldClassName={styles.ruleListRhs}
          field={`item_rhs_${parentIndex}_${index}`}
          noLabel={true}
          rules={[countIntegerNumberRule]}
        />
      );
      break;
    case 8:
      EntityValueComponents = (
        <Form.TimePicker
          noLabel
          hideDisabledOptions
          secondStep={30}
          scrollItemProps={{
            cycled: false,
          }}
          disabledSeconds={() => [30]}
          fieldClassName={styles.ruleListRhs}
          field={`item_rhs_${parentIndex}_${index}`}
        />
      );
      break;
    case 801:
      EntityValueComponents = (
        <Form.TimePicker
          noLabel
          style={{ width: "311px", marginLeft: "8px" }}
          field={`item_rhs_${parentIndex}_${index}`}
          type="timeRange"
          format="HH:mm"
          rules={[
            {
              validator: (rule, value, cb) => {
                const startTime = dayjs(value[0]).format("HH:mm")?.split(":");
                const newStartTime =
                  Math.floor(Number(startTime[0])) * 60 * 60 * 1000 +
                  Math.floor(Number(startTime[1])) * 60 * 1000;
                const endTime = dayjs(value[1]).format("HH:mm")?.split(":");
                const newEndTime =
                  Math.floor(Number(endTime[0])) * 60 * 60 * 1000 +
                  Math.floor(Number(endTime[1])) * 60 * 1000;
                if (newEndTime < newStartTime) {
                  cb(
                    I18n.t(
                      "cross_day_time_cannot_be_selected_",
                      {},
                      "不可以选择跨天时间"
                    )
                  );
                  return false;
                } else if (newStartTime === newEndTime) {
                  cb(
                    I18n.t(
                      "cross_day_time_cannot_be_selected_",
                      {},
                      "不可以选择跨天时间"
                    )
                  );
                  return false;
                } else if (newStartTime > newEndTime) {
                  cb(
                    I18n.t(
                      "cross_day_time_cannot_be_selected_",
                      {},
                      "不可以选择跨天时间"
                    )
                  );
                  return false;
                } else {
                  return true;
                }
              },
            },
          ]}
        />
      );
      break;
    case 17:
      EntityValueComponents = (
        <Form.InputNumber
          noLabel
          fieldClassName={styles.ruleListRhs}
          field={`item_rhs_${parentIndex}_${index}`}
          style={{ width: 311 }}
          precision={2}
          min={0.01}
          placeholder={I18n.t("please_enter", {}, "请输入")}
          rules={[
            {
              required: true,
              message: I18n.t("this_field_is_required", {}, "该字段为必填项"),
            },
          ]}
        />
      );
      break;
    case 3: // Mix (multiple choice + input box)
    case 5: // Input box
    case 6: // Batch Input
    default:
      EntityValueComponents = (
        <Form.InputNumber
          fieldClassName={styles.ruleListRhs}
          field={`item_rhs_${parentIndex}_${index}`}
          noLabel={true}
        />
      );
  }
  return EntityValueComponents;
};
const RuleLists: React.FC<ruleListsProps> = ({
  parentIndex,
  groupInfo,
  formApi,
  values,
  deleteItem,
  fieldList,
  changeJoinWay,
  changeFormValuesByRuleList,
  deleteGroup,
  viewType,
  disabledGroup,
}) => {
  const { conditions, opGroup } = groupInfo;
  const changeOp = (value, index) => {
    changeJoinWay(Number(parentIndex), index, value);
  };

  const changeField = (e, index, parentIndex, formApi) => {
    const item = fieldList.find((item) => item.FieldName === e);
    if (!item) {
      return;
    }
    if (!formApi) {
      return;
    }
    formApi.setValue(`item_opCheck_${parentIndex}_${index}`, "");
  };
  const renderSelectOperator = function (filter, index, parentIndex) {
    const field = fieldList.find((item) => item?.FieldName === filter);
    if (!filter || !field) {
      return null;
    }
    const getRuleName = (opKey) => {
      for (let i = 0; i < ruleOpcheckArr.length; i++) {
        if (ruleOpcheckArr[i].serveOp === opKey) {
          return ruleOpcheckArr[i]?.clientName();
        }
      }
    };
    const OperatorSelect = (
      <Form.Select
        fieldClassName={styles.ruleListOp}
        style={{ width: 128 }}
        fieldStyle={{ padding: "4px 0px" }}
        field={`item_opCheck_${parentIndex}_${index}`}
        noLabel={true}
        placeholder={I18n.t("select_limit", {}, "选择限制")}
        // onChange={() => handleOperatorIdChange(index)}
      >
        {(field.OperatorList || []).map((id, index) => (
          <Form.Select.Option key={index} value={id}>
            {getRuleName(id)}
          </Form.Select.Option>
        ))}
      </Form.Select>
    );
    return OperatorSelect;
  };

  const content = (
    <div
      // getFormApi={getFormApi}
      className={styles.ruleListBox}
    >
      <div className={styles.ruleListHeader}>
        <span className={styles.ruleListHeaderText}>
          {`${I18n.t("route_Starling_10", {}, "规则组")}${parentIndex + 1}`}
        </span>
        <span>
          <Button
            disabled={viewType === "view" || disabledGroup}
            icon="delete"
            type="tertiary"
            theme="borderless"
            onClick={() => deleteGroup(parentIndex)}
          />
        </span>
      </div>
      <Form.RadioGroup
        fieldStyle={{ padding: "8px 0px" }}
        field={`item_opGroup_${parentIndex}_0`}
        label={I18n.t("trigger_condition", {}, "触发条件")}
      >
        <Form.Radio value="AND">
          {I18n.t(
            "meet_all_of_the_following_conditions",
            {},
            "满足下列所有条件"
          )}
        </Form.Radio>
        <Form.Radio value="OR">
          {I18n.t(
            "meet_any_of_the_following_conditions",
            {},
            "满足下列任一条件"
          )}
        </Form.Radio>
      </Form.RadioGroup>
      {conditions.map((item, index) => {
        const itemContent = (
          <React.Fragment key={index}>
            <div className={styles.ruleList} key={index}>
              <Form.Select
                onChange={(e) => changeField(e, index, parentIndex, formApi)}
                noLabel={true}
                style={{ width: 200 }}
                fieldStyle={{ padding: "4px 0px" }}
                fieldClassName={styles.ruleListLhs}
                field={`item_lhs_${parentIndex}_${index}`}
                filter
              >
                {fieldList.map((o, index) => (
                  <Form.Select.Option key={index} value={o.FieldName}>
                    {o.FieldDisplayName}
                  </Form.Select.Option>
                ))}
              </Form.Select>
              {
                // values[`item_lhs_${parentIndex}_${index}`] &&
                renderSelectOperator(
                  values[`item_lhs_${parentIndex}_${index}`],
                  index,
                  parentIndex
                )
              }
              {renderSelectValue(
                `item_lhs_${parentIndex}_${index}`,
                index,
                fieldList,
                parentIndex,
                `item_opCheck_${parentIndex}_${index}`,
                formApi
              )}
              <Button
                icon="minus_circle"
                type="tertiary"
                theme="borderless"
                onClick={() => deleteItem(parentIndex, index)}
                disabled={viewType === "view" || conditions?.length === 1}
                className={styles.ruleMinusCircle}
              />
            </div>
            <div>
              {/* <Form.Select
                optionList={operatorLists}
                noLabel={true}
                className={styles.ruleListLhs}
                onChange={e => changeOp(e, index)}
                field={`item_opGroup_${parentIndex}_${index}`}
              /> */}
            </div>
          </React.Fragment>
        );
        return itemContent;
      })}
      <div>
        <Button
          disabled={viewType === "view"}
          icon="plus_circle"
          theme="borderless"
          onClick={() =>
            changeOp(
              formApi.getValue(`item_opGroup_${parentIndex}_0`),
              conditions.length - 1
            )
          }
        >
          {I18n.t("route_Starling_9", {}, "添加规则")}
        </Button>
      </div>
    </div>
  );
  return content;
};

export default RuleLists;
