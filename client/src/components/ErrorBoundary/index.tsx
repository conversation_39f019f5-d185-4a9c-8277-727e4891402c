import { I18n } from '@ies/starling_intl';
import React from 'react';
import { Button, Empty as SemiEmpty } from '@ies/semi-ui-react';
import IllustrationConstruction from '@ies/semi-illustrations/construction.svg';
import IllustrationConstructionDark from '@ies/semi-illustrations/construction-dark.svg';
import { sendEvent } from '@/sdks/slardar';
const Empty: React.FC<any> = (props: any) => (
  <React.Suspense fallback={null}>
    <SemiEmpty {...props} />
  </React.Suspense>
);

interface ErrorBoundaryProps {
  componentName?: string;
  fallback?: JSX.Element;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

const defaultEmptyStyle = {
  paddingTop: 100,
};

export const DefaultErrorComponent = ({ message, name = '' }): React.ReactElement => (
  <Empty
    image={(
      <React.Suspense fallback={null}>
        <IllustrationConstruction />
      </React.Suspense>
    )}
    darkModeImage={(
      <React.Suspense fallback={null}>
        <IllustrationConstructionDark />
      </React.Suspense>
    )}
    description={message}
    style={defaultEmptyStyle}
  />
);

export const DefaultErrorButtonComponent = ({
  message,
  handleClick,
  title = I18n.t('encountered_an_unexpected_error', {}, '遇到了意料之外的错误'),
  loading = false,
  description = '',
}): React.ReactElement => (
  <Empty
    image={(
      <React.Suspense fallback={null}>
        <IllustrationConstruction />
      </React.Suspense>
    )}
    darkModeImage={(
      <React.Suspense fallback={null}>
        <IllustrationConstructionDark />
      </React.Suspense>
    )}
    title={title}
    style={defaultEmptyStyle}
    description={description}
  >
    <div style={{ textAlign: 'center' }}>
      <React.Suspense fallback={null}>
        <Button loading={loading} onClick={handleClick}>
          {message}
        </Button>
      </React.Suspense>
    </div>
  </Empty>
);

/**
 * @todo more specific info to display/report
 */
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error): void {
    sendEvent({
      name: 'blank_screen',
      metrics: {
        count: 1,
      },
      categories: {
        agentId: window?.$unitedHelpdeskI18nInitData?.userInfo?.agent?.ID || '',
        message: JSON.stringify(error),
        componentName: this.props.componentName || 'unknown',
      },
    });
  }

  render(): JSX.Element {
    if (this.state.hasError) {
      // If no fallback parameter is passed, the default strategy is to display the default fallback component
      if (typeof this.props.fallback === 'undefined') {
        return (
          <DefaultErrorComponent
            message={I18n.t(
              'component_loading_failed__please_refresh_the_page_and_try_again',
              {},
              '组件加载失败，请刷新页面重试'
            )}
          />
        );
      }
      // If there is a fallback parameter, the one customized by the user side shall prevail.
      return this.props.fallback;
    }

    return (this.props.children as unknown) as JSX.Element;
  }
}

export function withErrorBoundary<P>(Component: React.ComponentType<P>, componentName?: string): React.FC<P> {
  // Return function component here
  // eslint-disable-next-line max-len
  const withComponent: React.FC<P> = props => (
    <ErrorBoundary>
      <Component {...props} componentName={componentName} />
    </ErrorBoundary>
  );

  return withComponent;
}

export default ErrorBoundary;
