import { I18n } from '@ies/starling_intl';
/**
 * @file Form editing page wrapping component
 * <AUTHOR>
 */

import * as styles from './index.scss';
import React from 'react';
import { Button } from '@ies/semi-ui-react';
import { KefuSubPageHeader } from '@ies/kefu-components';
import { ButtonOperationProps, PageFormWrapperProps } from './component';

const ButtonOperation = (props: ButtonOperationProps): JSX.Element => (
  <div className={styles.buttonOperation} style={props.style}>
    <Button onClick={props.onCancel} className={styles.button} type="tertiary">
      {I18n.t('cancel', {}, '取消')}
    </Button>
    <Button
      disabled={props.disabled}
      onClick={props.onConfirm}
      className={styles.button}
      theme="solid"
      type="secondary"
    >
      {props.mode === 'update' ?
        I18n.t('save', {}, '保存') :
        props.mode === 'clone' ?
        I18n.t('copy', {}, '复制') :
        I18n.t('new', {}, '新建')}
    </Button>
  </div>
);

export function PageFormWrapper(props: PageFormWrapperProps): React.ReactElement {
  const buttonOperationProp = {
    ...props.buttonOperationProp,
    mode: props.mode || props.buttonOperationProp.mode,
  };

  const showBtn = typeof props.showBtn === 'undefined' ? true : props.showBtn;

  return (
    <div className={styles.form}>
      <div className={styles.inner} style={props.style || {}}>
        <div>
          <KefuSubPageHeader
            onBackClick={props.onBackClick}
            backText={props.backText}
            title={props.pageTitle}
            style={{
              marginBottom: 28,
            }}
          />
          {props.children}
        </div>
      </div>
      {showBtn && <ButtonOperation style={props.style || {}} {...buttonOperationProp} />}
    </div>
  );
}
