:local {

  .form {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    min-height: calc(100vh - 24px);
  }

  .inner {
    display: flex;
    min-height: calc(100% - 58px);
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    > div {

      &:first-child {
        width: 100%;
      }
    }
  }

  .content {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 45px;
  }

  .button-operation {
    z-index: 1000;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    position: sticky;
    bottom: 0;
    width: 100%;
    border-top: 2px solid var(--color-border);
    background-color: #fff;

    .button {
      margin: 12px 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
