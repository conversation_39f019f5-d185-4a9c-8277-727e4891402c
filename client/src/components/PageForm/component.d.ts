/**
 * @file types
 * <AUTHOR>
 */

import React from 'react';

type FormType = 'create'|'update' | 'clone';

export interface ButtonOperationProps {
  disabled?: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  mode?: FormType;
  style?: React.CSSProperties;
}

export interface PageFormWrapperProps {
  onBackClick: () => void;
  backText: string;
  mode: FormType;
  pageTitle: string;
  buttonOperationProp: ButtonOperationProps;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  showBtn?: boolean;
}