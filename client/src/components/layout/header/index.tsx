import { I18n } from '@ies/starling_intl';
import React, { useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { Col, Row, Avatar, Layout } from '@ies/semi-ui-react';
import SemiReactProfileDropdown from '../../SemiReactProfileDropdown';
import { observer } from 'mobx-react-lite';
import { useStore } from '@stores/index';
import * as styles from './index.scss';
import * as Api from '@api/index';

const { Header } = Layout;

const HeaderComponent: React.FunctionComponent<any> = observer(props => {
  const { setLocale, locale } = props;

  const history = useHistory();
  const userStore = useStore('user');

  const pathToHome = (): void => {
    history.push('/');
  };

  const nameFirstLetter = userStore.user.username;

  const userInfo = useMemo(
    () => ({
      avatar: userStore.user.avatar,
      name: nameFirstLetter,
      email: userStore.user.email,
      firstLetter: nameFirstLetter,
    }),
    [userStore.user]
  );

  const items = useMemo(
    () => [
      {
        leftIcon: 'language',
        text: I18n.t('language', {}, '语言'),
        rightText: locale === 'zh' ? I18n.t('chinese', {}, '中文') : 'English',
        items: [
          {
            text: I18n.t('chinese', {}, '中文'),
            onClick(e): void {
              setLocale('zh');
            },
          },
          {
            text: 'English',
            onClick(e): void {
              setLocale('en');
            },
          },
        ],
      },
    ],
    [locale]
  );

  const footer = useMemo(
    () => ({
      text: I18n.t('logout', {}, '登出'),
      onClick: (): void => Api.logout(),
    }),
    [Api]
  );

  return (
    <Header className={styles.header}>
      <Row>
        <Col span={6}>
          <div className={styles.headerBrand} onClick={pathToHome}>
            <i className={styles.headerBrandIcon} />
            <a className={styles.headerBrandName}>{I18n.t('routing_management', {}, '路由管理')}</a>
          </div>
        </Col>
        <Col span={18} className={styles.headerRight}>
          <SemiReactProfileDropdown userInfo={userInfo} items={items} footer={footer}>
            <Avatar size="small" src={userStore.user.avatar} icon={'user'}>
              {nameFirstLetter}
            </Avatar>
          </SemiReactProfileDropdown>
        </Col>
      </Row>
    </Header>
  );
});

export default HeaderComponent;
