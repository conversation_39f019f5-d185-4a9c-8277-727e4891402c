@import "@common/styles/color.scss";

:local {

  .header {
    display: block;
    height: 34px;
    box-sizing: content-box;
    padding: 13px 25px;
    box-shadow: inset 0 -1px 0 var(--color-border);

    &<PERSON> {
      min-width: 80px;
      width: fit-content;
      margin-right: 13px;
    }

    &Brand {
      cursor: pointer;

      &Icon {
        vertical-align: text-top;
        width: 36px;
        height: 36px;
        line-height: 40px;
        display: inline-block;
        margin-right: 13px;
        background: url(@common/images/logo.png) center center no-repeat;
      }

      &Name {
        vertical-align: top;
        color: #252729;
        font-size: 18px;
        line-height: 40px;
        font-weight: 600;
      }

      &HelpIcon {
        margin-right: 20px;
        color: $grey-4;
        cursor: pointer;
      }
    }

    &Right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    &Nav {

      &Item {
        display: inline-block;
        margin-right: 20px;
        width: 32px;
        text-align: center;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
