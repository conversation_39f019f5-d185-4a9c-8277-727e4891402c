import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import { Nav } from '@ies/semi-ui-react';
import * as styles from './index.scss';

export interface NavItem {
  itemKey: string;
  text: string;
  icon: string;
  url: string;
}

export interface NavSelectedItem {
  itemKey: string;
  selectedKeys: string[];
  selectedItems: any[];
  domEvent: MouseEvent;
  isOpen: boolean;
}

const { useState } = React;

const AppNav: React.FC = () => {
  const [openKeys] = useState([]);
  const location = useLocation();
  const history = useHistory();

  const navItems = [
    {
      itemKey: '/question_classification',
      text: I18n.t('front_end_problem_classification_configuration', {}, '前端问题分类配置'),
      icon: 'home',
      url: '/question_classification',
    },
    {
      itemKey: '/service_routing',
      text: I18n.t('im_manual_routing_rules', {}, 'IM人工路由规则'),
      icon: 'home',
      url: '/service_routing',
    },
    {
      itemKey: '/bot_routing',
      text: I18n.t('im_intelligent_routing_rules', {}, 'IM智能路由规则'),
      icon: 'home',
      url: '/bot_routing',
    },
  ] as NavItem[];

  const onSelect = (selectedItem: NavSelectedItem): void => {
    const item = navItems.find(i => i.itemKey === selectedItem.itemKey);
    if (!item) {
      return;
    }
    history.push(item.url);
  };

  return (
    <Nav
      className={styles['app-nav']}
      mode="vertical"
      openKeys={openKeys}
      selectedKeys={[location.pathname]}
      items={navItems}
      onSelect={onSelect}
      footer={{
        collapseButton: true,
      }}
    />
  );
};

export default AppNav;
