import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import { Breadcrumb, Skeleton } from '@ies/semi-ui-react';

export default function AppContent({ children }): React.FunctionComponentElement<undefined> {
  return (
    <>
      <Breadcrumb
        style={{
          marginBottom: '24px',
        }}
        routes={[
          I18n.t('home', {}, '首页'),
          I18n.t('when_this_page_title_is_long__it_needs_to_be_omitted', {}, '当这个页面标题很长时需要省略'),
          I18n.t('previous_page', {}, '上一页'),
          I18n.t('details_page', {}, '详情页'),
        ]}
      />
      <div
        style={{
          borderRadius: '10px',
          border: '1px solid var(--color-border)',
          height: '376px',
          padding: '32px',
        }}
      >
        <Skeleton placeholder={<Skeleton.Paragraph rows={2} />} loading={true} active>
          {children}
        </Skeleton>
      </div>
    </>
  );
}
