import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import { Form } from '@ies/semi-ui-react';
import styles from './index.scss';

interface IBizTypeSelect {
  field?: string;
  label?: string;
  width?: string;
  placeholder?: string;
  className?: string;
  noLabel?: boolean;
  rules?: any[];
  bizTypeList: any[];
  onChange?: (...args: any[]) => any;
  isIes: boolean;
}

const BizTypeSelect: React.FC<IBizTypeSelect> = props => {
  const bizTypeList = props.bizTypeList || [];
  // Temporarily grouped by entranceId
  const classifiedKey = 'EntranceId';
  // const classifiedKey = props.isIes ? 'EntranceId' : 'AppId';
  const getBizTypeData = function () {
    const list = [];

    bizTypeList.forEach(bizType => {
      const isExist = list.find(k => k[classifiedKey] === bizType[classifiedKey]);

      if (isExist) {
        isExist.children.push({
          label: `${bizType.ChannelName || bizType.Channel}` + `（${bizType.AppID}）`,
          value: String(bizType.ID),
          key: bizType.ID,
          AppID: bizType.AppID,
          children: [],
          EntranceId: bizType.EntranceId,
        });
      } else {
        const parent = Object.assign({
          label: `${bizType.EntranceName || bizType.AppName || bizType.AppID}`,
          value: bizType.ID,
          key: `parent-${bizType.ID}`,
          AppID: bizType.AppID,
          EntranceId: bizType.EntranceId,
          children: [
            {
              label: `${bizType.ChannelName || bizType.Channel}` + `（${bizType.AppID}）`,
              value: String(bizType.ID),
              key: bizType.ID,
              AppID: bizType.AppID,
              children: [],
              EntranceId: bizType.EntranceId,
            },
          ],
          disabled: true,
        });
        list.push(parent);
      }
    });

    return list;
  };
  const bizTypeTreeData = getBizTypeData();

  return (
    <Form.TreeSelect
      dropdownClassName="dropListDisableBox"
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      // expandAction="click"
      className={`${props.className || ''} ${styles.selectBox}`}
      style={{ width: props.width || '100%' }}
      label={props.label || I18n.t('business', {}, '业务')}
      rules={props.rules || []}
      placeholder={props.placeholder || I18n.t('please_select_the_entrance', {}, '请选择入口')}
      field={props.field || 'AppId'}
      noLabel={props.noLabel || false}
      treeData={bizTypeTreeData}
      defaultExpandAll
      clickToHide
    />
  );
};

export default BizTypeSelect;
