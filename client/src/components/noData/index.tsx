import React from 'react';
import styles from './index.scss';
import { Button } from '@ies/semi-ui-react';
import NoDataIcon from '@common/images/no-data.svg';

export default ({ tip, btnText, handleClickFn }) => (
  <span className={styles.container}>
    <img className={styles.img} src={NoDataIcon} alt="no-data" />
    <div className={styles.tip}>{tip}</div>
    <Button theme="solid" onClick={handleClickFn}>
      {btnText}
    </Button>
  </span>
);
