import * as React from 'react';
import { Tooltip, Icon } from '@ies/semi-ui-react';
import * as styles from './index.scss';

interface CardOperationProps {
  icon: string;
  tooltip: string;
  disabled?: boolean;
  onClick: () => void;
}

export const CardOperation: React.FC<CardOperationProps> = props => {
  const { icon, tooltip, onClick, disabled } = props;

  const className = React.useMemo(() => {
    const classList = [styles.cardOperationIcon];
    if (disabled) {
      classList.push(styles.cardOperationIconDisabled);
    }

    return classList.join(' ');
  }, [disabled]);

  return (
    <Tooltip disabled={disabled} content={tooltip}>
      <Icon
        className={className}
        style={{ width: 12, height: 12 }}
        type={icon}
        onClick={() => {
          !disabled && onClick();
        }}
      />
    </Tooltip>
  );
};
