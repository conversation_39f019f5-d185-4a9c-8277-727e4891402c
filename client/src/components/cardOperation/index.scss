:local {

  .container {
    margin-bottom: 16px;
    padding: 16px;
    border: 1px solid rgba(28, 31, 35, .08);
    border-radius: 3px;
  }
  
  .cardHead {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .titleBox {
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
  
  .cardTitle {
    line-height: 22px;
    color: var(--color-text-1);
  }
  
  .cardOperations {
    display: flex;
    align-items: center;
  }
  
  .cardOperationIcon {
    display: inline-block;
    width: 12px;
    height: 12px;
    cursor: pointer;
    margin-right: 12px;
    transition: background-color .1s ease-in-out;
    padding: 10px;
    box-sizing: content-box;
    border-radius: 3px;
  
    &-disabled {
      color: var(--color-disabled-text);
      cursor: not-allowed;
    }
  
    &:hover {
      background-color: var(--color-fill-0);
    }
  }
  
  
  .isOpen {
    margin-left: 8px;
    padding: 2px 8px;
    background: rgba(59, 179, 70, .15);
    border-radius: 3px;
    font-size: 12px;
    line-height: 16px;
    color: #1b5924;
  }
  
  .isClose {
    margin-left: 8px;
    padding: 2px 8px;
    background: rgba(249, 57, 32, .15);
    border-radius: 3px;
    font-size: 12px;
    line-height: 16px;
    color: #8e0805;
  }
  
  .cardLabel {
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 20px;
    color: var(--color-text-2);
  }
  
  .cardValue {
    margin-bottom: 20px;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: var(--color-text-0);
  }
  
  .tableHead {
    display: flex;
    align-items: center;
  }
  
  .tableHeadIcon {
    margin-left: 4px;
  }
  
  .ruleContentLabel {
    margin-right: 4px;
    font-size: 14px;
    line-height: 20px;
    color: var(--color-text-2);
  }
  
  .ruleContentText {
    margin-right: 4px;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: var(--color-text-1);
  }
}
