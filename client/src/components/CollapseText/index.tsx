import { I18n } from '@ies/starling_intl';
/**
 * @file collapsible text
 * <AUTHOR>
 */

import React, { useEffect, useRef, useState } from 'react';

export function CollapseHorizontalText({ children, containerStyle = {} }): React.ReactElement {
  const ref = useRef(null as HTMLDivElement);
  const [needShowCollapseBtn, setNeedShowCollapseBtn] = useState(false);
  const [isExpand, setIsExpand] = useState(false);

  useEffect(() => {
    if (ref.current) {
      const { width } = ref.current.getBoundingClientRect();
      const scrollWith = ref.current.scrollWidth;
      setNeedShowCollapseBtn(isExpand || scrollWith > width);
    }
  }, [children, isExpand]);

  return (
    <div style={containerStyle} className="flexible flex-align-center">
      <div
        ref={ref}
        className={isExpand ? '' : 'ellipsis inline-block'}
        style={{
          width: 'calc(100% - 28px)',
        }}
      >
        {children}
        {needShowCollapseBtn && isExpand && (
          <span className="link-text" onClick={() => setIsExpand(v => !v)}>
            {isExpand ? I18n.t('put_it_away', {}, '收起') : I18n.t('expand', {}, '展开')}
          </span>
        )}
      </div>
      {needShowCollapseBtn && !isExpand && (
        <span className="link-text" onClick={() => setIsExpand(v => !v)}>
          {isExpand ? I18n.t('put_it_away', {}, '收起') : I18n.t('expand', {}, '展开')}
        </span>
      )}
    </div>
  );
}
