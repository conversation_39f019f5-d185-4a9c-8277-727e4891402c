.filterWrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;

  .filterItem {
    margin: 0 7px 7px 0;
  }
  
  .controlInput {
    width: 268px !important;
    margin-right: 20px;
  }
  
  .circleStatus {
    margin-right: 20px;
    border-radius: 100px;
    background: transparent;
    border: 1px solid var(--color-border);
    :global {
      .united_automation_rule-select-inset-label,
      .united_automation_rule-tree-select-prefix-text {
        color: var(--color-text-1);
        font-weight: 600;
      }
    }
  }
  
  .activeSelect {
    background: var(--color-primary-light-default) !important; 
  }

  .selectDropdownTag {
    max-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
  }

  .selectDropdownTag:hover {
    white-space: unset;
  }
}

.selectDropdownOption {
  max-width: 700px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
}
