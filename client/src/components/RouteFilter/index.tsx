import { Icon, Input, Select } from '@ies/semi-ui-react';
import I18n from '@ies/starling_intl';
import React, { useEffect, useState } from 'react';
import styles from './index.scss';

const statusOption = [
  {
    value: 1,
    label: I18n.t('enable', { }, 'Enable'),
  },
  {
    value: 0,
    label: I18n.t('disable', { }, 'Disable'),
  }
];

interface IRouteFilterProps {
  statusList: any;
  skillGroups: any;
  ruleName: string;
  changeRuleName: (value: string) => void;
  skillGroupList: any;
  changeSkillGroupList: (value: string, e: React.MouseEvent<HTMLInputElement>) => void;
  changeStatusList: (value: string, e: React.MouseEvent<HTMLInputElement>) => void;
  className?: string;
}


export default function RouteFilter(props: IRouteFilterProps) {

  const { statusList, skillGroups, changeRuleName, changeStatusList, changeSkillGroupList, skillGroupList, className, ruleName } = props;

  const [filterValue, setFilterValue] = useState(ruleName);

  useEffect(() => {
    setFilterValue(ruleName);
  }, [ruleName]);

  const renderMultipleWithCustomTag = (optionNode, { onClose }) => {
    const children = optionNode?.label?.props?.children || '';
    const content = (
      <div className={styles.selectDropdownTag} title={children}>
        {children}
      </div>
    );
    return {
      isRenderInTag: true,
      content,
    };
  };

  const searchMultilpe = (sugInput, option) => {
    const children = (option?.label?.props?.children || '').toLowerCase();
    const searchInput = sugInput.toLowerCase();
    return children.includes(searchInput);
  };
  
  return (
    <div className={`${className} ${styles.filterWrapper}`}>
      <Input
        value={filterValue}
        placeholder={I18n.t('search_routing_rules', {}, 'Search routing rules')}
        onChange={setFilterValue}
        onEnterPress={e => changeRuleName(e.target.value)}
        className={`${styles.controlInput} ${styles.filterItem}`}
        prefix={<Icon type="search" />}
        showClear
        onClear={() => changeRuleName('')}
      />
      <Select
        value={statusList}
        multiple
        onChange={changeStatusList}
        insetLabel={I18n.t('enabled_state', { }, 'enabled state')}
        className={`${styles.circleStatus}  ${styles.filterItem} ${statusList.length > 0 ? styles.activeSelect : ''}`}
      >
        {statusOption.map(_item =>
          <Select.Option value={_item.value} key={_item.value}>{_item.label}</Select.Option>
        )}
      </Select>
      <Select
        value={skillGroupList}
        multiple
        filter={searchMultilpe}
        onChange={changeSkillGroupList}
        insetLabel={I18n.t('skill_group', { }, 'Skill group')}
        maxTagCount={2}
        className={`${styles.circleStatus}  ${styles.filterItem} ${skillGroupList.length > 0 ? styles.activeSelect : ''}`}
        style={{ minWidth: 200 }}
        renderSelectedItem={renderMultipleWithCustomTag}
      >
        {skillGroups.map(_item =>
          <Select.Option value={_item.ID} key={_item.ID}><div className={styles.selectDropdownOption} title={_item.Name}>{_item.Name}</div></Select.Option>
        )}
      </Select>
    </div>
  );
}
