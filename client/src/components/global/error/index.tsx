import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import * as styles from './index.scss';

interface IProps {
  message: string;
}

function ErrorComponent(props): React.FunctionComponentElement<IProps> {
  return (
    <div className={styles.errorPage}>
      <p className={styles.errorText}>{props.message || I18n.t('default_error_message', {}, '默认的错误信息')}</p>
    </div>
  );
}

export default ErrorComponent;
