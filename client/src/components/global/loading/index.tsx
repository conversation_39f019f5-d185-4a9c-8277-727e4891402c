import * as React from 'react';
import { Spin, Icon } from '@ies/semi-ui-react';
import * as styles from './index.scss';

function PageLoading() {
  const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;
  return (
    <div className={styles.pageLoading}>
      <Spin className={styles.spin} indicator={antIcon} />
    </div>
  );
}

interface IProps {
  location: string;
}

// Component delayed loading
function loadableComp(LazyComponent) {
  return class LoadComponent extends React.Component<IProps> {
    constructor(props, context) {
      super(props, context);
      this.state = {};
    }

    shouldComponentUpdate(nextProps) {
      return nextProps.location !== this.props.location;
    }

    render() {
      return (
        <React.Suspense fallback={<PageLoading />}>
          <LazyComponent {...this.props} />
        </React.Suspense>
      );
    }
  };
}

export default loadableComp;
