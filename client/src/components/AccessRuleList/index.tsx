import React, { useState, useEffect, useContext } from 'react';
import { I18n } from '@ies/starling_intl';
import { Tooltip, Icon, Tag, Button, Modal, Toast } from '@ies/semi-ui-react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { CardOperation } from '@/components/cardOperation';
import { useHistory } from 'react-router-dom';
import UpIcon from '@/images/route_up_arrow.svg';
import DownIcon from '@/images/route_down_arrow.svg';
import TopIcon from '@/images/route_top_arrow.svg';
import BottomIcon from '@/images/route_bottom_arrow.svg';
import { OperationType, routeType } from '@/const/enums';
import { KefuPageEmptyContent } from '@ies/kefu-components';
import { getFieldByValue } from '@/common/utils/treeData';
import { numberToTime } from '@/common/utils/dateTimeField';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import { isJSON } from '@/common/utils/index';
import { CollapseHorizontalText } from '@components/NewCollapseText';
import { ErrorCodeNoPermission } from '../../const/index';
import format, { millisecondToTime } from '@common/utils/date';
import { formatNum } from '@/common/utils/inputFloat';
import {
  ROUTE_TYPE_EVENT_KEY_MAP,
} from '@constants/property';
import * as t from '@http_idl/demo';
import { UserContext } from '@/context/user';
import { useAccessDoorAuthority } from '@/hooks';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';
import { getAllAccessPartyList } from '@/common/utils/hook';
import { StrategyItem } from '@/api';
import * as config from './config';
import DropdownList from './DropdownList';
import styles from './index.scss';

interface RuleListProps {
  ruleList: any[];
  fieldList: any[];
  ruleType: routeType;
  tabKey: string;
  getPreReleaseRuleList?: () => void;
  bizTypeList?: any[];
  newRuleId?: string;
  skillGroupStrategy?: StrategyItem[];
  accessPartyStrategy?: StrategyItem[];
}

const CardReOrderType = {
  Top: 'top',
  Up: 'up',
  Down: 'down',
  Bottom: 'bottom',
};

const RuleList: React.FC<RuleListProps> = ({
  ruleList,
  newRuleId,
  bizTypeList,
  getPreReleaseRuleList,
  fieldList,
  ruleType,
  accessPartyStrategy,
  skillGroupStrategy,
  tabKey
}) => {
  const [ruleDataList, setRuleDataList] = useState([]);
  const user = useContext(UserContext);
  const [getChooseObj, changeChooseObj] = useState({ Id: '' });
  const [modalVisible, changeVisible] = useState(false);

  const { hasEditAuth } = useAccessDoorAuthority();

  // Adjust priority
  const onReOrder = (key, ruleDataChild) => {
    switch (key) {
      case 'top':
        let topRouterList = [];
        const routerList = [];
        if (ruleDataChild.Id === ruleDataList[0].Id) {
          Toast.error(I18n.t('already_top_rule', {}, 'Already top rule'));
          return false;
        }
        routerList.push(ruleDataChild);
        ruleDataList.forEach((item, index) => {
          if (item.Id !== ruleDataChild.Id) {
            routerList.push(item);
          }
        });
        topRouterList = routerList.map((item, index) => ({
          Id: item.Id,
          Priority: index + 1,
        }));
        t.demoClient
          .UpdateRulePriority({
            Rules: topRouterList,
            Version: 'v1',
          })
          ?.then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('top_success', {}, 'Top success'));
            getPreReleaseRuleList();
          })
          ?.catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_top' });
          });
        break;
      case 'up':
        let upRouterList = [];
        if (ruleDataChild.Id === ruleDataList[0].Id) {
          Toast.error(I18n.t('already_top_rule', {}, 'Already top rule'));
          return false;
        }
        ruleDataList.forEach((item, index) => {
          if (item.Id === ruleDataChild.Id) {
            const temp = ruleDataList[index - 1];
            ruleDataList[index - 1] = ruleDataChild;
            ruleDataList[index] = temp;
          }
        });
        upRouterList = ruleDataList.map((item, index) => ({
          Id: item.Id,
          Priority: index + 1,
        }));

        t.demoClient
          ?.UpdateRulePriority({
            Rules: upRouterList,
            Version: 'v1',
          })
          ?.then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('move_up_successfully', {}, 'Move up successfully'));
            getPreReleaseRuleList();
          })
          ?.catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_up' });
          });
        break;
      case 'down':
        let downRouterList = [];
        if (ruleDataChild.Id === ruleDataList[ruleDataList.length - 1].Id) {
          Toast.error(I18n.t('already_the_bottom_rule', {}, 'Already the bottom rule'));
          return false;
        }
        for (let i = 0; i < ruleDataList.length; i++) {
          if (ruleDataList[i].Id === ruleDataChild.Id) {
            const temp = ruleDataList[i + 1];
            ruleDataList[i + 1] = ruleDataChild;
            ruleDataList[i] = temp;
            downRouterList = ruleDataList.map((item, index) => ({
              Id: item.Id,
              Priority: index + 1,
            }));
            t.demoClient
              ?.UpdateRulePriority({
                Rules: downRouterList,
                Version: 'v1',
              })
              ?.then(res => {
                if (res.code !== 0) {
                  Toast.error(res.message);
                  return;
                }
                Toast.success(I18n.t('move_down_successfully', {}, 'Move down successfully'));
                getPreReleaseRuleList();
              })
              ?.catch(error => {
                errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_down' });
              });
            return false;
          }
        }
        break;
      default:
        let bottomRouterList = [];
        const bottomList = [];
        if (ruleDataChild.Id === ruleDataList[ruleDataList.length - 1].Id) {
          Toast.error(I18n.t('already_the_bottom_rule', {}, 'Already the bottom rule'));
          return false;
        }
        ruleDataList.forEach((item, index) => {
          if (item.Id !== ruleDataChild.Id) {
            bottomList.push(item);
          }
        });
        bottomList.push(ruleDataChild);

        bottomRouterList = bottomList.map((item, index) => ({
          Id: item.Id,
          Priority: index + 1,
        }));
        t.demoClient
          .UpdateRulePriority({
            Rules: bottomRouterList,
            Version: 'v1',
          })
          .then(res => {
            if (res.code !== 0) {
              Toast.error(res.message);
              return;
            }
            Toast.success(I18n.t('bottom_successful', {}, 'Bottom successful'));
            getPreReleaseRuleList();
          })
          .catch(error => {
            errorReporting({ error, type: 'callback_name', name: 'UpdateRulePriority_bottom' });
          });
    }
  };

  const history = useHistory();

  useEffect(() => {
    setRuleDataList(safeJSONParse(JSON.stringify(ruleList)));
  }, [JSON.stringify(ruleList)]);

  const createRouteRule = (plaformTag, vals, singleRuleData) => {
    if (fieldList.length > 0 && accessPartyStrategy.length > 0 && skillGroupStrategy.length > 0) {
      history.push({
        pathname: `/${plaformTag}`,
        state: {
          plaformTag,
          fieldLists: fieldList,
          bizTypeList,
          viewType: 'create',
          Enable: false,
          EventKey: ROUTE_TYPE_EVENT_KEY_MAP[plaformTag],
          RuleGroupId: vals.RuleGroupId,
          ruleLength: ruleDataList.length,
          Priority: singleRuleData.Priority + 1,
          accessPartyStrategy,
          skillGroupStrategy
        },
      });
    }
  };

  // View, edit
  const clickDropItemEdit = (val, plaformTag, operation) => {
    if (operation === OperationType.Edit) {
      if (fieldList.length > 0 && accessPartyStrategy.length > 0 && skillGroupStrategy.length > 0) {
        history.push({
          pathname: `/${plaformTag}`,
          state: {
            plaformTag,
            fieldLists: fieldList,
            ruleInfo: val,
            bizTypeList,
            viewType: 'edit',
            Enable: val.Status,
            ruleId: val.Id,
            RuleGroupId: val.RuleGroupId,
            ruleLength: ruleDataList.length || 0,
            EventKey: ROUTE_TYPE_EVENT_KEY_MAP[plaformTag],
            Priority: val.Priority,
            accessPartyStrategy,
            skillGroupStrategy
          },
        });
      }
    } else {
      if (fieldList.length > 0 && accessPartyStrategy.length > 0 && skillGroupStrategy.length > 0) {
        history.push({
          pathname: `/${plaformTag}`,
          state: {
            plaformTag,
            fieldLists: fieldList,
            ruleInfo: val,
            bizTypeList,
            viewType: 'view',
            Enable: val.Status,
            ruleId: val.Id,
            RuleGroupId: val.RuleGroupId,
            EventKey: ROUTE_TYPE_EVENT_KEY_MAP[plaformTag],
            Priority: val.Priority,
            accessPartyStrategy,
            skillGroupStrategy
          },
        });
      }
    }
  };

  // Enable and disable rules
  const clickDropItem = async (operation, val) => {
    switch (operation) {
      case 'del':
        changeChooseObj(val);
        changeVisible(true);
        break;
      default:
        if (val.Status === 1) {
          const confirmed = await new Promise(resolve => {
            Modal.confirm({
              icon: <Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />,
              title: I18n.t('disable_rules', {}, 'Disable rules'),
              content: I18n.t('after_being_disabled__the_diversion_rule_will_not_take_effect', {}, 'After being disabled, the diversion rule will not take effect'),
              okText: I18n.t('disable', {}, 'Disable'),
              cancelText: I18n.t('cancel', {}, 'Cancel'),
              okButtonProps: { type: 'danger' },
              onOk() {
                resolve(true);
              },
              onCancel() {
                resolve(false);
              },
            });
          });
          if (confirmed) {
            t.demoClient
              .UpdateRuleStatus({
                Ids: [val.Id],
                RuleStatus: 0,
                Version: 'v1',
              })
              .then(res => {
                if (res.code === 0) {
                  Toast.success(
                    `${I18n.t(
                      'the__{placeholder1}__rule_is_disabled',
                      { placeholder1: val.DisplayName },
                      '「{placeholder1}」rule is disabled'
                    )}`
                  );
                  getPreReleaseRuleList();
                } else {
                  if (res.message) {
                    Toast.error(res.message);
                    return;
                  } else {
                    Toast.error(
                      `${I18n.t(
                        '_{placeholder1}__rule_disabled_failed',
                        { placeholder1: val.DisplayName },
                        '「{placeholder1}」rule disabled failed'
                      )}`
                    );
                  }
                }
              })
              .catch(err => {
                Toast.error(
                  `${I18n.t(
                    '_{placeholder1}__rule_disabled_failed',
                    { placeholder1: val.DisplayName },
                    '「{placeholder1}」rule disabled failed'
                  )}`
                );
              });
          }
        } else {
          t.demoClient
            .UpdateRuleStatus({
              Ids: [val.Id],
              RuleStatus: 1,
              Version: 'v1',
            })
            .then(res => {
              if (res.code === 0) {
                Toast.success(
                  `${I18n.t(
                    'the__{placeholder1}__rule_is_enabled',
                    { placeholder1: val.DisplayName },
                    '「{placeholder1}」rule is enabled'
                  )}`
                );
                getPreReleaseRuleList();
              } else {
                if (res.message) {
                  Toast.error(res.message);
                  return;
                } else {
                  Toast.error(
                    `${I18n.t(
                      '_{placeholder1}__rule_enabled_failed',
                      { placeholder1: val.DisplayName },
                      '「{placeholder1} rule enabled failed'
                    )}`
                  );
                }
              }
            })
            .catch(err => {
              Toast.error(
                `${I18n.t(
                  '_{placeholder1}__rule_enabled_failed',
                  { placeholder1: val.DisplayName },
                  '「{placeholder1}」rule enabled failed'
                )}`
              );
            });
        }
    }
  };

  const clickClone = async (operation, val, plaformTag) => {
    val.DisplayName = '';
    if (operation === OperationType.Clone) {
      if (fieldList.length > 0 && accessPartyStrategy.length > 0 && skillGroupStrategy.length > 0) {
        history.push({
          pathname: `/${plaformTag}`,
          state: {
            plaformTag,
            fieldLists: fieldList,
            ruleInfo: val,
            bizTypeList,
            viewType: 'clone',
            Enable: val.Status,
            ruleId: val.Id,
            EventKey: ROUTE_TYPE_EVENT_KEY_MAP[plaformTag],
            RuleGroupId: val.RuleGroupId,
            ruleLength: ruleDataList.length || 0,
            Priority: val.Priority + 1,
            accessPartyStrategy,
            skillGroupStrategy
          },
        });
      }
    }
  };

  const handleOk = async () => {
    // Request interface
    // delete
    const res = await t.demoClient.UpdateRuleStatus({
      Ids: [getChooseObj.Id],
      RuleStatus: 3,
      Version: 'v1',
      PermCode: '',
      Draft: true,
    });

    if (res.code !== 0) {
      if (res.code === ErrorCodeNoPermission) {
        Toast.error(I18n.t('no_delete_permission', {}, 'No delete permission'));
      } else {
        Toast.error(res.message);
      }
      return;
    }

    changeVisible(false);
    await getPreReleaseRuleList();
  };

  // Delete data
  const handleCancel = () => {
    changeVisible(false);
  };

  const getMaualAccessPartyContent = ruleDataChild => {
    // access party diversion data configuration
    const skillGroupStrategyStr = ruleDataChild?.skillGroupRouteStrategyList?.filter(item => item.enable === 1)?.map(item => skillGroupStrategy?.find(i => i.strategyKey === item.strategyKey)?.strategyName)?.join(',') || '';
    const accessPartyName = getAllAccessPartyList().find(item => item.value === String(ruleDataChild?.accessPartyId))?.label || '';
    const changeAccessPartyCondition = accessPartyStrategy.find(item => item.strategyKey === String(ruleDataChild?.changeAccessPartyStrategy?.condition?.[0]))?.strategyName || '';
    const changeAccessPartyName = getAllAccessPartyList().find(item => item.value === String(ruleDataChild?.changeAccessPartyStrategy?.newAccessPartyId))?.label || '';

    return (
      <>
        <div className={styles.ruleFilterBox}>
          <div className={styles.ruleAccessDot} />
          <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
            <span className={styles.ruleContentLabel}>{I18n.t('offload_to_the_access_party', {}, 'offload to the access party')}</span>
            <span className={styles.ruleContentText}>{accessPartyName}</span>
          </CollapseHorizontalText>
        </div>
        {
          skillGroupStrategyStr && (
            <div className={styles.ruleFilterBox}>
              <div className={styles.ruleStrategyDot} />
              <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                <>
                  <span className={styles.ruleContentLabel}>{I18n.t('the_skill_group_selection_strategy_is_based_on_the_following_priorities', {}, 'The skill group selection strategy is based on the following priorities')}</span>
                  <span className={styles.ruleContentText}>{skillGroupStrategyStr}</span>
                </>
              </CollapseHorizontalText>

            </div>
          )
        }
        {
          changeAccessPartyName && (
            <div className={styles.ruleFilterBox}>
              <div className={styles.ruleStrategyDot} />
              <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                <>
                  <span className={styles.ruleContentLabel}>{I18n.t('change_strategy_when', {}, 'Change strategy when')}</span>
                  <span className={styles.ruleContentText}>{changeAccessPartyCondition}</span>
                  <span className={styles.ruleContentLabel}>{I18n.t('the_access_party_is_changed_to', {}, 'The access party is changed to')}</span>
                  <span className={styles.ruleContentText}>{changeAccessPartyName}</span>
                </>
              </CollapseHorizontalText>
            </div>
          )
        }
      </>
    );
  };

  const ruleContent = (ruleDataChild, index) => {
    const status = ruleDataChild.Status ? I18n.t('enable', {}, 'Enable') : I18n.t('disable', {}, 'Disable');

    // Convert the rule field to the specific rule name
    const getFieldDisplayName = (filterData = {} as any) => {
      const FieldName = filterData?.Lhs?.VarExpr || filterData?.Lhs?.FeatureExpr?.FeatureName;
      const OperatorId = filterData?.OpCheck;
      let FieldValue =
        filterData?.Rhs?.Constant ||
        filterData?.Rhs?.ConstantList ||
        filterData?.Rhs?.ExprList ||
        filterData?.Rhs?.VonstantList;
      if (FieldName === 'ticket_category.full_category_ids') {
        const categoryIds = [];
        FieldValue.forEach(item => {
          if (item.split(',').length > 2) {
            categoryIds.push(item);
          }
        });
        FieldValue = [...categoryIds];
      }
      if (Array.isArray(FieldValue)) {
        FieldValue = FieldValue.map(item => {
          const isFlag = isJSON(item);
          if (isFlag) {
            return safeJSONParse(item);
          } else {
            return item;
          }
        });
      } else {
        const isFlag = isJSON(FieldValue);
        if (isFlag) {
          FieldValue = safeJSONParse(FieldValue);
        }
        // FieldValue = safeJSONParse(FieldValue)
      }
      const field = fieldList.find(o => o.FieldName === FieldName);
      if (field) {
        const { FieldDisplayName, OperatorFieldvalues } = field;
        const { FieldValueType, FieldValueList = [] } = OperatorFieldvalues[OperatorId] || {};
        let FieldValueName = '';
        switch (FieldValueType) {
          case 8: // Time radio TimePicker
            FieldValueName = numberToTime(FieldValue);
            break;
          case 801: // Time radio TimePicker
            FieldValueName = millisecondToTime(FieldValue)?.join(' ~ ');
            break;
          case 17: // input float
            FieldValueName = formatNum(FieldValue);
            break;
          case 1: // Radio
            FieldValueName =
              FieldValueList.find(o => {
                if (typeof FieldValue === 'string') {
                  return o.value === FieldValue;
                } else {
                  return o.value === String(FieldValue);
                }
              })?.name || FieldValue;
            break;
          case 2: // Multiple choice
          case 701:
            FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
              .reduce((str, val) => {
                const v = FieldValueList.find(o => o.value === String(val))?.name;
                return `${str}、${v ? v : val}`;
              }, '')
              .slice(1);

            break;
          case 7: // Tree
            FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
              .reduce((str, val) => {
                const target = getFieldByValue(
                  JSON.stringify([user?.accessPartyId, ruleType, FieldName, OperatorId]),
                  FieldValueList,
                  val
                );
                const v = target?.name;
                return `${str}、${v ? v : val}`;
              }, '')
              .slice(1);
            break;
          case 3: // Mix (multiple choice + input box)
          case 4: // Time control
          case 14:
            FieldValueName = format(Number(FieldValue), 'YYYY-mm-dd HH: ii ss');
            break;

          case 5: // Input box
          case 6: // Batch Input
          default:
            FieldValueName = FieldValue;
        }

        return {
          FieldDisplayName,
          FieldValueName: Array.isArray(FieldValueName) ? FieldValueName.join('、') : FieldValueName,
        };
      }

      return {
        FieldDisplayName: FieldName,
        FieldValueName: Array.isArray(FieldValue) ? FieldValue.join('、') : FieldValue,
      };
    };

    const renderFilterComponents = (filterData, index, filter) => {
      if (!filterData) {
        return null;
      }
      const FilterUnit = filter?.Conditions || [];
      const OperateTypes = filter?.OpGroup;
      const { FieldDisplayName, FieldValueName } = getFieldDisplayName(filterData);
      const isLastFilter = FilterUnit.length === index + 1;
      const contactText = isLastFilter ? null : (
        <span className={styles.ruleContentLabel}>{config.ConditionOperateType[OperateTypes]()}</span>
      );
      return (
        <>
          <span className={styles.ruleContentText}>{FieldDisplayName}</span>
          <span className={styles.ruleContentLabel}>
            {config.ruleOpcheckArr.filter(item => item.serveOp === filterData.OpCheck)[0]?.clientName()}
          </span>
          <span className={styles.ruleContentText}>{FieldValueName}</span>
          {contactText}
        </>
      );
    };

    // Specific rules of the rule list (xxx if xxx)
    const renderConditionGroupsComps = (filterDatas, k) => (
      <React.Fragment key={k}>
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
          <span className={styles.bracketsColor}>(</span>
        )}
        {filterDatas?.Conditions ?
          filterDatas?.Conditions?.map((filterData, h) => (
            <React.Fragment key={h}>{renderFilterComponents(filterData, h, filterDatas)}</React.Fragment>
          )) :
          filterDatas?.ConditionGroups?.map((newFilterDatas, n) => (
            <React.Fragment key={n}>
              {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                <span className={styles.bracketsColor}>(</span>
              )}
              {newFilterDatas?.Conditions?.map((newFilterData, m) => (
                <React.Fragment key={m}>{renderFilterComponents(newFilterData, m, newFilterDatas)}</React.Fragment>
              ))}
              {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                <span className={styles.bracketsColor}>)</span>
              )}
              {filterDatas?.ConditionGroups.length === n + 1 ? null : (
                <span className={styles.ruleContentLabel}>{config.ConditionOperateType[filterDatas?.OpGroup]()}</span>
              )}
            </React.Fragment>
          ))}
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
          <span className={styles.bracketsColor}>)</span>
        )}
        {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 ? null : (
          <span className={styles.ruleContentLabel}>
            {config.ConditionOperateType[ruleDataChild?.Expression?.OpGroup]()}
          </span>
        )}
      </React.Fragment>
    );

    return (
      <>
        {/* {it != 0 ? <div className={styles.empty} /> : null} */}
        <div className={styles.ruleBoxHeader}>
          <div className={styles.headerTitle}>
            <div className={styles.headerLeftIconBox}>
              <span>{`${ruleDataChild.ruleIndex + 1}.`}</span>
            </div>
            <div className={styles.headerNameBox}>
              <Tooltip className={styles.tooltip} content={ruleDataChild.DisplayName}>
                <span className={styles.headerName}>{ruleDataChild.DisplayName}</span>
              </Tooltip>
              {ruleDataChild.DraftEditType === 1 ? (
                <Tag className={styles.statusTag} color={'violet'}>
                  {config.DraftEditType[ruleDataChild.DraftEditType]()}
                </Tag>
              ) : ruleDataChild.DraftEditType === 2 ? (
                <Tag className={styles.statusTag} color={'violet'}>
                  {config.DraftEditType[ruleDataChild.DraftEditType]()}
                </Tag>
              ) : null}
              <Tag className={styles.statusTag} color={ruleDataChild.Status === 1 ? 'teal' : 'red'}>
                {status}
              </Tag>
            </div>
          </div>
          {
            tabKey === 'preReleaseRules' && (
              <div className={styles.cardOperations}>
                <>
                  <CardOperation
                    icon={TopIcon.id}
                    tooltip={I18n.t('top', {}, 'Top')}
                    onClick={() => {
                      onReOrder(CardReOrderType.Top, ruleDataChild);
                    }}
                  />
                  <CardOperation
                    icon={UpIcon.id}
                    tooltip={I18n.t('move_one_up', {}, 'Move one up')}
                    onClick={() => {
                      onReOrder(CardReOrderType.Up, ruleDataChild);
                    }}
                  />
                  <CardOperation
                    icon={DownIcon.id}
                    tooltip={I18n.t('move_one_down', {}, 'Move one down')}
                    onClick={() => {
                      onReOrder(CardReOrderType.Down, ruleDataChild);
                    }}
                  />
                  <CardOperation
                    icon={BottomIcon.id}
                    tooltip={I18n.t('bottom', {}, 'Bottom')}
                    onClick={() => {
                      onReOrder(CardReOrderType.Bottom, ruleDataChild);
                    }}
                  />
                </>
                <DropdownList
                  ruleDataChild={ruleDataChild}
                  ruleType={ruleType}
                  clickDropItem={clickDropItem}
                  clickDropItemEdit={clickDropItemEdit}
                  clickClone={clickClone}
                />
              </div>
            )}
        </div>
        <div className={styles.positionText}>
          <div className={styles.headerTipBox}>
            <span className={styles.headerTips}>
              {`${I18n.t(
                '{placeholder0}_updated_to_{placeholder2}',
                {
                  placeholder0: ruleDataChild.UpdaterAgentName,
                  placeholder2: ruleDataChild.UpdatedAt,
                },
                '{placeholder0} updates at {placeholder2}'
              )}`}
            </span>
          </div>
          <div className={styles.ruleFilterBox}>
            <div className={styles.ruleFilterDot} />
            <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
              <span className={styles.ruleContentLabel}>{I18n.t('if', {}, 'If')}</span>
              {ruleDataChild?.Expression?.Conditions ? (
                ruleDataChild?.Expression?.Conditions?.map((filterData, k) => (
                  <React.Fragment key={k}>
                    {renderFilterComponents(filterData, k, ruleDataChild?.Expression)}
                  </React.Fragment>
                ))
              ) : (
                ruleDataChild?.Expression?.ConditionGroups?.map((filterDatas, k) =>
                  renderConditionGroupsComps(filterDatas, k)
                )
              )}
            </CollapseHorizontalText>
          </div>
          {getMaualAccessPartyContent(ruleDataChild)}
        </div>
      </>
    );
  };

  const renderRuleContent = (ruleData, index) => {
    const highLight = ruleData.Id === newRuleId ? '1px solid #361C8A' : '';
    return (
      <div className={styles.ruleBox} style={{ border: highLight }}>
        {ruleContent(ruleData, index)}
      </div>
    );
  };

  return (
    <div>
      {ruleDataList.length ? (
        <div className={styles.title}>
          <div className={styles.titlePoint} />
          <span style={{ color: 'var(--color-text-2)' }}>{I18n.t('the_access_party_shunt_rule_is_judged_in_sequence_from_top_to_bottom', {}, 'The access party shunt rule is judged in sequence from top to bottom')}</span>
        </div>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={I18n.t('no_access_party_offload_rule_yet', {}, 'No access party offload rule yet')}
          // eslint-disable-next-line react/no-children-prop
          children={null}
        />
      )}
      <DndProvider backend={HTML5Backend}>
        <div style={{ marginBottom: 40 }}>
          {ruleDataList.map((ruleData, index) => {
            const singleRuleData = index > 0 ? ruleDataList[index - 1] : { Priority: 0 };
            return (
              <div key={index}>
                {tabKey === 'preReleaseRules' && (
                  <Tooltip
                    content={I18n.t('no_access_party_offload_rule_yet', {}, 'No access party offload rule yet')}
                  >
                    <Button
                      className={styles.ruleBtn}
                      disabled={!hasEditAuth}
                      onClick={() => {
                        createRouteRule(ruleType, ruleData, singleRuleData);
                      }}
                      icon="plus_circle"
                      style={{ width: 32 }}
                    />
                  </Tooltip>
                )}
                <div className={styles.intervalBox} />
                {renderRuleContent(ruleData, index)}
                {
                  ruleDataList.length === index + 1 && tabKey === 'preReleaseRules' && (
                    <Tooltip
                      content={I18n.t('no_access_party_offload_rule_yet', {}, 'No access party offload rule yet')}
                    >
                      <Button
                        className={styles.ruleBtn}
                        disabled={!hasEditAuth}
                        onClick={() => {
                          createRouteRule(ruleType, ruleData, ruleDataList[index]);
                        }}
                        icon="plus_circle"
                        style={{ width: 32 }}
                      />
                    </Tooltip>
                  )
                }
              </div>
            );
          })}
        </div>
      </DndProvider>
      <Modal
        title={I18n.t('delete', {}, 'Delete')}
        visible={modalVisible}
        onOk={handleOk}
        cancelText={I18n.t('cancel', {}, 'Cancel')}
        okText={I18n.t('ok', {}, 'OK')}
        onCancel={handleCancel}
        icon={<Icon type="alert_triangle" style={{ color: '#F93920' }} size="extra-large" />}
      >
        <p>
          {I18n.t(
            'once_deleted__the_data_will_not_be_recovered__please_operate_with_caution',
            {},
            'Be cautious! The data will not be recovered once deleted.'
          )}
        </p>
      </Modal>
    </div>
  );
};

export default RuleList;
