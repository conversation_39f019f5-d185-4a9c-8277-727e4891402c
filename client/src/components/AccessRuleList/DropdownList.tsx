import React from 'react';
import { I18n } from '@ies/starling_intl';
import { OperationType } from '@/const/enums';
import { useAccessDoorAuthority } from '@/hooks/useAccessDoorAuthority';
import { KefuDropdown } from '@ies/kefu-components';

const DropdownList = ({
  ruleDataChild,
  ruleType,
  clickDropItem,
  clickDropItemEdit,
  clickClone,
}) => {
  const { hasEditAuth, hasViewAuth } = useAccessDoorAuthority();

  let dropdownItems = [];
  if (!hasEditAuth && hasViewAuth) {
    dropdownItems = [
      {
        text: I18n.t('view', {}, 'View'),
        onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
      },
    ];
  }
  if (hasEditAuth && !hasViewAuth) {
    dropdownItems =
      [
        {
          text: ruleDataChild?.Status === 1 ? I18n.t('disable', {}, 'Disable') : I18n.t('enable', {}, 'Enable'),
          onClick: () =>
            clickDropItem(
              ruleDataChild?.Status === 1 ? OperationType.Disable : OperationType.Enable,
              ruleDataChild
            ),
        },
        {
          text: I18n.t('edit', {}, 'Edit'),
          onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
        },
        {
          text: I18n.t('copy', {}, 'Copy'),
          onClick: () => clickClone(OperationType.Clone, ruleDataChild, ruleType),
        },
        {
          text: I18n.t('delete', {}, 'Delete'),
          type: 'danger',
          onClick: () => clickDropItem(OperationType.Delete, ruleDataChild),
        },
      ];
  }
  if (hasEditAuth && hasViewAuth) {
    dropdownItems =
      [
        {
          text: ruleDataChild?.Status === 1 ? I18n.t('disable', {}, 'Disable') : I18n.t('enable', {}, 'Enable'),
          onClick: () =>
            clickDropItem(
              ruleDataChild?.Status === 1 ? OperationType.Disable : OperationType.Enable,
              ruleDataChild
            ),
        },
        {
          text: I18n.t('view', {}, 'View'),
          onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Check),
        },
        {
          text: I18n.t('edit', {}, 'Edit'),
          onClick: () => clickDropItemEdit(ruleDataChild, ruleType, OperationType.Edit),
        },
        {
          text: I18n.t('copy', {}, 'Copy'),
          onClick: () => clickClone(OperationType.Clone, ruleDataChild, ruleType),
        },
        {
          text: I18n.t('delete', {}, 'Delete'),
          type: 'danger',
          onClick: () => clickDropItem(OperationType.Delete, ruleDataChild),
        },
      ];
  }

  if (!hasEditAuth && !hasViewAuth) {
    dropdownItems = [];
  }

  return (
    dropdownItems.length > 0 ? (
      <KefuDropdown
        dropdownItems={dropdownItems}
      />
    ) : null);
};

export default DropdownList;
