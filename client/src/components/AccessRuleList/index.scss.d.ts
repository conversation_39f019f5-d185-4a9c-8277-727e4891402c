export const title: string;
export const titlePoint: string;
export const intervalBox: string;
export const ruleBox: string;
export const ruleBoxHeader: string;
export const headerTitle: string;
export const iconBox: string;
export const positionIcon: string;
export const headerLeftIconBox: string;
export const handleIcon: string;
export const headerTipBox: string;
export const headerTips: string;
export const headerNameBox: string;
export const headerName: string;
export const statusTag: string;
export const moreBtn: string;
export const dangerOperator: string;
export const ruleFilterBox: string;
export const ruleSkillBox: string;
export const ruleFilterDot: string;
export const ruleSkillDot: string;
export const ruleEmptyDot: string;
export const ruleAccessDot: string;
export const ruleStrategyDot: string;
export const ruleContentLabel: string;
export const ruleContentText: string;
export const ruleSkillIcon: string;
export const tooltip: string;
export const unExpandBox: string;
export const unExpandBoxLeft: string;
export const expandBtn: string;
export const ticketRuleItem: string;
export const dropdownLine: string;
export const ruleBtn: string;
export const cardOperations: string;
export const positionText: string;
export const isShow: string;
export const empty: string;
export const bracketsColor: string;
