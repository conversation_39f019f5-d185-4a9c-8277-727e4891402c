import { I18n } from '@ies/starling_intl';

export const FilterOperateType = {
  0: () => I18n.t('or_2', {}, 'or'),
  1: () => I18n.t('and_2', {}, 'and'),
};
export const ConditionOperateType = {
  AND: () => I18n.t('and', {}, 'and'),
  OR: () => I18n.t('or', {}, 'or'),
  NOT: () => I18n.t('not', {}, 'not'),
};

export const DraftEditType = {
  1: () => I18n.t('added_rules', {}, 'Added rules'),
  2: () => I18n.t('editing_rules', {}, 'Editing rules'),
  0: () => I18n.t('unmodified_rule', {}, 'Unmodified rule'),
};

export const OperatorTypeMap = {
  [1]: () => I18n.t('equals', {}, 'equals to'),
  [2]: () => I18n.t('not_equal_to', {}, 'not equal(s) to'),
  [3]: () => I18n.t('include', {}, 'include'),
  [4]: () => I18n.t('greater_than', {}, 'greater than'),
  [5]: () => I18n.t('less_than', {}, 'less than'),
  [6]: () => I18n.t('greater_than_or_equal_to', {}, 'no less than'),
  [7]: () => I18n.t('less_than_or_equal_to', {}, 'no more than'),
  [8]: () => I18n.t('started_with', {}, 'started at'),
  [9]: () => I18n.t('ended_in', {}, 'ended at'),
  [10]: () => I18n.t('empty', {}, 'empty'),
  [11]: () => I18n.t('not_empty', {}, 'not empty'),
  [12]: () => I18n.t('include', {}, 'include'),
  [13]: () => I18n.t('include', {}, 'include'),
  [14]: () => I18n.t('equals', {}, 'equals to'),
  [15]: () => I18n.t('not_equal_to', {}, 'not equal(s) to'),
  [16]: () => I18n.t('equals', {}, 'equals to'),
  [17]: () => I18n.t('not_equal_to', {}, 'not equal(s) to'),
  [18]: () => I18n.t('greater_than', {}, 'greater than'),
  [19]: () => I18n.t('less_than', {}, 'less than'),
  [20]: () => I18n.t('greater_than_or_equal_to', {}, 'no less than'),
  [21]: () => I18n.t('less_than_or_equal_to', {}, 'no more than'),
  [22]: () => I18n.t('not_included', {}, 'not include'),
  [30]: () => I18n.t('equals', {}, 'equals to'),
  [31]: () => I18n.t('not_equal_to', {}, 'not equal(s) to'),
  [32]: () => I18n.t('not_included', {}, 'not include'),
  [33]: () => I18n.t('include', {}, 'include'),
  [34]: () => I18n.t('not_included', {}, 'not include'),
  [35]: () => I18n.t('belong_to', {}, 'belong to'),
  [36]: () => I18n.t('do_not_belong', {}, 'do not belong'),
};

export const ruleOpcheckArr = [
  { serveOp: '==', clientOp: 1, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 2, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: 'CONTAINS', clientOp: 3, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: '>', clientOp: 4, opType: 'math', clientName: () => I18n.t('greater_than', {}, 'greater than') },
  { serveOp: '<', clientOp: 5, opType: 'math', clientName: () => I18n.t('less_than', {}, 'less than') },
  { serveOp: '>=', clientOp: 6, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, 'no less than') },
  { serveOp: '<=', clientOp: 7, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, 'no more than') },
  { serveOp: 'START_WITH', clientOp: 8, opType: 'fun', clientName: () => I18n.t('started_with', {}, 'started at') },
  { serveOp: 'END_WITH', clientOp: 9, opType: 'fun', clientName: () => I18n.t('ended_in', {}, 'ended at') },
  { serveOp: 'IS_NULL', clientOp: 10, opType: 'fun', clientName: () => I18n.t('empty', {}, 'empty') },
  { serveOp: 'IS_NOT_NULL', clientOp: 11, opType: 'fun', clientName: () => I18n.t('not_empty', {}, 'not empty') },
  { serveOp: 'LIST_IN', clientOp: 12, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: 'STRING_CONTAINS', clientOp: 13, opType: 'fun', clientName: () => I18n.t('include', {}, 'include') },
  { serveOp: '==', clientOp: 14, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 15, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: '==', clientOp: 16, opType: 'math', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: '!=', clientOp: 17, opType: 'math', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  { serveOp: '>', clientOp: 18, opType: 'math', clientName: () => I18n.t('greater_than', {}, 'greater than') },
  { serveOp: '<', clientOp: 19, opType: 'math', clientName: () => I18n.t('less_than', {}, 'less than') },
  { serveOp: '>=', clientOp: 20, opType: 'math', clientName: () => I18n.t('greater_than_or_equal_to', {}, 'no less than') },
  { serveOp: '<=', clientOp: 21, opType: 'math', clientName: () => I18n.t('less_than_or_equal_to', {}, 'no more than') },
  { serveOp: 'LIST_NOT_IN', clientOp: 22, opType: 'fun', clientName: () => I18n.t('not_included', {}, 'not include') },
  { serveOp: 'LIST_EQUAL', clientOp: 30, opType: 'fun', clientName: () => I18n.t('equals', {}, 'equals to') },
  { serveOp: 'LIST_NOT_EQUAL', clientOp: 31, opType: 'fun', clientName: () => I18n.t('not_equal_to', {}, 'not equal(s) to') },
  {
    serveOp: 'STRING_NOT_CONTAINS',
    clientOp: 32,
    opType: 'fun',
    clientName: () => I18n.t('not_included', {}, 'not include'),
  },
  { serveOp: 'LIST_RETAIN', clientOp: 33, opType: 'fun', clientName: () => I18n.t('intersect', {}, 'intersect') },
  { serveOp: 'NOT LIST_IN', clientOp: 34, opType: 'fun', clientName: () => I18n.t('not_included', {}, 'not include') },
  { serveOp: 'BETWEEN_ALL_CLOSE', clientOp: 35, opType: 'fun', clientName: () => I18n.t('belong_to', {}, 'belong to') },
  {
    serveOp: 'NOT BETWEEN_ALL_OPEN',
    clientOp: 36,
    opType: 'fun',
    clientName: () => I18n.t('do_not_belong', {}, 'do not belong'),
  },
  {
    serveOp: 'NOT LIST_RETAIN',
    clientOp: 37,
    opType: 'fun',
    clientName: () => I18n.t('no_intersection', {}, 'no intersection'),
  },
];

