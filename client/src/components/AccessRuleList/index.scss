:local {
  .title {
    position: relative;
    top: 2px;
    margin: 20px 0 12px;
    padding-left: 45px;
    color: rgba(28, 31, 35, .6);
    font-size: 14px;
    font-weight: 600;
  }

  .title::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 12px;
    left: 29px;
    top: 18px;
    background: rgba(28, 31, 35, .08);
  }

  .titlePoint {
    position: absolute;
    left: 23px;
    top: 4px;
    width: 14px;
    height: 14px;
    border: 2px solid #ededed;
    border-radius: 50%;
  }

  .intervalBox {
    margin-left: 29px;
    width: 1px;
    height: 8px;
    background: rgba(28, 31, 35, .08);
  }

  .ruleBox {
    width: 612px;
    padding: 12px 16px;
    position: relative;
    border: 1px solid rgba(28, 31, 35, .08);
    border-radius: 6px;
  }

  .ruleBoxHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .headerTitle {
    display: flex;
    align-items: center;
    flex-grow: 1;
    margin-right: 20px;
    font-weight: 600;
    font-size: 16px;
    color: rgba(28, 31, 35, .8);
  }

  .iconBox {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .positionIcon {
    margin-right: 18px;
    cursor: pointer;
  }

  .headerLeftIconBox {
    flex-shrink: 0;
    color: var(--color-text-1);
  }

  .handleIcon {
    color: rgba(28, 31, 35, .6);
  }

  .headerTipBox {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    position: absolute;
    bottom: -7px;
    right: 0;
  }

  .headerTips {
    margin-right: 8px;
    font-size: 12px;
    color: var(--color-text-2);
  }

  .headerNameBox {
    flex-grow: 1;
    width: 0;
    margin-left: 4px;
    display: flex;
    align-items: center;
    --tag-margin: 4px;
  }

  .headerName {
    display: inline-block;
    max-width: calc(100% - 40px - var(--tag-margin));
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--color-text-1);
  }

  .statusTag {
    margin-left: var(--tag-margin);
    min-width: 64px;
  }
  
  .moreBtn {
    margin-left: 8px;
    background-color: transparent;
  }

  .dangerOperator {
    color: #f93920;
  }

  .ruleFilterBox {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;
  }

  .ruleSkillBox {
    display: flex;
    margin-bottom: 8px;
    align-items: baseline;
  }

  .ruleFilterDot {
    flex-shrink: 0;
    margin-left: 16px;
    margin-right: 6px;
    height: 7px;
    width: 7px;
    border-radius: 50%;
    border: 2px solid #0077fa;
  }

  .ruleSkillDot {
    flex-shrink: 0;
    margin-left: 16px;
    margin-right: 6px;
    height: 7px;
    width: 7px;
    border-radius: 50%;
    border: 2px solid #2cb8c5;
  }

  .ruleEmptyDot {
    flex-shrink: 0;
    margin-left: 16px;
    margin-right: 6px;
    height: 7px;
    width: 7px;
    border-radius: 50%;
    border: 2px solid red;
  }


  .ruleAccessDot {
    flex-shrink: 0;
    margin-left: 16px;
    margin-right: 6px;
    height: 7px;
    width: 7px;
    border-radius: 50%;
    border: 2px solid #3EB34C;
  }

  .ruleStrategyDot {
    flex-shrink: 0;
    margin-left: 16px;
    margin-right: 6px;
    height: 7px;
    width: 7px;
    border-radius: 50%;
    border: 2px solid #586261;
  }
  
  .ruleContentLabel {
    margin-right: 4px;
    font-size: 12px;
    line-height: 16px;
    color: var(--color-text-2);
  }

  .ruleContentText {
    margin-right: 4px;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    color: var(--color-text-1);
  }

  .ruleSkillIcon {
    margin-right: 4px;
  }

  .tooltip {
    word-break: break-all;
  }

  .unExpandBox {
    display: flex;
    align-items: baseline;
    flex-grow: 1;
    width: 0;
  }

  .unExpandBoxLeft {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .expandBtn {
    flex-shrink: 0;
    margin-left: 4px;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    color: #0077fa;
    cursor: pointer;
  }

  .ticketRuleItem {
    margin-top: 16px;
  }

  .dropdownLine {
    box-sizing: border-box;
    width: 176px;
    padding-left: 30px;
  }

  .rule-btn {
    margin: 8px 0 0 14px !important;
    cursor: pointer;
    position: relative;
  }

  .rule-btn::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 8px;
    left: 15px;
    top: -8px;
    background: rgba(28, 31, 35, .08);
  }

  .cardOperations {
    display: flex;
    align-items: center;
  }

  .positionText {
    position: relative;
    padding-bottom: 8px;
  }

  .isShow {
    opacity: 0;
  }

  .empty {
    height: 10px;
    width: 100%;
  }

  .bracketsColor {
    color: var(--color-text-2);
  }
}
