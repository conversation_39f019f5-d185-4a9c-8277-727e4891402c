import { I18n } from '@ies/starling_intl';
import * as React from 'react';
import { Form } from '@ies/semi-ui-react';
import styles from './index.scss';

interface ISkillGroupsSelect {
  field?: string;
  label?: string;
  width?: string;
  placeholder?: string;
  noLabel?: boolean;
  allow?: boolean;
  multiple?: boolean;
  rules?: any[];
  skillGroups: any[];
  /**
   * Excluded options
   */
  omittedGroups?: string[];
  onChange?: (...args: any[]) => any;
}

// const ChannelTypeMap = {
//   1: 'IM skill group',//IM
//   2: 'Work order skill group',//work order
//   3: 'Telephone Skills Group',//Telephone
//   4: 'Other skill groups',//Other
// };

const SkillGroupsSelect: React.FC<ISkillGroupsSelect> = props => {
  const { omittedGroups = [] } = props;
  const omittedSet = new Set(omittedGroups);
  const skillGroups =
    props.skillGroups
      .map(v => ({
        label: v.Name,
        value: v.ID,
      }))
      .filter(item => !omittedSet.has(item.value)) || [];

  return (
    <Form.Select
      className={styles.selectBox}
      style={{ width: props.width || '100%' }}
      label={props.label || ''}
      rules={props.rules || []}
      placeholder={props.placeholder || I18n.t('select_skill_group', {}, '选择技能组')}
      field={props.field || ''}
      noLabel={props.noLabel || false}
      multiple={props.multiple || false}
      optionList={skillGroups}
      maxTagCount={4}
      showClear
      filter
    />
  );
};

export default SkillGroupsSelect;
