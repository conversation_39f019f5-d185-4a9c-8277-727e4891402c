import { I18n } from '@ies/starling_intl';
import React, { useContext } from 'react';
import styles from './index.scss';
import * as config from './config';
import { Tooltip, Tag } from '@ies/semi-ui-react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { KefuPageEmptyContent } from '@ies/kefu-components';

import { getFieldByValue } from '@/common/utils/treeData';
import NoContent from '@ies/semi-illustrations/noContent.svg';
import BotIcon from '@common/images/bot.svg';
import { useIsEcomAccessParty } from '@hooks/useTicketRuleBaseParam';
import PeopleIcon from '@common/images/people.svg';
import { CollapseHorizontalText } from '@components/NewCollapseText';
import { routeType } from '@/const/enums';
import { AdminRule, FieldValueItem, SkillGroup } from '@http_idl/demo';
import { numberToTime } from '@/common/utils/dateTimeField';
import { UserContext } from '@/context/user';
import { routeTypeMap } from '@constants/property';
import { formatNum } from '@/common/utils/inputFloat';
import format, { millisecondToTime } from '@common/utils/date';
import { safeJSONParse } from '@/common/utils';
import { errorReporting } from '@/common/utils/errorReporting';

interface RuleListProps {
  ruleList: AdminRule[];
  skillGroups: SkillGroup[];
  fieldList: any[];
  ruleType: routeType;
  botList?: FieldValueItem[];
  ticketAllSkillGroups?: SkillGroup[];
}

const RuleList: React.FC<RuleListProps> = ({
  ruleList,
  skillGroups,
  fieldList,
  ruleType,
  botList,
  ticketAllSkillGroups,
}) => {
  const isEcomAccessParty = useIsEcomAccessParty();
  const user = useContext(UserContext);

  const getSkillGroupName = ({
    id,
    isShunt = null,
    skillList = null,
    supportOverflow = null,
    isAutoShunt = null,
    autoShuntSkillList = [],
  }) => {
    let ids = [];
    if (isShunt && !isAutoShunt) {
      ids = skillList;
    } else if (isShunt && isAutoShunt) {
      ids = autoShuntSkillList;
    } else {
      ids = Array.isArray(id) ? id : [id];
    }
    const skillGroup = (skillGroups || [])?.filter(o => {
      if (ids?.length) {
        return ids?.includes(o?.ID);
      } else {
        return '';
      }
    });
    if (supportOverflow) {
      const group = (ticketAllSkillGroups || [])?.filter(o => {
        if (ids?.length) {
          return ids?.includes(o?.ID);
        } else {
          return '';
        }
      });
      return group?.map(val => val?.Name)?.join('、');
    }
    if (skillGroup.length) {
      return skillGroup?.map(val => val?.Name)?.join('、');
    }
    return id;
  };
  function isJSON(str) {
    if (typeof str === 'string') {
      try {
        const newStr = safeJSONParse(str);
        if (newStr !== str) {
          return true;
        } else {
          return false;
        }
      } catch (error) {
        errorReporting({ error, type: 'promise_name', name: 'getSkillGroupName' });
        return false;
      }
    }
  }
  const getFieldDisplayName = (filterData = {} as any) => {
    const FieldName = filterData?.Lhs?.VarExpr || filterData?.Lhs?.FeatureExpr?.FeatureName;
    const OperatorId = filterData?.OpCheck;
    let FieldValue =
      filterData?.Rhs?.Constant ||
      filterData?.Rhs?.ConstantList ||
      filterData?.Rhs?.ExprList ||
      filterData?.Rhs?.VonstantList;
    if (FieldName === 'ticket_category.full_category_ids') {
      const categoryIds = [];
      FieldValue.forEach(item => {
        if (item.split(',').length > 2) {
          categoryIds.push(item);
        }
      });
      FieldValue = [...categoryIds];
    }
    if (Array.isArray(FieldValue)) {
      FieldValue = FieldValue.map(item => {
        const isFlag = isJSON(item);
        if (isFlag) {
          return safeJSONParse(item);
        } else {
          return item;
        }
      });
    } else {
      const isFlag = isJSON(FieldValue);
      if (isFlag) {
        FieldValue = safeJSONParse(FieldValue);
      }
    }

    const field = fieldList.find(o => o.FieldName === FieldName);
    if (field) {
      const { FieldDisplayName, Fieldvalues, OperatorFieldvalues, OperatorList, OperatorIds } = field;
      const { FieldValueType, FieldValueList = [] } = OperatorFieldvalues[OperatorId] || {};
      let FieldValueName = '';

      switch (FieldValueType) {
        case 8: // Time radio TimePicker
          FieldValueName = numberToTime(FieldValue);
          break;
        case 801: // Time radio TimePicker
          FieldValueName = millisecondToTime(FieldValue)?.join(' ~ ');
          break;
        case 17: // input float
          FieldValueName = formatNum(FieldValue);
          break;
        case 1: // Radio
          FieldValueName =
            FieldValueList.find(o => {
              if (typeof FieldValue === 'string') {
                return o.value === FieldValue;
              } else {
                return o.value === String(FieldValue);
              }
            })?.name || FieldValue;
          break;
        case 2: // Multiple choice
        case 701:
          FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
            .reduce((str, val) => {
              const v = FieldValueList.find(o => o.value === String(val))?.name;
              return `${str}、${v ? v : val}`;
            }, '')
            .slice(1);
          break;
        case 7: // Tree
          FieldValueName = (Array.isArray(FieldValue) ? FieldValue : [FieldValue])
            .reduce((str, val) => {
              const target = getFieldByValue(
                JSON.stringify([user?.accessPartyId, ruleType, FieldName, OperatorId]),
                FieldValueList,
                val
              );
              const v = target?.name;
              return `${str}、${v ? v : val}`;
            }, '')
            .slice(1);
          break;
        case 3: // Mix (multiple choice + input box)
        case 4: // Time control
        case 14:
          FieldValueName = format(Number(FieldValue), 'YYYY-mm-dd HH: ii ss');
          break;
        case 5: // Input box
        case 6: // Batch Input
        default:
          FieldValueName = FieldValue;
      }

      return {
        FieldDisplayName,
        FieldValueName: Array.isArray(FieldValueName) ? FieldValueName.join('、') : FieldValueName,
      };
    }

    return {
      FieldDisplayName: FieldName,
      FieldValueName: Array.isArray(FieldValue) ? FieldValue.join('、') : FieldValue,
    };
  };

  const renderFilterComponents = (filterData, index, Filter) => {
    if (!filterData) {
      return null;
    }
    const FilterUnit = Filter?.Conditions || [];
    const OperateTypes = Filter?.OpGroup;
    // const { OperatorId } = filterData;

    const { FieldDisplayName, FieldValueName } = getFieldDisplayName(filterData);
    const isLastFilter = FilterUnit.length === index + 1;
    const contactText = isLastFilter ? null : (
      <span className={styles.ruleContentLabel}>{config.ConditionOperateType[OperateTypes]()}</span>
    );
    return (
      <>
        <span className={styles.ruleContentText}>{FieldDisplayName}</span>
        <span className={styles.ruleContentLabel}>
          {config.ruleOpcheckArr.filter(item => item.serveOp === filterData.OpCheck)[0]?.clientName()}
        </span>
        <span className={styles.ruleContentText}>{FieldValueName}</span>
        {contactText}
      </>
    );
  };
  const getBotName = id => {
    const bot = (botList || []).find(o => o.value === Number(id || '0'));
    if (bot) {
      return bot.name;
    }
    return id;
  };
  // const addNewRule = () => {};

  const ruleContent = (ruleDataChild, index) => {
    const isLast = ruleDataChild.Priority === 9999; // Bottom rules
    const status = ruleDataChild.Status ? I18n.t('enable', {}, '启用') : I18n.t('disable', {}, '禁用');
    // const isShow = it != 0 ? styles.isShow : '';
    return (
      <>
        {/* {it != 0 ? <div className={styles.empty} /> : null} */}
        <div className={styles.ruleBoxHeader}>
          <div className={styles.headerTitle}>
            <div className={styles.headerLeftIconBox}>
              <span>{`${index + 1}.`}</span>
            </div>
            <div className={styles.headerNameBox}>
              <Tooltip className={styles.tooltip} content={ruleDataChild.DisplayName}>
                <span className={styles.headerName}>{ruleDataChild.DisplayName}</span>
              </Tooltip>
              <Tag className={styles.statusTag} color={ruleDataChild.Status ? 'teal' : 'red'}>
                {status}
              </Tag>
            </div>
          </div>
        </div>
        <div className={styles.positionText}>
          <div className={styles.headerTipBox}>
            <span className={styles.headerTips}>
              {`${I18n.t(
                '{placeholder0}_updated_to_{placeholder2}',
                {
                  placeholder0: ruleDataChild.UpdaterAgentName,
                  placeholder2: ruleDataChild.UpdatedAt,
                },
                '{placeholder0} 更新于 {placeholder2}'
              )}`}
            </span>
          </div>
          <div className={styles.ruleFilterBox}>
            <div className={styles.ruleFilterDot} />
            <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
              <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>

              {isLast ? (
                <span className={styles.ruleContentText}>{I18n.t('missed_the_above_rules', {}, '未命中以上规则')}</span>
              ) : ruleDataChild?.Expression?.Conditions ? (
                ruleDataChild?.Expression?.Conditions?.map((filterData, k) => (
                  <React.Fragment key={k}>
                    {renderFilterComponents(filterData, k, ruleDataChild?.Expression)}
                  </React.Fragment>
                ))
              ) : (
                ruleDataChild?.Expression?.ConditionGroups?.map((filterDatas, k) => (
                  <React.Fragment key={k}>
                    {/* ruleDataChild?.Expression.conditionGroups */}
                    {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
                      <span className={styles.bracketsColor}>(</span>
                    )}
                    {/* {filterDatas.Conditions.map((filterData, h) => (
                    <React.Fragment key={h}>{renderFilterComponents(filterData, h, filterDatas)}</React.Fragment>
                  ))} */}
                    {filterDatas?.Conditions ?
                      filterDatas?.Conditions?.map((filterData, h) => (
                        <React.Fragment key={h}>{renderFilterComponents(filterData, h, filterDatas)}</React.Fragment>
                        )) :
                      filterDatas?.ConditionGroups?.map((newFilterDatas, n) => (
                        <React.Fragment key={n}>
                          {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                            <span className={styles.bracketsColor}>(</span>
                            )}
                          {newFilterDatas?.Conditions?.map((newFilterData, m) => (
                            <React.Fragment key={m}>
                              {renderFilterComponents(newFilterData, m, newFilterDatas)}
                            </React.Fragment>
                            ))}
                          {filterDatas?.ConditionGroups.length === n + 1 && n === 0 ? null : (
                            <span className={styles.bracketsColor}>)</span>
                            )}
                          {filterDatas?.ConditionGroups.length === n + 1 ? null : (
                            <span className={styles.ruleContentLabel}>
                              {config.ConditionOperateType[filterDatas?.OpGroup]()}
                            </span>
                            )}
                        </React.Fragment>
                        ))}
                    {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 && k === 0 ? null : (
                      <span className={styles.bracketsColor}>)</span>
                    )}
                    {ruleDataChild?.Expression?.ConditionGroups.length === k + 1 ? null : (
                      <span className={styles.ruleContentLabel}>
                        {config.ConditionOperateType[ruleDataChild?.Expression?.OpGroup]()}
                      </span>
                    )}
                  </React.Fragment>
                ))
              )}
            </CollapseHorizontalText>
          </div>
          {/* bot */}
          {ruleType === routeType.bot_routing && ruleDataChild.IsOpen === 1 && (
            <div className={styles.ruleFilterBox}>
              <div className={styles.ruleSkillDot} />
              <span className={styles.ruleContentLabel}>{I18n.t('provide', {}, '提供')}</span>
              <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
            </div>
          )}

          <div className={styles.ruleSkillBox}>
            {ruleType === routeType.bot_routing ? (
              ruleDataChild.IsOpen === 1 ? (
                <>
                  <div className={styles.ruleSkillDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={BotIcon} />
                  <span className={styles.ruleContentText}>{getBotName(ruleDataChild.BotId)}</span>
                </>
              ) : (
                <>
                  <div className={styles.ruleEmptyDot} />
                  <span className={styles.ruleContentLabel}>{I18n.t('not_available', {}, '不提供')}</span>
                  <span className={styles.ruleContentText}>{I18n.t('smart_customer_service', {}, '智能客服')}</span>
                </>
              )
            ) : ruleType === routeType.service_routing ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>
                    {getSkillGroupName({
                      id: ruleDataChild.SkillGroupId,
                      isShunt: ruleDataChild.isShunt,
                      skillList: ruleDataChild?.skillList?.map(val => val.value),
                      isAutoShunt: ruleDataChild.isAutoShunt,
                      autoShuntSkillList: ruleDataChild.autoShuntSkillList,
                    })}
                  </span>
                  {Boolean(ruleDataChild.SupportOverflow) && (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('and_when', {}, '且当')}</span>
                      <span className={styles.ruleContentText}>
                        {getSkillGroupName({
                          id: ruleDataChild.SkillGroupId,
                          isShunt: ruleDataChild.isShunt,
                          skillList: ruleDataChild?.skillList?.map(val => val.value),
                        })}
                      </span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('the_number_of_people_in_line_is_greater_than', {}, '排队人数大于')}
                      </span>
                      <span className={styles.ruleContentText}>{ruleDataChild.OverflowThreshold}</span>
                      <span className={styles.ruleContentLabel}>
                        {I18n.t('when_people__overflow_to', {}, '人时 溢出至')}
                      </span>
                      <span className={styles.ruleContentText}>
                        {ruleDataChild.SkillGroupOverflow.map(groupId =>
                          getSkillGroupName({ id: groupId, supportOverflow: ruleDataChild.SupportOverflow })
                        ).join('、')}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
            ) : ruleType === routeType.ticket_routing ? (
              <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                <div className={styles.ruleSkillDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  {ruleDataChild?.RouteType ? (
                    <>
                      <span className={styles.ruleContentLabel}>{I18n.t('routing_timing_is', {}, '路由时机是')}</span>
                      <span className={styles.ruleContentText}>
                        {ruleDataChild?.RouteType?.map(v => routeTypeMap[v]?.())?.join(',')}
                      </span>
                      <span className={styles.ruleContentLabel}>{I18n.t('time', {}, '时')}</span>
                    </>
                  ) : (
                    <></>
                  )}
                  <span className={styles.ruleContentLabel}>{I18n.t('flow_to', {}, '流转至')}</span>
                  <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                  <span className={styles.ruleContentText}>
                    {getSkillGroupName({
                      id: ruleDataChild.SkillGroupId,
                      isShunt: ruleDataChild.isShunt,
                      skillList: ruleDataChild?.skillList?.map(val => val.value),
                      isAutoShunt: ruleDataChild.isAutoShunt,
                      autoShuntSkillList: ruleDataChild.autoShuntSkillList,
                    })}
                  </span>
                </CollapseHorizontalText>
              </div>
            ) : null}
          </div>
          {[routeType.ticket_routing].includes(ruleType) && Boolean(ruleDataChild.SupportOverflow) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>
                    {routeType.ticket_routing === ruleType ?
                      I18n.t('work_orders_to_be_assigned_within_the_group', {}, '组内待分配工单') :
                      null}
                  </span>
                  <span className={styles.ruleContentLabel}>{I18n.t('greater_than', {}, '大于')}</span>
                  <span className={styles.ruleContentText}>{ruleDataChild.queueOverflowCount}</span>
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('overflow_to', {}, '溢出至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>
                      {ruleDataChild?.skillGroupOverflowList
                        ?.map(group =>
                          getSkillGroupName({ id: group.id, supportOverflow: ruleDataChild.SupportOverflow })
                        )
                        .join('、')}
                    </span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}
          {/* Recycling */}
          {ruleType === routeType.ticket_routing && Boolean(ruleDataChild.SupportReclaim) && (
            <div className={styles.ticketRuleItem}>
              <div className={styles.ruleFilterBox}>
                <div className={styles.ruleFilterDot} />
                <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                  <span className={styles.ruleContentLabel}>{I18n.t('if', {}, '如果')}</span>
                  <span className={styles.ruleContentText}>{ruleDataChild.ReclaimConditionName}</span>
                  {ruleDataChild.ReclaimType === 'time' && (
                    <>
                      {/* < span className = {styles.rule ContentLabel} > greater than </span > */}
                      <span className={styles.ruleContentText}>
                        {`${I18n.t(
                          '{placeholder0}_hours_{placeholder2}_minutes',
                          {
                            placeholder0: ruleDataChild.ReclaimHours,
                            placeholder2: ruleDataChild.ReclaimMinutes,
                          },
                          '{placeholder0} 小时 {placeholder2} 分'
                        )}`}
                      </span>
                    </>
                  )}
                </CollapseHorizontalText>
              </div>
              <div className={styles.ruleSkillBox}>
                <div style={{ width: '100%' }} className={styles.ruleFilterBox}>
                  <div className={styles.ruleSkillDot} />
                  <CollapseHorizontalText containerStyle={{ width: 'calc(100% - 57px)' }}>
                    <span className={styles.ruleContentLabel}>{I18n.t('reclaimed_to', {}, '回捞至')}</span>
                    <img className={styles.ruleSkillIcon} src={PeopleIcon} />
                    <span className={styles.ruleContentText}>
                      {getSkillGroupName({ id: ruleDataChild.SkillGroupReclaim })}
                    </span>
                  </CollapseHorizontalText>
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };
  const renderRuleContent = (ruleData, index) => <div className={styles.ruleBox}>{ruleContent(ruleData, index)}</div>;
  return (
    <div>
      {ruleList.length ? (
        <div className={styles.title}>
          <div className={styles.titlePoint} />
          <span style={{ color: 'var(--color-text-2)' }}>
            {`${I18n.t(
              '{placeholder1}_rule_judgment_in_top_down_order',
              {
                placeholder1:
                  ruleType === routeType.accessparty_routing ?
                    I18n.t('shunt', {}, '分流') :
                    I18n.t('routing', {}, '路由'),
              },
              '从上而下的顺序依次进行{placeholder1}规则判断'
            )}`}
          </span>
        </div>
      ) : (
        <KefuPageEmptyContent
          image={NoContent}
          description=""
          title={`${I18n.t(
            'no_{placeholder1}_rule',
            {
              placeholder1:
                ruleType === routeType.accessparty_routing ?
                  I18n.t('shunt', {}, '分流') :
                  I18n.t('routing', {}, '路由'),
            },
            '暂无{placeholder1}规则'
          )}`}
        >
          {}
        </KefuPageEmptyContent>
      )}
      <DndProvider backend={HTML5Backend}>
        <div style={{ marginBottom: 40 }}>
          {ruleList.map((ruleData, index) => (
            <div key={index}>
              <div className={styles.intervalBox} />
              {renderRuleContent(ruleData, index)}
            </div>
          ))}
        </div>
      </DndProvider>
    </div>
  );
};

export default RuleList;
