import { useState, useEffect, useContext, useRef } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { customPerformanceTime } from '@/sdks/slardar';
import { isBoe } from '@/common/utils/env';
import { errorReporting } from '@/common/utils/errorReporting';
import { AOP_TICKET_ROUTE, SERVICE_ROUTE, TICKET_ROUTE } from '@/common/constants/property';

interface UseRuleListOptions<T> {
  eventKey: string;
  TabKey: string;
  filterRuleList: (ruleList: t.Rule[]) => T[]; // @todo
  ruleName?: string;
  statusList?: number[];
  skillGroupList?: string[];
}
// sourceId: string;
//   appId: number;
//   eventId: string;
//   StopStatus?: t.RuleStopStatus;

export function useRuleList<T>(options: UseRuleListOptions<T>) {
  const { eventKey, filterRuleList, Tab<PERSON>ey, ruleName, skillGroupList, statusList } = options;
  const pathname = location.pathname.split('/');
  const typeStr = pathname[pathname.length - 1];
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const [inputValue, setInputValue] = useState('');
  const [filterSearch, setFilterSearch] = useState(false);

  const [ruleList, setRuleList] = useState([]);
  const [existRulesIfNotFilter, setExistRulesIfNotFilter] = useState(false);
  const [preRuleList, setPreRuleList] = useState<T[]>([]);
  // Ref to track if the component is still mounted
  const isMounted = useRef(true);
  // Ref to track the latest request ID
  const latestRequestId = useRef(0); 
 
  const getOnlineRuleList = async () => {
    setLoading(true);
    const currentRequestId = latestRequestId.current + 1;
    latestRequestId.current = currentRequestId;
    try {
      const res = (eventKey === SERVICE_ROUTE || eventKey === TICKET_ROUTE || eventKey === AOP_TICKET_ROUTE) ? await t.demoClient.getNewRuleListV4({
        EventKey: eventKey,
        // The access party shunt has no access party concept, so pass 0
        AccessPartyId: typeStr === 'accessparty_split_flow' ? '0' : user?.accessPartyId,
        IsDraft: 0,
        ExtraInfo: {
          skillGroupList: skillGroupList.length > 0 ? JSON.stringify(skillGroupList) : undefined,
        },
        statusList,
        ruleName,
      }) : await t.demoClient.getNewRuleList({
        EventKey: eventKey,
        // The access party shunt has no access party concept, so pass 0
        AccessPartyId: typeStr === 'accessparty_split_flow' ? '0' : user?.accessPartyId,
        IsDraft: 0,
      });
      // Dealing with interface timing issues

      if (isMounted.current && currentRequestId === latestRequestId.current) {
        // First screen loading event tracking
        !isBoe && customPerformanceTime();
        // console.log(filterRuleList(res.data)?.map((val, index) => ({ ...val, ruleIndex: index })), res.data);
        setRuleList(
          filterRuleList(res.data)?.map((val, index) => ({
            ...val,
            ruleIndex: index,
          }))
        );
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useRuleList' });
    } finally {
      setLoaded(true);
      setLoading(false);
    }
  };
  // EventKey: eventId,
  const getPreReleaseRuleList = async () => {
    setLoading(true);
    const currentRequestId = latestRequestId.current + 1;
    latestRequestId.current = currentRequestId;
    try {
      const res = (eventKey === SERVICE_ROUTE || eventKey === TICKET_ROUTE || eventKey === AOP_TICKET_ROUTE) ? await t.demoClient.getNewRuleListV4({
        // The access party shunt has no access party concept, so pass 0
        AccessPartyId: typeStr === 'accessparty_split_flow' ? '0' : user?.accessPartyId,
        EventKey: eventKey,
        IsDraft: 1,
        ExtraInfo: {
          skillGroupList: skillGroupList.length > 0 ? JSON.stringify(skillGroupList) : undefined,
        },
        statusList,
        ruleName,
      }) : await t.demoClient.getNewRuleList({
        // The access party shunt has no access party concept, so pass 0
        AccessPartyId: typeStr === 'accessparty_split_flow' ? '0' : user?.accessPartyId,
        EventKey: eventKey,
        IsDraft: 1,
      });
      if (isMounted.current && currentRequestId === latestRequestId.current) {
        setPreRuleList(
          filterRuleList(res.data)?.map((val, index) => ({
            ...val,
            ruleIndex: index,
          }))
        );
        setExistRulesIfNotFilter(res.existRulesIfNotFilter === 1);
      }

    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getPreReleaseRuleList' });
    } finally {
      setLoaded(true);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      if (TabKey === 'onlineRule') {
        getOnlineRuleList();
      } else if (TabKey === 'readyToRelease') {
        getPreReleaseRuleList();
      }
    }
  }, [user?.accessPartyId, eventKey, TabKey, ruleName, statusList, skillGroupList]);
  return {
    loading,
    loaded,
    inputValue,
    setInputValue,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
    filterSearch,
    existRulesIfNotFilter
  };
}
