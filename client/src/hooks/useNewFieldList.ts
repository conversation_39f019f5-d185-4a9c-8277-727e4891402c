import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseFieldListOptions {
  appids: string[];
  eventId: string;
  when: boolean;
  accessPartyId?: string;
}
export function useNewZJFieldList(options: UseFieldListOptions) {
  const { eventId, appids, when, accessPartyId } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [fieldList, setFieldList] = useState<t.FieldCondition[]>([]);
  const getFieldList = async (appIds: string[] = []) => {
    setLoading(true);
    try {
      const res =
        (
          await t.demoClient.getNewFieldList({
            AccessPartyId: user?.accessPartyId,
            ZjOtherAccessPartyId: accessPartyId,
            EventId: eventId,
            AppIds: appIds,
          })
        ).data || [];
      setFieldList(res);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getNewFieldList' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId && when) {
      getFieldList(appids);
    }
  }, [user?.accessPartyId, JSON.stringify(appids), when]);

  return {
    loading,
    fieldList,
    getFieldList,
  };
}
export function useNewFieldList(options: UseFieldListOptions) {
  const { eventId, appids, when, accessPartyId } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [fieldList, setFieldList] = useState<t.FieldCondition[]>([]);
  const getFieldList = async (appIds: string[] = []) => {
    setLoading(true);
    if (eventId === '') {
      return;
    }
    try {
      const res =
        (
          await t.demoClient.getNewFieldList({
            AccessPartyId: accessPartyId ? accessPartyId : user?.accessPartyId,
            EventId: eventId,
            AppIds: appIds,
          })
        ).data || [];
      setFieldList(res);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getNewFieldList' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId && when) {
      getFieldList(appids);
    }
  }, [user?.accessPartyId, JSON.stringify(appids), when, eventId]);

  return {
    loading,
    fieldList,
    getFieldList,
  };
}
