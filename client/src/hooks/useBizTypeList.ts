import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { errorReporting } from '@/common/utils/errorReporting';

export function useBizTypeList() {
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const [bizTypeList, setBizTypeList] = useState<t.BizType[]>([]);
  const getBizTypeList = async () => {
    setLoading(true);
    try {
      const res =
        (
          await t.demoClient.queryBizTypesByAccessPartyID({
            AccessPartyId: user?.accessPartyId,
          })
        )?.data || [];

      setBizTypeList(res);
      return res;
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useBizTypeList' });
    } finally {
      setLoaded(true);
      setLoading(false);
    }
    return [];
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      getBizTypeList();
    }
  }, [user?.accessPartyId]);

  return {
    loading,
    loaded,
    bizTypeList,
    getBizTypeList,
  };
}
