import { useState, useEffect, useContext, useCallback } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { ApiAuthFailedCode, ApiAuthFailedMsg } from '@constants/property';
import { Toast } from '@ies/semi-ui-react';
import { errorReporting } from '@/common/utils/errorReporting';

export interface UseCardListOptions {
  appids: string[]; // @todo
  when: boolean;
}

export function useCardList(options: UseCardListOptions) {
  const { appids, when } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [cardList, setCardList] = useState<t.AppQuestionCardThrift[]>([]);
  const getCard = useCallback(async (appids = []) => {
    setLoading(true);
    try {
      const res = await t.demoClient.getCard({
        AccessPartyId: user?.accessPartyId,
        AppIds: [],
      });
      if (res.code === ApiAuthFailedCode) {
        Toast.error(ApiAuthFailedMsg);
        setCardList([]);
      } else {
        setCardList(res.data || []);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getCard' });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (user?.accessPartyId && when) {
      getCard(appids);
    }
  }, [user?.accessPartyId, when, JSON.stringify(appids)]);

  return {
    loading,
    cardList,
    getCard,
  };
}
