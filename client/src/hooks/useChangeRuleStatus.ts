import { errorReporting } from '@/common/utils/errorReporting';
import { UserContext } from '@/context/user';
import { AdminRule } from '@/http_idl/demo';
import * as t from '@http_idl/demo';
import { useContext } from 'react';

interface ChangeRuleStatusOptions {
  ruleData: AdminRule;
  newStatus: t.RuleStopStatus;
  eventId: string;
  sourceId: string;
  appId: number;
  onSuccess?: () => void;
  onFailure?: (message: string) => void;
}

// Save the new ID after rule editing
const ruleIdMap = {};

export function useChangeRuleStatus() {
  const user = useContext(UserContext);

  async function changeRuleStatus(options: ChangeRuleStatusOptions) {
    const { ruleData, eventId, sourceId, appId, onSuccess, onFailure, newStatus } = options;

    try {
      const res = await t.demoClient.updateAdminRule({
        EventId: eventId,
        AppId: appId,
        SourceId: sourceId,
        // Prioritize new IDs
        Id: ruleIdMap[ruleData.Id] || ruleData.Id,
        AccessPartyId: user?.accessPartyId,
        StopStatus: newStatus,
      });
      if (res.code === 0) {
        // Save new ID
        ruleIdMap[ruleData.Id] = res.data.Id;
        onSuccess?.();
      } else {
        onFailure?.(res.message);
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useChangeRuleStatus' });
      onFailure?.(error.message);
    }
  }

  return { changeRuleStatus };
}
