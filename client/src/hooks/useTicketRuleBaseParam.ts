/**
 * @file Get the general parameters of the work order routing Rule
 * <AUTHOR>
 */

import { useUser } from '@hooks/useUser';
import {
  ECOM_ACCESS_PARTY_ID_LIST,
  TICKET_ROUTING_APP_ID,
  TICKET_ROUTING_EVENT_ID,
  TICKET_ROUTING_SOURCE_ID,
  COMMON_TICKET_ROUTING_EVENT_ID,
  COMMON_TICKET_ROUTING_SOURCE_ID,
  COMMON_TICKET_ROUTING_APP_ID,
  RANGER_TICKET_ROUTE,
  Talent_TICKET_ROUTE,
  TICKET_ROUTE,
  AOP_TICKET_ROUTE,
  AOP_TICKET_ROUTE_ID,
  ACCESS_PARTY
} from '@constants/property';

type TicketRuleBaseParam = {
  sourceId: string;
  appId: number;
  eventId: string;
};

type newTicketRuleBaseParam = {
  EventKey: string;
};
/**
 * Is the current access party an e-commerce
 */
export function useIsEcomAccessParty(): boolean {
  const user = useUser();
  const { accessPartyId } = user;

  return ECOM_ACCESS_PARTY_ID_LIST.includes(accessPartyId);
}

export function useIsAfterSales(tabKey: string): boolean {
  const user = useUser();
  const accessPartyId = user?.accessPartyId || '';
  const isContainTicketAfterSales =
    [ACCESS_PARTY.SELLWE_SERVICE_HOSTING, ACCESS_PARTY.GLOAB_SELLING].includes(accessPartyId) &&
    tabKey === 'afterSalesRoute';
  return isContainTicketAfterSales;
}

/**
 * @return eventId appId sourceId
 */
export function useTicketRuleBaseParam(tabKey?: string): TicketRuleBaseParam {
  const isEcomTicket = useIsEcomAccessParty();
  const isContainTicketAfterSales = useIsAfterSales(tabKey);
  if (isEcomTicket) {
    return {
      sourceId: TICKET_ROUTING_SOURCE_ID,
      appId: TICKET_ROUTING_APP_ID,
      eventId: TICKET_ROUTING_EVENT_ID,
    };
  }
  if (isContainTicketAfterSales) {
    return {
      sourceId: '',
      appId: null,
      eventId: AOP_TICKET_ROUTE_ID,
    };
  }
  return {
    sourceId: COMMON_TICKET_ROUTING_SOURCE_ID,
    appId: COMMON_TICKET_ROUTING_APP_ID,
    eventId: COMMON_TICKET_ROUTING_EVENT_ID,
  };
}

export function useNewTicketRuleBaseParam(tabKey: string): newTicketRuleBaseParam {
  const isEcomTicket = useIsEcomAccessParty();
  const isContainTicketAfterSales = useIsAfterSales(tabKey);
  const user = useUser();
  const { accessPartyId } = user;
  if (isContainTicketAfterSales) {
    return {
      EventKey: AOP_TICKET_ROUTE,
    };
  }
  if (isEcomTicket) {
    return {
      EventKey: accessPartyId === '16' ? Talent_TICKET_ROUTE : RANGER_TICKET_ROUTE,
    };
  }
  return {
    EventKey: TICKET_ROUTE,
  };
}
