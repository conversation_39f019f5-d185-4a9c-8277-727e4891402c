import { useState, useEffect } from 'react';
import * as t from '@http_idl/demo';
import { useStore } from '@stores/index';
import { Global } from '@stores/global';
import { useUser } from '@hooks/useUser';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseSkillGroupsOptions {
  channelType?: t.ChannelType;
  routerKey?: string;
}

export function useSkillGroups(options: UseSkillGroupsOptions) {
  const { channelType, routerKey } = options;
  const user = useUser();
  const global = useStore('global') as Global;
  const [loading, setLoading] = useState(false);

  const [skillGroups, setSkillGroups] = useState<t.SkillGroup[]>([]);
  const [ticketSkillGroups, setTicketSkillGroups] = useState<t.SkillGroup[]>([]);
  const getSkillGroupsByAgentId = async (channelType: t.ChannelType) => {
    if (channelType === 0) {
      return;
    }
    setLoading(true);
    const defaultPartyIds = [user?.accessPartyId];
    const allowPartyIds: number[] | undefined =
      global.tcc?.skill_group_allow_access_party[user?.accessPartyId]?.allow_access_party_list;
    let ids = defaultPartyIds
      .concat(allowPartyIds?.map((id: number): string => id.toString()))
      .filter((v: string | undefined): boolean => Boolean(v));
    if (channelType === t.ChannelType.TICKET || channelType === t.ChannelType.IM) {
      ids = Array.from(new Set([...ids, '2', '3', '9', '10', '11', '35', '40', '46', '51', '52']));
    }
    try {
      const res =
        (
          await t.demoClient.getSkillGroupsByAgentId({
            AccessPartyIds: ids,
            channelType,
          })
        ).data || [];
      setTicketSkillGroups(res);
      setSkillGroups(res.filter(val => val.AccessPartyId.includes(user?.accessPartyId)));
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getSkillGroupsByAgentId' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      getSkillGroupsByAgentId(channelType);
    }
  }, [user?.accessPartyId, channelType, routerKey]);

  return {
    loading,
    skillGroups,
    ticketSkillGroups,
    getSkillGroupsByAgentId,
  };
}
