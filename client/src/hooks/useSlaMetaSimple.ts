import { useState, useEffect, useContext, createContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { errorReporting } from '@/common/utils/errorReporting';

interface useSlaMetaSimpleProps {
  sourceId: string;
}

export function useSlaMetaSimple(options: useSlaMetaSimpleProps) {
  const { sourceId } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [slaMetaSimple, setSlaMetaSimple] = useState<t.SLAAimMetaSimple[]>([]);
  const getSlaMetaSimple = async (sourceId: string) => {
    setLoading(true);
    try {
      const res =
        (
          await t.demoClient.getSLAAimMetaSimpleList({
            AccessPartyId: user?.accessPartyId,
            SourceId: sourceId,
          })
        ).data || [];
      setSlaMetaSimple(res);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useSlaMetaSimple' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId && sourceId) {
      getSlaMetaSimple(sourceId);
    }
  }, [user?.accessPartyId, sourceId]);

  return {
    loading,
    slaMetaSimple,
    getFieldList: getSlaMetaSimple,
  };
}

export const SlaMetaContext = createContext<t.SLAAimMetaSimple[]>([]);
