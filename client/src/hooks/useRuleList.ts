import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { ApiAuthFailedCode, ApiAuthFailedMsg } from '@constants/property';
import { Toast } from '@ies/semi-ui-react';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseRuleListOptions<T> {
  eventId: string;
  sourceId: string;
  appId: number;
  StopStatus?: t.RuleStopStatus;
  filterRuleList: (ruleList: t.AdminRule[]) => T[]; // @todo
}

export function useRuleList<T>(options: UseRuleListOptions<T>) {
  const { eventId, sourceId, appId, filterRuleList, StopStatus } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const [inputValue, setInputValue] = useState('');
  const [filterSearch, setFilterSearch] = useState(false);

  const [ruleList, setRuleList] = useState<T[]>([]);
  const getRuleList = async (
    { clearSearch } = {
      clearSearch: false,
    }
  ) => {
    setLoading(true);
    try {
      const res = await t.demoClient.getAdminRuleList({
        AccessPartyId: user?.accessPartyId,
        DisplayNameLike: clearSearch ? '' : inputValue,
        EventId: eventId,
        SourceId: sourceId,
        AppId: appId,
        StopStatus,
      });
      if (res.code === ApiAuthFailedCode) {
        Toast.error(ApiAuthFailedMsg);
      } else {
        setFilterSearch(clearSearch ? false : Boolean(inputValue));
        setRuleList(filterRuleList(res.data || []));
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getRuleList' });
    } finally {
      setLoading(false);
      setLoaded(true);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      getRuleList();
    }
  }, [user?.accessPartyId]);

  return {
    loading,
    loaded,
    inputValue,
    setInputValue,
    ruleList,
    getRuleList,
    filterSearch,
  };
}
