import { useState, useEffect, useRef } from 'react';

import { equal } from '@wry/equality';
import { usePrev } from './usePrev';
import { errorReporting } from '@/common/utils/errorReporting';

interface QueryOptions<R> {
  variables: R;
  lazy?: boolean;
}

// class Query<T> {
//   client: T
//   constructor(client) {
//     this.client = client
//   }

//   useQuery = <R, S>(queryApi: (req: R) => Promise<S>, options: QueryOptions<R>) => {
//     const { variables, lazy } = options;
//     const prevVariables = usePrev(variables);

//     const [data, setData] = useState<S>(null);
//     const [loading, setLoading] = useState(false);
//     const [error, setError] = useState(null);

//     const fetchDataRef = useRef<(req?: R) => Promise<S>>();

//     fetchDataRef.current =
//       fetchDataRef.current ||
//       async function fetchData(req?: R) {
//         if (!req) {
//           req = variables;
//         }
//         let data = null;
//         setLoading(true);
//         try {
//           data = await queryApi(req);
//           setData(data);
//         } catch (error) {
//           setData(null);
//           setError(error);
//         } finally {
//           setLoading(false);
//         }
//         return data;
//       };

//     useEffect(() => {
//       if (lazy) {
//         return;
//       }
//       if (!equal(variables, prevVariables)) {
//         fetchDataRef.current(variables);
//       }
//     }, [variables, prevVariables]);

//     return { data, loading, error, refetch: fetchDataRef.current, fetchData: fetchDataRef.current };
//   }
// }

export function useQuery<R, S>(queryApi: (req: R) => Promise<S>, options: QueryOptions<R>) {
  const { variables, lazy } = options;
  const prevVariables = usePrev(variables);

  const fetchIdRef = useRef(0);

  const [data, setData] = useState<S>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchDataRef = useRef<(req?: R) => Promise<S>>();

  fetchDataRef.current =
    fetchDataRef.current ||
    async function fetchData(req?: R) {
      const currentFetchId = fetchIdRef.current;
      const thisFetchId = currentFetchId + 1;
      fetchIdRef.current = thisFetchId;
      if (!req) {
        req = variables;
      }
      let data = null;
      setLoading(true);
      try {
        data = await queryApi(req);
        if (fetchIdRef.current === thisFetchId) {
          setData(data);
        }
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'useQuery' });
        if (fetchIdRef.current === thisFetchId) {
          setData(null);
          setError(error);
        }
      } finally {
        if (fetchIdRef.current === thisFetchId) {
          setLoading(false);
        }
      }
      return data;
    };

  useEffect(() => {
    if (lazy) {
      return;
    }
    if (!equal(variables, prevVariables)) {
      fetchDataRef.current(variables);
    }
  }, [variables, prevVariables]);

  return {
    data,
    loading,
    error,
    refetch: fetchDataRef.current,
    fetchData: fetchDataRef.current,
  };
}
