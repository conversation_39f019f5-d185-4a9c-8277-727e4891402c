import { errorReporting } from '@/common/utils/errorReporting';
import { getStrategyList, StrategyItem } from '@/api';
import { useEffect, useState } from 'react';

export function useStrategyList() {
  // original skill sroup policy list
  const [skillGroupStrategy, setSkillGroupStrategy] = useState<StrategyItem[]>([]);

  // original access party policy list
  const [accessPartyStrategy, setAccessPartyStrategy] = useState<StrategyItem[]>([]);

  useEffect(() => {
    // request a skillset List
    getStrategyList({
      strategyKey: ['skillGroupStrategy', 'accessPartyChangeStrategy'],
      lang: localStorage.getItem('unitedLang') || 'en'
    }).then(res => {
      setSkillGroupStrategy(res?.data?.skillGroupStrategy || []);
      setAccessPartyStrategy(res?.data?.changeAccessPartyStrategy || []);
    }).catch(error => {
      errorReporting({ error, type: 'promise_name', name: 'getStrategyList' });
    });
  }, []);

  return {
    skillGroupStrategy, accessPartyStrategy
  };
}
