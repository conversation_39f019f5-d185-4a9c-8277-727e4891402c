import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseFieldListOptions {
  appids: string[];
  eventId: string;
  when: boolean;
  // The access party has no access party concept, so pass true
  noAccessParty?: boolean;
}

export function useFieldList(options: UseFieldListOptions) {
  const { eventId, appids, when, noAccessParty } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [fieldList, setFieldList] = useState<t.FieldCondition[]>([]);
  const getFieldList = async (appIds: string[] = []) => {
    setLoading(true);
    try {
      const res =
        (
          await t.demoClient.getFieldList({
            AccessPartyId: noAccessParty ? '0' : user?.accessPartyId,
            EventId: eventId,
            lang: localStorage.getItem('unitedLang') || 'en'
          })
        ).data || [];
      setFieldList(res);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getFieldList' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId && when) {
      getFieldList(appids);
    }
  }, [user?.accessPartyId, JSON.stringify(appids), when, eventId]);

  return {
    loading,
    fieldList,
    getFieldList,
  };
}
