import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { customPerformanceTime } from '@/sdks/slardar';
import { isBoe } from '@/common/utils/env';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseRuleListOptions<T> {
  eventKey: string;
  tabKey: string;
  filterRuleList: (ruleList: t.Rule[]) => T[];
}

export function useRuleList<T>(options: UseRuleListOptions<T>) {
  const { eventKey, filterRuleList, tabKey } = options;
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const [inputValue, setInputValue] = useState('');
  const [ruleList, setRuleList] = useState([]);
  const [preRuleList, setPreRuleList] = useState<T[]>([]);
  const getOnlineRuleList = async () => {
    setLoading(true);
    try {
      const res = await t.demoClient.getNewRuleList({
        EventKey: eventKey,
        // The access party shunt has no access party concept, so pass 0
        AccessPartyId: '0',
        IsDraft: 0,
      });
      // First screen loading event tracking
      !isBoe && customPerformanceTime();
      setRuleList(
        filterRuleList(res.data)?.map((val, index) => ({
          ...val,
          ruleIndex: index,
        }))
      );
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useRuleList' });
    } finally {
      setLoaded(true);
      setLoading(false);
    }
  };
  // EventKey: eventId,
  const getPreReleaseRuleList = async () => {
    setLoading(true);
    try {
      const res = await t.demoClient.getNewRuleList({
        AccessPartyId: '0',
        EventKey: eventKey,
        IsDraft: 1,
      });
      setPreRuleList(
        filterRuleList(res.data)?.map((val, index) => ({
          ...val,
          ruleIndex: index,
        }))
      );
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getPreReleaseRuleList' });
    } finally {
      setLoaded(true);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      if (tabKey === 'onlineRule') {
        getOnlineRuleList();
      } else if (tabKey === 'readyToRelease') {
        getPreReleaseRuleList();
      }
    }
  }, [user?.accessPartyId, eventKey, tabKey]);
  return {
    loading,
    loaded,
    inputValue,
    setInputValue,
    ruleList,
    preRuleList,
    getOnlineRuleList,
    getPreReleaseRuleList,
  };
}
