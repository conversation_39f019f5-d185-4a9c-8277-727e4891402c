import { useContext } from 'react';
import { UserContext } from '@/context/user';
import { shared } from '@ies/unified_communications_sdk';
import { DOOR_AUTHORITY_MAP } from '@/common/constants/property';
export const useDoorAuthority = (): Record<string, boolean> => {
  const user = useContext(UserContext);
  const pathname = location?.pathname?.split('/');
  const typeStr = pathname?.[pathname?.length - 1];
  const hasEditAuth = shared?.judgeAccessPartyRights(
    `res.route_management/${DOOR_AUTHORITY_MAP[typeStr].EDIT}`,
    user?.accessPartyId
  );
  const hasViewAuth = shared?.judgeAccessPartyRights(
    `res.route_management/${DOOR_AUTHORITY_MAP[typeStr].VIEW}`,
    user?.accessPartyId
  );
  return {
    hasViewAuth,
    hasEditAuth,
  };
};
