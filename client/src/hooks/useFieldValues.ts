import { useContext, useEffect, useState } from 'react';
import { UserContext } from '@/context/user';
import * as t from '@http_idl/demo';
import { errorReporting } from '@/common/utils/errorReporting';

interface UseFieldValuesOptions {
  fieldId: string;
  operatorId: number;
}

export function useFieldValues(options: UseFieldValuesOptions) {
  const { fieldId, operatorId } = options;
  const [loading, setLoading] = useState(false);
  const user = useContext(UserContext);

  const [fieldValues, setFieldValues] = useState<t.FieldValueItem[]>([]);

  useEffect(() => {
    async function getFieldValues() {
      setLoading(true);
      try {
        const res =
          (
            await t.demoClient.getFieldValues({
              FieldId: fieldId,
              OperatorId: operatorId,
              AccessPartyId: user?.accessPartyId,
            })
          )?.data?.FieldValueList || [];

        setFieldValues(res);
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'getFieldValues' });
      } finally {
        setLoading(false);
      }
    }
    if (user?.accessPartyId && typeof fieldId === 'string' && typeof operatorId === 'number') {
      getFieldValues();
    }
  }, [user?.accessPartyId, fieldId, operatorId]);

  return {
    fieldValues,
    loading,
  };
}
