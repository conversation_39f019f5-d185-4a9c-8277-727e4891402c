import { useState, useEffect, useContext } from 'react';
import * as t from '@http_idl/demo';
import { UserContext } from '@context/user';
import { errorReporting } from '@/common/utils/errorReporting';

export function useBotList() {
  const user = useContext(UserContext);
  const [loading, setLoading] = useState(false);

  const [botList, setBotList] = useState<t.FieldValueItem[]>([]);
  const getBotList = async () => {
    setLoading(true);
    try {
      const res =
        (
          await t.demoClient.getFieldValues({
            FieldId: '100000',
            OperatorId: 0,
            AccessPartyId: user?.accessPartyId,
            Operator: '==',
          })
        )?.data?.FieldValueList || [];

      setBotList(res);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'useBizTypeList' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user?.accessPartyId) {
      getBotList();
    }
  }, [user?.accessPartyId]);

  return {
    loading,
    botList,
    getBotList,
  };
}
