import { ChannelType } from '@http_idl/demo';
export enum RuleType {
  Service,
  Bot,
  Ticket,
  Offline,
  QualityCheck,
}

export enum OperationType {
  None = '',
  Add = 'add',
  Delete = 'del',
  Edit = 'edit',
  Adjust = 'adjust',
  Enable = 'enable',
  Disable = 'disable',
  Clone = 'clone',
  Check = 'check',
}

export enum CardReOrderType {
  Top,
  Up,
  Down,
  Bottom,
}
export enum routeType {
  service_routing = 'service_create_rule',
  bot_routing = 'bot_create_rule',
  offline_routing = 'offline_create_rule',
  quality_check_routing = 'qualitycheck_create_rule',
  ticket_routing = 'ticket_create_rule',
  quality_check_extract_rule = 'qualitycheck_create_extract_rule',
  accessparty_routing = 'accessparty_create_rule',
  im_accessparty_routing = 'im_accessparty_routing_create_rule',
  ticket_accessparty_routing = 'ticket_accessparty_routing_create_rule'
}
export enum NewRuleType {
  Service = 'service_routing',
  Bot = 'bot_routing',
  Ticket = 'ticket_routing',
  Offline = 'offline_routing',
  Accessparty = 'accessparty_split_flow',
  QualityCheck = 'quality_check_routing',
}

export enum ChannelTypeMAP {
  service_create_rule = ChannelType.IM,
  ticket_create_rule = ChannelType.TICKET,
}


// The route type of the current page
export enum RoutePageType {
  ACCESSPARTY_SPLIT_FLOW // access party shunt
}