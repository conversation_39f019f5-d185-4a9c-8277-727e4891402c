import { AdminRule } from '@/http_idl/demo';

export interface ServiceAdminRule extends AdminRule {
  SkillGroupId: string;
  SupportOverflow: number;
  OverflowThreshold: number;
  SkillGroupOverflow: string[];
  IsOpen: number;
  BotId: string;
}

export interface BotAdminRule extends AdminRule {
  SkillGroupId: string;
  SupportOverflow: number;
  SkillGroupOverflow: string[];
  IsOpen: number;
  BotId: string;
  needRiskIdentification: number;
}

export interface TicketAdminRule extends AdminRule {
  SkillGroupId: string;
  SupportOverflow: number;
  OverflowThreshold: number;
  SkillGroupOverflow: string[];
  IsOpen: number;
  BotId: string;
  SupportReclaim: number;
  ReclaimCondition: string;
  /**
   * Retrieval condition label for card rendering
   */
  ReclaimConditionName: string;
  ReclaimHours: number;
  ReclaimMinutes: number;
  ReclaimType: 'empty' | 'time';
  SkillGroupReclaim: string[];
  RouteType: number;
  RouteTypeName: string;
}

export type ExtendedAdminRule = ServiceAdminRule | BotAdminRule | TicketAdminRule;
