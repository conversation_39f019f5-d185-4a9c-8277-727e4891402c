import { createContext } from 'react';

interface UserContextProps {
  sso: {
    avatar: string;
    unique_name: string;
    email: string;
    name: string;
    tenant_name: string;
    tenant_en_name: string;
    open_id: string;
    employee_type: string;
    en_name: string;
    user_id: string;
    username: string;
    platform: number;
  };
  agent: Agent;
  perms: Record<string, any>;
  accessPartyId: string;
}

export const UserContext = createContext<UserContextProps>({} as UserContextProps);
