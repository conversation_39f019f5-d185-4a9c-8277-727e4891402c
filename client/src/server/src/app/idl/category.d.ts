/* tslint:disable */
/* eslint-disable */

import * as base from "./base";
export { base };

// model
export interface Resource {
  Id?: number;
  Name: string;
  DockingName: string;
  // Docking person name
  AccessPartyId: number;
  // Access party idA
  SubFlag: number;
  // Whether to turn on child resources 0-not turned on 1-turned on
  EnableFlag: number;
  // Available or not
  SubList: Array<SubResource>;
}

export interface SubResource {
  Id?: number;
  Name: string;
  EnableFlag: number;
}

// Available or not
export interface AccessParty {
  Id?: number;
  Name: string;
  // Access Party Name
  SubAccessParty?: Array<AccessParty>;
  // Secondary access party
  EnableFlag: number;
}

// Available or not
export interface App {
  Id?: number;
  Name: string;
  ResourceCount?: number;
  // Number of resources used
  CategoryCount?: number;
  // Number of tags used
  DockingName: string;
  // Docking person name
  LocalUpdateTime?: string;
  // Update time
  EnableFlag?: number;
}

//
export interface Category {
  Id?: number;
  // Tag id added to 0
  ResourceId?: number;
  // Resource id
  SubResourceId?: number;
  // Subresource id
  Name: string;
  // Label name
  Path?: string;
  // Label full path
  ParentId?: number;
  // Parent tag id
  Level?: number;
  // Level
  OrderIndex: number;
  // Current Sort
  SubCategoryList?: Array<Category>;
  // Sublabel collection
  // Whether to bind the app
  IsBindApp?: boolean;
  EnableFlag?: number;
}

//
// common
export interface CommonRequest {
  TenantId: number;
  // Tenant id
  PageNo?: number;
  // Current page number
  PageSize?: number;
  // Number of pages
  AgentId: number;
  // Customer Service Id
  AgentName: string;
  // Customer Service Name
  CountryCode: string;
}

// Country code
export interface CommonResponse {
  BaseResp: base.BaseResp;
}

// request and response
export interface ResourceRequest {
  CommonRequest: CommonRequest;
  Resource: Resource;
  // Resource list
  Base?: base.Base;
}

export interface GetResourceListRequest {
  CommonRequest: CommonRequest;
  SearchKey?: string;
  // Search key
  appId?: number;
  // Search for bound resource sub-resources based on appId
  AccessPartyId: number;
  // Isolate according to the access party id
  Base?: base.Base;
}

export interface GetResourceListResponse {
  ResourceList: Array<Resource>;
  // Resource list
  Total: number;
  // Total
  BaseResp: base.BaseResp;
}

export interface GetCategoryListRequest {
  CommonRequest: CommonRequest;
  ResourceId: number;
  // Resource id
  SubResourceId: number;
  // Subresource ID
  Base?: base.Base;
}

export interface GetCategoryListByIdsRequest {
  CommonRequest: CommonRequest;
  CategoryIds: Array<number>;
  // Tag ID Collection
  Base?: base.Base;
}

export interface GetCategoryListResponse {
  CategoryList: Array<Category>;
  BaseResp: base.BaseResp;
}

export interface AddOrUpdateCategoryRequest {
  CommonRequest: CommonRequest;
  ResourceId: number;
  // Resource id
  SubResourceId: number;
  // Subresource ID
  CategoryList: Array<Category>;
  // Modified/new tag collection, new tags may have subtags
  DeleteIdList: Array<number>;
  Base?: base.Base;
}

export interface GetAppListRequest {
  CommonRequest: CommonRequest;
  Name: string;
  Base?: base.Base;
}

export interface GetAppListResponse {
  AppList: Array<App>;
  // App List
  Total: number;
  // Total
  BaseResp: base.BaseResp;
}

export interface AppRequest {
  CommonRequest: CommonRequest;
  App: App;
  Base?: base.Base;
}

export interface GetCategoryAppListRequest {
  CommonRequest: CommonRequest;
  ResourceId: number;
  // Resource id
  SubResourceId: number;
  // Subresource ID
  AppId: number;
  Base?: base.Base;
}

export interface BindCategoryToAppRequest {
  CommonRequest: CommonRequest;
  ResourceId: number;
  // Resource id
  SubResourceId: number;
  // Subresource ID
  AppId: number;
  NewBindCategoryIds: Array<number>;
  // Add binding tag id
  UnBindCategoryIds: Array<number>;
  // Unbind tag id
  Base?: base.Base;
}

//
// struct BindTagToCategoryRequest{
// 1: required CommonRequest CommonRequest,
// 2: string ExtraCode,//tag three-level id
// 255: base.BaseResp BaseResp,
// }
export interface GetAccessPartyListRequest {
  CommonRequest: CommonRequest;
  Base?: base.Base;
}

export interface GetAccessPartyListRespone {
  AccessPartyList: Array<AccessParty>;
  BaseResp: base.BaseResp;
}
