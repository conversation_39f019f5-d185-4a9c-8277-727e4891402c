/* tslint:disable */
/* eslint-disable */

import * as base from "./base";
export { base };

// 组件类型
export enum FieldOptionType {
  // 单选
  SINGLE_CHOOSE = 1,
  // 多选
  MULTI_CHOOSE = 2,
  // 混合(多选+输入框)
  MIX = 3,
  // 时间控件
  DATE = 4,
  // 输入框
  INPUT = 5,
  // 批量输入
  BATCH_INPUT = 6,
  // 树
  TREE = 7,
  // 级联
  CASCADER = 8,
  // 多行文本
  TEXT = 9,
  // 数字
  INT = 10,
  // 常量
  CONSTANT = 11,
  // 富文本
  RICH_TEXT = 12,
  // 多语言文本
  MULTI_LANG = 13,
  // 时间点类型
  DATE_TIME_POINT = 14,
  // 条件级联下拉列表
  CONDITION_CASCADER_LIST = 16,
  // 浮点数输入框
  INPUT_FLOAT = 17,
  // 树状结构-只选择当前节点不选择子节点
  TREE_TO_LIST = 701,
}

// 操作渠道
export enum ChannelType {
  // IM
  IM = 1,
  // 工单
  TICKET = 2,
  // 电话
  PHONE = 3,
  // 管理员
  ADMIN = 4,
  // 电商工单
  ECOM_TICKET = 5,
  // BUZZ工单
  BUZZ = 6,
  // FEEDBACK
  FEEDBACK = 7,
  // 质检
  QUALITY_CHECK = 8,
  // IM离线留言会话
  IM_OFFLINE_SESSION = 9,
  // 触达外呼
  ACCESS_CALL = 10,
  // 预约回呼
  CALLBACK = 11,
  // 售后
  AFTER_SALE = 12,
}

// 卡片操作类型
export enum HandleType {
  OPEN = 1,
  CLOSE = 2,
  DELETE = 3,
}

// 规则任务状态
export enum RuleTaskStatus {
  // 运行中
  HAS_RUNNING = 1,
  NO_RUNNING = 0,
}

// 无运行中
// 规则禁用状态
export enum RuleStopStatus {
  // 禁用
  DISABLED = 1,
  USING = 0,
}

// 启用
// 规则状态
export enum EntityStatus {
  // 可用
  ENABLE = 1,
  UNABLE = 0,
}

// 条件操作类型
export enum FilterOperateType {
  // 求并集
  UNION = 0,
  INTERSECTION = 1,
}

// 新增部分
// 规则状态
export enum RuleStatus {
  // 启用
  ENABLE = 1,
  // 禁用
  UNABLE = 0,
  // 草稿
  DRAFT = 2,
  DELETE = 3,
}

// 优先级
// 规则生效环境
export enum RuleEnv {
  // PPE环境
  PPE = 0,
  PROD = 1,
}

// 线上环境
// 条件组合操作类型
export enum OpGroup {
  // 求交集
  AND = 1,
  // 求并集
  OR = 2,
  NOT = 3,
}

export enum ConditionUpdateType {
  // 选项更新
  OptionsUpdate = 1,
  // 条件删除
  DELETE = 2,
  ADD = 3,
}

// 条件新增
export enum OperateRuleItemType {
  // 更新状态
  STAUTS = 1,
  // 更新名字
  NAME = 2,
  // 更新优先级
  PRIORITY = 3,
  // 更新条件
  CONDITION = 4,
  // 更新条件选项
  CONDITION_OPTIONS = 5,
  // 更新条件间关系
  CONDITION_RELATION = 6,
  // 更新条件组间关系
  CONDITION_GROUP_RELATION = 7,
  // 更新规则返回值
  RETURN_VALUE = 8,
  // 路由时机变更
  ROUTE_TYPE = 9,
  OVERFLOW = 10,
}

// 溢出设置变更
export enum RuleOperationType {
  // 创建规则并启用
  CREATE_ENABLE = 1,
  // 创建规则并禁用
  CREATE_DISABLE = 2,
  // 规则状态变更, 启用/禁用/发布
  STATUS_CHANGE = 3,
  // 规则优先级变更
  PRIORITY_CHANGE = 4,
  UPDATE = 5,
}

// 技能组id
export enum RuleType {
  // 跟单规则
  DOCUMENTARY_RULE = 1,
  // 通用规则
  GENERAL_RULE = 2,
  // 下送
  SEND_DOWN = 3,
  // 升级
  UPGRADE = 4,
  // 完结
  FINISH = 5,
}

export interface QueryBizTypesByAccessPartyIDRequest {
  AccessPartyId: string;
}

export interface BizType {
  ID: number;
  // ID
  AppID: number;
  // 客服平台AppID
  AppName: string;
  // 客服平台App Name
  Channel: number;
  // 客服平台Channel
  ChannelName: string;
  // 客服平台 Channel Name
  HostAppID: string;
  // 宿主AppID
  HostAppName: string;
  // 宿主App name
  AppBaseID: string;
  // 标签应用ID
  ResourceID: string;
  // 资源ID
  SubResourceID: string;
  // 子资源ID
  AccessPartyID: string;
  // 接入方ID
  Scene?: string;
  // 场景
  EntranceId?: string;
  // 入口ID
  EntranceName?: string;
}

// 入口名
export interface QueryBizTypesByAccessPartyIDResponse {
  code: number;
  data: Array<BizType>;
  message: string;
}

export interface GetSkillGroupsByAgentIdRequest {
  AccessPartyIds: Array<string>;
  channelType?: ChannelType;
}

export interface SkillGroup {
  ID: string;
  TenantId: string;
  AccessPartyId: Array<string>;
  Name: string;
  ChannelType: ChannelType;
  MaxTaskNum: number;
  QuestionCategoryIds: Array<string>;
  CategoryIds: Array<string>;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
}

export interface GetSkillGroupsByAgentIdResponse {
  code: number;
  data: Array<SkillGroup>;
  message: string;
}

export interface GetCardRequest {
  AccessPartyId: string;
  AppIds: Array<string>;
}

// 应用ID列表
export interface CardQuestionThrift {
  Id: string;
  // 问题ID
  CardId: string;
  // 卡片ID
  QuestionName: string;
  // 问题内容
  SkillGroupId: string;
}

// 技能组ID
export interface AppQuestionCardThrift {
  Id: string;
  // 卡片ID
  TenantId: string;
  // 租户ID
  AccessPartyId: string;
  // 接入方ID
  AppId: string;
  // 应用ID
  CardName: string;
  // 卡片名称，用于title展示
  IsOpen: number;
  // 是否启用
  CardQuestions: Array<CardQuestionThrift>;
}

// 问题列表，list顺序代表C端展示顺序
export interface GetCardResponse {
  code: number;
  // 问题卡片列表
  data: Array<AppQuestionCardThrift>;
  message: string;
}

export interface AddCardQuestion {
  QuestionName: string;
  // 问题内容
  SkillGroupId: string;
}

// 技能组ID
export interface AddAppQuestionCard {
  AppId: string;
  // 应用ID
  CardName: string;
  // 卡片名称，用于title展示
  AddCardQuestions: Array<AddCardQuestion>;
}

// 问题列表，list顺序代表C端展示顺序
export interface CreateCardRequest {
  AddAppQuestionCard: AddAppQuestionCard;
  // 不带主键ID的问题卡片对象
  AccessPartyId: string;
}

export interface CreateCardResponse {
  code: number;
  message: string;
}

export interface UpdateCardQuestion {
  Id?: string;
  // 问题ID
  QuestionName: string;
  // 问题内容
  SkillGroupId: string;
}

// 技能组ID
export interface UpdateAppQuestionCardThrift {
  Id: string;
  // 卡片ID
  TenantId: string;
  // 租户ID
  AccessPartyId: string;
  // 接入方ID
  AppId: string;
  // 应用ID
  CardName: string;
  // 卡片名称，用于title展示
  UpdateCardQuestions: Array<UpdateCardQuestion>;
  // 问题列表，list顺序代表C端展示顺序，更新跟创建结构相同，用于兼容同时又问题增删的情况
  CardDisplayName?: string;
}

// 卡片展示名称 新版必传 向下兼容
export interface UpdateCardRequest {
  UpdateAppQuestionCardThrift: UpdateAppQuestionCardThrift;
  // 带有主键ID的问题卡片对象
  AccessPartyId: string;
}

export interface UpdateCardResponse {
  code: number;
  message: string;
}

export interface HandleCardRequest {
  CardId: string;
  // 卡片ID
  HandleType: HandleType;
  // 操作类型
  AccessPartyId: string;
}

export interface HandleCardResponse {
  code: number;
  message: string;
}

export interface SubResource {
  Id?: string;
  Name: string;
  EnableFlag: number;
}

// 是否可用
export interface Resource {
  Id?: string;
  Name: string;
  DockingName: string;
  // 对接人名称
  AccessPartyId: string;
  // 接入方id
  SubFlag: number;
  // 是否开启子资源 0-未开启 1-开启
  EnableFlag: number;
  // 是否可用
  SubList: Array<SubResource>;
}

export interface GetResourceListRequest {
  AccessPartyId: string;
}

export interface GetResourceListResponseData {
  ResourceList: Array<Resource>;
}

export interface GetResourceListResponse {
  code: number;
  data: GetResourceListResponseData;
  message: string;
}

export interface Category {
  Id?: string;
  // 标签id 新增为0
  ResourceId?: string;
  // 资源id
  SubResourceId?: string;
  // 子资源id
  Name: string;
  // 标签名称
  Path?: string;
  // 标签全路径
  ParentId?: string;
  // 父标签id
  Level?: number;
  // 层级
  OrderIndex: number;
  // 当前排序
  SubCategoryList?: Array<Category>;
  // 子标签集合
  // 是否绑定app
  IsBindApp?: boolean;
  EnableFlag?: number;
}

//
export interface GetCategoryListRequest {
  ResourceId: string;
  // 资源id
  SubResourceId: string;
}

// 子资源ID
export interface GetCategoryListResponseData {
  CategoryList: Array<Category>;
}

export interface GetCategoryListResponse {
  code: number;
  data: GetCategoryListResponseData;
  message: string;
}

export interface FieldValueItem {
  name: string;
  value: number;
}

export interface GetFieldValuesRequest {
  FieldId: string;
  OperatorId: number;
  AccessPartyId: string;
  AppIds?: Array<string>;
  Operator?: string;
}

export interface GetFieldValuesResponseData {
  FieldValueType: number;
  FieldValueList: Array<FieldValueItem>;
  FieldValues: string;
}

export interface GetFieldValuesResponse {
  code: number;
  data: GetFieldValuesResponseData;
  message: string;
}

export interface FieldCondition {
  FieldId: string;
  FieldDisplayName: string;
  // 字段的前端展示名称
  FieldMapName: string;
  // 字段的数据库存储名称
  OperatorIds: Array<number>;
  Fieldvalues: { [key: string]: Array<GetFieldValuesResponseData> };
}

export interface GetFieldListRequest {
  AccessPartyId: string;
  EventId: string;
  AppIds?: Array<string>;
}

export interface GetFieldListResponse {
  code: number;
  data: Array<FieldCondition>;
  message: string;
}

// 不可用
// 单个字段条件
export interface FilterUnit {
  FieldId?: string;
  FieldMapName: string;
  // todo 自定义字段前缀
  OperatorId: number;
  // todo 前端映射ID
  FieldValue: string;
}

// 求交集
// 条件
export interface Filter {
  OperateType: FilterOperateType;
  FilterUnitList: Array<FilterUnit>;
}

export interface AdminRule {
  Id: string;
  // ID
  DisplayName: string;
  // 名称
  Priority: number;
  // 优先级
  Filter: Filter;
  // 条件
  Value: string;
  // 动作信息 json, list查询时填充''，get查询时填充
  StopStatus: RuleStopStatus;
  // 禁用状态
  Status: EntityStatus;
  // 状态
  AppId: number;
  // 应用类型，如0：触发器、1：SLA、2：后台
  TenantId: string;
  // 租户ID
  AccessPartyId: string;
  // 接入方ID
  SourceId?: string;
  // 业务标识，区分工单、电商工单
  CreatedAt: string;
  // 创建时间
  UpdatedAt: string;
  // 更新时间
  Extra: string;
  // 附加信息
  CreatorAgentId: string;
  // 创建人ID
  UpdaterAgentId: string;
  // 更新人ID
  TaskStatus?: RuleTaskStatus;
  // 规则任务状态 get查询时填充
  UpdaterAgentName?: string;
}

// 规则任务状态 get查询时填充
export interface GetAdminRuleListRequest {
  AccessPartyId: string;
  DisplayNameLike?: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  StopStatus?: RuleStopStatus;
}

export interface GetAdminRuleListResponse {
  code: number;
  data: Array<AdminRule>;
  message: string;
}

export interface CreateAdminRuleRequest {
  AccessPartyId: string;
  DisplayName: string;
  Priority: number;
  Filter: Filter;
  Value: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  CreateCardRequest?: CreateCardRequest;
  Disable?: boolean;
}

export interface CreateAdminRuleResponse {
  code: number;
  message: string;
}

export interface UpdateAdminRuleRequest {
  AccessPartyId: string;
  DisplayName?: string;
  Priority?: number;
  Filter?: Filter;
  Value?: string;
  Id: string;
  EventId: string;
  SourceId: string;
  AppId: number;
  StopStatus?: RuleStopStatus;
  // 卡片的修改请求，未修改时可不传
  UpdateCardRequest?: UpdateCardRequest;
}

export interface UpdateAdminRuleResponse {
  code: number;
  message: string;
  data?: AdminRule;
}

export interface DeleteAdminRuleRequest {
  AccessPartyId: string;
  Id: string;
  SourceId: string;
  AppId: number;
}

export interface DeleteAdminRuleResponse {
  code: number;
  message: string;
}

export interface AdminRuleSimple {
  Id: string;
  Priority: number;
}

// 优先级
export interface BatchUpdateAdminRuleRequest {
  AccessPartyId: string;
  AdminRules: Array<AdminRuleSimple>;
  EventId: string;
  SourceId: string;
  AppId: number;
}

export interface BatchUpdateAdminRuleResponse {
  code: number;
  message: string;
}

export interface GetSLAAimMetaSimpleListRequest {
  // 接入方ID
  AccessPartyId?: string;
  // 业务标识，区分工单、电商工单
  SourceId?: string;
}

export interface SLAAimMetaSimple {
  Id: string;
  // ID
  Name: string;
  // 名称 首响、完结
  Extra: string;
}

// 附加信息
export interface GetSLAAimMetaSimpleListResponse {
  code: number;
  message: string;
  data: Array<SLAAimMetaSimple>;
}

// 已删除
// 规则优先级
export interface RulePriority {
  Id: string;
  // ID
  Priority: number;
}

// 求反计算
// 运算型参数表达式
export interface MathExpr {
  opMath: string;
  // 数学运算符
  Lhs: Expr;
  Rhs: Expr;
}

// 方法型参数表达式
export interface FuncExpr {
  FuncName: string;
  // 方法名
  ParamExprMap?: { [key: string]: Expr };
}

// 参数
// 特征型参数表达式
export interface FeatureExpr {
  FeatureName: string;
  // 特征名，为字符串，如"ticket.status"
  ParamExprMap?: { [key: string]: Expr };
}

// 参数map，key为字符串，如"abc"
// 参数表达式
export interface Expr {
  ExprList?: Array<Expr>;
  // 元素可以为任意一种Expr
  SubstringList?: Array<Expr>;
  // 元素可以为任意一种Expr，用于拼成字符串
  MathExpr?: MathExpr;
  FuncExpr?: FuncExpr;
  FeatureExpr?: FeatureExpr;
  // 特征型参数表达式，如"ticket.assignee_agent.status()"
  VarExpr?: string;
  // 变量型参数表达式，如"$id"
  ConstantList?: Array<string>;
  // 元素可以为字符串，数字，布尔值中任意一种常量
  Constant?: string;
}

// 常量型参数表达式：字符串，如\"abc\"，\"300\"；数字，如100.1；布尔值，如true
// 条件表达式
export interface ConditionExpr {
  OpCheck: string;
  Lhs: Expr;
  // 运算左值
  Rhs?: Expr;
}

// 运算右值
// 条件组合
export interface ConditionGroupExpr {
  OpGroup: string;
  Conditions?: Array<ConditionExpr>;
  ConditionGroups?: Array<ConditionGroupExpr>;
}

// conditions和conditionGroups有且只有一个非空
// 动作表达式
export interface ActionExpr {
  ActionName: string;
  // 动作名，为字符串，如"abc"
  ParamExprMap?: { [key: string]: Expr };
}

// 参数map，key为字符串，如"abc"
// 动作组合
export interface ActionGroupExpr {
  Sequential: boolean;
  ContinueOnFail: boolean;
  Actions: Array<ActionExpr>;
}

// 延时步骤
export interface DelayStepExpr {
  ActionGroup: ActionGroupExpr;
  // 即时动作
  FilterAim: string;
  // 即时条件名，为字符串，如"abc"
  DelayTime: Expr;
}

// 延时参数表达式
// 规则返回
export interface AimExpr {
  DelaySteps?: Array<DelayStepExpr>;
  // 延时步骤
  ActionGroup?: ActionGroupExpr;
  // 即时动作
  ReturnValue?: Expr;
}

// 路由返回 delaySteps/actionGroup/returnValue有且只有一个非空
// 规则元数据
export interface Rule {
  Id: string;
  // ID
  DisplayName: string;
  // 名称
  Priority: number;
  // 优先级
  Expression?: ConditionGroupExpr;
  // 规则条件部分
  ActionInfo?: AimExpr;
  // 动作部分
  RuleGroupId: string;
  // 规则组id
  Status: RuleStatus;
  // 规则状态
  Description?: string;
  // 规则描述
  CreatedAt: string;
  // 创建时间
  UpdatedAt: string;
  // 更新时间
  CreatorAgentId: string;
  // 创建人ID
  UpdaterAgentId: string;
  // 更新人ID
  DraftEditType: number;
  // 草稿编辑类型 1-新增 2-编辑 0-未修改
  // 更新人名字
  UpdaterAgentName: string;
}

// 创建规则
export interface CreateRuleV2Request {
  EventKey: string;
  // 事件key
  RuleGroupId?: string;
  // 规则组ID
  DisplayName: string;
  // 规则名称
  Priority: number;
  // 规则优先级
  Expression: ConditionGroupExpr;
  // 条件-DSL
  ActionInfo: AimExpr;
  // 动作部分
  Enable?: boolean;
  // 是否启用规则，不传默认为禁用
  Extra?: string;
  // 扩展字段，前端可用于复现规则
  Description: string;
  // 规则描述
  RuleEnv?: RuleEnv;
  // 规则生效环境，不传默认为线上
  AccessPartyId: string;
  Version: string;
  // 接口版本 UI传v1
  ExtraInfo?: { [key: string]: string };
}

// 额外信息
export interface CreateRuleV2Response {
  Rule?: Rule;
  code: number;
  message: string;
}

// 更新规则
export interface UpdateRuleRequest {
  Id: string;
  // 规则ID
  DisplayName?: string;
  // 规则名称
  Expression?: ConditionGroupExpr;
  // 条件-DSL
  ActionInfo?: AimExpr;
  // 路由的结果部分，用于对结果的特殊处理，比如绑定技能组
  Extra?: string;
  // 扩展字段，前端可用于复现规则
  Description?: string;
  // 规则描述
  Version: string;
  // 接口版本 UI传v1
  // 门神权限点 code
  PermCode?: string;
  Priority?: number;
  // 规则优先级
  ExtraInfo?: { [key: string]: string };
}

// 额外字段
export interface UpdateRuleResponse {
  Rule?: Rule;
  code: number;
  message: string;
}

// 启用/禁用/删除规则
export interface UpdateRuleStatusV2Request {
  Ids: Array<string>;
  // 规则ID的list
  RuleStatus: RuleStatus;
  // 建RuleStatus定义
  Version: string;
  // 接口版本 UI传v1
  // 门神权限点 code
  PermCode?: string;
  Draft?: boolean;
  // 是否为草稿规则
  ruleGroupId?: string;
  operateGroupAllRules?: number;
}

// 1 - 需要处理，0 -仅处理部分
export interface UpdateRuleStatusV2Response {
  code: number;
  message: string;
}

// 调整规则优先级
export interface UpdateRulePriorityV2Request {
  Rules: Array<RulePriority>;
  // 所有规则的优先级信息
  Version: string;
}

// 接口版本 UI传v1
export interface UpdateRulePriorityV2Response {
  RuleGroupId?: string;
  // 调整优先级会生成新的规则组版本(不包括草稿)
  newRuleIds?: { [key: string]: string };
  // 调整优先级会生成新的ruleId(不包括草稿)
  code: number;
  message: string;
}

// 根据id获取规则
export interface GetRuleByIdRequest {
  Id: string;
}

export interface GetRuleByIdResponse {
  Rule?: Rule;
  code: number;
  message: string;
}

// 获取规则列表
export interface GetRuleListV2Request {
  EventKey: string;
  // 事件Key
  AccessPartyId: string;
  // 接入方ID
  RuleGroupId?: string;
  // 规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
  IsDraft?: number;
  // 是否获取草稿箱里的规则1-是
  ExtraInfo?: { [key: string]: string };
  // 额外查询字段
  Page?: number;
  PageSize?: number;
}

export interface GetRuleListV2Response {
  data?: Array<Rule>;
  // 规则
  code: number;
  message: string;
  existRulesIfNotFilter?: number;
}

export interface PublishRuleGroupV2Request {
  RuleGroupId: string;
  // 规则组id
  // 权限点
  PermCode?: string;
  RuleIds?: Array<string>;
  // 规则ID集合
  eventKey?: string;
}

export interface PublishRuleGroupV2Response {
  RuleGroupId?: string;
  // 新的规则组id
  newRuleIds?: { [key: string]: string };
  // 发布会生成新的ruleId
  code: number;
  message: string;
}

export interface CopyRuleGroupV2Request {
  RuleGroupId: string;
}

// 规则组id
// 2: required string UpdaterAgentId, // 更新人
export interface CopyRuleGroupV2Response {
  code: number;
  message: string;
}

export interface GetRuleListV3Response {
  code: number;
}

export interface GetRuleListV3Request {
  name: string;
}

// 获取规则列表
export interface GetRuleListV4Request {
  EventKey: string;
  // 事件Key
  AccessPartyId: string;
  // 接入方ID
  RuleGroupId?: string;
  // 规则组ID - 不传以事件ID+接入方ID匹配规则组id；传了以这个为准
  IsDraft?: number;
  // 是否获取草稿箱里的规则1-是
  ExtraInfo?: { [key: string]: string };
  // 额外查询字段
  Page?: number;
  PageSize?: number;
  statusList?: Array<number>;
  // 状态，1-启动，0-禁用
  ruleName?: string;
}

// 规则名称
export interface GetRuleListV4Response {
  data?: Array<Rule>;
  // 规则
  code: number;
  message: string;
  existRulesIfNotFilter?: number;
}

export interface Agent {
  ID: string;
  TenantId: string;
  WorkType: number;
  UserName: string;
  NickName: string;
  UserId: string;
  UUID: string;
  Email: string;
  Mobile: string;
  CompanyId: string;
  ChannelTypes: Array<ChannelType>;
  Status: number;
  CreatedBy: string;
  CreatedAt: string;
  UpdatedBy: string;
  UpdatedAt: string;
  OperatorName: string;
  DepartmentId: string;
  ImMaxTaskNum: number;
  PhoneSeatNo?: string;
  CountryRegion?: string;
  // 国家地区
  FeedbackMaxTaskNum?: number;
  NgccServiceLine?: string;
  Extra: { [key: string]: string };
}

// 目前extra里包含的字段 c_n_agent_appid(IM appId),
// c_s_ecom_ticket_role(电商工单系统角色),
// c_n_ecom_ticket_is_super(电商工单是否超级管理员)
// c_s_ecom_ticket_kind(电商工单业务类型)
export interface CheckSkillGroupAutoAssign {
  autoAssign?: boolean;
  unautoAssignAgents?: Array<Agent>;
  skillGroupAgentsCount?: string;
  skillGroupId?: string;
}

export interface CheckSkillGroupAutoAssignRequest {
  skillGroupIdList: Array<string>;
}

export interface CheckSkillGroupAutoAssignResponse {
  // 1: optional bool autoAssign,
  // 2: optional list<Agent> unautoAssignAgents,
  // 3: optional i64 skillGroupAgentsCount,
  checkSkillGroupAutoAssign?: Array<CheckSkillGroupAutoAssign>;
  code: number;
  message: string;
}

// 规则内容变更
export interface ConditionGroupChange {
  ConditionGroupOrder: number;
  // 条件组序号
  ConditionChangeList?: Array<ConditionChange>;
  // 条件变更列表
  Relation?: string;
  // 条件关系变更后结果
  ConditionNameList?: Array<string>;
}

// 条件关系变更对应的条件列表
export interface ConditionChange {
  ConditionName: string;
  // 条件名字
  ConditionUpdateType: ConditionUpdateType;
  // 条件更新类型
  AddOptions?: Array<string>;
  // 条件选项新增
  DeleteOptions?: Array<string>;
}

// 条件选项删除
export interface OperateRuleItemLog {
  OperateItemType: OperateRuleItemType;
  BeforeValue?: string;
  AfterValue?: string;
  // 字符串或json结构
  ConditionGroupChangeList?: Array<ConditionGroupChange>;
}

export interface RuleOperationLog {
  Id: string;
  OperationType: RuleOperationType;
  OperateRuleItemLogs?: Array<OperateRuleItemLog>;
  OperatorAgentId: string;
  CreatedAt: string;
}

export interface GetRuleOperationLogsRequest {
  ruleId: string;
  eventId: string;
  accessPartyId: string;
  page: number;
  pageSize: number;
}

export interface BaseResp {
  StatusMessage: string;
  StatusCode: number;
  Extra?: { [key: string]: string };
}

export interface retutnValue {
  value?: string;
  percent?: number;
  skillGroupName?: string;
}

export interface changeItem {
  groupIndex?: string;
  conditionName?: string;
  updateValueType?: string;
  options?: string;
  updateConditionsType?: string;
  OperateItemType?: number;
}

export interface logList {
  status?: string;
  retutnValue?: Array<retutnValue>;
  priority?: string;
  name?: string;
  groupsRelationChange?: string;
  OperateItemType?: number;
  ConditionRelationChange?: Array<changeItem>;
  ValueChange?: Array<changeItem>;
  ConditionsChange?: Array<changeItem>;
}

export interface RuleOperationLog {
  agentId?: string;
  agentName?: string;
  title?: string;
  updateTime?: string;
  logList?: Array<logList>;
}

export interface GetRuleOperationLogsResponse {
  RuleOperationLogList?: Array<{ [key: string]: string }>;
  oldres?: Array<{ [key: string]: string }>;
  totalCount?: string;
  BaseResp: BaseResp;
}

export interface GetSkillGroupsByAccessPartiesResponse {
  SkillGroups: Array<SkillGroup>;
  BaseResp?: BaseResp;
}

export interface GetSkillGroupsByAccessPartiesRequest {
  TenantId: string;
  AccessPartyIds: Array<string>;
  ChannelType?: ChannelType;
}

// 问题匪类卡片rule
export interface ClientRule {
  Id: string;
  // ID
  DisplayName: string;
  // 名称
  Priority: number;
  // 优先级
  Expression?: ConditionGroupExpr;
  // 规则条件部分
  ActionInfo?: AimExpr;
  // 动作部分
  RuleGroupId: string;
  // 规则组id
  Status: RuleStatus;
  // 规则状态
  Description?: string;
  // 规则描述
  CreatedAt: string;
  // 创建时间
  UpdatedAt: string;
  // 更新时间
  CreatorAgentId: string;
  // 创建人ID
  UpdaterAgentId: string;
  // 更新人ID
  DraftEditType: number;
  // 草稿编辑类型 1-新增 2-编辑 0-未修改
  // 更新人名字
  UpdaterAgentName: string;
  cardInfo?: AppQuestionCardThrift;
}

export interface GetRuleGroupListByEventKeyRequest {
  EventKey: string;
  RuleGroupDisplayName?: string;
  // 名称
  RuleGroupStatus?: Array<RuleStatus>;
  // 状态
  CreatorAgentId?: Array<string>;
  // 创建人
  CreatorTime?: Array<string>;
  // 创建时间
  UpdaterAgentId?: Array<string>;
  // 更新人
  UpdaterTime?: Array<string>;
  // 更新时间
  AccessPartId: string;
  SkillGroupId?: string;
  Page: number;
  PageSize: number;
}

export interface GetRuleGroupListByEventKeyResponse {
  RuleGroupList?: Array<RuleGroupDetail>;
  // 规则组的list
  Count?: number;
  // 数量
  code: number;
  message: string;
}

// 规则组详情
export interface RuleGroupDetail {
  RuleGroupId: string;
  // 规则组id - 跳转详情页使用
  OriginId: string;
  // 原始id
  RuleGroupDisplayName: string;
  // 名称
  RuleGroupStatus: RuleStatus;
  // 状态
  Version: number;
  // 版本号
  RuleList: Array<AntlrRule>;
  // 规则集合
  DraftRuleGroupId?: string;
  // 草稿版本id - 跳转编辑页使用
  CreatorAgentId?: string;
  // 创建人
  CreatorTime?: string;
  // 创建时间
  UpdaterAgentId?: string;
  // 更新人
  UpdaterTime?: string;
  // 更新时间
  HaveReleaseVersion?: boolean;
  // 是否有线上生效版本
  ExtraInfo?: { [key: string]: string };
}

// 规则类型
// 规则
export interface AntlrRule {
  DisplayName: string;
  // 规则名称
  Priority: number;
  // 规则优先级
  Expression: ConditionGroupExpr;
  // 条件-DSL
  ActionInfo: AimExpr;
  // 动作部分
  CreatorAgentId?: string;
  // 创建人id
  Description?: string;
  // 规则描述
  // 规则ID
  Id?: string;
  // 规则原始ID
  OriginId?: string;
  // 用于前端复现规则
  Extra?: string;
}

export interface GetSkillGroupsByTypeResquest {
  TenantId: string;
  ChannelType?: ChannelType;
  PageNo: number;
  PageSize: number;
  AccessPartyId?: string;
  ChannelTypes?: Array<ChannelType>;
  SkillGroupLevel?: number;
  SkillGroupName?: string;
  OnlySimpleData?: boolean;
  // 是否只需要返回技能组的简单信息，不包含问题标签等关联数据
  BizLineEnum?: string;
  // 业务线枚举
  ImOfflineSessionEnable?: boolean;
  // 是否打开IM离线会话开关
  AccessPartyIds?: Array<string>;
  OnlyId?: boolean;
  // 是否只返回技能组的ID以及技能组名称,只支持查询条件包含channelType和接入方ID的简单批量查询，如果需要按照SkillGroupLevel等字段进行筛选，请使用OnlySimpleData
  HasCode?: boolean;
}

// 是否只返回有技能Code的技能组
export interface GetSkillGroupsByTypeResponse {
  SkillGroups: Array<SkillGroup>;
  TotalSize: number;
  code: number;
  message: string;
}

export interface GetExtraInfoRequestV2 {
  Scenes: string;
  EventKey: string;
  ExtraKeys?: Array<string>;
  AccessPartId?: string;
}

export interface GetExtraInfoResponseV2 {
  RuleGroupRelations?: Array<RuleGroupRelationStruct>;
  code: number;
  message: string;
}

export interface RuleGroupRelationStruct {
  RuleGroupId: string;
  extraInfos: { [key: string]: string };
  OriginId: string;
}

export interface GetFieldListRequest {
  AccessPartyId: string;
  EventId: string;
  AppIds?: Array<string>;
  ZjOtherAccessPartyId?: string;
}

export interface GetFieldListResponse {
  code: number;
  data: Array<FieldCondition>;
  message: string;
}

export interface BatchCreateRuleGroupResponse {
  RuleGroupIds?: Array<string>;
  code: number;
  message: string;
}

export interface BatchCreateRuleGroupRequest {
  RuleGroups: Array<CreateRuleGroupRequest>;
}

// 创建规则组
export interface CreateRuleGroupRequest {
  EventKey: string;
  // 事件key
  GroupDisplayName: string;
  // 规则组名称
  CreatorAgentId: string;
  // 创建人id
  Product: string;
  // 产品
  RuleList: Array<AntlrRule>;
  // 规则集合
  Enable?: boolean;
  // 是否启用规则，不传默认为待发布（草稿），点保存-传false，发布传true
  RuleEnv?: RuleEnv;
  // 规则生效环境，不传默认为线上
  AccessPartyId: string;
  // 接入方ID
  ExtraInfo?: Array<ExtraInfoStruct>;
  SkillGroupId?: string;
}

// 技能组
export interface ExtraInfoStruct {
  DisplayName: string;
  Key: string;
  Status: number;
  Type: number;
  Value: string;
}

export interface GetWorkStatusConfigsRequest {
  TenantId: string;
  ChannelType: ChannelType;
  AccessPartyId: string;
}

export interface GetWorkStatusConfigsResponse {
  WorkStatusConfigs: Array<WorkStatusConfig>;
  code: number;
  message: string;
}

export interface WorkStatusConfig {
  TenantId: string;
  // 租户ID
  ChannelType: ChannelType;
  // 坐席类型
  AccessPartyId: string;
  // 接入方id
  WorkStatus: number;
  // 工作状态值
  WorkStatusDesc: string;
  // 工作状态描述
  Enabled: boolean;
  // 是否启用
  ReceptionStatus: number;
  // 接线状态（0：不可接线；1: 可接线）
  UpdaterAgentId: string;
  // 更新人id
  updaterAgentName: string;
  // 更新人名字
  UpdatedAt: string;
  // 更新时间
  NGCCStatus?: string;
  // 对应的ngcc的status
  NgccEnable?: boolean;
  // 对应的ngcc电话外呼组件是否启用
  EnableConfig?: boolean;
}

// 是否可被配置（启用禁用）
// 启用/禁用/删除规则
export interface UpdateRuleGroupStatusRequest {
  RuleGroupId: string;
  // 规则组id
  RuleStatus: RuleStatus;
  // 状态
  UpdaterAgentId: string;
}

// 更新人
// 结果
export interface UpdateRuleGroupStatusResponse {
  Success?: boolean;
  // 成功失败
  code: number;
  message: string;
}

export interface BatchUpdateRuleGroupResponse {
  Success?: boolean;
  // 成功失败
  code: number;
  message: string;
}

// 修改规则组
export interface UpdateRuleGroupRequest {
  EventKey: string;
  // 事件key
  RuleGroupId: string;
  // 规则组ID
  OriginId: string;
  // 原始id
  GroupDisplayName: string;
  // 规则组名称
  Version: number;
  // 版本号
  UpdaterAgentId: string;
  // 创建人id
  Product: string;
  // 产品
  RuleList: Array<AntlrRule>;
  // 规则集合
  Enable?: boolean;
  // 是否启用规则，不传默认为禁用
  RuleEnv?: RuleEnv;
  // 规则生效环境，不传默认为线上
  AccessPartyId: string;
  // 接入方ID
  RuleType?: RuleType;
  SkillGroupId?: string;
}

export interface BatchUpdateRuleGroupRequest {
  RuleGroups: Array<UpdateRuleGroupRequest>;
}

export interface SearchSamBindRequest {
  sellerId?: string;
  // 商家id
  sellerCountryCode?: string;
  // 国家码
  imGroupId?: string;
  // 绑定IM技能组
  imAgentId?: string;
  // 绑定IM客服
  ticketGroupId?: string;
  // 绑定工单技能组
  ticketAgentId?: string;
  // 绑定工单客服
  operateAgentId?: string;
  // 更新人id
  updateTimeStart?: string;
  // 更新时间-start
  updateTimeEnd?: string;
  // 更新时间-end
  createTimeStart?: string;
  // 创建时间-start
  createTimeEnd?: string;
  // 创建时间-end
  pageNum?: string;
  // 页码，从1开始
  pageSize?: string;
  // 每页的数量
  geo?: string;
  // 合规区域
  emailDomain?: string;
  // 邮箱域名
  Base?: base.Base;
}

export interface SearchSamBindData {
  sellerId: string;
  // 商家id
  sellerImg?: string;
  // 商家头像地址
  sellerName?: string;
  // 商家名称
  imGroupName?: string;
  // 绑定IM技能组名称
  imAgentName?: string;
  // 绑定IM客服名称
  imAgentEmail?: string;
  // 绑定IM客服邮箱
  ticketGroupName?: string;
  // 绑定工单技能组名称
  ticketAgentName?: string;
  // 绑定工单客服名称
  ticketAgentEmail?: string;
  // 绑定工单客服邮箱
  note?: string;
  // 备注
  operateImg?: string;
  // 操作人头像
  operateName?: string;
  // 操作人名字
  updateTime?: string;
  // 更新时间
  createTime?: string;
  // 创建时间
  imGroupId?: string;
  // 绑定IM技能组
  imAgentId?: string;
  // 绑定IM客服
  ticketGroupId?: string;
  // 绑定工单技能组
  ticketAgentId?: string;
  // 绑定工单客服
  operateAgentId?: string;
  // 更新人id
  sellerCountry?: string;
  // 更新人id
  id?: string;
  // 商家id
  geo?: string;
  // 合规区域
  emailDomain?: string; 
  // 邮箱域名
}

// 合规区域
export interface SearchSamBindResponse {
  searchSamBindDataList: Array<SearchSamBindData>;
  totalSize: string;
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface CreateSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface CreateSamBindRequest {
  sellerId: string;
  // 商家id
  sellerCountryCode: string;
  // 商家国家/地区
  imGroupId: string;
  // 绑定IM技能组
  imAgentId: string;
  // 绑定IM客服
  ticketGroupId: string;
  // 绑定工单技能组
  ticketAgentId: string;
  // 绑定工单客服
  note?: string;
  // 备注
  geo?: string;
  // 合规区域
  emailDomain?: string;
  // 邮箱域名
  Base?: base.Base;
}

export interface UpdateSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface UpdateSamBindRequest {
  sellerId: string;
  // 商家id
  sellerCountryCode: string;
  // 商家国家/地区
  imGroupId: string;
  // 绑定IM技能组
  imAgentId: string;
  // 绑定IM客服
  ticketGroupId: string;
  // 绑定工单技能组
  ticketAgentId: string;
  // 绑定工单客服
  isDel: number;
  // 0 未删除 1删除
  note?: string;
  // 备注
  id: string;
  // id
  geo?: string;
  // 合规区域
  emailDomain?: string;
  // 邮箱域名
  Base?: base.Base;
}

export interface BatchCreateSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface BatchCreateSamBindRequest {
  excelUrl: string;
  // 文件地址
  geo?: string;
  // 合规区域
  Base?: base.Base;
}

export interface BatchDelSamBindByExcelResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface BatchDelSamBindByExcelRequest {
  excelUrl: string;
  // 文件地址
  geo?: string;
  // 合规区域
  Base?: base.Base;
}

export interface BatchTransferSamBindRequest {
  sellerIdList: Array<string>;
  // 商家id list
  imGroupId: string;
  // 绑定IM技能组
  imAgentId: string;
  // 绑定IM客服
  ticketGroupId: string;
  // 绑定工单技能组
  ticketAgentId: string;
  // 绑定工单客服
  emailDomain?: string;
  // 邮箱域名
  Base?: base.Base;
}

export interface BatchTransferSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface BatchDelSamBindRequest {
  sellerIdList: Array<string>;
  // 商家id list
  geo?: string;
  // 合规区域
  Base?: base.Base;
}

export interface BatchDelSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface BatchExportSamBindRequest {
  sellerId?: string;
  // 商家id
  sellerCountryCode?: string;
  // 国家码
  imGroupId?: string;
  // 绑定IM技能组
  imAgentId?: string;
  // 绑定IM客服
  ticketGroupId?: string;
  // 绑定工单技能组
  ticketAgentId?: string;
  // 绑定工单客服
  operateAgentId?: string;
  // 更新人id
  updateTimeStart?: string;
  // 更新时间-start
  updateTimeEnd?: string;
  // 更新时间-end
  createTimeStart?: string;
  // 创建时间-start
  createTimeEnd?: string;
  // 创建时间-end
  geo?: string;
  // 合规区域
  emailDomain?: string;
  // 邮箱域名
  Base?: base.Base;
}

export interface BatchExportSamBindResponse {
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface GetSellerInfoRequest {
  sellerId: string;
  // 商家id
  Base?: base.Base;
}

export interface GetSellerInfoResponse {
  sellerCountryCode: string;
  // 国家码
  bindStatus: number;
  // 能否绑定：1 能绑定 0：不能绑定
  BaseResp: base.BaseResp;
}

// status:0 成功 其他：失败
export interface GetAllSkillGroupsRequest {
  ShieldTypes?: Array<number>;
  // 1 测试技能组 2管理组 3 session特殊屏蔽组
  accessPartyId?: string;
  Base?: base.Base;
}

export interface GetAllSkillGroupsResponse {
  SkillGroupMap: { [key: string]: Array<SkillGroup> };
  BaseResp: base.BaseResp;
}

export interface GetSkillGroupAgentsRequest {
  TenantId?: string;
  SkillGroupId?: string;
  AgentName?: string;
  IsGroupLeader?: number;
  PageNo?: number;
  PageSize?: number;
  AgentEmail?: string;
  AccessPartyId?: string;
  Keyword?: string;
  // 模糊搜索key  email or name, 优先级高于email/userName
  priority?: number;
  Base?: base.Base;
}

export interface GetSkillGroupAgentsResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export interface GetAgentsByConditionRequest {
  TenantId?: string;
  Status?: number;
  PageNo?: number;
  PageSize?: number;
  ChannelType?: ChannelType;
  Keyword?: string;
  // 模糊搜索key  email or name, 优先级高于email/userName
  Base?: base.Base;
}

export interface GetAgentsByConditionResponse {
  Agents: Array<Agent>;
  TotalSize: number;
  BaseResp: base.BaseResp;
}

export class Demo {
  constructor(uriPrefix?: string);

  queryBizTypesByAccessPartyID(
    req: QueryBizTypesByAccessPartyIDRequest
  ): Promise<QueryBizTypesByAccessPartyIDResponse>;

  getSkillGroupsByAgentId(
    req: GetSkillGroupsByAgentIdRequest
  ): Promise<GetSkillGroupsByAgentIdResponse>;

  getCard(req: GetCardRequest): Promise<GetCardResponse>;

  createCard(req: CreateCardRequest): Promise<CreateCardResponse>;

  updateCard(req: UpdateCardRequest): Promise<UpdateCardResponse>;

  handleCard(req: HandleCardRequest): Promise<HandleCardResponse>;

  checkSkillGroupAutoAssign(
    req: CheckSkillGroupAutoAssignRequest
  ): Promise<CheckSkillGroupAutoAssignResponse>;

  getResourceList(
    req: GetResourceListRequest
  ): Promise<GetResourceListResponse>;

  getCategoryList(
    req: GetCategoryListRequest
  ): Promise<GetCategoryListResponse>;

  getSLAAimMetaSimpleList(
    req: GetSLAAimMetaSimpleListRequest
  ): Promise<GetSLAAimMetaSimpleListResponse>;

  getFieldList(req: GetFieldListRequest): Promise<GetFieldListResponse>;

  getFieldValues(req: GetFieldValuesRequest): Promise<GetFieldValuesResponse>;

  getAdminRuleList(
    req: GetAdminRuleListRequest
  ): Promise<GetAdminRuleListResponse>;

  createAdminRule(
    req: CreateAdminRuleRequest
  ): Promise<CreateAdminRuleResponse>;

  updateAdminRule(
    req: UpdateAdminRuleRequest
  ): Promise<UpdateAdminRuleResponse>;

  deleteAdminRule(
    req: DeleteAdminRuleRequest
  ): Promise<DeleteAdminRuleResponse>;

  batchUpdateAdminRule(
    req: BatchUpdateAdminRuleRequest
  ): Promise<BatchUpdateAdminRuleResponse>;

  createRule(req: CreateRuleV2Request): Promise<CreateRuleV2Response>;

  updateRuleById(req: UpdateRuleRequest): Promise<UpdateRuleResponse>;

  getNewRuleList(req: GetRuleListV2Request): Promise<GetRuleListV2Response>;

  getNewRuleListV4(req: GetRuleListV4Request): Promise<GetRuleListV4Response>;

  getNewRuleListV3(req: GetRuleListV3Request): Promise<GetRuleListV3Response>;

  UpdateRuleStatus(
    req: UpdateRuleStatusV2Request
  ): Promise<UpdateRuleStatusV2Response>;

  UpdateRulePriority(
    req: UpdateRulePriorityV2Request
  ): Promise<UpdateRulePriorityV2Response>;

  PublishRuleGroup(
    req: PublishRuleGroupV2Request
  ): Promise<PublishRuleGroupV2Response>;

  CopyRuleGroup(req: CopyRuleGroupV2Request): Promise<CopyRuleGroupV2Response>;

  GetRuleOperationLogs(
    req: GetRuleOperationLogsRequest
  ): Promise<GetRuleOperationLogsResponse>;

  GetSkillGroupsByAccessParties(
    req: GetSkillGroupsByAccessPartiesRequest
  ): Promise<GetSkillGroupsByAccessPartiesResponse>;

  // 规则列表查询
  GetRuleGroupListByEventKey(
    req: GetRuleGroupListByEventKeyRequest
  ): Promise<GetRuleGroupListByEventKeyResponse>;

  // 根据类型获取技能组列表 -- 编辑页选择技能组
  GetSkillGroupsByType(
    req: GetSkillGroupsByTypeResquest
  ): Promise<GetSkillGroupsByTypeResponse>;

  // 获取已配置信息
  GetExtraInfoV2(req: GetExtraInfoRequestV2): Promise<GetExtraInfoResponseV2>;

  getNewFieldList(req: GetFieldListRequest): Promise<GetFieldListResponse>;

  // 批量创建规则
  BatchCreateRuleGroup(
    req: BatchCreateRuleGroupRequest
  ): Promise<BatchCreateRuleGroupResponse>;

  // 获取工作状态配置
  GetWorkStatusConfigs(
    req: GetWorkStatusConfigsRequest
  ): Promise<GetWorkStatusConfigsResponse>;

  // 修改规则组状态 启用禁用
  UpdateRuleGroupStatus(
    req: UpdateRuleGroupStatusRequest
  ): Promise<UpdateRuleGroupStatusResponse>;

  // 批量更新规则
  BatchUpdateRuleGroup(
    req: BatchUpdateRuleGroupRequest
  ): Promise<BatchUpdateRuleGroupResponse>;

  // 搜索绑定配置信息
  SearchSamBind(req: SearchSamBindRequest): Promise<SearchSamBindResponse>;

  // 查询商家信息
  GetSellerInfo(req: GetSellerInfoRequest): Promise<GetSellerInfoResponse>;

  // 批量导出绑定配置信息
  BatchExportSamBind(
    req: BatchExportSamBindRequest
  ): Promise<BatchExportSamBindResponse>;

  // 批量删除绑定配置信息
  BatchDelSamBind(
    req: BatchDelSamBindRequest
  ): Promise<BatchDelSamBindResponse>;

  // 批量转移绑定配置信息
  BatchTransferSamBind(
    req: BatchTransferSamBindRequest
  ): Promise<BatchTransferSamBindResponse>;

  // 批量导入删除绑定配置信息
  BatchDelSamBindByExcel(
    req: BatchDelSamBindByExcelRequest
  ): Promise<BatchDelSamBindByExcelResponse>;

  // 批量新增绑定配置信息
  BatchCreateSamBind(
    req: BatchCreateSamBindRequest
  ): Promise<BatchCreateSamBindResponse>;

  // 编辑绑定配置信息
  UpdateSamBind(req: UpdateSamBindRequest): Promise<UpdateSamBindResponse>;

  // 新增绑定配置信息
  CreateSamBind(req: CreateSamBindRequest): Promise<CreateSamBindResponse>;

  // 获取全量技能组
  GetAllSkillGroups(
    req: GetAllSkillGroupsRequest
  ): Promise<GetAllSkillGroupsResponse>;

  // 获取技能组成员
  GetSkillGroupAgents(
    req: GetSkillGroupAgentsRequest
  ): Promise<GetSkillGroupAgentsResponse>;

  // 根据条件获取人员
  GetAgentsByCondition(
    req: GetAgentsByConditionRequest
  ): Promise<GetAgentsByConditionResponse>;
}

export const demoClient: Demo;
