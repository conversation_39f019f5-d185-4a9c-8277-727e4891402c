/* eslint-disable */
import 'whatwg-fetch';

import * as base from './base';

function checkStatus(response) {
  if (response && response.status >= 200 && response.status < 300) {
    return response;
  } else {
    const error = new Error(response?.statusText);
    error.response = response;
    throw error;
  }
}

function queryStringify(params, inBody) {
  const keys = [];

  function itemStringify(obj, prefix) {
    const type = Object.prototype.toString.call(obj);
    if (type === '[object Array]') {
      obj.forEach((item, key) => {
        itemStringify(item, `${prefix}[${key}]`);
      });
    } else if (type === '[object Object]') {
      for (const key in obj) {
        itemStringify(obj[key], `${prefix}[${key}]`);
      }
    } else if (type === '[object Date]') {
      keys.push(`${prefix}=${obj.toISOString()}`);
    } else if (type === '[object Null]') {
      keys.push(`${prefix}=`);
    } else if (type !== '[object Undefined]') {
      keys.push(`${prefix}=${encodeURIComponent(obj)}`);
    }
  }

  for (const k in params) {
    itemStringify(params[k], k);
  }

  const str = keys.join('&');
  return str && !inBody ? `?${str}` : str;
}

export const FieldOptionType = {
  SINGLE_CHOOSE: 1,
  MULTI_CHOOSE: 2,
  MIX: 3,
  DATE: 4,
  INPUT: 5,
  BATCH_INPUT: 6,
  TREE: 7,
  CASCADER: 8,
  TEXT: 9,
  INT: 10,
  CONSTANT: 11,
  RICH_TEXT: 12,
  MULTI_LANG: 13,
  DATE_TIME_POINT: 14,
  CONDITION_CASCADER_LIST: 16,
  INPUT_FLOAT: 17,
  TREE_TO_LIST: 701,
};

export const ChannelType = {
  IM: 1,
  TICKET: 2,
  PHONE: 3,
  ADMIN: 4,
  ECOM_TICKET: 5,
  BUZZ: 6,
  FEEDBACK: 7,
  QUALITY_CHECK: 8,
  IM_OFFLINE_SESSION: 9,
  ACCESS_CALL: 10,
  CALLBACK: 11,
  AFTER_SALE: 12,
};

export const HandleType = {
  OPEN: 1,
  CLOSE: 2,
  DELETE: 3,
};

export const RuleTaskStatus = {
  HAS_RUNNING: 1,
  NO_RUNNING: 0,
};

export const RuleStopStatus = {
  DISABLED: 1,
  USING: 0,
};

export const EntityStatus = {
  ENABLE: 1,
  UNABLE: 0,
};

export const FilterOperateType = {
  UNION: 0,
  INTERSECTION: 1,
};

export const RuleStatus = {
  ENABLE: 1,
  UNABLE: 0,
  DRAFT: 2,
  DELETE: 3,
};

export const RuleEnv = {
  PPE: 0,
  PROD: 1,
};

export const OpGroup = {
  AND: 1,
  OR: 2,
  NOT: 3,
};

export const ConditionUpdateType = {
  OptionsUpdate: 1,
  DELETE: 2,
  ADD: 3,
};

export const OperateRuleItemType = {
  STAUTS: 1,
  NAME: 2,
  PRIORITY: 3,
  CONDITION: 4,
  CONDITION_OPTIONS: 5,
  CONDITION_RELATION: 6,
  CONDITION_GROUP_RELATION: 7,
  RETURN_VALUE: 8,
  ROUTE_TYPE: 9,
  OVERFLOW: 10,
};

export const RuleOperationType = {
  CREATE_ENABLE: 1,
  CREATE_DISABLE: 2,
  STATUS_CHANGE: 3,
  PRIORITY_CHANGE: 4,
  UPDATE: 5,
};

export const RuleType = {
  DOCUMENTARY_RULE: 1,
  GENERAL_RULE: 2,
  SEND_DOWN: 3,
  UPGRADE: 4,
  FINISH: 5,
};

export class Demo {
  constructor(uriPrefix) {
    this.uriPrefix = typeof uriPrefix !== 'undefined' ? uriPrefix : '';
  }

  queryBizTypesByAccessPartyID(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/queryBizTypesByAccessPartyID${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getSkillGroupsByAgentId(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getSkillGroupsByAgentId${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getCard(req) {
    const uri = `${this.uriPrefix}/route_management/api/getCard`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  createCard(req) {
    const uri = `${this.uriPrefix}/route_management/api/createCard`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  updateCard(req) {
    const uri = `${this.uriPrefix}/route_management/api/updateCard`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  handleCard(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/handleCard${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  checkSkillGroupAutoAssign(req) {
    const uri = `${this.uriPrefix}/route_management/api/checkSkillGroupAutoAssign`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getResourceList(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getResourceList${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getCategoryList(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getCategoryList${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getSLAAimMetaSimpleList(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getSLAAimMetaSimpleList${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getFieldList(req) {
    const uri = `${this.uriPrefix}/route_management/api/getFieldList`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getFieldValues(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getFieldValues${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getAdminRuleList(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getAdminRuleList${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  createAdminRule(req) {
    const uri = `${this.uriPrefix}/route_management/api/createAdminRule`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  updateAdminRule(req) {
    const uri = `${this.uriPrefix}/route_management/api/updateAdminRule`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  deleteAdminRule(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/deleteAdminRule${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  batchUpdateAdminRule(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchUpdateAdminRule`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  createRule(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/createRule`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  updateRuleById(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/updateRuleById`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getNewRuleList(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/apiv2/getNewRuleList${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getNewRuleListV4(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/getNewRuleListV4`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getNewRuleListV3(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/apiv2/getNewRuleListV3${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  UpdateRuleStatus(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/UpdateRuleStatus`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  UpdateRulePriority(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/UpdateRulePriority`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  PublishRuleGroup(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/PublishRuleGroup`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  CopyRuleGroup(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/apiv2/CopyRuleGroup${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetRuleOperationLogs(req) {
    const uri = `${this.uriPrefix}/route_management/api/getRuleOperationLogs`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetSkillGroupsByAccessParties(req) {
    const uri = `${this.uriPrefix}/route_management/api/getSkillGroupsByAccessParties`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetRuleGroupListByEventKey(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/getRuleGroupListByEventKey`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetSkillGroupsByType(req) {
    const uri = `${this.uriPrefix}/route_management/api/getSkillGroupsByType`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetExtraInfoV2(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/getExtraInfoV2`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  getNewFieldList(req) {
    const uri = `${this.uriPrefix}/route_management/api/getNewFieldList`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchCreateRuleGroup(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/batchCreateRuleGroup`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetWorkStatusConfigs(req) {
    const query = queryStringify(req);
    const uri = `${this.uriPrefix}/route_management/api/getWorkStatusConfigs${query}`;
    const headers = { 'Content-Type': 'application/json' };
    return fetch(uri, { method: 'GET', headers, credentials: 'same-origin' })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  UpdateRuleGroupStatus(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/updateRuleGroupStatus`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchUpdateRuleGroup(req) {
    const uri = `${this.uriPrefix}/route_management/apiv2/batchUpdateRuleGroup`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  SearchSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/searchSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetSellerInfo(req) {
    const uri = `${this.uriPrefix}/route_management/api/getSellerInfo`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchExportSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchExportSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchDelSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchDelSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchTransferSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchTransferSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchDelSamBindByExcel(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchDelSamBindByExcel`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  BatchCreateSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/batchCreateSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  UpdateSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/updateSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  CreateSamBind(req) {
    const uri = `${this.uriPrefix}/route_management/api/createSamBind`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetAllSkillGroups(req) {
    const uri = `${this.uriPrefix}/route_management/api/getAllSkillGroups`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetSkillGroupAgents(req) {
    const uri = `${this.uriPrefix}/route_management/api/getSkillGroupAgents`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }

  GetAgentsByCondition(req) {
    const uri = `${this.uriPrefix}/route_management/api/getAgentsByCondition`;
    const headers = { 'Content-Type': 'application/json' };
    const body = JSON.stringify(req);
    return fetch(uri, {
      method: 'POST',
      headers,
      body,
      credentials: 'same-origin',
    })
      .then(checkStatus)
      .then(response => response.json && response.json());
  }
}

export const demoClient = new Demo();
