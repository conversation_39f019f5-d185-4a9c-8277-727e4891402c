import { makeAutoObservable } from 'mobx';
import { errorReporting } from '@/common/utils/errorReporting';

export class UserStore {
  user: User = {
    uuid: '',
    unique_name: '',
    username: '',
    email: '',
    user_id: '',
    platform: 0,
    tenant_key: '',
    avatar: '',
    create_time: '',
  };
  agent: Agent = {} as Agent;
  isUserOk = false;
  constructor() {
    makeAutoObservable(
      this,
      {},
      {
        //  Options parameter, autoBind refers to the automatic binding of this.
        autoBind: true,
      }
    );
  }
  async getUser(): Promise<any> {
    try {
      const { user, agent } = window.$pageData;
      this.user = Object.assign({}, this.user, user);
      this.agent = agent;
      this.isUserOk = true;
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'getUser' });
      this.isUserOk = false;
    }
    return {
      user: this.user,
      agent: this.agent,
    };
  }
}

export default new UserStore();
