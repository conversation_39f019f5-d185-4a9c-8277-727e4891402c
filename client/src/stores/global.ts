import { makeAutoObservable, runInAction } from 'mobx';
import { GetAllSkillGroupsRequest, demoClient } from '@http_idl/demo';
import { Option } from '@ies/kefu-filters/dist/lib/interface';
import AIS from '@ies/ais';

export class Global {
  tcc = window.$pageData.tcc;
  imAgentOptionList = [];
  ticketAgentOptionList = [];
  skillGroupMap: Record<string, Option[] | any[]> = {};
  countryList: Option[] = [];
  unitedLang: string = localStorage.getItem('unitedLang') || 'zh';
  constructor() {
    makeAutoObservable(
      this,
      {},
      {
        //  Options parameter, autoBind refers to the automatic binding of this.
        autoBind: true,
      }
    );
  }
  async fetchGetAllSkillGroups(req: GetAllSkillGroupsRequest): Promise<void> {
    const res = await demoClient.GetAllSkillGroups(req);
    runInAction(() => {
      this.skillGroupMap = res.SkillGroupMap;
    });
  }
  async fetchCountryRegion(): Promise<void> {
    const res = await AIS.fe.getJsonTCC({
      serviceName: 'ies.kefu.i18n_config',
      key: 'CountryRegionList',
    });
    const countryList = res?.data.map(item => ({
      label: this.unitedLang === 'zh' ? item.ChineseName : item.EnglishName,
      value: item.Code,
    }));
    runInAction(() => {
      this.countryList = countryList;
    });
  }
}

export default new Global();
