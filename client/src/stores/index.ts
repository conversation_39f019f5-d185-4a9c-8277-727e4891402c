import { createContext, useContext } from 'react';
import user from './user';
import global from './global';
import tableStore from './bindingsConfigStore/table';

const store = {
  user,
  global,
  tableStore,
};

const context = createContext(store);

type Store = typeof store;

const useStore = <K extends keyof Store>(name: K) => {
  const data = useContext(context) as Store;
  return name ? data[name] : null;
};

export { useStore, context, store };
