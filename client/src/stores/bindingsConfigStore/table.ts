import { I18n } from '@ies/starling_intl';
import { makeAutoObservable, runInAction } from 'mobx';
import { demoClient, SearchSamBindData, SearchSamBindRequest } from '@http_idl/demo';
import { Toast } from '@ies/semi-ui-react';
import { errorReporting } from '@/common/utils/errorReporting';

export class TableStore {
  samBindList: SearchSamBindData[] = [];
  skillGroupMap: any = {};
  bindingConfigVisible = false;
  filterData: Record<string, string | number> = {};
  isEdit = false;
  columnsRowInfo: SearchSamBindData = {} as SearchSamBindData;
  batchOperationModalVisible = false;
  batchOperationModalTitle = 0;
  selectedRowKeys: string[] = [];
  selectedItems: Record<string, any>[] = [];
  transferModalVisible = false;
  tableLoading = false;
  total = '10';
  geo = 'ROW';
  pagination: Record<string, string> = {
    pageNum: '1',
    pageSize: '20',
  };

  constructor() {
    makeAutoObservable(
      this,
      {},
      {
        //  Options parameter, autoBind refers to the automatic binding of this.
        autoBind: true,
      }
    );
  }

  async fetchSearchSamBind(req?: SearchSamBindRequest): Promise<void> {
    this.tableLoading = true;
    try {
      const res = await demoClient.SearchSamBind({
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        geo: this.geo,
        ...req,
      });
      if (res.BaseResp.StatusCode !== 0) {
        Toast.error(res.BaseResp.StatusMessage || I18n.t('failed_to_get_binding_list', {}, '获取绑定列表失败'));
        this.tableLoading = false;
      }
      runInAction(() => {
        this.samBindList = res.searchSamBindDataList || [];
        this.total = res.totalSize;
        this.tableLoading = false;
      });
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'fetchSearchSamBind' });
      this.tableLoading = false;
    }
  }
  setGeo(geo: string): void {
    this.geo = geo;
    this.setPagination({
      pageNum: '1',
      pageSize: this.pagination.pageSize,
    });
    this.setSelectedRowKeys([]);
    this.setSelectedItems([]);
    this.fetchSearchSamBind({
      ...this.filterData
    });
  }
  setFilterData(filterData: Record<string, string | number>): void {
    this.filterData = filterData;
  }
  setBindingConfigVisible(visible: boolean): void {
    this.bindingConfigVisible = visible;
  }
  setIsEdit(isEdit: boolean): void {
    this.isEdit = isEdit;
  }
  setColumnsRecord(columnsRowInfo: SearchSamBindData): void {
    this.columnsRowInfo = columnsRowInfo;
  }
  setBatchOperationModalVisible(visible: boolean): void {
    this.batchOperationModalVisible = visible;
  }
  setbBtchOperationModalTitle(title: number): void {
    this.batchOperationModalTitle = title;
  }
  setSelectedRowKeys(selectedRowKeys: string[]): void {
    this.selectedRowKeys = selectedRowKeys;
  }
  setSelectedItems(selectedItems: Array<Record<string, any>>): void {
    this.selectedItems = selectedItems;
  }
  setTransferModalVisible(visible: boolean): void {
    this.transferModalVisible = visible;
  }
  setPagination(pagination: Record<string, string>): void {
    this.pagination = pagination;
  }
}

export default new TableStore();
