import * as React from 'react';
import { createIntl, createIntlCache, IntlProvider } from 'react-intl';
import Starling from '@ies/starling_client';
import { LocaleProvider } from '@ies/semi-ui-react';
import zhCN from '@ies/semi-ui-react/locale/source/zh_CN'; // English
import enGB from '@ies/semi-ui-react/locale/source/en_GB';
import config from '../common/constants/config';

// Starling local
import zhLocaleStarling from '../locale/zh-CN.json';
import enLocaleStarling from '../locale/en.json';
import { errorReporting } from '@/common/utils/errorReporting';

let localeStarling = {
  zh: zhLocaleStarling,
  en: enLocaleStarling,
};

function createStarling(locale) {
  const params = { ...config.starling, locale };
  return new Starling(params);
}

const defaultLocale = 'zh';

const cache = createIntlCache();
let intl;

const semiLocaleMap = {
  zh: zhCN,
  en: enGB,
};

interface IState {
  messages: Record<string, string>;
  locale: string;
  isLoaded: boolean;
  basename: string;
}

/**
 * @param {*} WrappedComponent
 * @returns {*}  Add locale  and setLocale
 */
function withIntl(WrappedComponent): any {
  return class IntlComponent extends React.Component<Record<string, unknown>, IState> {
    constructor(props, context) {
      super(props, context);
      this.state = {
        messages: {},
        locale: defaultLocale,
        isLoaded: false,
        basename: props.basename,
      };
    }

    componentDidMount() {
      this.setLocale(defaultLocale);
    }

    setLocale = async locale => {
      try {
        if (process.env.NODE_ENV === 'production') {
          // Online environment overwrites local files through Starling platform
          await createStarling(locale).load(messages => {
            localeStarling = {
              ...localeStarling,
              [locale]: messages,
            };
          });
        }
      } catch (error) {
        errorReporting({ error, type: 'callback_name', name: 'withIntl' });
      } finally {
        intl = createIntl(
          {
            locale,
            messages: localeStarling[locale],
          },
          cache
        );

        this.setState({ locale, messages: localeStarling[locale], isLoaded: true });
      }
    };

    render() {
      const { locale, messages, isLoaded, basename } = this.state;

      if (!isLoaded) {
        return null;
      }
      return (
        <IntlProvider key={locale} locale={locale} messages={messages}>
          <LocaleProvider locale={semiLocaleMap[locale]}>
            <WrappedComponent basename={basename} setLocale={this.setLocale} locale={this.state.locale} />
          </LocaleProvider>
        </IntlProvider>
      );
    }
  };
}

export default withIntl;

export const formatMessage = (...args) => {
  if (!intl) {
    console.warn('i18n init failed.');
    return '';
  }
  if (!args.length) {
    throw new Error('lack parameter id');
  }
  if (typeof args[0] === 'object') {
    // FormatMessage ({id: 'open', defaultMessage: 'open'}, {name: 'name'})
    return intl.formatMessage(args[0], args[1]);
  }
  // Use key if no
  let defaultMessage = args[0];
  // FormatMessage ('open', 'open')
  if (typeof args[1] === 'string') {
    defaultMessage = args[1];
  } else if (typeof args[2] === 'string') {
    // FormatMessage ('open', {name: 'name'}, 'open {name}')
    defaultMessage = args[2];
  }
  const descriptor = {
    id: args[0],
    defaultMessage,
  };
  return intl.formatMessage(descriptor, args[1]);
};
