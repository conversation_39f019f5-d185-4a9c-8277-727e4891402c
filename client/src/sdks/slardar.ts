import slardarSG from '@slardar/web/sg';
import slardarVA from '@slardar/web/maliva';
import slardarTTP from '@slardar/web/oci';
import { isInSG, isInVA } from '@common/utils/env';
import { shared } from '@ies/unified_communications_sdk';

// const slardar = new BrowserSlardar().instance;
const slardarIns = isInSG() ? slardarSG : isInVA() ? slardarVA : slardarTTP;
// if (process.env.NODE_ENV === 'production') {
//   slardarIns('init', config.slardar);
// }
// slardarIns('init', config.slardar);
export const sendFirst = (): void => {
  const firstScreen = Date.now();
  // White screen time
  slardarIns('sendEvent', {
    name: 'firstPaint',
    metrics: {
      value: window.firstPaint - performance.timing.navigationStart,
    },
  });
  // first screen time
  slardarIns('sendEvent', {
    name: 'firstScreen',
    metrics: {
      value: firstScreen - performance.timing.navigationStart,
    },
  });
};
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const sendEvent = (params: any): void => {
  const { name = '', metrics = {}, categories = {} } = params;
  // White screen time
  slardarIns('sendEvent', {
    name,
    metrics: {
      ...metrics,
    },
    categories: {
      ...categories,
    },
  });
};

export const reportSlardar = (eventName, params = {}): void => {
  const slardarParams = {
    url: location.href,
    accessParty: shared.getAccessPartyId(),
    agent: shared.getAgent()?.Email,
  };
  if (process.env.NODE_ENV === 'production') {
    slardarIns('sendLog', {
      content: eventName,
      level: 'info',
      extra: {
        ...slardarParams,
        ...params,
      },
    });
  }
};
export const reportPageUnUsableTime = (pageName = '', unableTime = 0, totalTime = 0): void => {
  slardarIns('sendEvent', {
    name: 'pageUsableTime',
    metrics: {
      unableTime,
      totalTime,
    },
    categories: {
      pageName,
    },
  });
};

export const customPerformanceTime = (timeEnd = new Date().getTime()) => {
  // Page load time statistics
  const customPerformance = window.custom_performace || {};
  const timeStart = customPerformance['setting-route_management'];
  if (!timeStart) {
    return;
  }

  if (Object.keys(customPerformance).length === 1) {
    // First visit, load from URL, only inject the subname of the subapplication when loading for the first time
    slardarIns('sendEvent', {
      name: 'route_management_url_performance',
      metrics: { time: timeEnd - window.performance.timing.navigationStart },
    });
  } else {
    // Click Unified Workbench to load (sub-application to load)
    slardarIns('sendEvent', {
      name: 'route_management_nav_performance',
      metrics: { time: timeEnd - Number(timeStart) },
    });
  }
};

export default slardarIns;
