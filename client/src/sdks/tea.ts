import tea from 'byted-tea-sdk';
import config from '@common/constants/config';

tea('init', config.tea);

export default {
  setUser(userName) {
    tea('config', {
      user_unique_id: userName,
      log: true,
    });
    tea('send');
  },
  pageView({ module, page, label }) {
    tea('page_view', {
      module,
      page,
      label,
      username: this.username,
    });
  },
  submit() {
    tea('submit', {
      module,
      // page,
      // data,
    });
  },
};
