import { I18n } from '@ies/starling_intl';
/**
 * @file form field rules
 * <AUTHOR>
 */

import { BaseRule } from './types';

export function getRequiredRule(message = I18n.t('this_field_is_required', {}, '该字段为必填项')): BaseRule {
  return {
    required: true,
    message,
  };
}

export function getMaxLengthRule(max: number) {
  return {
    max,
    message: `${I18n.t('enter_a_maximum_of_{max}_characters', { max }, '最多输入 {max} 个字符')}`,
  };
}

export const countIntegerNumberRule = {
  validator(rule, value, cb) {
    if (!Number(value) && Number(value) !== 0) {
      cb(I18n.t('route_Starling_42', {}, '请输入数字'));
    } else if (value >= 0) {
      if (Number(value.toFixed(2)) === value) {
        cb();
      } else {
        cb(I18n.t('route_Starling_43', {}, '保留两位小数'));
      }
    } else {
      cb(I18n.t('please_enter_an_integer_greater_than_0', {}, '请输入大于0的整数'));
    }
  },
};
export function getNonEmptyRule(msg: string) {
  return {
    validator(rule, value, cb) {
      if (!value || !value.trim()) {
        cb(msg);
      } else {
        cb();
      }
    },
  };
}

interface GetIntegerNumberRuleOptions {
  min: number;
  max: number;
}
export function getIntegerNumberRule(options: GetIntegerNumberRuleOptions) {
  const { min, max } = options;
  return {
    validator(rule, value, cb) {
      if (Math.floor(value) !== value) {
        cb(I18n.t('please_enter_an_integer', {}, '请输入整数'));
      } else if (value >= min && value <= max) {
        cb();
      } else {
        cb(
          `${I18n.t(
            'the_valid_number_is_{min}~_{max}__please_enter_the_correct_value',
            { min, max },
            '有效数字为{min}~{max}，请输入正确数值'
          )}`
        );
      }
    },
  };
}
