import { I18n } from '@ies/starling_intl';
import { isBoe } from '../utils/env';
import { routeType } from '@/const/enums';
// Access-Party Routing
export const IM_MANUAL_ACCESS_PARTY_ROUTING = 'im_manual_access_party_routing';
export const IM_MANUAL_ROUTING_EVENT_ID = '7400668987958511622';

export const TICKET_MANUAL_ACCESS_PARTY_ROUTING = 'ticket_manual_access_party_routing';
export const TICKET_MANUAL_ROUTING_EVENT_ID = '7400668990135322630';

// Client Routing
export const CLIENT_ROUTING_EVENT_ID = '28';
export const CLIENT_ROUTING_SOURCE_ID = '42';
export const CLIENT_ROUTING_APP_ID = 7;

// Artificial
export const SERVICE_ROUTING_EVENT_ID = '10';
export const SERVICE_ROUTING_SOURCE_ID = '4';
export const SERVICE_ROUTING_APP_ID = 2;
export const SERVICE_ROUTE = 'IM_SKILL_GROUP_ROUTE';

// Robot
export const BOT_ROUTING_EVENT_ID = '13';
export const BOT_ROUTING_SOURCE_ID = '14';
export const BOT_ROUTING_APP_ID = 3;
export const BOT_ROUTE = 'IM_BOT_ROUTE';

// E-commerce ticket routing
export const TICKET_ROUTING_EVENT_ID = '25';
export const TICKET_ROUTING_SOURCE_ID = '35';
export const TICKET_ROUTING_APP_ID = 5;
export const TICKET_ROUTE = 'TICKET_ROUTE';
export const RANGER_TICKET_ROUTE = 'RANGER_TICKET_ROUTE';
export const Talent_TICKET_ROUTE = 'ECOMMERCE_TICKET_ROUTE';

// General work order
export const COMMON_TICKET_ROUTING_EVENT_ID = '16';
export const COMMON_TICKET_ROUTING_SOURCE_ID = '20';
export const COMMON_TICKET_ROUTING_APP_ID = 9;

// Offline
export const OFFLINE_ROUTING_EVENT_ID = '27';
export const OFFLINE_ROUTING_SOURCE_ID = '39';
export const OFFLINE_ROUTING_APP_ID = 6;
export const OFFLINE_ROUTE = 'FEEDBACK_ROUTE';

// Quality inspection
export const QUALITY_CHECK_ROUTING_EVENT_ID = '32';
export const QUALITY_CHECK_ROUTING_SOURCE_ID = '53';
export const QUALITY_CHECK_ROUTING_APP_ID = 15;
export const QUALITY_CHECK_ROUTE = 'QC_TICKET_SKILL_GROUP_ROUTE';

// access party shunt
export const ACCESSPARTY_EVENT_ID = '39';
export const ACCESSPARTY_SOURCE_ID = '';
export const ACCESSPARTY_APP_ID = '';
export const ACCESSPARTY_EVENT_KEY = 'EMAIL_ACCESS_PARTY_ROUTE';

/**
 * After-sales single route eventKey and eventId
 */
export const AOP_TICKET_ROUTE = 'AOP_TICKET_ROUTE';
export const AOP_TICKET_ROUTE_ID = '7246231559358089221';

export const ApiAuthFailedCode = 10401;
export const ApiAuthFailedMsg = I18n.t(
  'no_permission__please_contact_the_administrator_to_add',
  {},
  '无权限，请联系管理员添加'
);

// E-commerce access party ID list
export const ECOM_ACCESS_PARTY_ID_LIST = ['16', '17', '19'];

enum ROUTE_ENENT_ENUM {
  TICKET_ROUTE = 'TICKET_ROUTE',
  IM_SKILL_GROUP_ROUTE = 'IM_SKILL_GROUP_ROUTE',
  EMAIL_ACCESS_PARTY_ROUTE = 'EMAIL_ACCESS_PARTY_ROUTE',
  FEEDBACK_ROUTE = 'FEEDBACK_ROUTE',
  QC_TICKET_SKILL_GROUP_ROUTE = 'QC_TICKET_SKILL_GROUP_ROUTE',
  RANGER_TICKET_ROUTE = 'RANGER_TICKET_ROUTE',
  IM_BOT_ROUTE = 'IM_BOT_ROUTE',
  CLIENT_ROUTING_ROUTE = 'CLIENT_ROUTING_ROUTE',
  ECOMMERCE_TICKET_ROUTE = 'ECOMMERCE_TICKET_ROUTE',
  AOP_TICKET_ROUTE = 'AOP_TICKET_ROUTE',
}
export const ROUTE_EVENT_ID = {
  [ROUTE_ENENT_ENUM.TICKET_ROUTE]: '16',
  [ROUTE_ENENT_ENUM.IM_SKILL_GROUP_ROUTE]: '10',
  [ROUTE_ENENT_ENUM.EMAIL_ACCESS_PARTY_ROUTE]: '39',
  [ROUTE_ENENT_ENUM.FEEDBACK_ROUTE]: '27',
  [ROUTE_ENENT_ENUM.QC_TICKET_SKILL_GROUP_ROUTE]: '32',
  [ROUTE_ENENT_ENUM.RANGER_TICKET_ROUTE]: '16',
  [ROUTE_ENENT_ENUM.IM_BOT_ROUTE]: '13',
  [ROUTE_ENENT_ENUM.CLIENT_ROUTING_ROUTE]: '28',
  [ROUTE_ENENT_ENUM.ECOMMERCE_TICKET_ROUTE]: '16',
  [ROUTE_ENENT_ENUM.AOP_TICKET_ROUTE]: AOP_TICKET_ROUTE_ID,
};
export enum STATUS_CHANGE_ENUM {
  ENABLE = 'ENABLE',
  DISABLE = 'UNABLE',
  DELETE = 'DELETE',
  PUBLISH = 'PUBLISH',
}
export const LOG_RULE_STATUS_MAP = {
  [STATUS_CHANGE_ENUM.ENABLE]: () => I18n.t('route_Starling_29', {}, '规则启用'),
  [STATUS_CHANGE_ENUM.DISABLE]: () => I18n.t('route_Starling_30', {}, '规则禁用'),
  [STATUS_CHANGE_ENUM.DELETE]: () => I18n.t('route_Starling_31', {}, '规则删除'),
  [STATUS_CHANGE_ENUM.PUBLISH]: () => I18n.t('route_Starling_32', {}, '发布上线'),
};
export enum ROUTE_TYPE_ENUM {
  creat = '0',
  upgrade = '1',
  backTo = '2',
  imTemporaryStorage = '3',
  imFinished = '4',
  imDown = '5',
  recovery = '6',
}
export const routeTypeList = [
  {
    value: ROUTE_TYPE_ENUM.creat,
    name: () => I18n.t('route_Starling_48', {}, '创建'),
  },
  {
    value: ROUTE_TYPE_ENUM.upgrade,
    name: () => I18n.t('route_Starling_47', {}, '升级'),
  },
  {
    value: ROUTE_TYPE_ENUM.backTo,
    name: () => I18n.t('route_Starling_46', {}, '打回'),
  },
  {
    value: ROUTE_TYPE_ENUM.imTemporaryStorage,
    name: () => I18n.t('route_Starling_51', {}, 'im暂存'),
  },
  {
    value: ROUTE_TYPE_ENUM.imFinished,
    name: () => I18n.t('route_Starling_52', {}, 'im完结'),
  },
  {
    value: ROUTE_TYPE_ENUM.imDown,
    name: () => I18n.t('route_Starling_53', {}, 'im下送'),
  },
  {
    value: ROUTE_TYPE_ENUM.recovery,
    name: () => I18n.t('recovery', {}, '重启'),
  },
];

export const routeTypeMap = {
  [ROUTE_TYPE_ENUM.backTo]: () => I18n.t('route_Starling_46', {}, '打回'),
  [ROUTE_TYPE_ENUM.upgrade]: () => I18n.t('route_Starling_47', {}, '升级'),
  [ROUTE_TYPE_ENUM.creat]: () => I18n.t('route_Starling_48', {}, '创建'),
  [ROUTE_TYPE_ENUM.imTemporaryStorage]: () => I18n.t('route_Starling_51', {}, 'im暂存'),
  [ROUTE_TYPE_ENUM.imFinished]: () => I18n.t('route_Starling_52', {}, 'im完结'),
  [ROUTE_TYPE_ENUM.imDown]: () => I18n.t('route_Starling_53', {}, 'im下送'),
  [ROUTE_TYPE_ENUM.recovery]: () => I18n.t('recovery', {}, '重启'),
};

// Elastic to manual
export const FLEXIBLE_ART_EVENT_ID = '7088577616386628140';
export const FLEXIBLE_ART_ROUTE = 'SupplyAndDemandElastic';
export const DYNAMIC_EVENT_ID = isBoe ? '7184763064469456901' : '204';
export const DYNAMIC_EVENT_KEY = 'DynamicAgentMaxTaskNum';
export const FLEXIBLE_HOT_LINE_ROUTE = 'IVRSupplyAndDemandElastic';
export const FLEXIBLE_HOT_LINE_EVENT_ID = '7143574462405822514';
export const FLEXIBLE_CONFIG_KEY = 'elastic_rule_skill_groups';
export const ACCESS_PARTY = {
  ['GLOAB_SELLING']: '35',
  ['SELLWE_SERVICE_HOSTING']: '46',
};

export enum ModalTitle {
  BULK_DELETE = 0,
  BULK_IMPORT = 1,
}

export const permDescripLarkLink = 'https://bytedance.larkoffice.com/docx/F1UadyCKGomoT2xggULcHRVRnNe';
export const tempLarkLink = 'https://bytedance.larkoffice.com/sheets/Gva1sWUtEhR3oytkfeacTTYXnKc';
export const deleteTempLarkLink = 'https://bytedance.larkoffice.com/sheets/LWLQslSjBhu7IQtSltKcmg0ynId';
export const languageMap = {
  zh: 'cn',
  en: 'en',
};

export const ROUTE_PATH = {
  TICKET: 'ticket_routing_v2',
  IM_SERVICE: 'service_routing_v2',
  IM_BOT: 'bot_routing_v2',
  IM_MANUAL_ACCESS_PARTY: 'im_manual_access_party_routing',
  TICKET_MANUAL_ACCESS_PARTY: 'ticket_manual_access_party_routing',
};

export const DOOR_AUTHORITY_MAP = {
  [ROUTE_PATH.TICKET]: {
    EDIT: 'function.ticket_edit',
    VIEW: 'function.ticket_view',
  },
  [ROUTE_PATH.IM_BOT]: {
    VIEW: 'function.im_intelligent_view',
    EDIT: 'function.im_intelligent_edit',
  },
  [ROUTE_PATH.IM_SERVICE]: {
    VIEW: 'function.im_manual_view',
    EDIT: 'function.im_manual_edit',
  },
  [ROUTE_PATH.IM_MANUAL_ACCESS_PARTY]: {
    VIEW: 'function.im_edit',
    EDIT: 'function.im_view',
  },
  [ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY]: {
    VIEW: 'function.ticket_edit',
    EDIT: 'function.ticket_view',
  },
};

export const ROUTE_EVENT_KEY_MAP = {
  [ROUTE_PATH.IM_MANUAL_ACCESS_PARTY]: IM_MANUAL_ACCESS_PARTY_ROUTING,
  [ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY]: TICKET_MANUAL_ACCESS_PARTY_ROUTING,
};

export const ROUTE_EVENT_ID_MAP = {
  [ROUTE_PATH.IM_MANUAL_ACCESS_PARTY]: IM_MANUAL_ROUTING_EVENT_ID,
  [ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY]: TICKET_MANUAL_ROUTING_EVENT_ID,
};

export const ROUTE_TYPE_MAP = {
  [ROUTE_PATH.IM_MANUAL_ACCESS_PARTY]: routeType.im_accessparty_routing,
  [ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY]: routeType.ticket_accessparty_routing,
};

export const ROUTE_TYPE_EVENT_KEY_MAP = {
  [routeType.im_accessparty_routing]: IM_MANUAL_ACCESS_PARTY_ROUTING,
  [routeType.ticket_accessparty_routing]: TICKET_MANUAL_ACCESS_PARTY_ROUTING,
};
