import { isProd } from '../utils/isProd';
export default {
  /**
   * Front-end exception monitoring
   *  after creating application, replace dsn
   */
  slardar: {
    bid: 'ies_kefu_route_manage_i18n',
    env: isProd() ? 'production' : 'boe',
    release: process.env.BUILD_VERSION || '',
    plugins: {
      ajax: {
        ignoreUrls: [
          'sgali-mcs.byteoversea.com',
          'https://maliva-mcs.byteoversea.com',
          'https://starling-sg.byteoversea.com',
        ],
      },
      fetch: true,
      tti: true,
      performance: {
        fp: true,
        fcp: true,
        fid: true,
        mpfid: true,
        lcp: true,
        cls: true,
      },
    },
  },
  /**
   * Application statistics
   *  Access guide https://bytedance.feishu.cn/docs/doccnbxDbCUicHF1w6oxLDY7Xlh
   * After creating the application, replace the app_id
   */
  tea: {
    // Statistics
    app_id: 1663,
    channel: 'cn',
  },
  /**
   * Internationalized copy management
   * After creating the application, replace the api_key
   */
  starling: {
    // Internationalization
    api_key: 'd86e3970b78011e980088f571978e7b9',
    namespace: 'global', // namespace
    locale: 'en', // Current language
    test: false, // Whether to test the environment
    zoneHost: 'https://starling-oversea.byteoversea.com',
  },
  /**
   * Feedback work order system
   *  access document https://bytedance.feishu.cn/space/doc/doccnYf10eu03xG1v2ZtNQ
   */
  hornbill: {
    appKey: 30,
  },
};
