:global {

  .table-header-row {
    box-sizing: border-box;

    th {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }
  }

  .table-row {
    height: 64px;

    td {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }
  }

  .link-text {
    flex-shrink: 0;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;

    color: var(--color-link);
    cursor: pointer;
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .inline-block {
    display: inline-block;
  }

  .flexible {
    display: flex;
    flex-wrap: wrap;
  }

  .flex-align-center {
    align-items: center;
  }

  .united_route_manage-modal-header {
    margin: 24px 0 8px;
    word-break: break-all;
  }

  .united_route_manage-table-empty {
    color: var(--color-text-1);
  }
}
