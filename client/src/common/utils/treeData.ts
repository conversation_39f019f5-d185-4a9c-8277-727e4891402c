import { safeJSONParse } from './index';

interface DataCenterTreeNodeData {
  name: string;
  value: string | number;
  level: string;
  children?:
  | DataCenterTreeNodeData[]
  | {
    children: DataCenterTreeNodeData[];
  };
}

interface TreeNodeData {
  label: string;
  key: string;
  value: string | number;
  children?: TreeNodeData[];
}

export function formatTreeData(dataCenterTreeData: DataCenterTreeNodeData[]): TreeNodeData[] {
  function transformData(dataCenterTreeNodeData: DataCenterTreeNodeData): TreeNodeData {
    let children: TreeNodeData[];
    if (!dataCenterTreeNodeData.children) {
      children = [];
    } else if (Array.isArray(dataCenterTreeNodeData.children)) {
      children = dataCenterTreeNodeData.children.map(transformData);
    } else if (Array.isArray(dataCenterTreeNodeData.children.children)) {
      children = dataCenterTreeNodeData.children.children.map(transformData);
    } else {
      children = [];
    }

    return {
      label: dataCenterTreeNodeData.name,
      value: dataCenterTreeNodeData.value,
      key: String(dataCenterTreeNodeData.value),
      children,
    };
  }
  return dataCenterTreeData.map(transformData);
}

const cacheMap = new Map<string, Map<string | number, DataCenterTreeNodeData>>();

export function getFieldByValue(
  cacheKey: string,
  fieldValueList: DataCenterTreeNodeData[],
  value: string | number,
  leafOnly = true
) {
  let valueMap: Map<string | number, DataCenterTreeNodeData>;

  if (cacheMap.get(cacheKey)) {
    valueMap = cacheMap.get(cacheKey);
  } else {
    valueMap = new Map<string | number, DataCenterTreeNodeData>();
    function travel(fieldValueList?: DataCenterTreeNodeData[]) {
      if (!fieldValueList) {
        return;
      }
      fieldValueList.forEach(field => {
        // Take only leaf nodes
        let children: DataCenterTreeNodeData[];
        if (!field.children) {
          children = [];
        } else if (Array.isArray(field.children)) {
          children = field.children;
        } else if (Array.isArray(field.children.children)) {
          children = field.children.children;
        } else {
          children = [];
        }
        if (leafOnly) {
          if (!children || children.length === 0) {
            valueMap.set(field.value, field);
          } else {
            if (!valueMap.get(field.value)) {
              valueMap.set(field.value, field);
            }
          }
        } else {
          valueMap.set(field.value, field);
        }
        travel(children);
      });
    }

    travel(fieldValueList);

    cacheMap.set(cacheKey, valueMap);
  }

  return valueMap.get(value) || valueMap.get(String(value)) || valueMap.get(Number(value));
}
// Find all leaf nodes of the current ticket label
export function getLeafValues(data): string[] {
  let values = [];
  if (!data) {
    return values;
  }
  if (!data.children || data.children.length === 0) {
    values.push(data.value);
  } else {
    for (let i = 0; i < data.children.length; i++) {
      values = [...values, ...getLeafValues(data.children[i])];
    }
  }
  return values;
}

// Processing ticket label echo data
export function handleTicketTag(rhsList, fieldList, opCheck, fieldName): string[] {
  const fieldValueList = fieldList.find(f => f.FieldName === fieldName).OperatorFieldvalues?.[opCheck]?.FieldValueList;
  const newFieldValueList = formatTreeData(safeJSONParse(JSON.stringify(fieldValueList)));
  const leafValues = getLeafValues({ children: newFieldValueList });
  const treeList = leafValues.filter(item => rhsList.includes(`\"${item}\"`)).map(key => `\"${key}\"`);
  return treeList;
}
