import { sendEvent } from '@sdks/slardar';

export const errorReporting = ({ error, type, name }: { type?: string; error: any; name?: string }): void => {
  sendEvent({
    name: 'try_catch_error',
    metrics: {
      count: 1,
    },
    categories: {
      [type]: name,
      target: name,
      agentId: window?.$unitedHelpdeskI18nInitData?.userInfo?.agent?.ID,
      message: {
        message: error?.message || '',
        name: error?.name || '',
        stack: error?.stack || '',
        errorInfo: String(error),
      },
    },
  });
};
