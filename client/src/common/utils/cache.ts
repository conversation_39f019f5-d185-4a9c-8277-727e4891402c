import { errorReporting } from './errorReporting';

/**
 * Cache component, support setting cache time
 */
class Cache {
  private memStoreMap: Record<string, any>;
  private enable: boolean;
  constructor() {
    this.memStoreMap = {};
    this.enable = true;
    this.init();
  }

  init() {
    try {
      if (!window.localStorage) {
        this.enable = false;
      }
    } catch (error) {
      errorReporting({ error, type: 'pormise_name', name: 'addResourceBundle' });
      this.enable = false;
    }
  }

  /*
   *Set cache value, cacheTime
   *@param cacheTime  milliseconds
   */
  setStore(key: string, value: any, cacheTime: any) {
    if (!this.enable) {
      return this;
    }
    let content = '';

    if (!cacheTime) {
      // If there is no cacheTime, just go back, there is no point!
      return this;
    }

    const d = {
      cacheTime: new Date().getTime() + (cacheTime ? parseInt(cacheTime, 10) : 0),
      value,
    };
    content = JSON.stringify(d);

    try {
      this.memStoreMap[key] = content;
      localStorage.setItem(key, content);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'cache_setStore' });
      return this;
    }
    return this;
  }

  /*
   *Get cache
   */
  getStore(key: string, forceLocal: boolean) {
    // ForceLocal force localstory
    if (!this.enable) {
      return '';
    }

    let content: any = '';
    if (forceLocal) {
      content = localStorage.getItem(key);
    } else {
      content = this.memStoreMap[key] || localStorage.getItem(key);
    }

    if (!content) {
      return '';
    }
    try {
      content = JSON.parse(content);

      // Check whether the cache is valid
      if (content.cacheTime >= new Date().getTime()) {
        return content.value;
      } else {
        this.removeStore(key);
        return '';
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'cache_getStore' });
      return '';
    }
  }

  /*
   *Delete cache
   */
  removeStore(key: string) {
    if (!this.enable) {
      return this;
    }

    localStorage.removeItem(key);
    this.memStoreMap[key] = undefined;
    delete this.memStoreMap[key];
    return this;
  }
}

export default new Cache();
