import { unitedTimezone } from '@ies/united_helpdesk_i18n_utils';
/**
 * Format event
 * time can be a timestamp unit of ms or a new Date object
 * formatStr: YYYY-mm-dd HH: ii ss
 * Week: W
 * @example
 * format (123432434234, 'YYYY year mm month dd day HH: ii');
 * format (new Date (1234324234), 'YYYY year mm month dd day HH: ii')
 * @param {any} time
 * @param {string} formatStr
 * @returns {string} time
 *
 */
export default function formatI18n(time: any, formatStr = 'YYYY-MM-DD HH:mm:ss'): string {
  const date = new Date(Number(time)).getTime();
  return unitedTimezone.dateFormat({
    timestamp: date,
    formatStr,
  });
}
