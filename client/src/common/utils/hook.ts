import { useEffect, useRef } from 'react';
import { shared } from '@ies/unified_communications_sdk';
export const useDidMount = fn => useEffect(() => fn && fn(), []);

export interface AccessParty {
  label: string;
  value: string;
}

export const useDidUpdate = (fn, conditions) => {
  const didMoutRef = useRef(false);
  useEffect(() => {
    if (!didMoutRef.current) {
      didMoutRef.current = true;
      return;
    }
    // Cleanup effects when fn returns a function
    return fn && fn();
  }, conditions);
};

export const useWillUnmount = fn => useEffect(() => () => fn && fn(), []);

export const getAccessPartyName = (): string => {
  let accessPartyName = '';
  const partylist = shared.getAccessPartyList() || [];
  const partyId = shared.getAccessPartyId() || '';
  (partylist[0]?.SubAccessParty || []).forEach(item => {
    if (partyId === item?.Id) {
      accessPartyName = item.Name;
    }
  });
  return accessPartyName;
};

export const getAllAccessPartyList = () => {
  const list: AccessParty[] = [];
  const accessPartyList = shared?.getAccessPartyList() || [];
  accessPartyList.forEach(o =>
    (o?.SubAccessParty || []).forEach(k =>
      list.push({
        label: k.Name,
        value: k.Id
      }),
    ),
  );
  return list;
};
