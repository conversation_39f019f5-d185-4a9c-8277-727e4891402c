import { errorReporting } from './errorReporting';

export const safeJSONParse = (v: unknown, defaultValue?: any): any => {
  if (v && typeof v === 'string') {
    try {
      return JSON.parse(v);
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'safeJSONParse' });
      return defaultValue ?? null;
    }
  }
  return defaultValue ?? null;
};
export function isJSON(str: string): boolean {
  if (typeof str === 'string') {
    try {
      const newStr = safeJSONParse(str);
      if (newStr !== str) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      errorReporting({ error, type: 'callback_name', name: 'isJSON' });
      return false;
    }
  }
}
