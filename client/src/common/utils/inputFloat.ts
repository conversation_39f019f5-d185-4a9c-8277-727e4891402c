export function formatNum(num: string): string {
  const arrayNum = num.toString().split('.');
  // Only one bit (integer)
  if (arrayNum.length === 1) {
    return `${num}.00`;
  }
  if (arrayNum.length > 1) {
    // To the right of the decimal point, if less than two digits, add a 0
    if (arrayNum[1].length < 2) {
      return `${num.toString()}0`;
    }
    return typeof num === 'number' ? String(num) : num;
  }
}
