/**
 * Minutes to timestamp conversion in numeric representation
 * 123 - > 02:03:00
 */
export function numberToTime(value: number | string) {
  if (typeof value !== 'string' && typeof value !== 'number') {
    return '';
  }
  value = Number(value) || 0;
  const hour = String(Math.floor(value / 60)).padStart(2, '0');
  const minute = String(value % 60).padStart(2, '0');

  return `${hour}:${minute}:00`;
}

/**
 * Conversion of timestamp or date to minutes represented by numbers
 *  02:03:00 - > 123
 * Date - > 02:03:00
 */
export function timeToNumber(value: Date | string) {
  if (!value) {
    return 0;
  }
  let hour = 0;
  let minute = 0;
  if (typeof value === 'string') {
    [hour, minute] = value.split(':').map(n => Number(n));
  } else if (value instanceof Date) {
    hour = value.getHours();
    minute = value.getMinutes();
  } else {
    // pass
  }
  return hour * 60 + minute;
}


// Processing of timestamps in different time zones
export function getGLWZTime(time: string, timeZone: number, type = 'ms'): number {
  const localtime = time ? new Date(time) : new Date();
  // Take the number of local milliseconds
  const localmesc = new Date(time).getTime();
  // Take the number of milliseconds off the local time zone from the time zone of Greenwich
  const localOffset = localtime.getTimezoneOffset() * 60000;
  // Reverse to get GMT
  const utc = -localOffset + localmesc;
  // Get the specified time zone time
  const calctime = utc - 3600000 * timeZone;
  if (type === 's') {
    return calctime / 1000;
  }
  return calctime;
}

export function getGLWZToLocalTime(time: string | number, timeZone = 0, type = 'ms'): Date | number {
  const localtime = time ? new Date(time) : new Date();
  // Take the number of local milliseconds
  // console.log(time, localtime, 'time');
  const localmesc = new Date(time).getTime();
  // Take the number of milliseconds off the local time zone from the time zone of Greenwich
  const localOffset = localtime.getTimezoneOffset() * 60000;
  // Get local time
  const utc = localOffset + localmesc;
  // Get the specified time zone time
  const calctime = utc + 3600000 * timeZone;
  if (type === 's') {
    return calctime;
  }
  return new Date(calctime);

}