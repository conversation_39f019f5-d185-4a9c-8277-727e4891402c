import { I18n } from '@ies/starling_intl';
import dayjs from 'dayjs';
import { errorReporting } from './errorReporting';
function addZero(time: any) {
  time = String(time);
  return `0${time}`.substr(-2);
}

/**
 * Format event
 * time can be a timestamp unit ms or a new Date object
 * formatStr: YYYY-mm-dd HH: ii ss
 *  week: W
 * @example
 * format (123432434234, 'YYYY mm month dd day HH: ii');
 * format (new Date (1234324234), 'YYYYY mm month dd day HH: ii')
 * @param {any} time
 * @param {string} formatStr
 * @returns {string} time
 *
 */
export default function format(time: any, formatStr: string) {
  const date = new Date(Number(time));
  // Format time
  const arrWeek = [
    I18n.t('day', {}, '日'),
    I18n.t('one', {}, '一'),
    I18n.t('two', {}, '二'),
    I18n.t('three', {}, '三'),
    I18n.t('four', {}, '四'),
    I18n.t('five', {}, '五'),
    I18n.t('six', {}, '六'),
  ];
  const str = formatStr
    .replace(/yyyy|YYYY/, String(date.getFullYear()))
    .replace(/yy|YY/, addZero(date.getFullYear() % 100))
    .replace(/mm|MM/, addZero(date.getMonth() + 1))
    .replace(/m|M/g, String(date.getMonth() + 1))
    .replace(/dd|DD/, addZero(date.getDate()))
    .replace(/d|D/g, String(date.getDate()))
    .replace(/hh|HH/, addZero(date.getHours()))
    .replace(/h|H/g, String(date.getHours()))
    .replace(/ii|II/, addZero(date.getMinutes()))
    .replace(/i|I/g, String(date.getMinutes()))
    .replace(/ss|SS/, addZero(date.getSeconds()))
    .replace(/s|S/g, String(date.getSeconds()))
    .replace(/w/g, String(date.getDay()))
    .replace(/W/g, arrWeek[date.getDay()]);
  return str;
}

export const timeToMillisecond = (time: Date[] | string[]): string[] => {
  try {
    if (typeof time[0] === 'string' && typeof time[1] === 'string') {
      const defaultStartTime = time?.[0]?.split(':');
      const defaultEndTime = time?.[1]?.split(':');
      const newDefaultStartTime =
        Math.floor(Number(defaultStartTime[0])) * 60 * 60 * 1000 +
        Math.floor(Number(defaultStartTime[1])) * 60 * 1000 +
        Math.floor(Number(0)) * 1000;
      const newDefaultEndtTime =
        Math.floor(Number(defaultEndTime[0])) * 60 * 60 * 1000 +
        Math.floor(Number(defaultEndTime[1])) * 60 * 1000 +
        Math.floor(Number(59)) * 1000;
      return [`${newDefaultStartTime}`, `${newDefaultEndtTime}`];
    } else {
      const startTime =
        dayjs(time?.[0] || '')
          .format('HH:mm')
          ?.split(':') || '';
      const endTime =
        dayjs(time?.[1] || '')
          .format('HH:mm')
          ?.split(':') || '';
      const newStartTime =
        Math.floor(Number(startTime[0]) || 0) * 60 * 60 * 1000 +
        Math.floor(Number(startTime[1]) || 0) * 60 * 1000 +
        Math.floor(Number(0)) * 1000;
      const newEndtTime =
        Math.floor(Number(endTime[0])) * 60 * 60 * 1000 +
        Math.floor(Number(endTime[1])) * 60 * 1000 +
        Math.floor(Number(59)) * 1000;

      return [`${newStartTime}`, `${newEndtTime}`];
    }
  } catch (error) {
    errorReporting({ error, type: 'callback_name', name: 'timeToMillisecond' });
    return ['', ''];
  }
};
export const millisecondToTime = (milliseconds: string[]): string[] => {
  try {
    if (typeof milliseconds?.[0] === 'string' && milliseconds?.[0]?.includes(':')) {
      return milliseconds;
    }
    const newMilliseconds = milliseconds?.map(item => {
      const hours = Math.floor(Number(item) / (1000 * 60 * 60)); // count hours
      let minutes = String(Math.floor((Number(item) % (1000 * 60 * 60)) / (1000 * 60))); // Count minutes
      if (Number(minutes) < 10) {
        minutes = minutes?.padStart(2, '0');
      }
      return `${hours}:${minutes}`;
    });
    return newMilliseconds;
  } catch (error) {
    errorReporting({ error, type: 'callback_name', name: 'millisecondToTime' });
    return ['00:00', '00:00'];
  }
};
