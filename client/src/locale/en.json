{"\"{displayname}\"_rule_failed_to_disable": "Failed to disable rule「{displayName}」", "\"{displayname}\"_rule_failed_to_enable": "Failed to enable rule 「{displayName}」", "_please_check_the_skill_group_and_routing_condition_values_": "\"Please check the skill group and routing condition values\"", "_{placeholder1}__rule_disabled_failed": "\"{placeholder1}\" rule disabled failed", "_{placeholder1}__rule_enabled_failed": "\"{placeholder1}\" rule enabled failed", "abandon_editing__{placeholder1}": "Abandon editing \"{placeholder1}\"", "abandon_editing__{placeholder1}_": "Abandon editing \"{placeholder1}\"", "abandon_editing_of__{placeholder1}_?": "Abandon editing of \"{placeholder1}\"?", "abandon_editing_of__{titlenameedit}_": "A<PERSON>on editing of \"{titleNameEdit}\"", "abandon_new_\"": "Abandon creating「", "abandon_new__{titlenamecreate}_": "Abandon new \"{titleNameCreate}\"", "abandon_the_current_operation?": "Abandon the current operation?", "access_party_shunt": "Access party shunt", "access_party_shunt_rule": "Access party shunt rule", "add_binding_relationship": "Add bind relationship", "add_problem_classification": "Add problem classification", "add_routing_rules": "Add routing rules", "add_sampling_rules": "Add sampling rules", "add_to_pre_release_version": "Add to pre-release version", "add_trigger_condition": "Add trigger condition", "added_rules": "Added rules", "adding_rule_relationships": "Adding rule relationships", "adjust_judgment_order": "Adjust rule judgment order", "adjust_routing_order": "Reorder", "adjust_the_work_order_routing_judgment_order": "Adjust the judgment order of ticket routing rules", "adjusting_order": "Reordering", "after_selecting_the_need__if_the_information_hits_the_major_customer_complaint_m_8ab1920e9dda50e0acd8f5726e2f80ed": "After selecting \"Yes\", if the information hits the major customer complaint model, it will be directly circulated to the major customer service queue. But settings in \"manual routing rules\" are needed.", "already_the_bottom_rule": "Already the bottom rule", "already_top_rule": "Already top rule", "and": "and ", "and_2": "and", "and_when": "and when", "assign_skill_groups": "Assign skill groups", "back_to_front_end_problem_classification": "Back to front-end problem classification", "back_to_the_client_issue_category_card": "Back to the question card classification for clients", "binding_relationship": "Bind relationship", "bottom": "Bottom", "bottom_rules": "Bottom rules", "bottom_successful": "Bottom successful", "business": "Business", "c": "Edit \"{placeholder0}\"?", "can_configure_the_routing_rules_under_the_current_service__and_judge_them_in_seq_e7a942e65e36a8bb2b48179ba70198dd": "Routing rules for the current business can be configured, by which the session content is determined in number order, where rules of the skill group for fallback are the default configuration of the system.", "cancel": "Cancel", "card_name": "Card name", "card_question_name": "Card question", "card_unique_name_identification": "Unique name identification of cards", "chinese": "Chinese", "circulation_skill_group": "Circulation Skill Group", "client_issue_classification_card": "Client question classification card", "configuration_10": "Configuration 10", "configuration_11": "Configuration 11", "configuration_12": "Configuration 12", "configuration_14": "Configuration 14", "configuration_15": "Configuration 15", "configuration_17": "Configuration 17", "configuration_18": "Configuration 18", "configuration_19": "Configuration 19", "configuration_2": "Configuration 2", "configuration_20": "Configuration 20", "configuration_21": "Configuration 21", "configuration_23": "Configuration 23", "configuration_25": "Configuration 25", "configuration_26": "Configuration 26", "configuration_27": "Configuration 27", "configuration_28": "Configuration 28", "configuration_30": "Configuration 30", "configuration_delete_failed": "Failed to delete the configuration", "configuration_deleted": "Configuration deleted", "configuration_disabled": "Configuration disabled", "configuration_disabled_failed": "Failed to disable the configuration", "configuration_eight": "Configuration 8", "configuration_enabled": "Configuration enabled", "configuration_failed": "Failed to enable configuration", "configuration_five": "Configuration 5", "configuration_four": "Configuration 4", "configuration_nine": "Configuration 9", "configuration_one": "Configuration 1", "configuration_seven": "Configuration 7", "configuration_six": "Configuration 6", "configuration_three": "Configuration 3", "configuration_twenty_four": "Configuration 24", "configuration_twenty_nine": "Configuration 29", "configuration_twenty_two": "Configuration 22", "configuration_xiii": "Configuration 13", "configuration_xvi": "Configuration 16", "configure_the_offline_allocation_rules_under_the_current_access_party__and_judge_96720140bde4d52fb6733136ea186218": "Configure the offline allocation rules under the current business line, and determine the session content by number order.", "configure_the_shunt_rules_of_the_same_data_source_in_different_access_parties__a_861a5e42e067ac5da88827a865e35ee5": "Configure the shunt rules of the same data source in different access parties, and judge them in sequence according to the number order", "configure_the_work_order_allocation_rules_under_the_current_access_party__and_ju_4e9d613c8a0645f0096b8a092cf6ee7c": "Configure the allocation rules for tickets under the current business line, and determine the session content by number order.", "copy": "Copy", "copy_to_pre_release_rules": "Copy to pre-release rules", "create_successfully__return_to_the_rules_page_after_2s": "Create successfully, return to the rules page after 2s", "creator": "Creator", "data_acquisition_failed__please_exit_and_try_again": "Data acquisition failed, please exit and try again", "day": "Day(s)", "decide_which_users_under_which_portal_can_see_and_click_on_the_card__and_the_car_9924ae7b0870ae97ee8b48aac045c6d8": "Decide which users under which portal can see and click on the card, and the cards cannot be repeated", "default_error_message": "Default error information", "delete": "Delete", "deleteCondition": "Delete condition", "deleteValue": "delete value", "delete_\"{placeholder1}\"?": "Delete 「{placeholder1}」?", "delete_the_configuration_\"{placeholder1}\"?": "Delete configuration「{placeholder1}」?", "details_page": "Details page", "disable": "Disable", "disable_rule_\"{displayname}\"?": "Disable rule 「{displayName}」?", "disable_rules": "Disable rules", "disable_the_configuration_\"{placeholder1}\"?": "Disable the configuration「{placeholder1}」?", "display_order": "Display order", "drawing_rules": "Drawing rules", "duplicate_card_names": "Repeated card name", "duplicate_routing_rule_names": " Repeated routing rule name", "edit": "Edit", "edit_\"{placeholder0}\"?": "{placeholder0} \"edit?", "editing_rules": "Editing rules", "effective_status": "Effective status", "empty": "empty", "enable": "Enable", "ended_in": "ended at", "enter_a_maximum_of_{max}_characters": "Maximum: {max} characters", "enter_card_name": "Enter card name", "enter_routing_rule_name": "Enter routing rule name", "enter_the_question_text": "Enter your question here", "entrance": "Entrance", "entrance_name": "Entrance name", "equals": "equals to", "expand": "Show more", "failed_to_adjust_the_order": "Reorder failed", "failed_to_save_offline_routing_rules": "Failed to save the offline routing rule", "failed_to_save_work_order_routing_rules": "Failed to save the routing rules for ticket", "feedback_suggestions": "<PERSON><PERSON><PERSON>", "five": "V", "flow_to": "Circulate to", "flow_to_robot": "Circulate to robot", "for_example__you_can_enter__which_question_type_do_you_belong_to?": "Questions shown to clients, for example: Which question type does yours belong to? ", "four": "IV", "from_top_to_bottom_in_order_to_determine_the_routing_rules": "Using routing rules to determine session content in top-down order", "front_end_problem_classification": "Front-end problem classification ", "front_end_problem_classification_configuration": "Configuration of front-end problem classification", "front_end_problem_classification_save_failed": "Failed to save the front-end problem classification", "give_up_\"": "Abandon「", "give_up_creating_a_new__{placeholder1}_?": "Give up creating a new \"{placeholder1}\"?", "go_to_settings": "Go to Settings", "greater_than": "greater than", "greater_than_or_equal_to": "no less than", "historical_version": "Historical version", "home": "Home", "hours": "hour(s)", "if": "If", "if_the_transfer_skill_group_is_queuing_when_the_user_enters_the_line__after_conf_238b2cf70846f004eafe775ba9559df4": "If the skill group for circulation is queuing, the user can be assigned to the skill group for overflow first if configured.", "if_you_do_not_access__then_skip_the_intelligent_customer_service_under_the_trigg_e809e0b670dd0baa5cadbca20ecdfb19": "If you do not access, then skip the customer service robots under the trigger condition and directly go to the customer service staff.", "im_intelligent_routing_rules": "IM intelligent routing rules", "im_manual_routing_rules": "IM manual routing rules", "im_skill_group": "IM skill group", "include": "include", "increaseCondition": "Increase condition", "increaseValue": "increase value", "input_display_guided_speech": "Enter the guiding phrase", "input_problem_classification": "Enter problem classification", "issue_category_deleted": "Problem classification deleted", "issue_category_saved": "Problem classification saved", "jump_will_open_a_new_page__please_come_back_and_try_again_after_setting": "Click the link will open a new page, please come back and try again after configurating the group.", "language": "Language", "less_than": "Less than", "less_than_or_equal_to": "not more than", "logout": "Logout", "meet_all_of_the_following_conditions": "Meet all of the following conditions", "meet_any_of_the_following_conditions": "Meet any of the following conditions", "meet_combination_rules": "Meet combination rules", "minutes": "minute(s)", "missed_the_above_rules": "Missed the above rules", "move_down_successfully": "Move down successfully", "move_one_down": "Move one down", "move_one_up": "Move one up", "move_up_successfully": "Move up successfully", "new": "Create", "new_configuration": "Create configuration", "new_routing_rule": "Create a routing rule", "new_shunt_rule": "New shunt rule", "no": "No", "no_2": "No", "no_delete_permission": "No delete permission", "no_editing_permission": "No editing permission", "no_front_end_classification_configuration": "No configuration for front-end classification ", "no_offline_routing_rules": "No offline routing rules.", "no_online_diversion_rules": "No online diversion rules", "no_online_routing_rules": "No online routing rules", "no_permission": "No permission", "no_permission__please_contact_the_administrator_to_add": "No permission. Please contact the administrator.", "no_permission_to_publish_online": "No permission to publish online", "no_pre_release_diversion_rules": "No pre-release diversion rules", "no_pre_release_routing_rules": "No pre-release routing rules", "no_problem_classification": "No problem classification", "no_quality_inspection_routing_rules_for_now": "No QA routing rules", "no_routing_rules": "No routing rules", "no_seats_in_the_skill_group": "No agents in the skill group!", "no_user_information_is_found_or_the_user_has_been_disabled__please_try_again": "No user information is found or the user has been disabled. Please try again.", "no_work_order_routing_rules": "No ticket routing rules", "no_{placeholder1}_rule": "No {placeholder1} rule", "not": "Not", "not_available": "(do/does) not provide", "not_empty": "not empty", "not_equal_to": "not equal(s) to", "not_included": "not include", "note_{placeholder1}_editing_problem": "Note: {placeholder1} editing problem", "offline_routing_rule_name": "Offline routing rule name", "offline_routing_rules": "Offline routing rules", "offline_routing_rules_saved": "Offline routing rule saved", "ok": "OK", "on_the_c_side_page__the_problem_classification_will_be_displayed_through_the_lis_47c5ab1d7591fbffb0fa89527e88eeec": "Customers will see on their page the problem classification displayed from top to bottom.", "once_abandoned__the_data_will_not_be_recovered__please_operate_with_caution": "Be cautious! The data will not be recovered once abandoned.", "once_deleted__the_data_will_not_be_recovered__please_operate_with_caution": "Be cautious! The data will not be recovered once deleted.", "one": "I", "online": "Online", "online_rules": "Online rules", "operation_successful": "Operation successful", "operator": "operator", "or": "or", "or_2": "or", "other_skill_groups": "Other Skill Groups", "overflow_handling_skill_set": "Skill Group for Overflow Handling ", "overflow_skill_group": "Overflow Skill Group", "overflow_to": "Overflow to", "overflow_when_the_number_of_people_in_line_is_greater_than_the_following": "Overflow is triggered when the number of people in line is greater than the following", "overflow_when_the_work_order_to_be_allocated_in_the_group_is_greater_than_the_fo_c6732ac9d2eb7964958a8c3b646df506": "Overflow is triggered when the number of tickets to be assigned in the group is greater than the following number.", "platform_customer_service": "Platform customer service", "please_add_a_question_category": "Please add a problem classification", "please_enter": "Enter", "please_enter_a_routing_rule_name": "Please enter the routing rule name", "please_enter_an_integer": "Please enter an integer", "please_enter_an_integer_greater_than_0": "Please enter an integer greater than 0", "please_enter_the_card_name": "Please enter the card name", "please_enter_the_guided_speech": "Please enter the guiding phrase", "please_enter_the_name_of_the_offline_routing_rule": "Please enter the name of the offline routing rule", "please_enter_the_name_of_the_question": "Please enter a question", "please_enter_the_name_of_the_work_order_routing_rule": "Please enter the name of the ticket routing rule", "please_fill_in_the_binding_relationship": "Fill in the bind relationship", "please_fill_in_the_name_of_the_routing_rule": "Please fill in the name of the routing rule", "please_fill_in_the_routing_rules": "Please fill in the routing rule.", "please_fill_in_the_{typename}_rule": "Please fill in the {typeName} rule", "please_fill_in_{typename}_rule_name": "Please fill in {typeName} rule name", "please_select": "Select", "please_select_assign_skill_group": "Please select a skill group ", "please_select_overflow_skill_group": "Please select an overflow skill group", "please_select_the_entrance": "Please select an entrance", "please_select_the_flow_robot": "Please select a circulation robot", "please_select_the_overflow_handling_skill_group": "Please select a skill group for overflow handling ", "please_select_the_routing_timing": "Please select the routing timing", "please_select_the_salvage_handling_skill_set": "Please select a skill group for reclaiming tickets", "please_select_the_transfer_access_party": "Please select the transfer access party", "please_select_the_transfer_skill_group": "Please select a circulation skill group", "please_select_the_trigger_condition": "Please select the trigger condition", "please_select_whether_overflow_is_supported": "Whether overflow is supported", "please_select_whether_risk_identification_is_required": "Whether risk identification is required", "please_select_whether_to_access_the_intelligent_customer_service_robot": "Whether to access the intelligent customer service robot", "please_select_whether_to_support_reclaim": "Whether ticket reclaim is supported", "pre_release_version": "Pre-release version", "previous_page": "Previous page", "problem_classification": "Problem classification", "problem_classification_configuration": "Problem classification configuration", "problem_classification_delete_failed": "Problem classification deletion failed", "problem_classification_display": "Question classification display", "problem_classification_failed_to_save": "Failed to save the problem classification", "problem_configuration": "Problem configuration", "production_environment": "Production environment", "provide": "provide(s)", "put_it_away": "Show Less", "qc_routing_rules_have_been_saved": "QA routing rules saved", "quality_inspection_routing_rules": "QA routing rules", "quality_inspection_routing_rules_save_failed": "Failed to save QA routing rules", "reclaimed_to": "Reclaimed to", "recovery_when_the_following_conditions_are_met": "Re-claim tickets when the following conditions are met", "recycling_skill_set": "Ticket Reclaim Skill Group", "release_online": "Release online", "replace_to_pre_release_version": "Replace to pre-release version", "return_to_draw_rule": "Return to draw rule", "return_to_the_list_of_im_intelligent_routing_rules": "Return to the list of IM intelligent routing rules", "return_to_the_list_of_im_manual_routing_rules": "Return to the list of IM manual routing rules", "return_to_the_list_of_quality_control_routing_rules": "Return to the list of quality control routing rules", "return_to_the_list_of_routing_rules": "Back to routing rules list", "return_to_the_list_of_work_order_routing_rules": "Return to the list of work order routing rules", "return_to_the_list_of_{routetitle}": "Back to {routeTitle} list", "return_to_work_order_routing_rules_home_page": "Return to the home page of routing rules for tickets ", "returns_a_list_of_offline_routing_rules": "Returns a list of offline routing rules", "returns_the_list_of_access_shunt_rules": "Returns the list of access shunt rules", "route_ Starling_12": "Whether to enable shunt", "route_Starling_1": "Add a Condition-group", "route_Starling_10": "Rule group", "route_Starling_12": "Are you sure you want to cancel this modification?", "route_Starling_13": "Be cautious! The data will not be recovered once abandoned.", "route_Starling_14": "add a skill group", "route_Starling_15": "Updating routing rule", "route_Starling_16": "Status change", "route_Starling_17": "Creating and enable routing rule", "route_Starling_18": "Order change", "route_Starling_19": "Creating and disable routing rule", "route_Starling_2": "Operation log ", "route_Starling_20": "Shunt rule change", "route_Starling_21": "Value change", "route_Starling_22": "less", "route_Starling_23": "Conditions change", "route_Starling_24": "Condition relationships change", "route_Starling_25": "Condition-groups relationships change", "route_Starling_26": "Incoming flow, diverted to", "route_Starling_27": "more", "route_Starling_29": "Rule enabled", "route_Starling_3": "The sum of the traffic distribution is not 100%, please check and modify", "route_Starling_30": "Rule disabled", "route_Starling_31": "Rule delete", "route_Starling_32": "Release online", "route_Starling_33": "Rules Skillset Confirmation", "route_Starling_34": "It is currently a ttp routing rule. It is detected that the skill group", "route_Starling_35": "does not correspond to the regional skill group. Do you need to modify the rule?", "route_Starling_36": "Revise", "route_Starling_37": "It is currently a row routing rule. It is detected that the skill group", "route_Starling_4": "Current skill group", "route_Starling_5": "No agents in the skill group! Click on the group name to set.", "route_Starling_6": "These agents in the current group with ticket auto-assignment limit set to 0, please click group to set the volume for team members", "route_Starling_7": "The skill group has not activated ticket auto-assignment! Click on the group name to set.", "route_Starling_8": "No operation log yet", "route_Starling_9": "Add a rule", "routing": "Routing", "routing_management": "Routing management", "routing_rule_deletion_failed": "Failed to delete the routing rule", "routing_rule_name": "Routing rule name", "routing_rule_saving_failed": "Failed to save the offline routing rule", "routing_rule_validation_failed": "Routing rule validation failed", "routing_rules": "Routing rules", "routing_rules_can_be_configured_to_customize_the_flow_trigger_process": "Customized circulation trigger processes can be configured by routing rules", "routing_rules_deleted": "Routing rule deleted", "routing_rules_saved": "Routing rule saved", "routing_timing": "Routing timing", "routing_timing_is": "When routing timing is", "rule_group_released_successfully": "Rule group released successfully", "rule_group_replication_successful": "Rule group replication successful", "save": "Save", "search_route_name": "Search route name", "search_routing_rules": "Search routing rules", "search_triage_rules": "Search triage rules", "search_{placeholder1}_rule": "Search {placeholder1} rule", "select_flow_robot": "Please select the circulation robot", "select_limit": "Select limit", "select_skill_group": "Select skill group", "selection_criteria": "Select conditions", "show_client_questions__for_example__which_question_type_do_you_belong_to?": "Questions displayed in the client side, e.g. which problem classification do you fall into?", "show_guided_speech_skills": "Display guiding phrase", "shunt": "<PERSON><PERSON>", "six": "VI", "skill_group_matching": "Skill group matching", "skill_sets_are_not_set_up_for_automatic_order_splitting": "The skill group has not activated ticket auto-assignment!", "skills_group_personnel_take_orders_parameter_is_0_hint": "Skill group personnel maximum ticket assignment is 0!", "smart_customer_service": "Intelligent customer service", "started_with": "started at", "status": "Status", "still_save": "Still save", "telephone_skills_group": "Phone Call Skill Group ", "test_version_name": "Test version name", "the_\"{displayname}\"_rule_is_disabled": "Rule 「{displayName}」 disabled", "the_\"{displayname}\"_rule_is_enabled": "Rule 「{displayName}」 enabled", "the_\"{placeholder1}\"_rule_deletion_failed": "Failed to delete rule 「{placeholder1}」", "the_\"{placeholder1}\"_rule_has_been_deleted": "Rule 「{placeholder1}」 deleted", "the__{placeholder1}__rule_is_disabled": "The \"{placeholder1}\" rule is disabled", "the__{placeholder1}__rule_is_enabled": "The \"{placeholder1}\" rule is enabled", "the_currently_set_skill_group_is_not_automatically_divided_into_single_groups__p": "The current skill group did not activate ticket auto-assignment, please click this link to set the group attributes", "the_front_end_problem_category_has_been_saved": "Front-end problem classification saved", "the_number_of_people_in_line_is_greater_than": "When the number of people in line is greater than", "the_operation_was_successful__the_configuration_is_already_in_the_first_place": "Successful operation. The configuration is not at the top.", "the_operation_was_successful__the_configuration_is_at_the_end": "Successful operation. The configuration is now at the bottom.", "the_routing_rules_under_the_current_entry_can_be_configured__and_they_can_be_jud_d4688e468145de95f4ad81abbf8efb05": "Routing rules under the current entry can be configured, by which the session content is determined by number order, where the skill group for fallback is the default configuration of the system.", "the_system_prohibits_individual_access": "Individual access is not allowed", "the_traffic_diversion_from_the_access_side_directly_affects_online_data_please_be_careful_when_performing_operations": "Access party diversion directly affects online data, please be careful in operation!", "the_valid_number_is_{min}~_{max}__please_enter_the_correct_value": "Valid only from {min} to {max}. Please re-enter.", "there_are_no_seats_in_the_current_group__please_click_this_link_to_increase_the_": "There are no agents in the current group, please click this link to add the staff", "there_is_{showagent}_in_the_current_group_with_the_odd_number_set_to_0__please_c": "These agents {showAgent} in the current group with ticket auto-assignment limit set to 0, please click this link to set the volume for team members", "this_field_is_required": "This field is required", "this_page_is_not_accessible_separately__please_visit_the_unified_workbench": "This page is not accessible individually. Please visit the Unified Workbench.", "three": "III", "through_the_\"salvage\"_function__some_special_work_orders_in_processing_are_redis_1a7a0fe5d1de5eb580bda4dfb0307604": "Through the \"reclaim\" function, some tickets under special processing are assigned to agents who can handle them in time to achieve compliance solutions.", "time": ",", "time_2": "Time", "time_is_required": "Time is required", "to_increase": "To increase", "top": "Top", "top_success": "Top success", "transfer_access_party": "Transfer access party", "trigger_condition": "Trigger condition", "two": "II", "unknown_cause__service_exception": "Exception services for some unknown reason", "unknown_rule_type": "Unknown rule type", "unmodified_rule": "Unmodified rule", "unnamed_draw_rule": "Unnamed draw rule", "unnamed_routing_rules": "Unnamed routing rules", "unnamed_{typename}_rule": "Unnamed {typeName} rule", "update": "Update", "update_person": "Update person", "update_successful__return_to_the_rules_page_after_2s": "Update successful, return to the rules page after 2s", "version_name": "Version name", "version_number": "Version number", "view": "View", "what_is_overflow?": "What is overflow?", "what_is_salvage?": "What is reclaiming tickets?", "when_disabled__the_problem_classification_will_not_take_effect": "The problem classification will not take effect when disabled.", "when_disabled__the_routing_rule_will_not_take_effect": "The routing rule will not take effect when disabled.", "when_disabled__the_{placeholder1}_rule_will_not_take_effect": "When disabled, the {placeholder1} rule will not take effect", "when_disabled__this_configuration_will_not_be_displayed_on_the_c_side": "This configuration will not be displayed to customers when disabled.", "when_no_draw_rule_is_currently_matched": "When no draw rule is currently matched", "when_no_im_human_customer_service_matching_rules_are_obtained": "When no matching rules for IM human customer service are obtained", "when_no_im_smart_customer_service_matching_rules_are_obtained": "When no matching rules for IM intelligent customer service are obtained", "when_no_work_order_matching_rules_are_obtained": "When no ticket matching rules are obtained", "when_people__overflow_to": ", overflow to", "when_the_user_problem_classification_cannot_be_obtained__the_problem_classificat_4300c4aeb7931ac6d6d8691e4e0c2b4c": "When a user's problem classification cannot be obtained, it can be configured at the front end as guidance so that the user can select information and be matched to the corresponding skill group more efficiently.", "when_this_page_title_is_long__it_needs_to_be_omitted": "Omit the page title if it is too long.", "whether_overflow_is_supported": "Whether overflow is supported ", "whether_risk_identification_is_required": "Whether risk identification is required", "whether_to_access_the_intelligent_customer_service_robot": "Whether to access the intelligent customer service robot", "whether_to_support_salvage": "Whether reclaiming tickets is supported", "work_order_routing_rule_name": "Name of the ticket routing rule ", "work_order_routing_rules": "Ticket routing rules", "work_order_routing_rules_saved": "Ticket routing rule saved", "work_order_skill_group": "Ticket Skill Group", "work_orders_to_be_assigned_within_the_group": "Tickets to be assigned within the group", "yes": "Yes", "yyyy_mm_month_dd_day": "YYYY mm month dd day", "{placeholder0}_\"copy?": "Copy \"{placeholder0}\"?", "{placeholder0}_hours_{placeholder2}_minutes": "{placeholder0} hour(s) {placeholder2} minute(s)", "{placeholder0}_updated_to_{placeholder2}": "{placeholder0} updates at {placeholder2}", "{placeholder1}_rule_judgment_in_top_down_order": "{placeholder1} rule judgment in top-down order", "{typename}_rule_name": "{typeName} Rule name"}