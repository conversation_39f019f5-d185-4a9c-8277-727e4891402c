{"\"{displayname}\"_rule_failed_to_disable": "「{displayName}」规则禁用失败", "\"{displayname}\"_rule_failed_to_enable": "「{displayName}」规则启用失败", "_please_check_the_skill_group_and_routing_condition_values_": "「请检查技能组与路由条件值」", "_{placeholder1}__rule_disabled_failed": "「{placeholder1}」规则禁用失败", "_{placeholder1}__rule_enabled_failed": "「{placeholder1}」规则启用失败", "abandon_editing__{placeholder1}": "放弃编辑「{placeholder1}", "abandon_editing__{placeholder1}_": "放弃编辑「{placeholder1}」", "abandon_editing_of__{placeholder1}_?": "放弃「{placeholder1}」的编辑？", "abandon_editing_of__{titlenameedit}_": "放弃「{titleNameEdit}」的编辑", "abandon_new_\"": "放弃新建「", "abandon_new__{titlenamecreate}_": "放弃新建「{titleNameCreate}」", "abandon_the_current_operation?": "放弃当前操作？", "access_party_shunt": "接入方分流", "access_party_shunt_rule": "接入方分流规则", "add_binding_relationship": "添加绑定关系", "add_problem_classification": "添加问题分类", "add_routing_rules": "新增路由规则", "add_sampling_rules": "添加抽检规则", "add_to_pre_release_version": "添加到预发布版本", "add_trigger_condition": "添加触发条件", "added_rules": "新增规则", "adding_rule_relationships": "添加规则关系", "adjust_judgment_order": "调整判断顺序", "adjust_routing_order": "调整路由顺序", "adjust_the_work_order_routing_judgment_order": "调整工单路由判断顺序", "adjusting_order": "正在调整顺序", "after_selecting_the_need__if_the_information_hits_the_major_customer_complaint_m_8ab1920e9dda50e0acd8f5726e2f80ed": "选择需要后，若信息命中重大客诉模型，则直接流转重大客服队列，但需要在“人工路由规则”里设置。", "already_the_bottom_rule": "已经是底部规则", "already_top_rule": "已经是顶部规则", "and": "且", "and_2": "并且", "and_when": "且当", "assign_skill_groups": "分配技能组", "back_to_front_end_problem_classification": "返回前端问题分类", "back_to_the_client_issue_category_card": "返回客户端问题分类卡片", "binding_relationship": "绑定关系", "bottom": "置底", "bottom_rules": "兜底规则", "bottom_successful": "置底成功", "business": "业务", "c": "{placeholder0}」的编辑？", "can_configure_the_routing_rules_under_the_current_service__and_judge_them_in_seq_e7a942e65e36a8bb2b48179ba70198dd": "可配置当前业务下路由规则，按照编号顺序依次判断，其中兜底技能组规则为系统默认配置", "cancel": "取消", "card_name": "卡片名称", "card_question_name": "卡片问题名称", "card_unique_name_identification": "卡片唯一名称标识", "chinese": "中文", "circulation_skill_group": "流转技能组", "client_issue_classification_card": "客户端问题分类卡片", "configuration_10": "配置十", "configuration_11": "配置十一", "configuration_12": "配置十二", "configuration_14": "配置十四", "configuration_15": "配置十五", "configuration_17": "配置十七", "configuration_18": "配置十八", "configuration_19": "配置十九", "configuration_2": "配置二", "configuration_20": "配置二十", "configuration_21": "配置二十一", "configuration_23": "配置二十三", "configuration_25": "配置二十五", "configuration_26": "配置二十六", "configuration_27": "配置二十七", "configuration_28": "配置二十八", "configuration_30": "配置三十", "configuration_delete_failed": "配置删除失败", "configuration_deleted": "配置已删除", "configuration_disabled": "配置已禁用", "configuration_disabled_failed": "配置禁用失败", "configuration_eight": "配置八", "configuration_enabled": "配置已启用", "configuration_failed": "配置启用失败", "configuration_five": "配置五", "configuration_four": "配置四", "configuration_nine": "配置九", "configuration_one": "配置一", "configuration_seven": "配置七", "configuration_six": "配置六", "configuration_three": "配置三", "configuration_twenty_four": "配置二十四", "configuration_twenty_nine": "配置二十九", "configuration_twenty_two": "配置二十二", "configuration_xiii": "配置十三", "configuration_xvi": "配置十六", "configure_the_offline_allocation_rules_under_the_current_access_party__and_judge_96720140bde4d52fb6733136ea186218": "配置当前接入方下的离线分配规则，按照编号顺序依次判断", "configure_the_shunt_rules_of_the_same_data_source_in_different_access_parties__a_861a5e42e067ac5da88827a865e35ee5": "配置同一份数据源在不同接入方的分流规则，按照编号顺序依次判断", "configure_the_work_order_allocation_rules_under_the_current_access_party__and_ju_4e9d613c8a0645f0096b8a092cf6ee7c": "配置当前接入方下的工单分配规则，按照编号顺序依次判断", "copy": "复制", "copy_to_pre_release_rules": "复制到预发布规则", "create_successfully__return_to_the_rules_page_after_2s": "创建成功,2s后返回规则页面", "creator": "创建人", "data_acquisition_failed__please_exit_and_try_again": "数据获取失败，请退出后重试", "day": "日", "decide_which_users_under_which_portal_can_see_and_click_on_the_card__and_the_car_9924ae7b0870ae97ee8b48aac045c6d8": "决定哪个入口下的哪些用户可看到并点击该卡片，卡片之间不可重复", "default_error_message": "默认的错误信息", "delete": "删除", "deleteCondition": "删除规则条件", "deleteValue": "删除规则条件值", "delete_\"{placeholder1}\"?": "删除「{placeholder1}」？", "delete_the_configuration_\"{placeholder1}\"?": "删除配置「{placeholder1}」？", "details_page": "详情页", "disable": "禁用", "disable_rule_\"{displayname}\"?": "禁用规则「{displayName}」？", "disable_rules": "禁用规则", "disable_the_configuration_\"{placeholder1}\"?": "禁用配置「{placeholder1}」？", "display_order": "展示顺序", "drawing_rules": "抽单规则", "duplicate_card_names": "卡片名称重复", "duplicate_routing_rule_names": "路由规则名称重复", "edit": "编辑", "edit_\"{placeholder0}\"?": "{placeholder0}」的编辑？", "editing_rules": "编辑规则", "effective_status": "生效状态", "empty": "为空", "enable": "启用", "ended_in": "结束于", "enter_a_maximum_of_{max}_characters": "最多输入 {max} 个字符", "enter_card_name": "输入卡片名称", "enter_routing_rule_name": "输入路由规则名称", "enter_the_question_text": "输入问题文案", "entrance": "入口", "entrance_name": "入口名称", "equals": "等于", "expand": "展开", "failed_to_adjust_the_order": "调整顺序失败", "failed_to_save_offline_routing_rules": "离线路由规则保存失败", "failed_to_save_work_order_routing_rules": "工单路由规则保存失败", "feedback_suggestions": "反馈建议", "five": "五", "flow_to": "流转至", "flow_to_robot": "流转至机器人", "for_example__you_can_enter__which_question_type_do_you_belong_to?": "展示在 C 端用户侧的问题，比如可以输入：请问您属于哪个问题类型呢？", "four": "四", "from_top_to_bottom_in_order_to_determine_the_routing_rules": "从上而下的顺序依次进行路由规则判断", "front_end_problem_classification": "前端问题分类", "front_end_problem_classification_configuration": "前端问题分类配置", "front_end_problem_classification_save_failed": "前端问题分类保存失败", "give_up_\"": "放弃「", "give_up_creating_a_new__{placeholder1}_?": "放弃新建「{placeholder1}」？", "go_to_settings": "去设置", "greater_than": "大于", "greater_than_or_equal_to": "大于等于", "historical_version": "历史版本", "home": "首页", "hours": "小时", "if": "如果", "if_the_transfer_skill_group_is_queuing_when_the_user_enters_the_line__after_conf_238b2cf70846f004eafe775ba9559df4": "若用户进线时的流转技能组正在排队，配置溢出技能组后，可将用户优先分配至溢出技能组。", "if_you_do_not_access__then_skip_the_intelligent_customer_service_under_the_trigg_e809e0b670dd0baa5cadbca20ecdfb19": "若不接入，那么在该触发条件下跳过智能客服，直接进入人工客服", "im_intelligent_routing_rules": "IM智能路由规则", "im_manual_routing_rules": "IM人工路由规则", "im_skill_group": "IM 技能组", "include": "包含", "increaseCondition": "增加规则条件", "increaseValue": "增加规则条件值", "input_display_guided_speech": "输入展示引导话术", "input_problem_classification": "输入问题分类", "issue_category_deleted": "问题分类已删除", "issue_category_saved": "问题分类已保存", "jump_will_open_a_new_page__please_come_back_and_try_again_after_setting": "跳转将打开新页面，设置后请回来重试", "language": "语言", "less_than": "小于", "less_than_or_equal_to": "小于等于", "logout": "登出", "meet_all_of_the_following_conditions": "满足下列所有条件", "meet_any_of_the_following_conditions": "满足下列任一条件", "meet_combination_rules": "满足组合规则", "minutes": "分钟", "missed_the_above_rules": "未命中以上规则", "move_down_successfully": "下移成功", "move_one_down": "向下挪动一位", "move_one_up": "向上挪动一位", "move_up_successfully": "上移成功", "new": "新建", "new_configuration": "新建配置", "new_routing_rule": "新建路由规则", "new_shunt_rule": "新建分流规则", "no": "否", "no_2": "无", "no_delete_permission": "没有删除权限", "no_editing_permission": "没有编辑权限", "no_front_end_classification_configuration": "暂无前端分类配置", "no_offline_routing_rules": "暂无离线路由规则", "no_online_diversion_rules": "暂无线上分流规则", "no_online_routing_rules": "暂无线上路由规则", "no_permission": "无权限", "no_permission__please_contact_the_administrator_to_add": "无权限，请联系管理员添加", "no_permission_to_publish_online": "没有发布上线权限", "no_pre_release_diversion_rules": "暂无预发布分流规则", "no_pre_release_routing_rules": "暂无预发布路由规则", "no_problem_classification": "暂无问题分类", "no_quality_inspection_routing_rules_for_now": "暂无质检路由规则", "no_routing_rules": "暂无路由规则", "no_seats_in_the_skill_group": "技能组内无坐席", "no_user_information_is_found_or_the_user_has_been_disabled__please_try_again": "未查询到用户信息或用户已被禁用，请重试", "no_work_order_routing_rules": "暂无工单路由规则", "no_{placeholder1}_rule": "暂无{placeholder1}规则", "not": "非", "not_available": "不提供", "not_empty": "不为空", "not_equal_to": "不等于", "not_included": "不包含", "note_{placeholder1}_editing_problem": "注意：{placeholder1}的编辑问题", "offline_routing_rule_name": "离线路由规则名称", "offline_routing_rules": "离线路由规则", "offline_routing_rules_saved": "离线路由规则已保存", "ok": "确定", "on_the_c_side_page__the_problem_classification_will_be_displayed_through_the_lis_47c5ab1d7591fbffb0fa89527e88eeec": "在 C 端页面会通过从上到下的列表顺序去展示问题分类", "once_abandoned__the_data_will_not_be_recovered__please_operate_with_caution": "一旦放弃，数据将无法恢复，请谨慎操作", "once_deleted__the_data_will_not_be_recovered__please_operate_with_caution": "一旦删除，数据将无法恢复，请谨慎操作", "one": "一", "online": "线上", "online_rules": "线上规则", "operation_successful": "操作成功", "operator": "运算符", "or": "或", "or_2": "或者", "other_skill_groups": "其他技能组", "overflow_handling_skill_set": "溢出处理技能组", "overflow_skill_group": "溢出技能组", "overflow_to": "溢出至", "overflow_when_the_number_of_people_in_line_is_greater_than_the_following": "排队人数大于以下人数时溢出", "overflow_when_the_work_order_to_be_allocated_in_the_group_is_greater_than_the_fo_c6732ac9d2eb7964958a8c3b646df506": "组内待分配工单大于以下数字时溢出", "platform_customer_service": "平台客服", "please_add_a_question_category": "请添加问题分类", "please_enter": "请输入", "please_enter_a_routing_rule_name": "请输入路由规则名称", "please_enter_an_integer": "请输入整数", "please_enter_an_integer_greater_than_0": "请输入大于0的整数", "please_enter_the_card_name": "请输入卡片名称", "please_enter_the_guided_speech": "请输入引导话术", "please_enter_the_name_of_the_offline_routing_rule": "请输入离线路由规则名称", "please_enter_the_name_of_the_question": "请输入问题名称", "please_enter_the_name_of_the_work_order_routing_rule": "请输入工单路由规则名称", "please_fill_in_the_binding_relationship": "请填写绑定关系", "please_fill_in_the_name_of_the_routing_rule": "请填写路由规则名称", "please_fill_in_the_routing_rules": "请填写路由规则", "please_fill_in_the_{typename}_rule": "请填写{typeName}规则", "please_fill_in_{typename}_rule_name": "请填写{typeName}规则名称", "please_select": "请选择", "please_select_assign_skill_group": "请选择分配技能组", "please_select_overflow_skill_group": "请选择溢出技能组", "please_select_the_entrance": "请选择入口", "please_select_the_flow_robot": "请选择流转机器人", "please_select_the_overflow_handling_skill_group": "请选择溢出处理技能组", "please_select_the_routing_timing": "请选择路由时机", "please_select_the_salvage_handling_skill_set": "请选择回捞处理技能组", "please_select_the_transfer_access_party": "请选择流转接入方", "please_select_the_transfer_skill_group": "请选择流转技能组", "please_select_the_trigger_condition": "请选择触发条件", "please_select_whether_overflow_is_supported": "请选择是否支持溢出", "please_select_whether_risk_identification_is_required": "请选择是否需要风险识别", "please_select_whether_to_access_the_intelligent_customer_service_robot": "请选择是否接入智能客服机器人", "please_select_whether_to_support_reclaim": "请选择是否支持回捞", "pre_release_version": "预发布版本", "previous_page": "上一页", "problem_classification": "问题分类", "problem_classification_configuration": "问题分类配置", "problem_classification_delete_failed": "问题分类删除失败", "problem_classification_display": "问题分类展示", "problem_classification_failed_to_save": "问题分类保存失败", "problem_configuration": "问题配置", "production_environment": "生产环境", "provide": "提供", "put_it_away": "收起", "qc_routing_rules_have_been_saved": "质检路由规则已保存", "quality_inspection_routing_rules": "质检路由规则", "quality_inspection_routing_rules_save_failed": "质检路由规则保存失败", "reclaimed_to": "回捞至", "recovery_when_the_following_conditions_are_met": "满足以下条件时回捞", "recycling_skill_set": "回捞处理技能组", "release_online": "发布上线", "replace_to_pre_release_version": "替换到预发布版本", "return_to_draw_rule": "返回抽单规则", "return_to_the_list_of_im_intelligent_routing_rules": "返回IM智能路由规则列表", "return_to_the_list_of_im_manual_routing_rules": "返回IM人工路由规则列表", "return_to_the_list_of_quality_control_routing_rules": "返回质检路由规则列表", "return_to_the_list_of_routing_rules": "返回路由规则列表", "return_to_the_list_of_work_order_routing_rules": "返回工单路由规则列表", "return_to_the_list_of_{routetitle}": "返回{routeTitle}列表", "return_to_work_order_routing_rules_home_page": "返回工单路由规则首页", "returns_a_list_of_offline_routing_rules": "返回离线路由规则列表", "returns_the_list_of_access_shunt_rules": "返回接入方分流规则列表", "route_ Starling_12": "是否启用分流", "route_Starling_1": "添加规则组", "route_Starling_10": "规则组", "route_Starling_12": "请问您是否确认要取消本次修改？", "route_Starling_13": "一旦取消，数据将无法恢复，请谨慎操作", "route_Starling_14": "新增分配技能组", "route_Starling_15": "规则内容变更", "route_Starling_16": "规则状态变更", "route_Starling_17": "创建规则并启用", "route_Starling_18": "规则优先级变更", "route_Starling_19": "创建规则并禁用", "route_Starling_2": "操作日志", "route_Starling_20": "分流规则的技能组/分流比例变更", "route_Starling_21": "规则条件值改变", "route_Starling_22": "折叠", "route_Starling_23": "规则条件改变", "route_Starling_24": "规则间的条件关系改变", "route_Starling_25": "规则组间的关系改变", "route_Starling_26": "进线流量，分流至", "route_Starling_27": "更多", "route_Starling_28": "规则技能组变更", "route_Starling_29": "规则启用", "route_Starling_3": "流量分布总和非100%，请检查并修改", "route_Starling_30": "规则禁用", "route_Starling_31": "规则删除", "route_Starling_32": "发布上线", "route_Starling_33": "规则技能组确认", "route_Starling_34": "当前是ttp路由规则，检测到技能组", "route_Starling_35": "并非对应区域技能组，是否需要修改规则", "route_Starling_36": "修改", "route_Starling_37": "当前是row路由规则，检测到技能组", "route_Starling_4": "当前设置技能组", "route_Starling_5": "无坐席，点击技能组名称跳转设置", "route_Starling_6": "人员接单参数为0，请点击技能组名称跳转设置人员接单数", "route_Starling_7": "未设置自动分单，请点击技能组名称跳转去设置", "route_Starling_8": "暂无操作日志", "route_Starling_9": "添加规则", "routing": "路由", "routing_management": "路由管理", "routing_rule_deletion_failed": "路由规则删除失败", "routing_rule_name": "路由规则名称", "routing_rule_saving_failed": "路由规则保存失败", "routing_rule_validation_failed": "路由规则校验失败", "routing_rules": "路由规则", "routing_rules_can_be_configured_to_customize_the_flow_trigger_process": "路由规则可以配置定制流转触发流程", "routing_rules_deleted": "路由规则已删除", "routing_rules_saved": "路由规则已保存", "routing_timing": "路由时机", "routing_timing_is": "路由时机是", "rule_group_released_successfully": "规则组发布成功", "rule_group_replication_successful": "规则组复制成功", "save": "保存", "search_route_name": "搜索路由名称", "search_routing_rules": "搜索路由规则", "search_triage_rules": "搜索分流规则", "search_{placeholder1}_rule": "搜索{placeholder1}规则", "select_flow_robot": "选择流转机器人", "select_limit": "选择限制", "select_skill_group": "选择技能组", "selection_criteria": "选择条件", "show_client_questions__for_example__which_question_type_do_you_belong_to?": "展示客户端的问题，例如：请问您是属于哪个问题类型呢？", "show_guided_speech_skills": "展示引导话术", "shunt": "分流", "six": "六", "skill_group_matching": "技能组匹配", "skill_sets_are_not_set_up_for_automatic_order_splitting": "技能组未设置自动分单！", "skills_group_personnel_take_orders_parameter_is_0_hint": "技能组人员接单参数为0提示", "smart_customer_service": "智能客服", "started_with": "开始于", "status": "状态", "still_save": "仍保存", "telephone_skills_group": "电话技能组", "test_version_name": "测试版本名称", "the_\"{displayname}\"_rule_is_disabled": "「{displayName}」规则已禁用", "the_\"{displayname}\"_rule_is_enabled": "「{displayName}」规则已启用", "the_\"{placeholder1}\"_rule_deletion_failed": "「{placeholder1}」规则删除失败", "the_\"{placeholder1}\"_rule_has_been_deleted": "「{placeholder1}」规则已删除", "the__{placeholder1}__rule_is_disabled": "「{placeholder1}」规则已禁用", "the__{placeholder1}__rule_is_enabled": "「{placeholder1}」规则已启用", "the_currently_set_skill_group_is_not_automatically_divided_into_single_groups__p": "当前设置的技能组并非自动分单组别，请点击此链接去设置组别属性", "the_front_end_problem_category_has_been_saved": "前端问题分类已保存", "the_number_of_people_in_line_is_greater_than": "排队人数大于", "the_operation_was_successful__the_configuration_is_already_in_the_first_place": "操作成功，该配置已在首位", "the_operation_was_successful__the_configuration_is_at_the_end": "操作成功，该配置已在最后", "the_routing_rules_under_the_current_entry_can_be_configured__and_they_can_be_jud_d4688e468145de95f4ad81abbf8efb05": "可配置当前入口下路由规则，按照编号顺序依次判断，其中兜底技能组为系统默认配置", "the_system_prohibits_individual_access": "系统禁止单独访问", "the_traffic_diversion_from_the_access_side_directly_affects_online_data_please_be_careful_when_performing_operations": "接入方分流直接影响线上数据，操作请务必谨慎！！", "the_valid_number_is_{min}~_{max}__please_enter_the_correct_value": "有效数字为{min}~{max}，请输入正确数值", "there_are_no_seats_in_the_current_group__please_click_this_link_to_increase_the_": "当前组内并无坐席，请点击此链接去增加人员", "there_is_{showagent}_in_the_current_group_with_the_odd_number_set_to_0__please_c": "当前组内有{showAgent}分单数设置为0，请点击此链接去设置组员接单数", "this_field_is_required": "该字段为必填项", "this_page_is_not_accessible_separately__please_visit_the_unified_workbench": "该页面不可单独访问，请访问统一工作台", "three": "三", "through_the_\"salvage\"_function__some_special_work_orders_in_processing_are_redis_1a7a0fe5d1de5eb580bda4dfb0307604": "通过“回捞”功能重新分配部分特殊的处理中工单到能够及时处理的坐席手中，达成合规解决。", "time": "时", "time_2": "时间", "time_is_required": "时间为必填项", "to_increase": "去增加", "top": "置顶", "top_success": "置顶成功", "transfer_access_party": "流转接入方", "trigger_condition": "触发条件", "two": "二", "unknown_cause__service_exception": "未知原因, 服务异常", "unknown_rule_type": "未知规则类型", "unmodified_rule": "未修改规则", "unnamed_draw_rule": "未命名抽单规则", "unnamed_routing_rules": "未命名路由规则", "unnamed_{typename}_rule": "未命名{typeName}规则", "update": "更新", "update_person": "更新人", "update_successful__return_to_the_rules_page_after_2s": "更新成功,2s后返回规则页面", "version_name": "版本名称", "version_number": "版本号", "view": "查看", "what_is_overflow?": "什么是溢出？", "what_is_salvage?": "什么是回捞？", "when_disabled__the_problem_classification_will_not_take_effect": "禁用后，该问题分类将不会生效", "when_disabled__the_routing_rule_will_not_take_effect": "禁用后，该路由规则将不会生效", "when_disabled__the_{placeholder1}_rule_will_not_take_effect": "禁用后，该{placeholder1}规则将不会生效", "when_disabled__this_configuration_will_not_be_displayed_on_the_c_side": "禁用后，该配置将无法在 C 端进行展示", "when_no_draw_rule_is_currently_matched": "当前未匹配到任何抽单规则时", "when_no_im_human_customer_service_matching_rules_are_obtained": "当未获取任何 IM 人工客服匹配规则时", "when_no_im_smart_customer_service_matching_rules_are_obtained": "当未获取任何 IM 智能客服匹配规则时", "when_no_work_order_matching_rules_are_obtained": "当未获取任何工单匹配规则时", "when_people__overflow_to": "人时 溢出至", "when_the_user_problem_classification_cannot_be_obtained__the_problem_classificat_4300c4aeb7931ac6d6d8691e4e0c2b4c": "当获取不到用户问题分类时，可在前端配置问题分类，引导用户进行信息选择，从而更便捷地匹配到相应技能组", "when_this_page_title_is_long__it_needs_to_be_omitted": "当这个页面标题很长时需要省略", "whether_overflow_is_supported": "是否支持溢出", "whether_risk_identification_is_required": "是否需要风险识别", "whether_to_access_the_intelligent_customer_service_robot": "是否接入智能客服机器人", "whether_to_support_salvage": "是否支持回捞", "work_order_routing_rule_name": "工单路由规则名称", "work_order_routing_rules": "工单路由规则", "work_order_routing_rules_saved": "工单路由规则已保存", "work_order_skill_group": "工单技能组", "work_orders_to_be_assigned_within_the_group": "组内待分配工单", "yes": "是", "yyyy_mm_month_dd_day": "YYYY年mm月dd日", "{placeholder0}_\"copy?": "{placeholder0}」的复制？", "{placeholder0}_hours_{placeholder2}_minutes": "{placeholder0} 小时 {placeholder2} 分", "{placeholder0}_updated_to_{placeholder2}": "{placeholder0} 更新于 {placeholder2}", "{placeholder1}_rule_judgment_in_top_down_order": "从上而下的顺序依次进行{placeholder1}规则判断", "{typename}_rule_name": "{typeName}规则名称"}