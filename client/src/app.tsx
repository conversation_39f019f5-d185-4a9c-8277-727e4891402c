import { I18n } from '@ies/starling_intl';
import React, { useEffect, useState } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { Layout } from '@ies/semi-ui-react';
import { shared } from '@ies/unified_communications_sdk';
import { UserContext } from '@context/user';

import AppHeader from '@components/layout/header';
import AppNav from '@components/layout/nav';
import AppRouter from './router';
import withIntl from './sdks/i18n';
import { observer } from 'mobx-react-lite';
import { PATH_PREFIX } from './const';
import './stores/isolateGlobalState';
import { sendFirst } from './sdks/slardar';
import * as styles from './index.scss';
import { useDidMount } from '@common/utils/hook';
import { useStore } from '@stores/index';
import { NoAuth } from './NoAuth';
import AIS from '@ies/ais';
import ErrorBoundary from './components/ErrorBoundary';
import { errorReporting } from './common/utils/errorReporting';

const { Header, Sider, Content } = Layout;

interface APPProps {
  setLocale: () => void;
  locale: string;
  basename?: string;
}

function App(props): React.FunctionComponentElement<APPProps> {
  const supportsHistory = 'pushState' in window.history;
  const { setLocale, locale, basename = '/' } = props;
  const [noAuth, setNoAuth] = useState(false);

  const userStore = useStore('user');
  const global = useStore('global');

  const [user, setUser] = useState(null);

  const hideNav = window.__PROWER_BY_GAR__;

  useDidMount(() => {
    const user = shared.getCoreData();
    // slardar('config', {
    //   slardar_web_id: user.sso.unique_name,
    // });
    // Use slardar to report the first screen and white screen
    sendFirst();
    setUser(shared.getCoreData());
    const unsubscribe = shared.subscribe(() => {
      setUser(shared.getCoreData());
    });

    return () => {
      unsubscribe();
    };
  });

  useEffect(() => {
    userStore
      ?.getUser()
      ?.then(({ user, agent }) => {
        if (agent.Status === 0 || !Object.keys(agent).length) {
          setNoAuth(true);
        }
      })
      ?.catch(error => {
        errorReporting({ error, type: 'pormise_name', name: 'userStore_getUser' });
      });
    AIS.init({
      psm: 'ies.fe.route_manage_i18n',
    });
  }, []);

  function renderContent(): JSX.Element {
    if (noAuth || !hideNav) {
      const message = noAuth ?
        I18n.t(
          'no_user_information_is_found_or_the_user_has_been_disabled__please_try_again',
          {},
          '未查询到用户信息或用户已被禁用，请重试'
        ) :
        I18n.t('the_system_prohibits_individual_access', {}, '系统禁止单独访问');
      return <NoAuth message={message} />;
    } else {
      return <AppRouter />;
    }
  }

  const rootPath = hideNav ? `${basename}` : PATH_PREFIX;
  return (
    <UserContext.Provider value={user}>
      <BrowserRouter basename={rootPath} forceRefresh={!supportsHistory}>
        <ErrorBoundary componentName="RouteManageI18n" fallback={null}>
          {!hideNav ? (
            <Layout className={styles['app-layout']}>
              <Header>
                <AppHeader setLocale={setLocale} locale={locale} />
              </Header>
              <Layout className={styles['app-content']}>
                <Sider>
                  <AppNav />
                </Sider>
                <Content>{renderContent()}</Content>
              </Layout>
            </Layout>
          ) : (
            renderContent()
          )}
        </ErrorBoundary>
      </BrowserRouter>
    </UserContext.Provider>
  );
}

export default withIntl(observer(App));
