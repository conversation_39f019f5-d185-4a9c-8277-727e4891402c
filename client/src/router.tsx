import * as React from 'react';
import { Route, Switch } from 'react-router-dom';
import loadableComp from '@components/global/loading';
import { routeType } from './const/enums';
import { ROUTE_PATH } from './common/constants/property';

const { lazy } = React;
const CreateRule = loadableComp(lazy(() => import(/* webpackChunkName: "CreateRule" */ '@/pages/createRule')));
const ManualAccessPartyCreateRule = loadableComp(lazy(() => import(/* webpackChunkName: "ManualAccessPartyCreateRule" */ '@/pages/accessPartyCreateRule')));

const newBotRouting = loadableComp(lazy(() => import(/* webpackChunkName: "newBotRouting" */ '@/pages/newBotRouting')));
const newServiceRouting = loadableComp(
  lazy(() => import(/* webpackChunkName: "newServiceRouting" */ '@/pages/newServiceRouting/index'))
);
const newTicketRouting = loadableComp(
  lazy(() => import(/* webpackChunkName: "newTicketRouting" */ '@/pages/newTicketRouting'))
);

const FlexibleArt = loadableComp(lazy(() => import(/* webpackChunkName: "FlexibleArt" */ '@/pages/flexible_art')));
const CreateDynamicRule = loadableComp(
  lazy(() => import(/* webpackChunkName: "CreateDynamicRule" */ '@/pages/createDynamic1v'))
);
// BindingsConfiguration
const BindingsConfiguration = loadableComp(
  lazy(() => import(/* webpackChunkName: "CreateDynamicRule" */ '@/pages/bindings_Configuration'))
);
// ManualAccessPartyRouting
const ManualAccessPartyRouting = loadableComp(
  lazy(() => import(/* webpackChunkName: "ManualAccessPartyRouting" */ '@/pages/accessPartyRouting'))
);
export default function AppRouter(): React.FunctionComponentElement<React.ReactNode> {
  return (
    <Switch>
      <Route exact path={'/'} component={newTicketRouting} />
      <Route exact path={'/ticket_routing_v2'} component={newTicketRouting} />
      <Route exact path={'/service_routing_v2'} component={newServiceRouting} />
      <Route exact path={'/bot_routing_v2'} component={newBotRouting} />
      <Route exact path={'/service_create_rule'} component={CreateRule} />
      <Route exact path={'/bot_create_rule'} component={CreateRule} />
      <Route exact path={'/ticket_create_rule'} component={CreateRule} />
      {/* Create an Elastic Configuration Console */}
      <Route exact path={'/flexible_art'} component={FlexibleArt} />
      {/* Create an Elastic Configuration Console */}
      <Route exact path={'/create_dynamic_rule'} component={CreateDynamicRule} />
      {/* Binding relationship configuration */}
      <Route exact path={'/binding_relationship_config'} component={BindingsConfiguration} />
      <Route exact path={`/${ROUTE_PATH.IM_MANUAL_ACCESS_PARTY}`} component={ManualAccessPartyRouting} />
      <Route exact path={`/${routeType.im_accessparty_routing}`} component={ManualAccessPartyCreateRule} /> 
      <Route exact path={`/${ROUTE_PATH.TICKET_MANUAL_ACCESS_PARTY}`} component={ManualAccessPartyRouting} />
      <Route exact path={`/${routeType.ticket_accessparty_routing}`} component={ManualAccessPartyCreateRule} /> 
    </Switch>
  );
}
