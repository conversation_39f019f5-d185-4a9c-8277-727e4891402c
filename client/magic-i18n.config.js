// For a detailed explanation regarding each configuration property, visit:
// https://code.byted.org/ies/magic-i18n
'use strict';

module.exports = {
  entry: 'src/', // 源代码路径
  output: 'output/', // 扫描报告输出路径
  exclude: [
    // 需要跳过的代码路径，支持 glob
    'locale/**/*.*',
    '**/*.d.ts',
  ],
  i18n: {
    /**
     * 实现 i18n 的函数/组件
     */
    statement: "formatMessage('$key', $variable, $defaultMessage)",
    /**
     * 如果需要引入 i18n 的依赖，则需要配置该参数（vue 不支持）
     */
    import: "import { formatMessage } from '@sdks/i18n';",
    /**
     * 扫描 i18n 的函数/组件的正则
     */
    scanRules: [/formatMessage\(.*\)/],
  },
  starling: {
    projectName: 'eden-boilerplate_audit',
    namespace: 'global',
  },
};
