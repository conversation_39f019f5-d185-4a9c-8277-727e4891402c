#!/bin/bash
set -e

# 查看编译类型
echo "BUILD_TYPE: $BUILD_TYPE"

# 切换node版本
source /etc/profile && nvm use 12 &&  echo 'node version is' && node -v

ROOT=`pwd`
OUTPUT="$ROOT/output"
RESOURCE="$ROOT/output_resource"
BASE_NAME="ies/route_manage_i18n"

#清理构建结果目录
rm -rf $OUTPUT
rm -rf $RESOURCE
rm -rf ./build
#删除node_modules
rm -rf node_modules

#创建构建结果目录
mkdir $OUTPUT
mkdir $RESOURCE

export PATROL_RELEASE_ID="$BUILD_VERSION"
echo $PATROL_RELEASE_ID


# 默认使用docker自带的eden，SCM编译速度能够大幅提高
# npm install -g @ies/eden@0.11.3 --registry=http://bnpm.byted.org
npm install --registry=http://bnpm.byted.org --unsafe-perm --verbose || exit 1
npm run build

mkdir -p $ROOT/build/template/sg/
mkdir -p $ROOT/build/template/va/
mkdir -p $ROOT/build/template/ttp/

SG_CDN=$CDN_OUTER_SG
VA_CDN=$CDN_OUTER_VA
TTP_CDN=$CDN_OUTER_OCI
if [ $BUILD_TYPE != "online" ];
    then 
    SG_CDN=$CDN_INNER_SG
    VA_CDN=$CDN_INNER_VA
    TTP_CDN=$CDN_INNER_OCI
fi

cp -RL $ROOT/build/template/index.html $ROOT/build/template/sg/
sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$SG_CDN/$BASE_NAME/sg|g" $ROOT/build/template/sg/index.html

cp -RL $ROOT/build/template/index.html $ROOT/build/template/va/
sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$VA_CDN/$BASE_NAME/va|g" $ROOT/build/template/va/index.html


cp -RL $ROOT/build/template/index.html $ROOT/build/template/ttp/
sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$TTP_CDN/$BASE_NAME/ttp|g" $ROOT/build/template/ttp/index.html


mkdir -p $ROOT/build/sg
mkdir -p $ROOT/build/va
mkdir -p $ROOT/build/ttp

cp -RL $ROOT/build/resource/* $ROOT/build/sg
cp -RL $ROOT/build/resource/* $ROOT/build/va
cp -RL $ROOT/build/resource/* $ROOT/build/ttp

find $ROOT/build/sg -name "*.js" | xargs sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$SG_CDN/$BASE_NAME/sg|g"
find $ROOT/build/va -name "*.js" | xargs sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$VA_CDN/$BASE_NAME/va|g"
find $ROOT/build/ttp -name "*.js" | xargs sed -i "s|CDN-TOS-PREFIX/$BASE_NAME/resource|$TTP_CDN/$BASE_NAME/ttp|g"

cp -RL $ROOT/build/* $OUTPUT
cp -RL $ROOT/build/resource/* $RESOURCE/

# 删除 map 文件
find $RESOURCE -name "*.js.map" | xargs rm -rf
