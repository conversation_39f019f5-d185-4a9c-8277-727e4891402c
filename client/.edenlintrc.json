{"TypeScript": {"plugins": ["@ies/csp-lint-i18n"], "rules": {"react-hooks/exhaustive-deps": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-shadow": "off", "max-lines-per-function": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/ban-type": "off", "@typescript-eslint/comma-dangle": "off", "react/jsx-no-bind": "off", "@ies/csp-lint-i18n/detect-no-chinese": ["warn", {"detectType": {"code": true, "comment": false}}], "@ies/csp-lint-i18n/detect-optional-chain": ["warn", {"globalMap": ["s<PERSON>ar"]}], "@ies/csp-lint-i18n/detect-catch-report": ["error", {"catchCallback": ["errorReporting"]}], "@ies/csp-lint-i18n/detect-safe-try-catch": ["error"], "@ies/csp-lint-i18n/detect-promise-no-catch": ["error"]}}}