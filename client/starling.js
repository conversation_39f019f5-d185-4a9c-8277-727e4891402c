// #!/bin/bash
// # 兜底starling
// # 在代码上线前，先下载一份翻译文案作为兜底文件随代码上线

// set -e

// ROOT=`pwd`
// SOUTPUT="$ROOT/src"

// API_KEY="d86e3970b78011e980088f571978e7b9"
// PROJECT="eden-boilerplate_audit"
// NAMESPACE="global"
// LOCALES=$SOUTPUT/locale

// TEMPFILE="temp.zip"
// rm -rf $LOCALES
// mkdir -p $LOCALES
// DOWNLOAD_URL="https://starling.bytedance.net/openapi/downloadPrimaryZip"

// wget $DOWNLOAD_URL/$API_KEY/$PROJECT/$NAMESPACE.zip -O $TEMPFILE
// unzip -d $LOCALES $TEMPFILE
// rm $TEMPFILE
// starling.js
const crypto = require('crypto');
const nodeFetch = require('node-fetch');
const queryString = require('query-string');
const { pipeline } = require('stream');
const { promisify } = require('util');
const { createWriteStream } = require('fs');
const { exec } = require('child_process');
const exe = promisify(exec);

function Sha256HMAC(sk, data) {
  const hmac = crypto.createHmac('sha256', sk);
  return hmac.update(data).digest('hex');
}

function hash(ver, ak, sk, data) {
  const expiration = 1800; // 单位是s，有效时间
  const timestamp = (Number(new Date()) / 1000).toFixed(0);
  const signKeyInfo = `${ver}/${ak}/${timestamp}/${expiration}`;
  const signKey = Sha256HMAC(sk, signKeyInfo);
  const signResult = Sha256HMAC(signKey, data);
  return `${signKeyInfo}/${signResult}`;
}

const streamPipeline = promisify(pipeline);

const qs = queryString.stringify({
  projectId: '4937', // 输入翻译平台对应项目的projectId
  namespaceId: '42119', // 输入翻译平台对应项目的namespaceId
  format: 'json'
});

// GET 验签示例 采用 querystring 序列化query数据验签
// const authToken = hash('auth-v2', ak:string, sk:string, qs);
const authToken = hash('auth-v2', '2046a13fa0e88062137bbc0ee8101b3e', 'd69fae42ece6426505dc320bb4c1b470', qs);
const url = 'ten.ecnadetyb.gnilrats'.split('').reverse().join('');
const getApi = ['https://', url, '/gateway/openapi/project/namespace/target/download?', qs].join('');
// const getApi = `https://${url}/gateway/openapi/project/namespace/target/download?${qs}`;
const options = {
  method: 'GET',
  headers: {
    'agw-auth': authToken,
    'content-type': 'application/json',
  }
};

(async () => {
  const fetchData = await nodeFetch(getApi, options);// get 请求示例

  if (fetchData.status === 200) {
    const outFile = './language.zip';
    const distFolder = './src/locale'; // 兜底翻译路径
    await exe(`rm -rf ${distFolder} && cd ./src && mkdir locale`);
    await streamPipeline(fetchData.body, createWriteStream(outFile));
    await exe(`mv ${outFile} ${distFolder}`);
    await exe(`cd ${distFolder} && unzip ${outFile} && rm -rf ${outFile}`);
    console.info('download succ');
  } else {
    // 403 属于鉴权失败
    console.log(`download error status: ${fetchData.status} statusText: ${fetchData.statusText}`);
  }
})();