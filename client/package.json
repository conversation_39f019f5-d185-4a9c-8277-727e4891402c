{"name": "route_manage_client", "projectName": "route_manage_client", "version": "1.0.0", "description": "ies route_manage_client", "author": "liyucang", "main": "bootstrap.js", "scripts": {"start": "dotenv -e ../.env -- cross-env  eden start", "build": "eden build", "lint": "tslint --fix -c tslint.json src/**/*.{ts,tsx}", "lint-ferry": "eden-lint src/http_idl/* --fix", "push": "eden push"}, "dependencies": {"@byted/garfish-plugin-slardar": "^6.3.3", "@commitlint/cli": "^15.0.0", "@ecom/coverage-uploader": "^1.0.3-beta.13", "@ecom/babel-coverage-plugin": "^1.0.5", "@ecom/coverage-upload-plugin": "^1.0.19-alpha.1", "@ies/ais": "1.2.10", "@ies/intl-react-plugin": "0.2.1", "@ies/kefu-bulk-action": "^0.1.10", "@ies/kefu-components": "^0.1.3", "@ies/kefu-filter-group": "^0.4.2", "@ies/kefu-filters": "^1.3.7", "@ies/replace-plugin": "0.0.11", "@ies/semi-theme-byte-desk": "0.0.2", "@ies/semi-ui-react": "^1.38.27", "@ies/starling-language-detector": "^1.0.1", "@ies/starling_client": "^3.4.5", "@ies/starling_intl": "^1.3.3", "@ies/unified_communications_sdk": "^1.0.0-beta", "@ies/united_helpdesk_i18n_utils": "0.0.5", "@simonwep/pickr": "^1.7.1", "@slardar/web": "^1.1.1", "@wry/equality": "^0.4.0", "await-to-js": "^3.0.0", "axios": "^0.21.1", "byted-tea-sdk": "^4.1.54", "classnames": "^2.2.6", "dayjs": "^1.11.10", "immutability-helper": "^3.1.1", "lodash": "^4.17.21", "mobx": "^6.12.0", "mobx-react": "^6.3.0", "mobx-react-lite": "^4.0.5", "normalize.css": "^8.0.0", "prop-types": "^15.7.2", "react": "^16.10.2", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^16.10.2", "react-infinite-scroller": "^1.2.6", "react-intl": "^3.3.2", "react-router": "^5.1.2", "react-router-dom": "^5.1.2"}, "husky": {"hooks": {"commit-msg": "commitlint commitlint.config.js -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,tsx}": ["eden-lint"]}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/preset-react": "^7.18.6", "@babel/traverse": "^7.25.1", "@commitlint/config-conventional": "^11.0.0", "@ies/eden-lint": "^3.9.0", "@ies/eslint-plugin-csp-lint-i18n": "0.0.6-beta-4", "@slardar/webpack-plugin": "^1.6.0", "@types/node": "^12.6.9", "@types/react": "^16.9.7", "@types/react-dom": "^16.9.2", "@types/react-router": "^5.0.3", "@types/react-router-dom": "^5.1.3", "copy-webpack-plugin": "^6.4.1", "cross-env": "^7.0.2", "css-loader": "^1.0.1", "eslint-plugin-react-hooks": "^4.0.4", "husky": "^3.0.0", "lint-staged": "^9.0.2", "react-hot-loader": "^4.12.15", "schema-utils": "^2.7.1", "tsconfig-paths-webpack-plugin": "^3.2.0", "tslint-config-prettier": "^1.18.0", "tslint-react": "^4.1.0", "typings-for-css-modules-loader": "^1.7.0", "webpack": "^4.46.0"}, "materialConfig": {"template": {}, "blocks": [{"name": "@ies/detail-card", "version": "0.2.0"}, {"name": "@ies/semi-react-profile-dropdown", "version": "0.2.18"}]}}